{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeReleaseResources-87:/values-b+es+419/values-b+es+419.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-b+es+419\\values-b+es+419.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,233,319,421,549,630,695,790,860,923,1016,1088,1151,1225,1289,1346,1464,1522,1584,1641,1721,1855,1944,2025,2136,2217,2297,2387,2454,2520,2596,2678,2766,2839,2916,2986,3063,3152,3226,3320,3422,3494,3575,3679,3732,3799,3892,3981,4043,4107,4170,4281,4378,4480,4578", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,85,101,127,80,64,94,69,62,92,71,62,73,63,56,117,57,61,56,79,133,88,80,110,80,79,89,66,65,75,81,87,72,76,69,76,88,73,93,101,71,80,103,52,66,92,88,61,63,62,110,96,101,97,82", "endOffsets": "228,314,416,544,625,690,785,855,918,1011,1083,1146,1220,1284,1341,1459,1517,1579,1636,1716,1850,1939,2020,2131,2212,2292,2382,2449,2515,2591,2673,2761,2834,2911,2981,3058,3147,3221,3315,3417,3489,3570,3674,3727,3794,3887,3976,4038,4102,4165,4276,4373,4475,4573,4656"}}]}]}