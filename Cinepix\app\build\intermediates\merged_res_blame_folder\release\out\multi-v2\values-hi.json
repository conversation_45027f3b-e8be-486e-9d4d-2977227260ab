{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeReleaseResources-87:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,269,347,489,658,738", "endColumns": "72,90,77,141,168,79,77", "endOffsets": "173,264,342,484,653,733,811"}, "to": {"startLines": "69,158,258,263,272,290,291", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5540,13513,22872,23264,24097,25816,25896", "endColumns": "72,90,77,141,168,79,77", "endOffsets": "5608,13599,22945,23401,24261,25891,25969"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,189,260,328,424,492,615,736", "endColumns": "67,65,70,67,95,67,122,120,86", "endOffsets": "118,184,255,323,419,487,610,731,818"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11615,11683,11749,11820,11888,11984,12052,12175,12296", "endColumns": "67,65,70,67,95,67,122,120,86", "endOffsets": "11678,11744,11815,11883,11979,12047,12170,12291,12378"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,159", "endColumns": "103,113", "endOffsets": "154,268"}, "to": {"startLines": "254,255", "startColumns": "4,4", "startOffsets": "22480,22584", "endColumns": "103,113", "endOffsets": "22579,22693"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,493,699,782,863,946,1041,1141,1210,1273,1359,1445,1510,1574,1638,1706,1819,1935,2047,2120,2204,2273,2342,2426,2508,2575,2638,2691,2753,2807,2868,2928,2995,3058,3128,3189,3251,3317,3380,3447,3507,3567,3641,3715", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,80,82,94,99,68,62,85,85,64,63,63,67,112,115,111,72,83,68,68,83,81,66,62,52,61,53,60,59,66,62,69,60,61,65,62,66,59,59,73,73,52", "endOffsets": "281,488,694,777,858,941,1036,1136,1205,1268,1354,1440,1505,1569,1633,1701,1814,1930,2042,2115,2199,2268,2337,2421,2503,2570,2633,2686,2748,2802,2863,2923,2990,3053,3123,3184,3246,3312,3375,3442,3502,3562,3636,3710,3763"}, "to": {"startLines": "2,11,15,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,543,9676,9759,9840,9923,10018,10118,10187,10250,10336,10422,10487,10551,10615,10683,10796,10912,11024,11097,11181,11250,11319,11403,11485,11552,12383,12436,12498,12552,12613,12673,12740,12803,12873,12934,12996,13062,13125,13192,13252,13312,13386,13460", "endLines": "10,14,18,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "endColumns": "17,12,12,82,80,82,94,99,68,62,85,85,64,63,63,67,112,115,111,72,83,68,68,83,81,66,62,52,61,53,60,59,66,62,69,60,61,65,62,66,59,59,73,73,52", "endOffsets": "331,538,744,9754,9835,9918,10013,10113,10182,10245,10331,10417,10482,10546,10610,10678,10791,10907,11019,11092,11176,11245,11314,11398,11480,11547,11610,12431,12493,12547,12608,12668,12735,12798,12868,12929,12991,13057,13120,13187,13247,13307,13381,13455,13508"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "56,57,58,59,60,61,62,270", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4205,4303,4406,4511,4612,4725,4831,23895", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "4298,4401,4506,4607,4720,4826,4953,23991"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,509,631,743,843,937,1069,1201,1313,1441,1594,1738,1882,1989,2080,2211,2301,2406,2511,2616,2715,2840,2949,3073,3197,3299,3406,3536,3657,3783,3905,3992,4075,4171,4306,4460", "endColumns": "106,100,98,96,121,111,99,93,131,131,111,127,152,143,143,106,90,130,89,104,104,104,98,124,108,123,123,101,106,129,120,125,121,86,82,95,134,153,97", "endOffsets": "207,308,407,504,626,738,838,932,1064,1196,1308,1436,1589,1733,1877,1984,2075,2206,2296,2401,2506,2611,2710,2835,2944,3068,3192,3294,3401,3531,3652,3778,3900,3987,4070,4166,4301,4455,4553"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14082,14189,14290,14389,14486,14608,14720,14820,14914,15046,15178,15290,15418,15571,15715,15859,15966,16057,16188,16278,16383,16488,16593,16692,16817,16926,17050,17174,17276,17383,17513,17634,17760,17882,17969,18052,18148,18283,23087", "endColumns": "106,100,98,96,121,111,99,93,131,131,111,127,152,143,143,106,90,130,89,104,104,104,98,124,108,123,123,101,106,129,120,125,121,86,82,95,134,153,97", "endOffsets": "14184,14285,14384,14481,14603,14715,14815,14909,15041,15173,15285,15413,15566,15710,15854,15961,16052,16183,16273,16378,16483,16588,16687,16812,16921,17045,17169,17271,17378,17508,17629,17755,17877,17964,18047,18143,18278,18432,23180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "908,1014,1112,1222,1308,1410,1531,1609,1686,1777,1870,1965,2059,2159,2252,2347,2441,2532,2623,2704,2809,2911,3009,3119,3222,3331,3489,23654", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "1009,1107,1217,1303,1405,1526,1604,1681,1772,1865,1960,2054,2154,2247,2342,2436,2527,2618,2699,2804,2906,3004,3114,3217,3326,3484,3585,23731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,129,205,273,340,416,495,582", "endColumns": "73,75,67,66,75,78,86,91", "endOffsets": "124,200,268,335,411,490,577,669"}, "to": {"startLines": "50,102,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3590,9276,19299,19367,19434,19510,19589,19676", "endColumns": "73,75,67,66,75,78,86,91", "endOffsets": "3659,9347,19362,19429,19505,19584,19671,19763"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,283,403,515,597,689,799,933,1049,1199,1280,1379,1466,1559,1680,1796,1900,2041,2183,2309,2476,2599,2709,2824,2947,3035,3126,3250,3372,3467,3565,3673,3814,3962,4072,4167,4239,4320,4402,4488,4589,4665,4745,4841,4937,5028,5126,5208,5306,5400,5499,5610,5686,5782", "endColumns": "113,113,119,111,81,91,109,133,115,149,80,98,86,92,120,115,103,140,141,125,166,122,109,114,122,87,90,123,121,94,97,107,140,147,109,94,71,80,81,85,100,75,79,95,95,90,97,81,97,93,98,110,75,95,89", "endOffsets": "164,278,398,510,592,684,794,928,1044,1194,1275,1374,1461,1554,1675,1791,1895,2036,2178,2304,2471,2594,2704,2819,2942,3030,3121,3245,3367,3462,3560,3668,3809,3957,4067,4162,4234,4315,4397,4483,4584,4660,4740,4836,4932,5023,5121,5203,5301,5395,5494,5605,5681,5777,5867"}, "to": {"startLines": "51,52,53,54,68,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,104,159,266,269,271,276,277,278,279,280,281,282,283,284,285,286,287,288,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3664,3778,3892,4012,5458,5613,5705,5815,5949,6065,6215,6296,6395,6482,6575,6696,6812,6916,7057,7199,7325,7492,7615,7725,7840,7963,8051,8142,8266,8388,8483,8581,8689,8830,8978,9088,9450,13604,23572,23809,23996,24533,24609,24689,24785,24881,24972,25070,25152,25250,25344,25443,25554,25630,25726", "endColumns": "113,113,119,111,81,91,109,133,115,149,80,98,86,92,120,115,103,140,141,125,166,122,109,114,122,87,90,123,121,94,97,107,140,147,109,94,71,80,81,85,100,75,79,95,95,90,97,81,97,93,98,110,75,95,89", "endOffsets": "3773,3887,4007,4119,5535,5700,5810,5944,6060,6210,6291,6390,6477,6570,6691,6807,6911,7052,7194,7320,7487,7610,7720,7835,7958,8046,8137,8261,8383,8478,8576,8684,8825,8973,9083,9178,9517,13680,23649,23890,24092,24604,24684,24780,24876,24967,25065,25147,25245,25339,25438,25549,25625,25721,25811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,376,474,563,641,738,827,912,980,1049,1130,1215,1288,1369,1435", "endColumns": "94,82,92,97,88,77,96,88,84,67,68,80,84,72,80,65,119", "endOffsets": "195,278,371,469,558,636,733,822,907,975,1044,1125,1210,1283,1364,1430,1550"}, "to": {"startLines": "66,67,101,103,105,162,163,256,257,259,260,264,265,268,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5280,5375,9183,9352,9522,13848,13926,22698,22787,22950,23018,23406,23487,23736,24266,24347,24413", "endColumns": "94,82,92,97,88,77,96,88,84,67,68,80,84,72,80,65,119", "endOffsets": "5370,5453,9271,9445,9606,13921,14018,22782,22867,23013,23082,23482,23567,23804,24342,24408,24528"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,214,295,402,534,617,682,776,845,904,989,1052,1110,1175,1236,1297,1403,1461,1521,1580,1650,1766,1845,1925,2029,2104,2180,2277,2344,2410,2480,2557,2643,2711,2787,2868,2946,3032,3119,3216,3315,3389,3459,3563,3617,3684,3774,3866,3928,3992,4055,4160,4268,4369,4478", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,80,106,131,82,64,93,68,58,84,62,57,64,60,60,105,57,59,58,69,115,78,79,103,74,75,96,66,65,69,76,85,67,75,80,77,85,86,96,98,73,69,103,53,66,89,91,61,63,62,104,107,100,108,78", "endOffsets": "209,290,397,529,612,677,771,840,899,984,1047,1105,1170,1231,1292,1398,1456,1516,1575,1645,1761,1840,1920,2024,2099,2175,2272,2339,2405,2475,2552,2638,2706,2782,2863,2941,3027,3114,3211,3310,3384,3454,3558,3612,3679,3769,3861,3923,3987,4050,4155,4263,4364,4473,4552"}, "to": {"startLines": "19,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "749,4124,4958,5065,5197,9611,13685,13779,14023,18437,18522,18585,18643,18708,18769,18830,18936,18994,19054,19113,19183,19768,19847,19927,20031,20106,20182,20279,20346,20412,20482,20559,20645,20713,20789,20870,20948,21034,21121,21218,21317,21391,21461,21565,21619,21686,21776,21868,21930,21994,22057,22162,22270,22371,23185", "endLines": "22,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "endColumns": "12,80,106,131,82,64,93,68,58,84,62,57,64,60,60,105,57,59,58,69,115,78,79,103,74,75,96,66,65,69,76,85,67,75,80,77,85,86,96,98,73,69,103,53,66,89,91,61,63,62,104,107,100,108,78", "endOffsets": "903,4200,5060,5192,5275,9671,13774,13843,14077,18517,18580,18638,18703,18764,18825,18931,18989,19049,19108,19178,19294,19842,19922,20026,20101,20177,20274,20341,20407,20477,20554,20640,20708,20784,20865,20943,21029,21116,21213,21312,21386,21456,21560,21614,21681,21771,21863,21925,21989,22052,22157,22265,22366,22475,23259"}}]}]}