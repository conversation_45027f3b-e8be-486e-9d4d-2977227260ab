{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeReleaseResources-87:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,343,434,511,585,662,740,815,880,945,1018,1093,1161,1234,1300", "endColumns": "76,75,84,90,76,73,76,77,74,64,64,72,74,67,72,65,115", "endOffsets": "177,253,338,429,506,580,657,735,810,875,940,1013,1088,1156,1229,1295,1411"}, "to": {"startLines": "66,67,101,103,105,162,163,256,257,259,260,264,265,268,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4824,4901,8130,8282,8442,12113,12187,19890,19968,20113,20178,20513,20586,20819,21322,21395,21461", "endColumns": "76,75,84,90,76,73,76,77,74,64,64,72,74,67,72,65,115", "endOffsets": "4896,4972,8210,8368,8514,12182,12259,19963,20038,20173,20238,20581,20656,20882,21390,21456,21572"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,225,278,350,404,479,557", "endColumns": "55,55,57,52,71,53,74,77,58", "endOffsets": "106,162,220,273,345,399,474,552,611"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10231,10287,10343,10401,10454,10526,10580,10655,10733", "endColumns": "55,55,57,52,71,53,74,77,58", "endOffsets": "10282,10338,10396,10449,10521,10575,10650,10728,10787"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,156", "endColumns": "100,102", "endOffsets": "151,254"}, "to": {"startLines": "254,255", "startColumns": "4,4", "startOffsets": "19686,19787", "endColumns": "100,102", "endOffsets": "19782,19885"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,450,608,678,747,815,892,968,1022,1084,1158,1232,1294,1355,1414,1480,1568,1651,1739,1802,1869,1934,1988,2062,2135,2196,2258,2310,2368,2415,2476,2533,2595,2652,2713,2769,2824,2887,2949,3012,3061,3112,3177,3242", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,69,68,67,76,75,53,61,73,73,61,60,58,65,87,82,87,62,66,64,53,73,72,60,61,51,57,46,60,56,61,56,60,55,54,62,61,62,48,50,64,64,48", "endOffsets": "282,445,603,673,742,810,887,963,1017,1079,1153,1227,1289,1350,1409,1475,1563,1646,1734,1797,1864,1929,1983,2057,2130,2191,2253,2305,2363,2410,2471,2528,2590,2647,2708,2764,2819,2882,2944,3007,3056,3107,3172,3237,3286"}, "to": {"startLines": "2,11,15,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,500,8581,8651,8720,8788,8865,8941,8995,9057,9131,9205,9267,9328,9387,9453,9541,9624,9712,9775,9842,9907,9961,10035,10108,10169,10792,10844,10902,10949,11010,11067,11129,11186,11247,11303,11358,11421,11483,11546,11595,11646,11711,11776", "endLines": "10,14,18,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "endColumns": "17,12,12,69,68,67,76,75,53,61,73,73,61,60,58,65,87,82,87,62,66,64,53,73,72,60,61,51,57,46,60,56,61,56,60,55,54,62,61,62,48,50,64,64,48", "endOffsets": "332,495,653,8646,8715,8783,8860,8936,8990,9052,9126,9200,9262,9323,9382,9448,9536,9619,9707,9770,9837,9902,9956,10030,10103,10164,10226,10839,10897,10944,11005,11062,11124,11181,11242,11298,11353,11416,11478,11541,11590,11641,11706,11771,11820"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,155,254,354,452,524,607,707,811,912,1023,1101,1193,1273,1358,1460,1570,1667,1771,1873,1977,2089,2192,2289,2390,2493,2574,2665,2766,2871,2957,3051,3145,3248,3357,3453,3539,3608,3679,3758,3836,3924,4000,4077,4171,4261,4350,4441,4520,4612,4704,4796,4900,4976,5064", "endColumns": "99,98,99,97,71,82,99,103,100,110,77,91,79,84,101,109,96,103,101,103,111,102,96,100,102,80,90,100,104,85,93,93,102,108,95,85,68,70,78,77,87,75,76,93,89,88,90,78,91,91,91,103,75,87,85", "endOffsets": "150,249,349,447,519,602,702,806,907,1018,1096,1188,1268,1353,1455,1565,1662,1766,1868,1972,2084,2187,2284,2385,2488,2569,2660,2761,2866,2952,3046,3140,3243,3352,3448,3534,3603,3674,3753,3831,3919,3995,4072,4166,4256,4345,4436,4515,4607,4699,4791,4895,4971,5059,5145"}, "to": {"startLines": "51,52,53,54,68,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,104,159,266,269,271,276,277,278,279,280,281,282,283,284,285,286,287,288,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3423,3523,3622,3722,4977,5115,5198,5298,5402,5503,5614,5692,5784,5864,5949,6051,6161,6258,6362,6464,6568,6680,6783,6880,6981,7084,7165,7256,7357,7462,7548,7642,7736,7839,7948,8044,8373,11906,20661,20887,21066,21577,21653,21730,21824,21914,22003,22094,22173,22265,22357,22449,22553,22629,22717", "endColumns": "99,98,99,97,71,82,99,103,100,110,77,91,79,84,101,109,96,103,101,103,111,102,96,100,102,80,90,100,104,85,93,93,102,108,95,85,68,70,78,77,87,75,76,93,89,88,90,78,91,91,91,103,75,87,85", "endOffsets": "3518,3617,3717,3815,5044,5193,5293,5397,5498,5609,5687,5779,5859,5944,6046,6156,6253,6357,6459,6563,6675,6778,6875,6976,7079,7160,7251,7352,7457,7543,7637,7731,7834,7943,8039,8125,8437,11972,20735,20960,21149,21648,21725,21819,21909,21998,22089,22168,22260,22352,22444,22548,22624,22712,22798"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,322,442,610,690", "endColumns": "65,80,69,119,167,79,76", "endOffsets": "166,247,317,437,605,685,762"}, "to": {"startLines": "69,158,258,263,272,290,291", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5049,11825,20043,20393,21154,22803,22883", "endColumns": "65,80,69,119,167,79,76", "endOffsets": "5110,11901,20108,20508,21317,22878,22955"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "805,900,995,1095,1177,1274,1380,1457,1532,1623,1716,1813,1909,2003,2096,2191,2283,2374,2465,2543,2639,2734,2829,2926,3022,3120,3268,20740", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "895,990,1090,1172,1269,1375,1452,1527,1618,1711,1808,1904,1998,2091,2186,2278,2369,2460,2538,2634,2729,2824,2921,3017,3115,3263,3357,20814"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,116,183,248,312,381,453,529", "endColumns": "60,66,64,63,68,71,75,79", "endOffsets": "111,178,243,307,376,448,524,604"}, "to": {"startLines": "50,102,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3362,8215,16917,16982,17046,17115,17187,17263", "endColumns": "60,66,64,63,68,71,75,79", "endOffsets": "3418,8277,16977,17041,17110,17182,17258,17338"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,317,409,499,612,713,807,896,1006,1115,1210,1322,1424,1532,1639,1736,1824,1930,2017,2114,2211,2308,2397,2504,2597,2699,2800,2895,2994,3092,3196,3292,3394,3481,3561,3654,3784,3927", "endColumns": "108,102,91,89,112,100,93,88,109,108,94,111,101,107,106,96,87,105,86,96,96,96,88,106,92,101,100,94,98,97,103,95,101,86,79,92,129,142,80", "endOffsets": "209,312,404,494,607,708,802,891,1001,1110,1205,1317,1419,1527,1634,1731,1819,1925,2012,2109,2206,2303,2392,2499,2592,2694,2795,2890,2989,3087,3191,3287,3389,3476,3556,3649,3779,3922,4003"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12323,12432,12535,12627,12717,12830,12931,13025,13114,13224,13333,13428,13540,13642,13750,13857,13954,14042,14148,14235,14332,14429,14526,14615,14722,14815,14917,15018,15113,15212,15310,15414,15510,15612,15699,15779,15872,16002,20243", "endColumns": "108,102,91,89,112,100,93,88,109,108,94,111,101,107,106,96,87,105,86,96,96,96,88,106,92,101,100,94,98,97,103,95,101,86,79,92,129,142,80", "endOffsets": "12427,12530,12622,12712,12825,12926,13020,13109,13219,13328,13423,13535,13637,13745,13852,13949,14037,14143,14230,14327,14424,14521,14610,14717,14810,14912,15013,15108,15207,15305,15409,15505,15607,15694,15774,15867,15997,16140,20319"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "56,57,58,59,60,61,62,270", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3890,3982,4083,4177,4271,4364,4458,20965", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "3977,4078,4172,4266,4359,4453,4549,21061"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,272,363,469,542,604,681,740,799,877,934,990,1049,1107,1161,1246,1302,1360,1414,1479,1571,1645,1721,1813,1875,1937,2016,2083,2149,2213,2282,2360,2421,2492,2559,2619,2698,2765,2848,2933,3007,3072,3148,3196,3260,3336,3414,3476,3540,3603,3683,3759,3837,3914", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,69,90,105,72,61,76,58,58,77,56,55,58,57,53,84,55,57,53,64,91,73,75,91,61,61,78,66,65,63,68,77,60,70,66,59,78,66,82,84,73,64,75,47,63,75,77,61,63,62,79,75,77,76,68", "endOffsets": "197,267,358,464,537,599,676,735,794,872,929,985,1044,1102,1156,1241,1297,1355,1409,1474,1566,1640,1716,1808,1870,1932,2011,2078,2144,2208,2277,2355,2416,2487,2554,2614,2693,2760,2843,2928,3002,3067,3143,3191,3255,3331,3409,3471,3535,3598,3678,3754,3832,3909,3978"}, "to": {"startLines": "19,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "658,3820,4554,4645,4751,8519,11977,12054,12264,16145,16223,16280,16336,16395,16453,16507,16592,16648,16706,16760,16825,17343,17417,17493,17585,17647,17709,17788,17855,17921,17985,18054,18132,18193,18264,18331,18391,18470,18537,18620,18705,18779,18844,18920,18968,19032,19108,19186,19248,19312,19375,19455,19531,19609,20324", "endLines": "22,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "endColumns": "12,69,90,105,72,61,76,58,58,77,56,55,58,57,53,84,55,57,53,64,91,73,75,91,61,61,78,66,65,63,68,77,60,70,66,59,78,66,82,84,73,64,75,47,63,75,77,61,63,62,79,75,77,76,68", "endOffsets": "800,3885,4640,4746,4819,8576,12049,12108,12318,16218,16275,16331,16390,16448,16502,16587,16643,16701,16755,16820,16912,17412,17488,17580,17642,17704,17783,17850,17916,17980,18049,18127,18188,18259,18326,18386,18465,18532,18615,18700,18774,18839,18915,18963,19027,19103,19181,19243,19307,19370,19450,19526,19604,19681,20388"}}]}]}