{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeReleaseResources-87:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,415,514,630,739,834,928,1064,1199,1310,1440,1569,1693,1816,1925,2022,2163,2253,2361,2475,2581,2680,2796,2903,3013,3122,3228,3340,3466,3598,3722,3852,3939,4022,4123,4258,4410", "endColumns": "106,100,101,98,115,108,94,93,135,134,110,129,128,123,122,108,96,140,89,107,113,105,98,115,106,109,108,105,111,125,131,123,129,86,82,100,134,151,91", "endOffsets": "207,308,410,509,625,734,829,923,1059,1194,1305,1435,1564,1688,1811,1920,2017,2158,2248,2356,2470,2576,2675,2791,2898,3008,3117,3223,3335,3461,3593,3717,3847,3934,4017,4118,4253,4405,4497"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13817,13924,14025,14127,14226,14342,14451,14546,14640,14776,14911,15022,15152,15281,15405,15528,15637,15734,15875,15965,16073,16187,16293,16392,16508,16615,16725,16834,16940,17052,17178,17310,17434,17564,17651,17734,17835,17970,22771", "endColumns": "106,100,101,98,115,108,94,93,135,134,110,129,128,123,122,108,96,140,89,107,113,105,98,115,106,109,108,105,111,125,131,123,129,86,82,100,134,151,91", "endOffsets": "13919,14020,14122,14221,14337,14446,14541,14635,14771,14906,15017,15147,15276,15400,15523,15632,15729,15870,15960,16068,16182,16288,16387,16503,16610,16720,16829,16935,17047,17173,17305,17429,17559,17646,17729,17830,17965,18117,22858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,280,387,500,578,670,776,901,1011,1146,1226,1328,1415,1504,1614,1738,1845,1969,2091,2220,2389,2509,2622,2741,2859,2947,3038,3153,3270,3367,3466,3566,3692,3824,3927,4019,4091,4171,4253,4338,4428,4507,4586,4681,4777,4865,4962,5046,5149,5247,5354,5471,5549,5653", "endColumns": "111,112,106,112,77,91,105,124,109,134,79,101,86,88,109,123,106,123,121,128,168,119,112,118,117,87,90,114,116,96,98,99,125,131,102,91,71,79,81,84,89,78,78,94,95,87,96,83,102,97,106,116,77,103,94", "endOffsets": "162,275,382,495,573,665,771,896,1006,1141,1221,1323,1410,1499,1609,1733,1840,1964,2086,2215,2384,2504,2617,2736,2854,2942,3033,3148,3265,3362,3461,3561,3687,3819,3922,4014,4086,4166,4248,4333,4423,4502,4581,4676,4772,4860,4957,5041,5144,5242,5349,5466,5544,5648,5743"}, "to": {"startLines": "51,52,53,54,68,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,104,159,266,269,271,276,277,278,279,280,281,282,283,284,285,286,287,288,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3672,3784,3897,4004,5399,5553,5645,5751,5876,5986,6121,6201,6303,6390,6479,6589,6713,6820,6944,7066,7195,7364,7484,7597,7716,7834,7922,8013,8128,8245,8342,8441,8541,8667,8799,8902,9266,13345,23247,23484,23670,24195,24274,24353,24448,24544,24632,24729,24813,24916,25014,25121,25238,25316,25420", "endColumns": "111,112,106,112,77,91,105,124,109,134,79,101,86,88,109,123,106,123,121,128,168,119,112,118,117,87,90,114,116,96,98,99,125,131,102,91,71,79,81,84,89,78,78,94,95,87,96,83,102,97,106,116,77,103,94", "endOffsets": "3779,3892,3999,4112,5472,5640,5746,5871,5981,6116,6196,6298,6385,6474,6584,6708,6815,6939,7061,7190,7359,7479,7592,7711,7829,7917,8008,8123,8240,8337,8436,8536,8662,8794,8897,8989,9333,13420,23324,23564,23755,24269,24348,24443,24539,24627,24724,24808,24911,25009,25116,25233,25311,25415,25510"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-si\\values-si.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,302,404,519,602,666,755,822,882,976,1039,1095,1165,1232,1287,1406,1463,1527,1581,1654,1776,1859,1944,2046,2124,2204,2290,2357,2423,2493,2566,2648,2720,2797,2869,2939,3032,3105,3195,3288,3362,3434,3525,3579,3645,3729,3814,3876,3940,4003,4108,4208,4303,4403", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,77,101,114,82,63,88,66,59,93,62,55,69,66,54,118,56,63,53,72,121,82,84,101,77,79,85,66,65,69,72,81,71,76,71,69,92,72,89,92,73,71,90,53,65,83,84,61,63,62,104,99,94,99,79", "endOffsets": "219,297,399,514,597,661,750,817,877,971,1034,1090,1160,1227,1282,1401,1458,1522,1576,1649,1771,1854,1939,2041,2119,2199,2285,2352,2418,2488,2561,2643,2715,2792,2864,2934,3027,3100,3190,3283,3357,3429,3520,3574,3640,3724,3809,3871,3935,3998,4103,4203,4298,4398,4478"}, "to": {"startLines": "19,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "716,4117,4927,5029,5144,9420,13425,13514,13757,18122,18216,18279,18335,18405,18472,18527,18646,18703,18767,18821,18894,19523,19606,19691,19793,19871,19951,20037,20104,20170,20240,20313,20395,20467,20544,20616,20686,20779,20852,20942,21035,21109,21181,21272,21326,21392,21476,21561,21623,21687,21750,21855,21955,22050,22863", "endLines": "22,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "endColumns": "12,77,101,114,82,63,88,66,59,93,62,55,69,66,54,118,56,63,53,72,121,82,84,101,77,79,85,66,65,69,72,81,71,76,71,69,92,72,89,92,73,71,90,53,65,83,84,61,63,62,104,99,94,99,79", "endOffsets": "880,4190,5024,5139,5222,9479,13509,13576,13812,18211,18274,18330,18400,18467,18522,18641,18698,18762,18816,18889,19011,19601,19686,19788,19866,19946,20032,20099,20165,20235,20308,20390,20462,20539,20611,20681,20774,20847,20937,21030,21104,21176,21267,21321,21387,21471,21556,21618,21682,21745,21850,21950,22045,22145,22938"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "56,57,58,59,60,61,62,270", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4195,4297,4400,4505,4610,4709,4813,23569", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "4292,4395,4500,4605,4704,4808,4922,23665"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,488,666,750,833,914,1007,1099,1162,1224,1313,1404,1475,1545,1606,1672,1811,1953,2090,2161,2240,2310,2375,2465,2554,2621,2689,2742,2800,2847,2908,2968,3035,3096,3161,3220,3285,3354,3417,3484,3538,3595,3666,3737", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,83,82,80,92,91,62,61,88,90,70,69,60,65,138,141,136,70,78,69,64,89,88,66,67,52,57,46,60,59,66,60,64,58,64,68,62,66,53,56,70,70,51", "endOffsets": "282,483,661,745,828,909,1002,1094,1157,1219,1308,1399,1470,1540,1601,1667,1806,1948,2085,2156,2235,2305,2370,2460,2549,2616,2684,2737,2795,2842,2903,2963,3030,3091,3156,3215,3280,3349,3412,3479,3533,3590,3661,3732,3784"}, "to": {"startLines": "2,11,15,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,538,9484,9568,9651,9732,9825,9917,9980,10042,10131,10222,10293,10363,10424,10490,10629,10771,10908,10979,11058,11128,11193,11283,11372,11439,12162,12215,12273,12320,12381,12441,12508,12569,12634,12693,12758,12827,12890,12957,13011,13068,13139,13210", "endLines": "10,14,18,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "endColumns": "17,12,12,83,82,80,92,91,62,61,88,90,70,69,60,65,138,141,136,70,78,69,64,89,88,66,67,52,57,46,60,59,66,60,64,58,64,68,62,66,53,56,70,70,51", "endOffsets": "332,533,711,9563,9646,9727,9820,9912,9975,10037,10126,10217,10288,10358,10419,10485,10624,10766,10903,10974,11053,11123,11188,11278,11367,11434,11502,12210,12268,12315,12376,12436,12503,12564,12629,12688,12753,12822,12885,12952,13006,13063,13134,13205,13257"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,277,376,475,557,642,733,819,899,976,1051,1130,1212,1285,1366,1433", "endColumns": "88,82,98,98,81,84,90,85,79,76,74,78,81,72,80,66,117", "endOffsets": "189,272,371,470,552,637,728,814,894,971,1046,1125,1207,1280,1361,1428,1546"}, "to": {"startLines": "66,67,101,103,105,162,163,256,257,259,260,264,265,268,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5227,5316,8994,9167,9338,13581,13666,22378,22464,22619,22696,23086,23165,23411,23929,24010,24077", "endColumns": "88,82,98,98,81,84,90,85,79,76,74,78,81,72,80,66,117", "endOffsets": "5311,5394,9088,9261,9415,13661,13752,22459,22539,22691,22766,23160,23242,23479,24005,24072,24190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,191,254,320,396,465,554,640", "endColumns": "75,59,62,65,75,68,88,85,69", "endOffsets": "126,186,249,315,391,460,549,635,705"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11507,11583,11643,11706,11772,11848,11917,12006,12092", "endColumns": "75,59,62,65,75,68,88,85,69", "endOffsets": "11578,11638,11701,11767,11843,11912,12001,12087,12157"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,116", "endOffsets": "161,278"}, "to": {"startLines": "254,255", "startColumns": "4,4", "startOffsets": "22150,22261", "endColumns": "110,116", "endOffsets": "22256,22373"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,915,1006,1099,1193,1287,1387,1480,1575,1669,1760,1851,1935,2044,2148,2246,2356,2456,2563,2722,2821", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "216,323,430,513,618,734,824,910,1001,1094,1188,1282,1382,1475,1570,1664,1755,1846,1930,2039,2143,2241,2351,2451,2558,2717,2816,2898"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "885,1001,1108,1215,1298,1403,1519,1609,1695,1786,1879,1973,2067,2167,2260,2355,2449,2540,2631,2715,2824,2928,3026,3136,3236,3343,3502,23329", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "996,1103,1210,1293,1398,1514,1604,1690,1781,1874,1968,2062,2162,2255,2350,2444,2535,2626,2710,2819,2923,3021,3131,3231,3338,3497,3596,23406"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,126,200,276,349,431,517,610", "endColumns": "70,73,75,72,81,85,92,96", "endOffsets": "121,195,271,344,426,512,605,702"}, "to": {"startLines": "50,102,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3601,9093,19016,19092,19165,19247,19333,19426", "endColumns": "70,73,75,72,81,85,92,96", "endOffsets": "3667,9162,19087,19160,19242,19328,19421,19518"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,181,264,339,482,651,743", "endColumns": "75,82,74,142,168,91,86", "endOffsets": "176,259,334,477,646,738,825"}, "to": {"startLines": "69,158,258,263,272,290,291", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5477,13262,22544,22943,23760,25515,25607", "endColumns": "75,82,74,142,168,91,86", "endOffsets": "5548,13340,22614,23081,23924,25602,25689"}}]}]}