int anim abc_fade_in 0x7f010000
int anim abc_fade_out 0x7f010001
int anim abc_grow_fade_in_from_bottom 0x7f010002
int anim abc_popup_enter 0x7f010003
int anim abc_popup_exit 0x7f010004
int anim abc_shrink_fade_out_from_bottom 0x7f010005
int anim abc_slide_in_bottom 0x7f010006
int anim abc_slide_in_top 0x7f010007
int anim abc_slide_out_bottom 0x7f010008
int anim abc_slide_out_top 0x7f010009
int anim abc_tooltip_enter 0x7f01000a
int anim abc_tooltip_exit 0x7f01000b
int anim btn_checkbox_to_checked_box_inner_merged_animation 0x7f01000c
int anim btn_checkbox_to_checked_box_outer_merged_animation 0x7f01000d
int anim btn_checkbox_to_checked_icon_null_animation 0x7f01000e
int anim btn_checkbox_to_unchecked_box_inner_merged_animation 0x7f01000f
int anim btn_checkbox_to_unchecked_check_path_merged_animation 0x7f010010
int anim btn_checkbox_to_unchecked_icon_null_animation 0x7f010011
int anim btn_radio_to_off_mtrl_dot_group_animation 0x7f010012
int anim btn_radio_to_off_mtrl_ring_outer_animation 0x7f010013
int anim btn_radio_to_off_mtrl_ring_outer_path_animation 0x7f010014
int anim btn_radio_to_on_mtrl_dot_group_animation 0x7f010015
int anim btn_radio_to_on_mtrl_ring_outer_animation 0x7f010016
int anim btn_radio_to_on_mtrl_ring_outer_path_animation 0x7f010017
int anim design_bottom_sheet_slide_in 0x7f010018
int anim design_bottom_sheet_slide_out 0x7f010019
int anim design_snackbar_in 0x7f01001a
int anim design_snackbar_out 0x7f01001b
int anim fragment_fast_out_extra_slow_in 0x7f01001c
int anim lb_decelerator_2 0x7f01001d
int anim lb_decelerator_4 0x7f01001e
int anim mtrl_bottom_sheet_slide_in 0x7f01001f
int anim mtrl_bottom_sheet_slide_out 0x7f010020
int anim mtrl_card_lowers_interpolator 0x7f010021
int anim nav_default_enter_anim 0x7f010022
int anim nav_default_exit_anim 0x7f010023
int anim nav_default_pop_enter_anim 0x7f010024
int anim nav_default_pop_exit_anim 0x7f010025
int animator design_appbar_state_list_animator 0x7f020000
int animator design_fab_hide_motion_spec 0x7f020001
int animator design_fab_show_motion_spec 0x7f020002
int animator fragment_close_enter 0x7f020003
int animator fragment_close_exit 0x7f020004
int animator fragment_fade_enter 0x7f020005
int animator fragment_fade_exit 0x7f020006
int animator fragment_open_enter 0x7f020007
int animator fragment_open_exit 0x7f020008
int animator lb_guidedactions_item_pressed 0x7f020009
int animator lb_guidedactions_item_unpressed 0x7f02000a
int animator lb_guidedstep_slide_down 0x7f02000b
int animator lb_guidedstep_slide_up 0x7f02000c
int animator lb_onboarding_description_enter 0x7f02000d
int animator lb_onboarding_logo_enter 0x7f02000e
int animator lb_onboarding_logo_exit 0x7f02000f
int animator lb_onboarding_page_indicator_enter 0x7f020010
int animator lb_onboarding_page_indicator_fade_in 0x7f020011
int animator lb_onboarding_page_indicator_fade_out 0x7f020012
int animator lb_onboarding_start_button_fade_in 0x7f020013
int animator lb_onboarding_start_button_fade_out 0x7f020014
int animator lb_onboarding_title_enter 0x7f020015
int animator lb_playback_bg_fade_in 0x7f020016
int animator lb_playback_bg_fade_out 0x7f020017
int animator lb_playback_controls_fade_in 0x7f020018
int animator lb_playback_controls_fade_out 0x7f020019
int animator lb_playback_description_fade_in 0x7f02001a
int animator lb_playback_description_fade_out 0x7f02001b
int animator lb_playback_rows_fade_in 0x7f02001c
int animator lb_playback_rows_fade_out 0x7f02001d
int animator linear_indeterminate_line1_head_interpolator 0x7f02001e
int animator linear_indeterminate_line1_tail_interpolator 0x7f02001f
int animator linear_indeterminate_line2_head_interpolator 0x7f020020
int animator linear_indeterminate_line2_tail_interpolator 0x7f020021
int animator mtrl_btn_state_list_anim 0x7f020022
int animator mtrl_btn_unelevated_state_list_anim 0x7f020023
int animator mtrl_card_state_list_anim 0x7f020024
int animator mtrl_chip_state_list_anim 0x7f020025
int animator mtrl_extended_fab_change_size_collapse_motion_spec 0x7f020026
int animator mtrl_extended_fab_change_size_expand_motion_spec 0x7f020027
int animator mtrl_extended_fab_hide_motion_spec 0x7f020028
int animator mtrl_extended_fab_show_motion_spec 0x7f020029
int animator mtrl_extended_fab_state_list_animator 0x7f02002a
int animator mtrl_fab_hide_motion_spec 0x7f02002b
int animator mtrl_fab_show_motion_spec 0x7f02002c
int animator mtrl_fab_transformation_sheet_collapse_spec 0x7f02002d
int animator mtrl_fab_transformation_sheet_expand_spec 0x7f02002e
int animator nav_default_enter_anim 0x7f02002f
int animator nav_default_exit_anim 0x7f020030
int animator nav_default_pop_enter_anim 0x7f020031
int animator nav_default_pop_exit_anim 0x7f020032
int array exo_controls_playback_speeds 0x7f030000
int attr SharedValue 0x7f040000
int attr SharedValueId 0x7f040001
int attr action 0x7f040002
int attr actionBarDivider 0x7f040003
int attr actionBarItemBackground 0x7f040004
int attr actionBarPopupTheme 0x7f040005
int attr actionBarSize 0x7f040006
int attr actionBarSplitStyle 0x7f040007
int attr actionBarStyle 0x7f040008
int attr actionBarTabBarStyle 0x7f040009
int attr actionBarTabStyle 0x7f04000a
int attr actionBarTabTextStyle 0x7f04000b
int attr actionBarTheme 0x7f04000c
int attr actionBarWidgetTheme 0x7f04000d
int attr actionButtonStyle 0x7f04000e
int attr actionDropDownStyle 0x7f04000f
int attr actionLayout 0x7f040010
int attr actionMenuTextAppearance 0x7f040011
int attr actionMenuTextColor 0x7f040012
int attr actionModeBackground 0x7f040013
int attr actionModeCloseButtonStyle 0x7f040014
int attr actionModeCloseContentDescription 0x7f040015
int attr actionModeCloseDrawable 0x7f040016
int attr actionModeCopyDrawable 0x7f040017
int attr actionModeCutDrawable 0x7f040018
int attr actionModeFindDrawable 0x7f040019
int attr actionModePasteDrawable 0x7f04001a
int attr actionModePopupWindowStyle 0x7f04001b
int attr actionModeSelectAllDrawable 0x7f04001c
int attr actionModeShareDrawable 0x7f04001d
int attr actionModeSplitBackground 0x7f04001e
int attr actionModeStyle 0x7f04001f
int attr actionModeTheme 0x7f040020
int attr actionModeWebSearchDrawable 0x7f040021
int attr actionOverflowButtonStyle 0x7f040022
int attr actionOverflowMenuStyle 0x7f040023
int attr actionProviderClass 0x7f040024
int attr actionTextColorAlpha 0x7f040025
int attr actionViewClass 0x7f040026
int attr activatedAnimationDuration 0x7f040027
int attr activityAction 0x7f040028
int attr activityChooserViewStyle 0x7f040029
int attr activityName 0x7f04002a
int attr ad_marker_color 0x7f04002b
int attr ad_marker_width 0x7f04002c
int attr adjustable 0x7f04002d
int attr alertDialogButtonGroupStyle 0x7f04002e
int attr alertDialogCenterButtons 0x7f04002f
int attr alertDialogStyle 0x7f040030
int attr alertDialogTheme 0x7f040031
int attr allowDividerAbove 0x7f040032
int attr allowDividerAfterLastItem 0x7f040033
int attr allowDividerBelow 0x7f040034
int attr allowStacking 0x7f040035
int attr alpha 0x7f040036
int attr alphabeticModifiers 0x7f040037
int attr altSrc 0x7f040038
int attr alwaysExpand 0x7f040039
int attr animateCircleAngleTo 0x7f04003a
int attr animateRelativeTo 0x7f04003b
int attr animationMode 0x7f04003c
int attr animation_enabled 0x7f04003d
int attr appBarLayoutStyle 0x7f04003e
int attr applyMotionScene 0x7f04003f
int attr arcMode 0x7f040040
int attr argType 0x7f040041
int attr arrowBgColor 0x7f040042
int attr arrowColor 0x7f040043
int attr arrowHeadLength 0x7f040044
int attr arrowRadius 0x7f040045
int attr arrowShaftLength 0x7f040046
int attr artwork_display_mode 0x7f040047
int attr attributeName 0x7f040048
int attr autoCompleteMode 0x7f040049
int attr autoCompleteTextViewStyle 0x7f04004a
int attr autoSizeMaxTextSize 0x7f04004b
int attr autoSizeMinTextSize 0x7f04004c
int attr autoSizePresetSizes 0x7f04004d
int attr autoSizeStepGranularity 0x7f04004e
int attr autoSizeTextType 0x7f04004f
int attr autoTransition 0x7f040050
int attr auto_show 0x7f040051
int attr background 0x7f040052
int attr backgroundColor 0x7f040053
int attr backgroundInsetBottom 0x7f040054
int attr backgroundInsetEnd 0x7f040055
int attr backgroundInsetStart 0x7f040056
int attr backgroundInsetTop 0x7f040057
int attr backgroundOverlayColorAlpha 0x7f040058
int attr backgroundSplit 0x7f040059
int attr backgroundStacked 0x7f04005a
int attr backgroundTint 0x7f04005b
int attr backgroundTintMode 0x7f04005c
int attr badgeGravity 0x7f04005d
int attr badgeStyle 0x7f04005e
int attr badgeTextColor 0x7f04005f
int attr barLength 0x7f040060
int attr bar_gravity 0x7f040061
int attr bar_height 0x7f040062
int attr barrierAllowsGoneWidgets 0x7f040063
int attr barrierDirection 0x7f040064
int attr barrierMargin 0x7f040065
int attr baseCardViewStyle 0x7f040066
int attr behavior_autoHide 0x7f040067
int attr behavior_autoShrink 0x7f040068
int attr behavior_draggable 0x7f040069
int attr behavior_expandedOffset 0x7f04006a
int attr behavior_fitToContents 0x7f04006b
int attr behavior_halfExpandedRatio 0x7f04006c
int attr behavior_hideable 0x7f04006d
int attr behavior_overlapTop 0x7f04006e
int attr behavior_peekHeight 0x7f04006f
int attr behavior_saveFlags 0x7f040070
int attr behavior_skipCollapsed 0x7f040071
int attr blendSrc 0x7f040072
int attr borderRound 0x7f040073
int attr borderRoundPercent 0x7f040074
int attr borderWidth 0x7f040075
int attr borderlessButtonStyle 0x7f040076
int attr bottomAppBarStyle 0x7f040077
int attr bottomNavigationStyle 0x7f040078
int attr bottomSheetDialogTheme 0x7f040079
int attr bottomSheetStyle 0x7f04007a
int attr boxBackgroundColor 0x7f04007b
int attr boxBackgroundMode 0x7f04007c
int attr boxCollapsedPaddingTop 0x7f04007d
int attr boxCornerRadiusBottomEnd 0x7f04007e
int attr boxCornerRadiusBottomStart 0x7f04007f
int attr boxCornerRadiusTopEnd 0x7f040080
int attr boxCornerRadiusTopStart 0x7f040081
int attr boxStrokeColor 0x7f040082
int attr boxStrokeErrorColor 0x7f040083
int attr boxStrokeWidth 0x7f040084
int attr boxStrokeWidthFocused 0x7f040085
int attr brightness 0x7f040086
int attr browsePaddingBottom 0x7f040087
int attr browsePaddingEnd 0x7f040088
int attr browsePaddingStart 0x7f040089
int attr browsePaddingTop 0x7f04008a
int attr browseRowsFadingEdgeLength 0x7f04008b
int attr browseRowsMarginStart 0x7f04008c
int attr browseRowsMarginTop 0x7f04008d
int attr browseTitleIconStyle 0x7f04008e
int attr browseTitleTextStyle 0x7f04008f
int attr browseTitleViewLayout 0x7f040090
int attr browseTitleViewStyle 0x7f040091
int attr buffered_color 0x7f040092
int attr buttonBarButtonStyle 0x7f040093
int attr buttonBarNegativeButtonStyle 0x7f040094
int attr buttonBarNeutralButtonStyle 0x7f040095
int attr buttonBarPositiveButtonStyle 0x7f040096
int attr buttonBarStyle 0x7f040097
int attr buttonCompat 0x7f040098
int attr buttonGravity 0x7f040099
int attr buttonIconDimen 0x7f04009a
int attr buttonPanelSideLayout 0x7f04009b
int attr buttonSize 0x7f04009c
int attr buttonStyle 0x7f04009d
int attr buttonStyleSmall 0x7f04009e
int attr buttonTint 0x7f04009f
int attr buttonTintMode 0x7f0400a0
int attr cardBackground 0x7f0400a1
int attr cardBackgroundColor 0x7f0400a2
int attr cardCornerRadius 0x7f0400a3
int attr cardElevation 0x7f0400a4
int attr cardForeground 0x7f0400a5
int attr cardForegroundColor 0x7f0400a6
int attr cardMaxElevation 0x7f0400a7
int attr cardPreventCornerOverlap 0x7f0400a8
int attr cardType 0x7f0400a9
int attr cardUseCompatPadding 0x7f0400aa
int attr cardViewStyle 0x7f0400ab
int attr carousel_backwardTransition 0x7f0400ac
int attr carousel_emptyViewsBehavior 0x7f0400ad
int attr carousel_firstView 0x7f0400ae
int attr carousel_forwardTransition 0x7f0400af
int attr carousel_infinite 0x7f0400b0
int attr carousel_nextState 0x7f0400b1
int attr carousel_previousState 0x7f0400b2
int attr carousel_touchUpMode 0x7f0400b3
int attr carousel_touchUp_dampeningFactor 0x7f0400b4
int attr carousel_touchUp_velocityThreshold 0x7f0400b5
int attr chainUseRtl 0x7f0400b6
int attr checkBoxPreferenceStyle 0x7f0400b7
int attr checkMarkCompat 0x7f0400b8
int attr checkMarkTint 0x7f0400b9
int attr checkMarkTintMode 0x7f0400ba
int attr checkboxStyle 0x7f0400bb
int attr checkedButton 0x7f0400bc
int attr checkedChip 0x7f0400bd
int attr checkedIcon 0x7f0400be
int attr checkedIconEnabled 0x7f0400bf
int attr checkedIconMargin 0x7f0400c0
int attr checkedIconSize 0x7f0400c1
int attr checkedIconTint 0x7f0400c2
int attr checkedIconVisible 0x7f0400c3
int attr checkedTextViewStyle 0x7f0400c4
int attr chipBackgroundColor 0x7f0400c5
int attr chipCornerRadius 0x7f0400c6
int attr chipEndPadding 0x7f0400c7
int attr chipGroupStyle 0x7f0400c8
int attr chipIcon 0x7f0400c9
int attr chipIconEnabled 0x7f0400ca
int attr chipIconSize 0x7f0400cb
int attr chipIconTint 0x7f0400cc
int attr chipIconVisible 0x7f0400cd
int attr chipMinHeight 0x7f0400ce
int attr chipMinTouchTargetSize 0x7f0400cf
int attr chipSpacing 0x7f0400d0
int attr chipSpacingHorizontal 0x7f0400d1
int attr chipSpacingVertical 0x7f0400d2
int attr chipStandaloneStyle 0x7f0400d3
int attr chipStartPadding 0x7f0400d4
int attr chipStrokeColor 0x7f0400d5
int attr chipStrokeWidth 0x7f0400d6
int attr chipStyle 0x7f0400d7
int attr chipSurfaceColor 0x7f0400d8
int attr circleCrop 0x7f0400d9
int attr circleRadius 0x7f0400da
int attr circularProgressIndicatorStyle 0x7f0400db
int attr circularflow_angles 0x7f0400dc
int attr circularflow_defaultAngle 0x7f0400dd
int attr circularflow_defaultRadius 0x7f0400de
int attr circularflow_radiusInDP 0x7f0400df
int attr circularflow_viewCenter 0x7f0400e0
int attr clearTop 0x7f0400e1
int attr clearsTag 0x7f0400e2
int attr clickAction 0x7f0400e3
int attr clockFaceBackgroundColor 0x7f0400e4
int attr clockHandColor 0x7f0400e5
int attr clockIcon 0x7f0400e6
int attr clockNumberTextColor 0x7f0400e7
int attr closeIcon 0x7f0400e8
int attr closeIconEnabled 0x7f0400e9
int attr closeIconEndPadding 0x7f0400ea
int attr closeIconSize 0x7f0400eb
int attr closeIconStartPadding 0x7f0400ec
int attr closeIconTint 0x7f0400ed
int attr closeIconVisible 0x7f0400ee
int attr closeItemLayout 0x7f0400ef
int attr closed_captioning 0x7f0400f0
int attr collapseContentDescription 0x7f0400f1
int attr collapseIcon 0x7f0400f2
int attr collapsedSize 0x7f0400f3
int attr collapsedTitleGravity 0x7f0400f4
int attr collapsedTitleTextAppearance 0x7f0400f5
int attr collapsingToolbarLayoutStyle 0x7f0400f6
int attr color 0x7f0400f7
int attr colorAccent 0x7f0400f8
int attr colorBackgroundFloating 0x7f0400f9
int attr colorButtonNormal 0x7f0400fa
int attr colorControlActivated 0x7f0400fb
int attr colorControlHighlight 0x7f0400fc
int attr colorControlNormal 0x7f0400fd
int attr colorError 0x7f0400fe
int attr colorOnBackground 0x7f0400ff
int attr colorOnError 0x7f040100
int attr colorOnPrimary 0x7f040101
int attr colorOnPrimarySurface 0x7f040102
int attr colorOnSecondary 0x7f040103
int attr colorOnSurface 0x7f040104
int attr colorPrimary 0x7f040105
int attr colorPrimaryDark 0x7f040106
int attr colorPrimarySurface 0x7f040107
int attr colorPrimaryVariant 0x7f040108
int attr colorScheme 0x7f040109
int attr colorSecondary 0x7f04010a
int attr colorSecondaryVariant 0x7f04010b
int attr colorSurface 0x7f04010c
int attr colorSwitchThumbNormal 0x7f04010d
int attr columnWidth 0x7f04010e
int attr commitIcon 0x7f04010f
int attr constraintRotate 0x7f040110
int attr constraintSet 0x7f040111
int attr constraintSetEnd 0x7f040112
int attr constraintSetStart 0x7f040113
int attr constraint_referenced_ids 0x7f040114
int attr constraint_referenced_tags 0x7f040115
int attr constraints 0x7f040116
int attr content 0x7f040117
int attr contentDescription 0x7f040118
int attr contentInsetEnd 0x7f040119
int attr contentInsetEndWithActions 0x7f04011a
int attr contentInsetLeft 0x7f04011b
int attr contentInsetRight 0x7f04011c
int attr contentInsetStart 0x7f04011d
int attr contentInsetStartWithNavigation 0x7f04011e
int attr contentPadding 0x7f04011f
int attr contentPaddingBottom 0x7f040120
int attr contentPaddingEnd 0x7f040121
int attr contentPaddingLeft 0x7f040122
int attr contentPaddingRight 0x7f040123
int attr contentPaddingStart 0x7f040124
int attr contentPaddingTop 0x7f040125
int attr contentScrim 0x7f040126
int attr contrast 0x7f040127
int attr controlBackground 0x7f040128
int attr controller_layout_id 0x7f040129
int attr coordinatorLayoutStyle 0x7f04012a
int attr cornerFamily 0x7f04012b
int attr cornerFamilyBottomLeft 0x7f04012c
int attr cornerFamilyBottomRight 0x7f04012d
int attr cornerFamilyTopLeft 0x7f04012e
int attr cornerFamilyTopRight 0x7f04012f
int attr cornerRadius 0x7f040130
int attr cornerSize 0x7f040131
int attr cornerSizeBottomLeft 0x7f040132
int attr cornerSizeBottomRight 0x7f040133
int attr cornerSizeTopLeft 0x7f040134
int attr cornerSizeTopRight 0x7f040135
int attr counterEnabled 0x7f040136
int attr counterMaxLength 0x7f040137
int attr counterOverflowTextAppearance 0x7f040138
int attr counterOverflowTextColor 0x7f040139
int attr counterTextAppearance 0x7f04013a
int attr counterTextColor 0x7f04013b
int attr crossfade 0x7f04013c
int attr currentState 0x7f04013d
int attr curveFit 0x7f04013e
int attr customBoolean 0x7f04013f
int attr customColorDrawableValue 0x7f040140
int attr customColorValue 0x7f040141
int attr customDimension 0x7f040142
int attr customFloatValue 0x7f040143
int attr customIntegerValue 0x7f040144
int attr customNavigationLayout 0x7f040145
int attr customPixelDimension 0x7f040146
int attr customReference 0x7f040147
int attr customStringValue 0x7f040148
int attr data 0x7f040149
int attr dataPattern 0x7f04014a
int attr datePickerFormat 0x7f04014b
int attr dayInvalidStyle 0x7f04014c
int attr daySelectedStyle 0x7f04014d
int attr dayStyle 0x7f04014e
int attr dayTodayStyle 0x7f04014f
int attr defaultBrandColor 0x7f040150
int attr defaultBrandColorDark 0x7f040151
int attr defaultDuration 0x7f040152
int attr defaultNavHost 0x7f040153
int attr defaultQueryHint 0x7f040154
int attr defaultSearchBrightColor 0x7f040155
int attr defaultSearchColor 0x7f040156
int attr defaultSearchIcon 0x7f040157
int attr defaultSearchIconColor 0x7f040158
int attr defaultSectionHeaderColor 0x7f040159
int attr defaultState 0x7f04015a
int attr defaultValue 0x7f04015b
int attr default_artwork 0x7f04015c
int attr deltaPolarAngle 0x7f04015d
int attr deltaPolarRadius 0x7f04015e
int attr dependency 0x7f04015f
int attr deriveConstraintsFrom 0x7f040160
int attr destination 0x7f040161
int attr detailsActionButtonStyle 0x7f040162
int attr detailsDescriptionBodyStyle 0x7f040163
int attr detailsDescriptionSubtitleStyle 0x7f040164
int attr detailsDescriptionTitleStyle 0x7f040165
int attr dialogCornerRadius 0x7f040166
int attr dialogIcon 0x7f040167
int attr dialogLayout 0x7f040168
int attr dialogMessage 0x7f040169
int attr dialogPreferenceStyle 0x7f04016a
int attr dialogPreferredPadding 0x7f04016b
int attr dialogTheme 0x7f04016c
int attr dialogTitle 0x7f04016d
int attr disableDependentsState 0x7f04016e
int attr displayOptions 0x7f04016f
int attr divider 0x7f040170
int attr dividerHorizontal 0x7f040171
int attr dividerPadding 0x7f040172
int attr dividerVertical 0x7f040173
int attr dotBgColor 0x7f040174
int attr dotToArrowGap 0x7f040175
int attr dotToDotGap 0x7f040176
int attr dragDirection 0x7f040177
int attr dragScale 0x7f040178
int attr dragThreshold 0x7f040179
int attr drawPath 0x7f04017a
int attr drawableBottomCompat 0x7f04017b
int attr drawableEndCompat 0x7f04017c
int attr drawableLeftCompat 0x7f04017d
int attr drawableRightCompat 0x7f04017e
int attr drawableSize 0x7f04017f
int attr drawableStartCompat 0x7f040180
int attr drawableTint 0x7f040181
int attr drawableTintMode 0x7f040182
int attr drawableTopCompat 0x7f040183
int attr drawerArrowStyle 0x7f040184
int attr drawerLayoutStyle 0x7f040185
int attr dropDownListViewStyle 0x7f040186
int attr dropdownListPreferredItemHeight 0x7f040187
int attr dropdownPreferenceStyle 0x7f040188
int attr duration 0x7f040189
int attr editTextBackground 0x7f04018a
int attr editTextColor 0x7f04018b
int attr editTextPreferenceStyle 0x7f04018c
int attr editTextStyle 0x7f04018d
int attr elevation 0x7f04018e
int attr elevationOverlayColor 0x7f04018f
int attr elevationOverlayEnabled 0x7f040190
int attr emojiCompatEnabled 0x7f040191
int attr enableCopying 0x7f040192
int attr enableEdgeToEdge 0x7f040193
int attr enabled 0x7f040194
int attr endIconCheckable 0x7f040195
int attr endIconContentDescription 0x7f040196
int attr endIconDrawable 0x7f040197
int attr endIconMode 0x7f040198
int attr endIconTint 0x7f040199
int attr endIconTintMode 0x7f04019a
int attr enforceMaterialTheme 0x7f04019b
int attr enforceTextAppearance 0x7f04019c
int attr ensureMinTouchTargetSize 0x7f04019d
int attr enterAnim 0x7f04019e
int attr entries 0x7f04019f
int attr entryValues 0x7f0401a0
int attr errorContentDescription 0x7f0401a1
int attr errorEnabled 0x7f0401a2
int attr errorIconDrawable 0x7f0401a3
int attr errorIconTint 0x7f0401a4
int attr errorIconTintMode 0x7f0401a5
int attr errorMessageStyle 0x7f0401a6
int attr errorTextAppearance 0x7f0401a7
int attr errorTextColor 0x7f0401a8
int attr exitAnim 0x7f0401a9
int attr expandActivityOverflowButtonDrawable 0x7f0401aa
int attr expanded 0x7f0401ab
int attr expandedHintEnabled 0x7f0401ac
int attr expandedTitleGravity 0x7f0401ad
int attr expandedTitleMargin 0x7f0401ae
int attr expandedTitleMarginBottom 0x7f0401af
int attr expandedTitleMarginEnd 0x7f0401b0
int attr expandedTitleMarginStart 0x7f0401b1
int attr expandedTitleMarginTop 0x7f0401b2
int attr expandedTitleTextAppearance 0x7f0401b3
int attr extendMotionSpec 0x7f0401b4
int attr extendedFloatingActionButtonStyle 0x7f0401b5
int attr extraVisibility 0x7f0401b6
int attr fabAlignmentMode 0x7f0401b7
int attr fabAnimationMode 0x7f0401b8
int attr fabCradleMargin 0x7f0401b9
int attr fabCradleRoundedCornerRadius 0x7f0401ba
int attr fabCradleVerticalOffset 0x7f0401bb
int attr fabCustomSize 0x7f0401bc
int attr fabSize 0x7f0401bd
int attr fastScrollEnabled 0x7f0401be
int attr fastScrollHorizontalThumbDrawable 0x7f0401bf
int attr fastScrollHorizontalTrackDrawable 0x7f0401c0
int attr fastScrollVerticalThumbDrawable 0x7f0401c1
int attr fastScrollVerticalTrackDrawable 0x7f0401c2
int attr fast_forward 0x7f0401c3
int attr finishPrimaryWithSecondary 0x7f0401c4
int attr finishSecondaryWithPrimary 0x7f0401c5
int attr firstBaselineToTopHeight 0x7f0401c6
int attr floatingActionButtonStyle 0x7f0401c7
int attr flow_firstHorizontalBias 0x7f0401c8
int attr flow_firstHorizontalStyle 0x7f0401c9
int attr flow_firstVerticalBias 0x7f0401ca
int attr flow_firstVerticalStyle 0x7f0401cb
int attr flow_horizontalAlign 0x7f0401cc
int attr flow_horizontalBias 0x7f0401cd
int attr flow_horizontalGap 0x7f0401ce
int attr flow_horizontalStyle 0x7f0401cf
int attr flow_lastHorizontalBias 0x7f0401d0
int attr flow_lastHorizontalStyle 0x7f0401d1
int attr flow_lastVerticalBias 0x7f0401d2
int attr flow_lastVerticalStyle 0x7f0401d3
int attr flow_maxElementsWrap 0x7f0401d4
int attr flow_padding 0x7f0401d5
int attr flow_verticalAlign 0x7f0401d6
int attr flow_verticalBias 0x7f0401d7
int attr flow_verticalGap 0x7f0401d8
int attr flow_verticalStyle 0x7f0401d9
int attr flow_wrapMode 0x7f0401da
int attr focusOutEnd 0x7f0401db
int attr focusOutFront 0x7f0401dc
int attr focusOutSideEnd 0x7f0401dd
int attr focusOutSideStart 0x7f0401de
int attr font 0x7f0401df
int attr fontFamily 0x7f0401e0
int attr fontProviderAuthority 0x7f0401e1
int attr fontProviderCerts 0x7f0401e2
int attr fontProviderFetchStrategy 0x7f0401e3
int attr fontProviderFetchTimeout 0x7f0401e4
int attr fontProviderPackage 0x7f0401e5
int attr fontProviderQuery 0x7f0401e6
int attr fontProviderSystemFontFamily 0x7f0401e7
int attr fontStyle 0x7f0401e8
int attr fontVariationSettings 0x7f0401e9
int attr fontWeight 0x7f0401ea
int attr foregroundInsidePadding 0x7f0401eb
int attr fragment 0x7f0401ec
int attr framePosition 0x7f0401ed
int attr gapBetweenBars 0x7f0401ee
int attr gestureInsetBottomIgnored 0x7f0401ef
int attr goIcon 0x7f0401f0
int attr graph 0x7f0401f1
int attr guidanceBreadcrumbStyle 0x7f0401f2
int attr guidanceContainerStyle 0x7f0401f3
int attr guidanceDescriptionStyle 0x7f0401f4
int attr guidanceEntryAnimation 0x7f0401f5
int attr guidanceIconStyle 0x7f0401f6
int attr guidanceTitleStyle 0x7f0401f7
int attr guidedActionCheckedAnimation 0x7f0401f8
int attr guidedActionContentWidth 0x7f0401f9
int attr guidedActionContentWidthNoIcon 0x7f0401fa
int attr guidedActionContentWidthWeight 0x7f0401fb
int attr guidedActionContentWidthWeightTwoPanels 0x7f0401fc
int attr guidedActionDescriptionMinLines 0x7f0401fd
int attr guidedActionDisabledChevronAlpha 0x7f0401fe
int attr guidedActionEnabledChevronAlpha 0x7f0401ff
int attr guidedActionItemCheckmarkStyle 0x7f040200
int attr guidedActionItemChevronStyle 0x7f040201
int attr guidedActionItemContainerStyle 0x7f040202
int attr guidedActionItemContentStyle 0x7f040203
int attr guidedActionItemDescriptionStyle 0x7f040204
int attr guidedActionItemIconStyle 0x7f040205
int attr guidedActionItemTitleStyle 0x7f040206
int attr guidedActionPressedAnimation 0x7f040207
int attr guidedActionTitleMaxLines 0x7f040208
int attr guidedActionTitleMinLines 0x7f040209
int attr guidedActionUncheckedAnimation 0x7f04020a
int attr guidedActionUnpressedAnimation 0x7f04020b
int attr guidedActionVerticalPadding 0x7f04020c
int attr guidedActionsBackground 0x7f04020d
int attr guidedActionsBackgroundDark 0x7f04020e
int attr guidedActionsContainerStyle 0x7f04020f
int attr guidedActionsElevation 0x7f040210
int attr guidedActionsEntryAnimation 0x7f040211
int attr guidedActionsListStyle 0x7f040212
int attr guidedActionsSelectorDrawable 0x7f040213
int attr guidedActionsSelectorHideAnimation 0x7f040214
int attr guidedActionsSelectorShowAnimation 0x7f040215
int attr guidedActionsSelectorStyle 0x7f040216
int attr guidedButtonActionsListStyle 0x7f040217
int attr guidedButtonActionsWidthWeight 0x7f040218
int attr guidedStepBackground 0x7f040219
int attr guidedStepEntryAnimation 0x7f04021a
int attr guidedStepExitAnimation 0x7f04021b
int attr guidedStepHeightWeight 0x7f04021c
int attr guidedStepImeAppearingAnimation 0x7f04021d
int attr guidedStepImeDisappearingAnimation 0x7f04021e
int attr guidedStepKeyline 0x7f04021f
int attr guidedStepReentryAnimation 0x7f040220
int attr guidedStepReturnAnimation 0x7f040221
int attr guidedStepTheme 0x7f040222
int attr guidedStepThemeFlag 0x7f040223
int attr guidedSubActionsListStyle 0x7f040224
int attr guidelineUseRtl 0x7f040225
int attr haloColor 0x7f040226
int attr haloRadius 0x7f040227
int attr headerLayout 0x7f040228
int attr headerStyle 0x7f040229
int attr headersVerticalGridStyle 0x7f04022a
int attr height 0x7f04022b
int attr helperText 0x7f04022c
int attr helperTextEnabled 0x7f04022d
int attr helperTextTextAppearance 0x7f04022e
int attr helperTextTextColor 0x7f04022f
int attr hideAnimationBehavior 0x7f040230
int attr hideMotionSpec 0x7f040231
int attr hideOnContentScroll 0x7f040232
int attr hideOnScroll 0x7f040233
int attr hide_during_ads 0x7f040234
int attr hide_on_touch 0x7f040235
int attr high_quality 0x7f040236
int attr hintAnimationEnabled 0x7f040237
int attr hintEnabled 0x7f040238
int attr hintTextAppearance 0x7f040239
int attr hintTextColor 0x7f04023a
int attr homeAsUpIndicator 0x7f04023b
int attr homeLayout 0x7f04023c
int attr horizontalMargin 0x7f04023d
int attr horizontalOffset 0x7f04023e
int attr hoveredFocusedTranslationZ 0x7f04023f
int attr icon 0x7f040240
int attr iconEndPadding 0x7f040241
int attr iconGravity 0x7f040242
int attr iconPadding 0x7f040243
int attr iconSize 0x7f040244
int attr iconSpaceReserved 0x7f040245
int attr iconStartPadding 0x7f040246
int attr iconTint 0x7f040247
int attr iconTintMode 0x7f040248
int attr iconifiedByDefault 0x7f040249
int attr ifTagNotSet 0x7f04024a
int attr ifTagSet 0x7f04024b
int attr imageAspectRatio 0x7f04024c
int attr imageAspectRatioAdjust 0x7f04024d
int attr imageButtonStyle 0x7f04024e
int attr imageCardViewBadgeStyle 0x7f04024f
int attr imageCardViewContentStyle 0x7f040250
int attr imageCardViewImageStyle 0x7f040251
int attr imageCardViewInfoAreaStyle 0x7f040252
int attr imageCardViewStyle 0x7f040253
int attr imageCardViewTitleStyle 0x7f040254
int attr imagePanX 0x7f040255
int attr imagePanY 0x7f040256
int attr imageRotate 0x7f040257
int attr imageZoom 0x7f040258
int attr indeterminateAnimationType 0x7f040259
int attr indeterminateProgressStyle 0x7f04025a
int attr indicatorColor 0x7f04025b
int attr indicatorDirectionCircular 0x7f04025c
int attr indicatorDirectionLinear 0x7f04025d
int attr indicatorInset 0x7f04025e
int attr indicatorSize 0x7f04025f
int attr infoAreaBackground 0x7f040260
int attr infoVisibility 0x7f040261
int attr initialActivityCount 0x7f040262
int attr initialExpandedChildrenCount 0x7f040263
int attr insetForeground 0x7f040264
int attr is24HourFormat 0x7f040265
int attr isLightTheme 0x7f040266
int attr isMaterialTheme 0x7f040267
int attr isPreferenceVisible 0x7f040268
int attr itemBackground 0x7f040269
int attr itemFillColor 0x7f04026a
int attr itemHorizontalPadding 0x7f04026b
int attr itemHorizontalTranslationEnabled 0x7f04026c
int attr itemIconPadding 0x7f04026d
int attr itemIconSize 0x7f04026e
int attr itemIconTint 0x7f04026f
int attr itemMaxLines 0x7f040270
int attr itemPadding 0x7f040271
int attr itemRippleColor 0x7f040272
int attr itemShapeAppearance 0x7f040273
int attr itemShapeAppearanceOverlay 0x7f040274
int attr itemShapeFillColor 0x7f040275
int attr itemShapeInsetBottom 0x7f040276
int attr itemShapeInsetEnd 0x7f040277
int attr itemShapeInsetStart 0x7f040278
int attr itemShapeInsetTop 0x7f040279
int attr itemSpacing 0x7f04027a
int attr itemStrokeColor 0x7f04027b
int attr itemStrokeWidth 0x7f04027c
int attr itemTextAppearance 0x7f04027d
int attr itemTextAppearanceActive 0x7f04027e
int attr itemTextAppearanceInactive 0x7f04027f
int attr itemTextColor 0x7f040280
int attr itemsVerticalGridStyle 0x7f040281
int attr keep_content_on_player_reset 0x7f040282
int attr key 0x7f040283
int attr keyPositionType 0x7f040284
int attr keyboardIcon 0x7f040285
int attr keylines 0x7f040286
int attr lStar 0x7f040287
int attr labelBehavior 0x7f040288
int attr labelStyle 0x7f040289
int attr labelVisibilityMode 0x7f04028a
int attr lastBaselineToBottomHeight 0x7f04028b
int attr launchSingleTop 0x7f04028c
int attr layout 0x7f04028d
int attr layoutDescription 0x7f04028e
int attr layoutDuringTransition 0x7f04028f
int attr layoutManager 0x7f040290
int attr layout_anchor 0x7f040291
int attr layout_anchorGravity 0x7f040292
int attr layout_behavior 0x7f040293
int attr layout_collapseMode 0x7f040294
int attr layout_collapseParallaxMultiplier 0x7f040295
int attr layout_constrainedHeight 0x7f040296
int attr layout_constrainedWidth 0x7f040297
int attr layout_constraintBaseline_creator 0x7f040298
int attr layout_constraintBaseline_toBaselineOf 0x7f040299
int attr layout_constraintBaseline_toBottomOf 0x7f04029a
int attr layout_constraintBaseline_toTopOf 0x7f04029b
int attr layout_constraintBottom_creator 0x7f04029c
int attr layout_constraintBottom_toBottomOf 0x7f04029d
int attr layout_constraintBottom_toTopOf 0x7f04029e
int attr layout_constraintCircle 0x7f04029f
int attr layout_constraintCircleAngle 0x7f0402a0
int attr layout_constraintCircleRadius 0x7f0402a1
int attr layout_constraintDimensionRatio 0x7f0402a2
int attr layout_constraintEnd_toEndOf 0x7f0402a3
int attr layout_constraintEnd_toStartOf 0x7f0402a4
int attr layout_constraintGuide_begin 0x7f0402a5
int attr layout_constraintGuide_end 0x7f0402a6
int attr layout_constraintGuide_percent 0x7f0402a7
int attr layout_constraintHeight 0x7f0402a8
int attr layout_constraintHeight_default 0x7f0402a9
int attr layout_constraintHeight_max 0x7f0402aa
int attr layout_constraintHeight_min 0x7f0402ab
int attr layout_constraintHeight_percent 0x7f0402ac
int attr layout_constraintHorizontal_bias 0x7f0402ad
int attr layout_constraintHorizontal_chainStyle 0x7f0402ae
int attr layout_constraintHorizontal_weight 0x7f0402af
int attr layout_constraintLeft_creator 0x7f0402b0
int attr layout_constraintLeft_toLeftOf 0x7f0402b1
int attr layout_constraintLeft_toRightOf 0x7f0402b2
int attr layout_constraintRight_creator 0x7f0402b3
int attr layout_constraintRight_toLeftOf 0x7f0402b4
int attr layout_constraintRight_toRightOf 0x7f0402b5
int attr layout_constraintStart_toEndOf 0x7f0402b6
int attr layout_constraintStart_toStartOf 0x7f0402b7
int attr layout_constraintTag 0x7f0402b8
int attr layout_constraintTop_creator 0x7f0402b9
int attr layout_constraintTop_toBottomOf 0x7f0402ba
int attr layout_constraintTop_toTopOf 0x7f0402bb
int attr layout_constraintVertical_bias 0x7f0402bc
int attr layout_constraintVertical_chainStyle 0x7f0402bd
int attr layout_constraintVertical_weight 0x7f0402be
int attr layout_constraintWidth 0x7f0402bf
int attr layout_constraintWidth_default 0x7f0402c0
int attr layout_constraintWidth_max 0x7f0402c1
int attr layout_constraintWidth_min 0x7f0402c2
int attr layout_constraintWidth_percent 0x7f0402c3
int attr layout_dodgeInsetEdges 0x7f0402c4
int attr layout_editor_absoluteX 0x7f0402c5
int attr layout_editor_absoluteY 0x7f0402c6
int attr layout_goneMarginBaseline 0x7f0402c7
int attr layout_goneMarginBottom 0x7f0402c8
int attr layout_goneMarginEnd 0x7f0402c9
int attr layout_goneMarginLeft 0x7f0402ca
int attr layout_goneMarginRight 0x7f0402cb
int attr layout_goneMarginStart 0x7f0402cc
int attr layout_goneMarginTop 0x7f0402cd
int attr layout_insetEdge 0x7f0402ce
int attr layout_keyline 0x7f0402cf
int attr layout_marginBaseline 0x7f0402d0
int attr layout_optimizationLevel 0x7f0402d1
int attr layout_scrollFlags 0x7f0402d2
int attr layout_scrollInterpolator 0x7f0402d3
int attr layout_viewType 0x7f0402d4
int attr layout_wrapBehaviorInParent 0x7f0402d5
int attr lbDotRadius 0x7f0402d6
int attr lbImageCardViewType 0x7f0402d7
int attr lb_slideEdge 0x7f0402d8
int attr liftOnScroll 0x7f0402d9
int attr liftOnScrollTargetViewId 0x7f0402da
int attr limitBoundsTo 0x7f0402db
int attr lineHeight 0x7f0402dc
int attr lineSpacing 0x7f0402dd
int attr linearProgressIndicatorStyle 0x7f0402de
int attr listChoiceBackgroundIndicator 0x7f0402df
int attr listChoiceIndicatorMultipleAnimated 0x7f0402e0
int attr listChoiceIndicatorSingleAnimated 0x7f0402e1
int attr listDividerAlertDialog 0x7f0402e2
int attr listItemLayout 0x7f0402e3
int attr listLayout 0x7f0402e4
int attr listMenuViewStyle 0x7f0402e5
int attr listPopupWindowStyle 0x7f0402e6
int attr listPreferredItemHeight 0x7f0402e7
int attr listPreferredItemHeightLarge 0x7f0402e8
int attr listPreferredItemHeightSmall 0x7f0402e9
int attr listPreferredItemPaddingEnd 0x7f0402ea
int attr listPreferredItemPaddingLeft 0x7f0402eb
int attr listPreferredItemPaddingRight 0x7f0402ec
int attr listPreferredItemPaddingStart 0x7f0402ed
int attr logo 0x7f0402ee
int attr logoDescription 0x7f0402ef
int attr maintainLineSpacing 0x7f0402f0
int attr materialAlertDialogBodyTextStyle 0x7f0402f1
int attr materialAlertDialogTheme 0x7f0402f2
int attr materialAlertDialogTitleIconStyle 0x7f0402f3
int attr materialAlertDialogTitlePanelStyle 0x7f0402f4
int attr materialAlertDialogTitleTextStyle 0x7f0402f5
int attr materialButtonOutlinedStyle 0x7f0402f6
int attr materialButtonStyle 0x7f0402f7
int attr materialButtonToggleGroupStyle 0x7f0402f8
int attr materialCalendarDay 0x7f0402f9
int attr materialCalendarFullscreenTheme 0x7f0402fa
int attr materialCalendarHeaderCancelButton 0x7f0402fb
int attr materialCalendarHeaderConfirmButton 0x7f0402fc
int attr materialCalendarHeaderDivider 0x7f0402fd
int attr materialCalendarHeaderLayout 0x7f0402fe
int attr materialCalendarHeaderSelection 0x7f0402ff
int attr materialCalendarHeaderTitle 0x7f040300
int attr materialCalendarHeaderToggleButton 0x7f040301
int attr materialCalendarMonth 0x7f040302
int attr materialCalendarMonthNavigationButton 0x7f040303
int attr materialCalendarStyle 0x7f040304
int attr materialCalendarTheme 0x7f040305
int attr materialCalendarYearNavigationButton 0x7f040306
int attr materialCardViewStyle 0x7f040307
int attr materialCircleRadius 0x7f040308
int attr materialClockStyle 0x7f040309
int attr materialThemeOverlay 0x7f04030a
int attr materialTimePickerStyle 0x7f04030b
int attr materialTimePickerTheme 0x7f04030c
int attr maxAcceleration 0x7f04030d
int attr maxActionInlineWidth 0x7f04030e
int attr maxButtonHeight 0x7f04030f
int attr maxCharacterCount 0x7f040310
int attr maxHeight 0x7f040311
int attr maxImageSize 0x7f040312
int attr maxLines 0x7f040313
int attr maxVelocity 0x7f040314
int attr maxWidth 0x7f040315
int attr measureWithLargestChild 0x7f040316
int attr menu 0x7f040317
int attr menuGravity 0x7f040318
int attr methodName 0x7f040319
int attr mimeType 0x7f04031a
int attr min 0x7f04031b
int attr minHeight 0x7f04031c
int attr minHideDelay 0x7f04031d
int attr minSeparation 0x7f04031e
int attr minTouchTargetSize 0x7f04031f
int attr minWidth 0x7f040320
int attr mock_diagonalsColor 0x7f040321
int attr mock_label 0x7f040322
int attr mock_labelBackgroundColor 0x7f040323
int attr mock_labelColor 0x7f040324
int attr mock_showDiagonals 0x7f040325
int attr mock_showLabel 0x7f040326
int attr motionDebug 0x7f040327
int attr motionDurationLong1 0x7f040328
int attr motionDurationLong2 0x7f040329
int attr motionDurationMedium1 0x7f04032a
int attr motionDurationMedium2 0x7f04032b
int attr motionDurationShort1 0x7f04032c
int attr motionDurationShort2 0x7f04032d
int attr motionEasingAccelerated 0x7f04032e
int attr motionEasingDecelerated 0x7f04032f
int attr motionEasingEmphasized 0x7f040330
int attr motionEasingLinear 0x7f040331
int attr motionEasingStandard 0x7f040332
int attr motionEffect_alpha 0x7f040333
int attr motionEffect_end 0x7f040334
int attr motionEffect_move 0x7f040335
int attr motionEffect_start 0x7f040336
int attr motionEffect_strict 0x7f040337
int attr motionEffect_translationX 0x7f040338
int attr motionEffect_translationY 0x7f040339
int attr motionEffect_viewTransition 0x7f04033a
int attr motionInterpolator 0x7f04033b
int attr motionPath 0x7f04033c
int attr motionPathRotate 0x7f04033d
int attr motionProgress 0x7f04033e
int attr motionStagger 0x7f04033f
int attr motionTarget 0x7f040340
int attr motion_postLayoutCollision 0x7f040341
int attr motion_triggerOnCollision 0x7f040342
int attr moveWhenScrollAtTop 0x7f040343
int attr multiChoiceItemLayout 0x7f040344
int attr navGraph 0x7f040345
int attr navigationContentDescription 0x7f040346
int attr navigationIcon 0x7f040347
int attr navigationIconTint 0x7f040348
int attr navigationMode 0x7f040349
int attr navigationRailStyle 0x7f04034a
int attr navigationViewStyle 0x7f04034b
int attr negativeButtonText 0x7f04034c
int attr nestedScrollFlags 0x7f04034d
int attr nestedScrollViewStyle 0x7f04034e
int attr nestedScrollable 0x7f04034f
int attr nullable 0x7f040350
int attr number 0x7f040351
int attr numberOfColumns 0x7f040352
int attr numberOfRows 0x7f040353
int attr numericModifiers 0x7f040354
int attr onCross 0x7f040355
int attr onHide 0x7f040356
int attr onNegativeCross 0x7f040357
int attr onPositiveCross 0x7f040358
int attr onShow 0x7f040359
int attr onStateTransition 0x7f04035a
int attr onTouchUp 0x7f04035b
int attr onboardingDescriptionStyle 0x7f04035c
int attr onboardingHeaderStyle 0x7f04035d
int attr onboardingLogoStyle 0x7f04035e
int attr onboardingMainIconStyle 0x7f04035f
int attr onboardingNavigatorContainerStyle 0x7f040360
int attr onboardingPageIndicatorStyle 0x7f040361
int attr onboardingStartButtonStyle 0x7f040362
int attr onboardingTheme 0x7f040363
int attr onboardingTitleStyle 0x7f040364
int attr order 0x7f040365
int attr orderingFromXml 0x7f040366
int attr overlapAnchor 0x7f040367
int attr overlay 0x7f040368
int attr overlayDimActiveLevel 0x7f040369
int attr overlayDimDimmedLevel 0x7f04036a
int attr overlayDimMaskColor 0x7f04036b
int attr paddingBottomNoButtons 0x7f04036c
int attr paddingBottomSystemWindowInsets 0x7f04036d
int attr paddingEnd 0x7f04036e
int attr paddingLeftSystemWindowInsets 0x7f04036f
int attr paddingRightSystemWindowInsets 0x7f040370
int attr paddingStart 0x7f040371
int attr paddingTopNoTitle 0x7f040372
int attr paddingTopSystemWindowInsets 0x7f040373
int attr panelBackground 0x7f040374
int attr panelMenuListTheme 0x7f040375
int attr panelMenuListWidth 0x7f040376
int attr passwordToggleContentDescription 0x7f040377
int attr passwordToggleDrawable 0x7f040378
int attr passwordToggleEnabled 0x7f040379
int attr passwordToggleTint 0x7f04037a
int attr passwordToggleTintMode 0x7f04037b
int attr pathMotionArc 0x7f04037c
int attr path_percent 0x7f04037d
int attr pause 0x7f04037e
int attr percentHeight 0x7f04037f
int attr percentWidth 0x7f040380
int attr percentX 0x7f040381
int attr percentY 0x7f040382
int attr perpendicularPath_percent 0x7f040383
int attr persistent 0x7f040384
int attr picture_in_picture 0x7f040385
int attr pivotAnchor 0x7f040386
int attr placeholderActivityName 0x7f040387
int attr placeholderText 0x7f040388
int attr placeholderTextAppearance 0x7f040389
int attr placeholderTextColor 0x7f04038a
int attr placeholder_emptyVisibility 0x7f04038b
int attr play 0x7f04038c
int attr playbackControlButtonLabelStyle 0x7f04038d
int attr playbackControlsActionIcons 0x7f04038e
int attr playbackControlsAutoHideTickleTimeout 0x7f04038f
int attr playbackControlsAutoHideTimeout 0x7f040390
int attr playbackControlsButtonStyle 0x7f040391
int attr playbackControlsIconHighlightColor 0x7f040392
int attr playbackControlsTimeStyle 0x7f040393
int attr playbackMediaItemDetailsStyle 0x7f040394
int attr playbackMediaItemDurationStyle 0x7f040395
int attr playbackMediaItemNameStyle 0x7f040396
int attr playbackMediaItemNumberStyle 0x7f040397
int attr playbackMediaItemNumberViewFlipperLayout 0x7f040398
int attr playbackMediaItemNumberViewFlipperStyle 0x7f040399
int attr playbackMediaItemPaddingStart 0x7f04039a
int attr playbackMediaItemRowStyle 0x7f04039b
int attr playbackMediaItemSeparatorStyle 0x7f04039c
int attr playbackMediaListHeaderStyle 0x7f04039d
int attr playbackMediaListHeaderTitleStyle 0x7f04039e
int attr playbackPaddingEnd 0x7f04039f
int attr playbackPaddingStart 0x7f0403a0
int attr playbackProgressPrimaryColor 0x7f0403a1
int attr playbackProgressSecondaryColor 0x7f0403a2
int attr played_ad_marker_color 0x7f0403a3
int attr played_color 0x7f0403a4
int attr player_layout_id 0x7f0403a5
int attr polarRelativeTo 0x7f0403a6
int attr popEnterAnim 0x7f0403a7
int attr popExitAnim 0x7f0403a8
int attr popUpTo 0x7f0403a9
int attr popUpToInclusive 0x7f0403aa
int attr popUpToSaveState 0x7f0403ab
int attr popupMenuBackground 0x7f0403ac
int attr popupMenuStyle 0x7f0403ad
int attr popupTheme 0x7f0403ae
int attr popupWindowStyle 0x7f0403af
int attr positiveButtonText 0x7f0403b0
int attr preferenceCategoryStyle 0x7f0403b1
int attr preferenceCategoryTitleTextAppearance 0x7f0403b2
int attr preferenceCategoryTitleTextColor 0x7f0403b3
int attr preferenceFragmentCompatStyle 0x7f0403b4
int attr preferenceFragmentListStyle 0x7f0403b5
int attr preferenceFragmentStyle 0x7f0403b6
int attr preferenceInformationStyle 0x7f0403b7
int attr preferenceScreenStyle 0x7f0403b8
int attr preferenceStyle 0x7f0403b9
int attr preferenceTheme 0x7f0403ba
int attr prefixText 0x7f0403bb
int attr prefixTextAppearance 0x7f0403bc
int attr prefixTextColor 0x7f0403bd
int attr preserveIconSpacing 0x7f0403be
int attr pressedTranslationZ 0x7f0403bf
int attr primaryActivityName 0x7f0403c0
int attr progressBarPadding 0x7f0403c1
int attr progressBarStyle 0x7f0403c2
int attr quantizeMotionInterpolator 0x7f0403c3
int attr quantizeMotionPhase 0x7f0403c4
int attr quantizeMotionSteps 0x7f0403c5
int attr queryBackground 0x7f0403c6
int attr queryHint 0x7f0403c7
int attr queryPatterns 0x7f0403c8
int attr radioButtonStyle 0x7f0403c9
int attr rangeFillColor 0x7f0403ca
int attr ratingBarStyle 0x7f0403cb
int attr ratingBarStyleIndicator 0x7f0403cc
int attr ratingBarStyleSmall 0x7f0403cd
int attr reactiveGuide_animateChange 0x7f0403ce
int attr reactiveGuide_applyToAllConstraintSets 0x7f0403cf
int attr reactiveGuide_applyToConstraintSet 0x7f0403d0
int attr reactiveGuide_valueId 0x7f0403d1
int attr recyclerViewStyle 0x7f0403d2
int attr region_heightLessThan 0x7f0403d3
int attr region_heightMoreThan 0x7f0403d4
int attr region_widthLessThan 0x7f0403d5
int attr region_widthMoreThan 0x7f0403d6
int attr repeat 0x7f0403d7
int attr repeat_one 0x7f0403d8
int attr repeat_toggle_modes 0x7f0403d9
int attr resizeTrigger 0x7f0403da
int attr resize_mode 0x7f0403db
int attr resizedPaddingAdjustmentBottom 0x7f0403dc
int attr resizedPaddingAdjustmentTop 0x7f0403dd
int attr resizedTextSize 0x7f0403de
int attr restoreState 0x7f0403df
int attr reverseLayout 0x7f0403e0
int attr rewind 0x7f0403e1
int attr rippleColor 0x7f0403e2
int attr rotationCenterId 0x7f0403e3
int attr round 0x7f0403e4
int attr roundPercent 0x7f0403e5
int attr route 0x7f0403e6
int attr rowHeaderDescriptionStyle 0x7f0403e7
int attr rowHeaderDockStyle 0x7f0403e8
int attr rowHeaderStyle 0x7f0403e9
int attr rowHeight 0x7f0403ea
int attr rowHorizontalGridStyle 0x7f0403eb
int attr rowHoverCardDescriptionStyle 0x7f0403ec
int attr rowHoverCardTitleStyle 0x7f0403ed
int attr rowsVerticalGridStyle 0x7f0403ee
int attr saturation 0x7f0403ef
int attr scaleFromTextSize 0x7f0403f0
int attr scopeUris 0x7f0403f1
int attr scrimAnimationDuration 0x7f0403f2
int attr scrimBackground 0x7f0403f3
int attr scrimVisibleHeightTrigger 0x7f0403f4
int attr scrubber_color 0x7f0403f5
int attr scrubber_disabled_size 0x7f0403f6
int attr scrubber_dragged_size 0x7f0403f7
int attr scrubber_drawable 0x7f0403f8
int attr scrubber_enabled_size 0x7f0403f9
int attr searchHintIcon 0x7f0403fa
int attr searchIcon 0x7f0403fb
int attr searchOrbBrightColor 0x7f0403fc
int attr searchOrbColor 0x7f0403fd
int attr searchOrbIcon 0x7f0403fe
int attr searchOrbIconColor 0x7f0403ff
int attr searchOrbViewStyle 0x7f040400
int attr searchViewStyle 0x7f040401
int attr secondaryActivityAction 0x7f040402
int attr secondaryActivityName 0x7f040403
int attr sectionHeaderStyle 0x7f040404
int attr seekBarIncrement 0x7f040405
int attr seekBarPreferenceStyle 0x7f040406
int attr seekBarStyle 0x7f040407
int attr selectable 0x7f040408
int attr selectableItemBackground 0x7f040409
int attr selectableItemBackgroundBorderless 0x7f04040a
int attr selectedAnimationDelay 0x7f04040b
int attr selectedAnimationDuration 0x7f04040c
int attr selectionRequired 0x7f04040d
int attr selectorSize 0x7f04040e
int attr setsTag 0x7f04040f
int attr shapeAppearance 0x7f040410
int attr shapeAppearanceLargeComponent 0x7f040411
int attr shapeAppearanceMediumComponent 0x7f040412
int attr shapeAppearanceOverlay 0x7f040413
int attr shapeAppearanceSmallComponent 0x7f040414
int attr shortcutMatchRequired 0x7f040415
int attr shouldDisableView 0x7f040416
int attr showAnimationBehavior 0x7f040417
int attr showAsAction 0x7f040418
int attr showDelay 0x7f040419
int attr showDividers 0x7f04041a
int attr showMotionSpec 0x7f04041b
int attr showPaths 0x7f04041c
int attr showSeekBarValue 0x7f04041d
int attr showText 0x7f04041e
int attr showTitle 0x7f04041f
int attr show_buffering 0x7f040420
int attr show_fastforward_button 0x7f040421
int attr show_next_button 0x7f040422
int attr show_previous_button 0x7f040423
int attr show_rewind_button 0x7f040424
int attr show_shuffle_button 0x7f040425
int attr show_subtitle_button 0x7f040426
int attr show_timeout 0x7f040427
int attr show_vr_button 0x7f040428
int attr shrinkMotionSpec 0x7f040429
int attr shuffle 0x7f04042a
int attr shutter_background_color 0x7f04042b
int attr singleChoiceItemLayout 0x7f04042c
int attr singleLine 0x7f04042d
int attr singleLineTitle 0x7f04042e
int attr singleSelection 0x7f04042f
int attr sizePercent 0x7f040430
int attr skip_next 0x7f040431
int attr skip_previous 0x7f040432
int attr sliderStyle 0x7f040433
int attr snackbarButtonStyle 0x7f040434
int attr snackbarStyle 0x7f040435
int attr snackbarTextViewStyle 0x7f040436
int attr spanCount 0x7f040437
int attr spinBars 0x7f040438
int attr spinnerDropDownItemStyle 0x7f040439
int attr spinnerStyle 0x7f04043a
int attr splitLayoutDirection 0x7f04043b
int attr splitMinSmallestWidth 0x7f04043c
int attr splitMinWidth 0x7f04043d
int attr splitRatio 0x7f04043e
int attr splitTrack 0x7f04043f
int attr springBoundary 0x7f040440
int attr springDamping 0x7f040441
int attr springMass 0x7f040442
int attr springStiffness 0x7f040443
int attr springStopThreshold 0x7f040444
int attr srcCompat 0x7f040445
int attr stackFromEnd 0x7f040446
int attr staggered 0x7f040447
int attr startDestination 0x7f040448
int attr startIconCheckable 0x7f040449
int attr startIconContentDescription 0x7f04044a
int attr startIconDrawable 0x7f04044b
int attr startIconTint 0x7f04044c
int attr startIconTintMode 0x7f04044d
int attr state_above_anchor 0x7f04044e
int attr state_collapsed 0x7f04044f
int attr state_collapsible 0x7f040450
int attr state_dragged 0x7f040451
int attr state_liftable 0x7f040452
int attr state_lifted 0x7f040453
int attr statusBarBackground 0x7f040454
int attr statusBarForeground 0x7f040455
int attr statusBarScrim 0x7f040456
int attr strokeColor 0x7f040457
int attr strokeWidth 0x7f040458
int attr subMenuArrow 0x7f040459
int attr submitBackground 0x7f04045a
int attr subtitle 0x7f04045b
int attr subtitleCentered 0x7f04045c
int attr subtitleTextAppearance 0x7f04045d
int attr subtitleTextColor 0x7f04045e
int attr subtitleTextStyle 0x7f04045f
int attr suffixText 0x7f040460
int attr suffixTextAppearance 0x7f040461
int attr suffixTextColor 0x7f040462
int attr suggestionRowLayout 0x7f040463
int attr summary 0x7f040464
int attr summaryOff 0x7f040465
int attr summaryOn 0x7f040466
int attr surface_type 0x7f040467
int attr switchMinWidth 0x7f040468
int attr switchPadding 0x7f040469
int attr switchPreferenceCompatStyle 0x7f04046a
int attr switchPreferenceStyle 0x7f04046b
int attr switchStyle 0x7f04046c
int attr switchTextAppearance 0x7f04046d
int attr switchTextOff 0x7f04046e
int attr switchTextOn 0x7f04046f
int attr tabBackground 0x7f040470
int attr tabContentStart 0x7f040471
int attr tabGravity 0x7f040472
int attr tabIconTint 0x7f040473
int attr tabIconTintMode 0x7f040474
int attr tabIndicator 0x7f040475
int attr tabIndicatorAnimationDuration 0x7f040476
int attr tabIndicatorAnimationMode 0x7f040477
int attr tabIndicatorColor 0x7f040478
int attr tabIndicatorFullWidth 0x7f040479
int attr tabIndicatorGravity 0x7f04047a
int attr tabIndicatorHeight 0x7f04047b
int attr tabInlineLabel 0x7f04047c
int attr tabMaxWidth 0x7f04047d
int attr tabMinWidth 0x7f04047e
int attr tabMode 0x7f04047f
int attr tabPadding 0x7f040480
int attr tabPaddingBottom 0x7f040481
int attr tabPaddingEnd 0x7f040482
int attr tabPaddingStart 0x7f040483
int attr tabPaddingTop 0x7f040484
int attr tabRippleColor 0x7f040485
int attr tabSelectedTextColor 0x7f040486
int attr tabStyle 0x7f040487
int attr tabTextAppearance 0x7f040488
int attr tabTextColor 0x7f040489
int attr tabUnboundedRipple 0x7f04048a
int attr targetId 0x7f04048b
int attr targetPackage 0x7f04048c
int attr telltales_tailColor 0x7f04048d
int attr telltales_tailScale 0x7f04048e
int attr telltales_velocityMode 0x7f04048f
int attr textAllCaps 0x7f040490
int attr textAppearanceBody1 0x7f040491
int attr textAppearanceBody2 0x7f040492
int attr textAppearanceButton 0x7f040493
int attr textAppearanceCaption 0x7f040494
int attr textAppearanceHeadline1 0x7f040495
int attr textAppearanceHeadline2 0x7f040496
int attr textAppearanceHeadline3 0x7f040497
int attr textAppearanceHeadline4 0x7f040498
int attr textAppearanceHeadline5 0x7f040499
int attr textAppearanceHeadline6 0x7f04049a
int attr textAppearanceLargePopupMenu 0x7f04049b
int attr textAppearanceLineHeightEnabled 0x7f04049c
int attr textAppearanceListItem 0x7f04049d
int attr textAppearanceListItemSecondary 0x7f04049e
int attr textAppearanceListItemSmall 0x7f04049f
int attr textAppearanceOverline 0x7f0404a0
int attr textAppearancePopupMenuHeader 0x7f0404a1
int attr textAppearanceSearchResultSubtitle 0x7f0404a2
int attr textAppearanceSearchResultTitle 0x7f0404a3
int attr textAppearanceSmallPopupMenu 0x7f0404a4
int attr textAppearanceSubtitle1 0x7f0404a5
int attr textAppearanceSubtitle2 0x7f0404a6
int attr textBackground 0x7f0404a7
int attr textBackgroundPanX 0x7f0404a8
int attr textBackgroundPanY 0x7f0404a9
int attr textBackgroundRotate 0x7f0404aa
int attr textBackgroundZoom 0x7f0404ab
int attr textColorAlertDialogListItem 0x7f0404ac
int attr textColorSearchUrl 0x7f0404ad
int attr textEndPadding 0x7f0404ae
int attr textFillColor 0x7f0404af
int attr textInputLayoutFocusedRectEnabled 0x7f0404b0
int attr textInputStyle 0x7f0404b1
int attr textLocale 0x7f0404b2
int attr textOutlineColor 0x7f0404b3
int attr textOutlineThickness 0x7f0404b4
int attr textPanX 0x7f0404b5
int attr textPanY 0x7f0404b6
int attr textStartPadding 0x7f0404b7
int attr textureBlurFactor 0x7f0404b8
int attr textureEffect 0x7f0404b9
int attr textureHeight 0x7f0404ba
int attr textureWidth 0x7f0404bb
int attr theme 0x7f0404bc
int attr themeLineHeight 0x7f0404bd
int attr thickness 0x7f0404be
int attr thumbColor 0x7f0404bf
int attr thumbElevation 0x7f0404c0
int attr thumbRadius 0x7f0404c1
int attr thumbStrokeColor 0x7f0404c2
int attr thumbStrokeWidth 0x7f0404c3
int attr thumbTextPadding 0x7f0404c4
int attr thumbTint 0x7f0404c5
int attr thumbTintMode 0x7f0404c6
int attr thumb_down 0x7f0404c7
int attr thumb_down_outline 0x7f0404c8
int attr thumb_up 0x7f0404c9
int attr thumb_up_outline 0x7f0404ca
int attr tickColor 0x7f0404cb
int attr tickColorActive 0x7f0404cc
int attr tickColorInactive 0x7f0404cd
int attr tickMark 0x7f0404ce
int attr tickMarkTint 0x7f0404cf
int attr tickMarkTintMode 0x7f0404d0
int attr tickVisible 0x7f0404d1
int attr time_bar_min_update_interval 0x7f0404d2
int attr tint 0x7f0404d3
int attr tintMode 0x7f0404d4
int attr title 0x7f0404d5
int attr titleCentered 0x7f0404d6
int attr titleCollapseMode 0x7f0404d7
int attr titleEnabled 0x7f0404d8
int attr titleMargin 0x7f0404d9
int attr titleMarginBottom 0x7f0404da
int attr titleMarginEnd 0x7f0404db
int attr titleMarginStart 0x7f0404dc
int attr titleMarginTop 0x7f0404dd
int attr titleMargins 0x7f0404de
int attr titleTextAppearance 0x7f0404df
int attr titleTextColor 0x7f0404e0
int attr titleTextStyle 0x7f0404e1
int attr toolbarId 0x7f0404e2
int attr toolbarNavigationButtonStyle 0x7f0404e3
int attr toolbarStyle 0x7f0404e4
int attr tooltipForegroundColor 0x7f0404e5
int attr tooltipFrameBackground 0x7f0404e6
int attr tooltipStyle 0x7f0404e7
int attr tooltipText 0x7f0404e8
int attr touchAnchorId 0x7f0404e9
int attr touchAnchorSide 0x7f0404ea
int attr touchRegionId 0x7f0404eb
int attr touch_target_height 0x7f0404ec
int attr track 0x7f0404ed
int attr trackColor 0x7f0404ee
int attr trackColorActive 0x7f0404ef
int attr trackColorInactive 0x7f0404f0
int attr trackCornerRadius 0x7f0404f1
int attr trackHeight 0x7f0404f2
int attr trackThickness 0x7f0404f3
int attr trackTint 0x7f0404f4
int attr trackTintMode 0x7f0404f5
int attr transformPivotTarget 0x7f0404f6
int attr transitionDisable 0x7f0404f7
int attr transitionEasing 0x7f0404f8
int attr transitionFlags 0x7f0404f9
int attr transitionPathRotate 0x7f0404fa
int attr transitionShapeAppearance 0x7f0404fb
int attr triggerId 0x7f0404fc
int attr triggerReceiver 0x7f0404fd
int attr triggerSlack 0x7f0404fe
int attr ttcIndex 0x7f0404ff
int attr unplayed_color 0x7f040500
int attr upDuration 0x7f040501
int attr updatesContinuously 0x7f040502
int attr uri 0x7f040503
int attr useCompatPadding 0x7f040504
int attr useCurrentTime 0x7f040505
int attr useMaterialThemeColors 0x7f040506
int attr useSimpleSummaryProvider 0x7f040507
int attr use_artwork 0x7f040508
int attr use_controller 0x7f040509
int attr values 0x7f04050a
int attr verticalMargin 0x7f04050b
int attr verticalOffset 0x7f04050c
int attr viewInflaterClass 0x7f04050d
int attr viewTransitionMode 0x7f04050e
int attr viewTransitionOnCross 0x7f04050f
int attr viewTransitionOnNegativeCross 0x7f040510
int attr viewTransitionOnPositiveCross 0x7f040511
int attr visibilityMode 0x7f040512
int attr voiceIcon 0x7f040513
int attr warmth 0x7f040514
int attr waveDecay 0x7f040515
int attr waveOffset 0x7f040516
int attr wavePeriod 0x7f040517
int attr wavePhase 0x7f040518
int attr waveShape 0x7f040519
int attr waveVariesBy 0x7f04051a
int attr widgetLayout 0x7f04051b
int attr windowActionBar 0x7f04051c
int attr windowActionBarOverlay 0x7f04051d
int attr windowActionModeOverlay 0x7f04051e
int attr windowFixedHeightMajor 0x7f04051f
int attr windowFixedHeightMinor 0x7f040520
int attr windowFixedWidthMajor 0x7f040521
int attr windowFixedWidthMinor 0x7f040522
int attr windowMinWidthMajor 0x7f040523
int attr windowMinWidthMinor 0x7f040524
int attr windowNoTitle 0x7f040525
int attr yearSelectedStyle 0x7f040526
int attr yearStyle 0x7f040527
int attr yearTodayStyle 0x7f040528
int bool abc_action_bar_embed_tabs 0x7f050000
int bool abc_config_actionMenuItemAllCaps 0x7f050001
int bool config_materialPreferenceIconSpaceReserved 0x7f050002
int bool enable_system_alarm_service_default 0x7f050003
int bool enable_system_foreground_service_default 0x7f050004
int bool enable_system_job_service_default 0x7f050005
int bool mtrl_btn_textappearance_all_caps 0x7f050006
int bool workmanager_test_configuration 0x7f050007
int color abc_background_cache_hint_selector_material_dark 0x7f060000
int color abc_background_cache_hint_selector_material_light 0x7f060001
int color abc_btn_colored_borderless_text_material 0x7f060002
int color abc_btn_colored_text_material 0x7f060003
int color abc_color_highlight_material 0x7f060004
int color abc_decor_view_status_guard 0x7f060005
int color abc_decor_view_status_guard_light 0x7f060006
int color abc_hint_foreground_material_dark 0x7f060007
int color abc_hint_foreground_material_light 0x7f060008
int color abc_primary_text_disable_only_material_dark 0x7f060009
int color abc_primary_text_disable_only_material_light 0x7f06000a
int color abc_primary_text_material_dark 0x7f06000b
int color abc_primary_text_material_light 0x7f06000c
int color abc_search_url_text 0x7f06000d
int color abc_search_url_text_normal 0x7f06000e
int color abc_search_url_text_pressed 0x7f06000f
int color abc_search_url_text_selected 0x7f060010
int color abc_secondary_text_material_dark 0x7f060011
int color abc_secondary_text_material_light 0x7f060012
int color abc_tint_btn_checkable 0x7f060013
int color abc_tint_default 0x7f060014
int color abc_tint_edittext 0x7f060015
int color abc_tint_seek_thumb 0x7f060016
int color abc_tint_spinner 0x7f060017
int color abc_tint_switch_track 0x7f060018
int color accent_material_dark 0x7f060019
int color accent_material_light 0x7f06001a
int color androidx_core_ripple_material_light 0x7f06001b
int color androidx_core_secondary_text_default_material_light 0x7f06001c
int color background 0x7f06001d
int color background_floating_material_dark 0x7f06001e
int color background_floating_material_light 0x7f06001f
int color background_material_dark 0x7f060020
int color background_material_light 0x7f060021
int color black 0x7f060022
int color bright_foreground_disabled_material_dark 0x7f060023
int color bright_foreground_disabled_material_light 0x7f060024
int color bright_foreground_inverse_material_dark 0x7f060025
int color bright_foreground_inverse_material_light 0x7f060026
int color bright_foreground_material_dark 0x7f060027
int color bright_foreground_material_light 0x7f060028
int color button_material_dark 0x7f060029
int color button_material_light 0x7f06002a
int color call_notification_answer_color 0x7f06002b
int color call_notification_decline_color 0x7f06002c
int color cardview_dark_background 0x7f06002d
int color cardview_light_background 0x7f06002e
int color cardview_shadow_end_color 0x7f06002f
int color cardview_shadow_start_color 0x7f060030
int color checkbox_themeable_attribute_color 0x7f060031
int color common_google_signin_btn_text_dark 0x7f060032
int color common_google_signin_btn_text_dark_default 0x7f060033
int color common_google_signin_btn_text_dark_disabled 0x7f060034
int color common_google_signin_btn_text_dark_focused 0x7f060035
int color common_google_signin_btn_text_dark_pressed 0x7f060036
int color common_google_signin_btn_text_light 0x7f060037
int color common_google_signin_btn_text_light_default 0x7f060038
int color common_google_signin_btn_text_light_disabled 0x7f060039
int color common_google_signin_btn_text_light_focused 0x7f06003a
int color common_google_signin_btn_text_light_pressed 0x7f06003b
int color common_google_signin_btn_tint 0x7f06003c
int color design_bottom_navigation_shadow_color 0x7f06003d
int color design_box_stroke_color 0x7f06003e
int color design_dark_default_color_background 0x7f06003f
int color design_dark_default_color_error 0x7f060040
int color design_dark_default_color_on_background 0x7f060041
int color design_dark_default_color_on_error 0x7f060042
int color design_dark_default_color_on_primary 0x7f060043
int color design_dark_default_color_on_secondary 0x7f060044
int color design_dark_default_color_on_surface 0x7f060045
int color design_dark_default_color_primary 0x7f060046
int color design_dark_default_color_primary_dark 0x7f060047
int color design_dark_default_color_primary_variant 0x7f060048
int color design_dark_default_color_secondary 0x7f060049
int color design_dark_default_color_secondary_variant 0x7f06004a
int color design_dark_default_color_surface 0x7f06004b
int color design_default_color_background 0x7f06004c
int color design_default_color_error 0x7f06004d
int color design_default_color_on_background 0x7f06004e
int color design_default_color_on_error 0x7f06004f
int color design_default_color_on_primary 0x7f060050
int color design_default_color_on_secondary 0x7f060051
int color design_default_color_on_surface 0x7f060052
int color design_default_color_primary 0x7f060053
int color design_default_color_primary_dark 0x7f060054
int color design_default_color_primary_variant 0x7f060055
int color design_default_color_secondary 0x7f060056
int color design_default_color_secondary_variant 0x7f060057
int color design_default_color_surface 0x7f060058
int color design_error 0x7f060059
int color design_fab_shadow_end_color 0x7f06005a
int color design_fab_shadow_mid_color 0x7f06005b
int color design_fab_shadow_start_color 0x7f06005c
int color design_fab_stroke_end_inner_color 0x7f06005d
int color design_fab_stroke_end_outer_color 0x7f06005e
int color design_fab_stroke_top_inner_color 0x7f06005f
int color design_fab_stroke_top_outer_color 0x7f060060
int color design_icon_tint 0x7f060061
int color design_snackbar_background_color 0x7f060062
int color dim_foreground_disabled_material_dark 0x7f060063
int color dim_foreground_disabled_material_light 0x7f060064
int color dim_foreground_material_dark 0x7f060065
int color dim_foreground_material_light 0x7f060066
int color error 0x7f060067
int color error_color_material_dark 0x7f060068
int color error_color_material_light 0x7f060069
int color exo_black_opacity_60 0x7f06006a
int color exo_black_opacity_70 0x7f06006b
int color exo_bottom_bar_background 0x7f06006c
int color exo_edit_mode_background_color 0x7f06006d
int color exo_error_message_background_color 0x7f06006e
int color exo_styled_error_message_background 0x7f06006f
int color exo_white 0x7f060070
int color exo_white_opacity_70 0x7f060071
int color foreground_material_dark 0x7f060072
int color foreground_material_light 0x7f060073
int color highlighted_text_material_dark 0x7f060074
int color highlighted_text_material_light 0x7f060075
int color ic_launcher_background 0x7f060076
int color lb_action_text_color 0x7f060077
int color lb_background_protection 0x7f060078
int color lb_basic_card_bg_color 0x7f060079
int color lb_basic_card_content_text_color 0x7f06007a
int color lb_basic_card_info_bg_color 0x7f06007b
int color lb_basic_card_title_text_color 0x7f06007c
int color lb_browse_header_color 0x7f06007d
int color lb_browse_header_description_color 0x7f06007e
int color lb_browse_title_color 0x7f06007f
int color lb_control_button_color 0x7f060080
int color lb_control_button_text 0x7f060081
int color lb_default_brand_color 0x7f060082
int color lb_default_brand_color_dark 0x7f060083
int color lb_default_search_color 0x7f060084
int color lb_default_search_icon_color 0x7f060085
int color lb_details_description_body_color 0x7f060086
int color lb_details_description_color 0x7f060087
int color lb_details_overview_bg_color 0x7f060088
int color lb_error_background_color_opaque 0x7f060089
int color lb_error_background_color_translucent 0x7f06008a
int color lb_error_message 0x7f06008b
int color lb_grey 0x7f06008c
int color lb_guidedactions_background 0x7f06008d
int color lb_guidedactions_background_dark 0x7f06008e
int color lb_guidedactions_item_unselected_text_color 0x7f06008f
int color lb_list_item_unselected_text_color 0x7f060090
int color lb_media_background_color 0x7f060091
int color lb_page_indicator_arrow_background 0x7f060092
int color lb_page_indicator_arrow_shadow 0x7f060093
int color lb_page_indicator_dot 0x7f060094
int color lb_playback_background_progress_color 0x7f060095
int color lb_playback_controls_background_dark 0x7f060096
int color lb_playback_controls_background_light 0x7f060097
int color lb_playback_controls_time_text_color 0x7f060098
int color lb_playback_icon_highlight_no_theme 0x7f060099
int color lb_playback_media_row_highlight_color 0x7f06009a
int color lb_playback_media_row_separator_highlight_color 0x7f06009b
int color lb_playback_now_playing_bar_color 0x7f06009c
int color lb_playback_progress_color_no_theme 0x7f06009d
int color lb_playback_progress_secondary_color_no_theme 0x7f06009e
int color lb_playback_secondary_progress_color 0x7f06009f
int color lb_preference_decor_list_background 0x7f0600a0
int color lb_preference_item_category_text_color 0x7f0600a1
int color lb_preference_item_primary_text_color 0x7f0600a2
int color lb_preference_item_primary_text_color_default 0x7f0600a3
int color lb_preference_item_primary_text_color_disabled 0x7f0600a4
int color lb_preference_item_secondary_text_color 0x7f0600a5
int color lb_preference_item_secondary_text_color_default 0x7f0600a6
int color lb_preference_item_secondary_text_color_disabled 0x7f0600a7
int color lb_search_bar_hint 0x7f0600a8
int color lb_search_bar_hint_speech_mode 0x7f0600a9
int color lb_search_bar_text 0x7f0600aa
int color lb_search_bar_text_speech_mode 0x7f0600ab
int color lb_search_plate_hint_text_color 0x7f0600ac
int color lb_speech_orb_not_recording 0x7f0600ad
int color lb_speech_orb_not_recording_icon 0x7f0600ae
int color lb_speech_orb_not_recording_pulsed 0x7f0600af
int color lb_speech_orb_recording 0x7f0600b0
int color lb_tv_white 0x7f0600b1
int color lb_view_dim_mask_color 0x7f0600b2
int color material_blue_grey_800 0x7f0600b3
int color material_blue_grey_900 0x7f0600b4
int color material_blue_grey_950 0x7f0600b5
int color material_cursor_color 0x7f0600b6
int color material_deep_teal_200 0x7f0600b7
int color material_deep_teal_500 0x7f0600b8
int color material_grey_100 0x7f0600b9
int color material_grey_300 0x7f0600ba
int color material_grey_50 0x7f0600bb
int color material_grey_600 0x7f0600bc
int color material_grey_800 0x7f0600bd
int color material_grey_850 0x7f0600be
int color material_grey_900 0x7f0600bf
int color material_on_background_disabled 0x7f0600c0
int color material_on_background_emphasis_high_type 0x7f0600c1
int color material_on_background_emphasis_medium 0x7f0600c2
int color material_on_primary_disabled 0x7f0600c3
int color material_on_primary_emphasis_high_type 0x7f0600c4
int color material_on_primary_emphasis_medium 0x7f0600c5
int color material_on_surface_disabled 0x7f0600c6
int color material_on_surface_emphasis_high_type 0x7f0600c7
int color material_on_surface_emphasis_medium 0x7f0600c8
int color material_on_surface_stroke 0x7f0600c9
int color material_slider_active_tick_marks_color 0x7f0600ca
int color material_slider_active_track_color 0x7f0600cb
int color material_slider_halo_color 0x7f0600cc
int color material_slider_inactive_tick_marks_color 0x7f0600cd
int color material_slider_inactive_track_color 0x7f0600ce
int color material_slider_thumb_color 0x7f0600cf
int color material_timepicker_button_background 0x7f0600d0
int color material_timepicker_button_stroke 0x7f0600d1
int color material_timepicker_clock_text_color 0x7f0600d2
int color material_timepicker_clockface 0x7f0600d3
int color material_timepicker_modebutton_tint 0x7f0600d4
int color mtrl_btn_bg_color_selector 0x7f0600d5
int color mtrl_btn_ripple_color 0x7f0600d6
int color mtrl_btn_stroke_color_selector 0x7f0600d7
int color mtrl_btn_text_btn_bg_color_selector 0x7f0600d8
int color mtrl_btn_text_btn_ripple_color 0x7f0600d9
int color mtrl_btn_text_color_disabled 0x7f0600da
int color mtrl_btn_text_color_selector 0x7f0600db
int color mtrl_btn_transparent_bg_color 0x7f0600dc
int color mtrl_calendar_item_stroke_color 0x7f0600dd
int color mtrl_calendar_selected_range 0x7f0600de
int color mtrl_card_view_foreground 0x7f0600df
int color mtrl_card_view_ripple 0x7f0600e0
int color mtrl_chip_background_color 0x7f0600e1
int color mtrl_chip_close_icon_tint 0x7f0600e2
int color mtrl_chip_surface_color 0x7f0600e3
int color mtrl_chip_text_color 0x7f0600e4
int color mtrl_choice_chip_background_color 0x7f0600e5
int color mtrl_choice_chip_ripple_color 0x7f0600e6
int color mtrl_choice_chip_text_color 0x7f0600e7
int color mtrl_error 0x7f0600e8
int color mtrl_fab_bg_color_selector 0x7f0600e9
int color mtrl_fab_icon_text_color_selector 0x7f0600ea
int color mtrl_fab_ripple_color 0x7f0600eb
int color mtrl_filled_background_color 0x7f0600ec
int color mtrl_filled_icon_tint 0x7f0600ed
int color mtrl_filled_stroke_color 0x7f0600ee
int color mtrl_indicator_text_color 0x7f0600ef
int color mtrl_navigation_bar_colored_item_tint 0x7f0600f0
int color mtrl_navigation_bar_colored_ripple_color 0x7f0600f1
int color mtrl_navigation_bar_item_tint 0x7f0600f2
int color mtrl_navigation_bar_ripple_color 0x7f0600f3
int color mtrl_navigation_item_background_color 0x7f0600f4
int color mtrl_navigation_item_icon_tint 0x7f0600f5
int color mtrl_navigation_item_text_color 0x7f0600f6
int color mtrl_on_primary_text_btn_text_color_selector 0x7f0600f7
int color mtrl_on_surface_ripple_color 0x7f0600f8
int color mtrl_outlined_icon_tint 0x7f0600f9
int color mtrl_outlined_stroke_color 0x7f0600fa
int color mtrl_popupmenu_overlay_color 0x7f0600fb
int color mtrl_scrim_color 0x7f0600fc
int color mtrl_tabs_colored_ripple_color 0x7f0600fd
int color mtrl_tabs_icon_color_selector 0x7f0600fe
int color mtrl_tabs_icon_color_selector_colored 0x7f0600ff
int color mtrl_tabs_legacy_text_color_selector 0x7f060100
int color mtrl_tabs_ripple_color 0x7f060101
int color mtrl_text_btn_text_color_selector 0x7f060102
int color mtrl_textinput_default_box_stroke_color 0x7f060103
int color mtrl_textinput_disabled_color 0x7f060104
int color mtrl_textinput_filled_box_default_background_color 0x7f060105
int color mtrl_textinput_focused_box_stroke_color 0x7f060106
int color mtrl_textinput_hovered_box_stroke_color 0x7f060107
int color notification_action_color_filter 0x7f060108
int color notification_icon_bg_color 0x7f060109
int color notification_material_background_media_default_color 0x7f06010a
int color on_background 0x7f06010b
int color on_error 0x7f06010c
int color on_primary 0x7f06010d
int color on_primary_container 0x7f06010e
int color on_secondary 0x7f06010f
int color on_secondary_container 0x7f060110
int color on_surface 0x7f060111
int color on_surface_variant 0x7f060112
int color player_background 0x7f060113
int color player_controls 0x7f060114
int color player_progress 0x7f060115
int color preference_fallback_accent_color 0x7f060116
int color primary 0x7f060117
int color primary_container 0x7f060118
int color primary_dark_material_dark 0x7f060119
int color primary_dark_material_light 0x7f06011a
int color primary_material_dark 0x7f06011b
int color primary_material_light 0x7f06011c
int color primary_text_default_material_dark 0x7f06011d
int color primary_text_default_material_light 0x7f06011e
int color primary_text_disabled_material_dark 0x7f06011f
int color primary_text_disabled_material_light 0x7f060120
int color radiobutton_themeable_attribute_color 0x7f060121
int color ripple_material_dark 0x7f060122
int color ripple_material_light 0x7f060123
int color secondary 0x7f060124
int color secondary_container 0x7f060125
int color secondary_text_default_material_dark 0x7f060126
int color secondary_text_default_material_light 0x7f060127
int color secondary_text_disabled_material_dark 0x7f060128
int color secondary_text_disabled_material_light 0x7f060129
int color surface 0x7f06012a
int color surface_variant 0x7f06012b
int color switch_thumb_disabled_material_dark 0x7f06012c
int color switch_thumb_disabled_material_light 0x7f06012d
int color switch_thumb_material_dark 0x7f06012e
int color switch_thumb_material_light 0x7f06012f
int color switch_thumb_normal_material_dark 0x7f060130
int color switch_thumb_normal_material_light 0x7f060131
int color test_mtrl_calendar_day 0x7f060132
int color test_mtrl_calendar_day_selected 0x7f060133
int color tooltip_background_dark 0x7f060134
int color tooltip_background_light 0x7f060135
int color transparent 0x7f060136
int color vector_tint_color 0x7f060137
int color vector_tint_theme_color 0x7f060138
int color white 0x7f060139
int dimen abc_action_bar_content_inset_material 0x7f070000
int dimen abc_action_bar_content_inset_with_nav 0x7f070001
int dimen abc_action_bar_default_height_material 0x7f070002
int dimen abc_action_bar_default_padding_end_material 0x7f070003
int dimen abc_action_bar_default_padding_start_material 0x7f070004
int dimen abc_action_bar_elevation_material 0x7f070005
int dimen abc_action_bar_icon_vertical_padding_material 0x7f070006
int dimen abc_action_bar_overflow_padding_end_material 0x7f070007
int dimen abc_action_bar_overflow_padding_start_material 0x7f070008
int dimen abc_action_bar_stacked_max_height 0x7f070009
int dimen abc_action_bar_stacked_tab_max_width 0x7f07000a
int dimen abc_action_bar_subtitle_bottom_margin_material 0x7f07000b
int dimen abc_action_bar_subtitle_top_margin_material 0x7f07000c
int dimen abc_action_button_min_height_material 0x7f07000d
int dimen abc_action_button_min_width_material 0x7f07000e
int dimen abc_action_button_min_width_overflow_material 0x7f07000f
int dimen abc_alert_dialog_button_bar_height 0x7f070010
int dimen abc_alert_dialog_button_dimen 0x7f070011
int dimen abc_button_inset_horizontal_material 0x7f070012
int dimen abc_button_inset_vertical_material 0x7f070013
int dimen abc_button_padding_horizontal_material 0x7f070014
int dimen abc_button_padding_vertical_material 0x7f070015
int dimen abc_cascading_menus_min_smallest_width 0x7f070016
int dimen abc_config_prefDialogWidth 0x7f070017
int dimen abc_control_corner_material 0x7f070018
int dimen abc_control_inset_material 0x7f070019
int dimen abc_control_padding_material 0x7f07001a
int dimen abc_dialog_corner_radius_material 0x7f07001b
int dimen abc_dialog_fixed_height_major 0x7f07001c
int dimen abc_dialog_fixed_height_minor 0x7f07001d
int dimen abc_dialog_fixed_width_major 0x7f07001e
int dimen abc_dialog_fixed_width_minor 0x7f07001f
int dimen abc_dialog_list_padding_bottom_no_buttons 0x7f070020
int dimen abc_dialog_list_padding_top_no_title 0x7f070021
int dimen abc_dialog_min_width_major 0x7f070022
int dimen abc_dialog_min_width_minor 0x7f070023
int dimen abc_dialog_padding_material 0x7f070024
int dimen abc_dialog_padding_top_material 0x7f070025
int dimen abc_dialog_title_divider_material 0x7f070026
int dimen abc_disabled_alpha_material_dark 0x7f070027
int dimen abc_disabled_alpha_material_light 0x7f070028
int dimen abc_dropdownitem_icon_width 0x7f070029
int dimen abc_dropdownitem_text_padding_left 0x7f07002a
int dimen abc_dropdownitem_text_padding_right 0x7f07002b
int dimen abc_edit_text_inset_bottom_material 0x7f07002c
int dimen abc_edit_text_inset_horizontal_material 0x7f07002d
int dimen abc_edit_text_inset_top_material 0x7f07002e
int dimen abc_floating_window_z 0x7f07002f
int dimen abc_list_item_height_large_material 0x7f070030
int dimen abc_list_item_height_material 0x7f070031
int dimen abc_list_item_height_small_material 0x7f070032
int dimen abc_list_item_padding_horizontal_material 0x7f070033
int dimen abc_panel_menu_list_width 0x7f070034
int dimen abc_progress_bar_height_material 0x7f070035
int dimen abc_search_view_preferred_height 0x7f070036
int dimen abc_search_view_preferred_width 0x7f070037
int dimen abc_seekbar_track_background_height_material 0x7f070038
int dimen abc_seekbar_track_progress_height_material 0x7f070039
int dimen abc_select_dialog_padding_start_material 0x7f07003a
int dimen abc_star_big 0x7f07003b
int dimen abc_star_medium 0x7f07003c
int dimen abc_star_small 0x7f07003d
int dimen abc_switch_padding 0x7f07003e
int dimen abc_text_size_body_1_material 0x7f07003f
int dimen abc_text_size_body_2_material 0x7f070040
int dimen abc_text_size_button_material 0x7f070041
int dimen abc_text_size_caption_material 0x7f070042
int dimen abc_text_size_display_1_material 0x7f070043
int dimen abc_text_size_display_2_material 0x7f070044
int dimen abc_text_size_display_3_material 0x7f070045
int dimen abc_text_size_display_4_material 0x7f070046
int dimen abc_text_size_headline_material 0x7f070047
int dimen abc_text_size_large_material 0x7f070048
int dimen abc_text_size_medium_material 0x7f070049
int dimen abc_text_size_menu_header_material 0x7f07004a
int dimen abc_text_size_menu_material 0x7f07004b
int dimen abc_text_size_small_material 0x7f07004c
int dimen abc_text_size_subhead_material 0x7f07004d
int dimen abc_text_size_subtitle_material_toolbar 0x7f07004e
int dimen abc_text_size_title_material 0x7f07004f
int dimen abc_text_size_title_material_toolbar 0x7f070050
int dimen action_bar_size 0x7f070051
int dimen appcompat_dialog_background_inset 0x7f070052
int dimen cardview_compat_inset_shadow 0x7f070053
int dimen cardview_default_elevation 0x7f070054
int dimen cardview_default_radius 0x7f070055
int dimen clock_face_margin_start 0x7f070056
int dimen compat_button_inset_horizontal_material 0x7f070057
int dimen compat_button_inset_vertical_material 0x7f070058
int dimen compat_button_padding_horizontal_material 0x7f070059
int dimen compat_button_padding_vertical_material 0x7f07005a
int dimen compat_control_corner_material 0x7f07005b
int dimen compat_notification_large_icon_max_height 0x7f07005c
int dimen compat_notification_large_icon_max_width 0x7f07005d
int dimen def_drawer_elevation 0x7f07005e
int dimen default_dimension 0x7f07005f
int dimen design_appbar_elevation 0x7f070060
int dimen design_bottom_navigation_active_item_max_width 0x7f070061
int dimen design_bottom_navigation_active_item_min_width 0x7f070062
int dimen design_bottom_navigation_active_text_size 0x7f070063
int dimen design_bottom_navigation_elevation 0x7f070064
int dimen design_bottom_navigation_height 0x7f070065
int dimen design_bottom_navigation_icon_size 0x7f070066
int dimen design_bottom_navigation_item_max_width 0x7f070067
int dimen design_bottom_navigation_item_min_width 0x7f070068
int dimen design_bottom_navigation_label_padding 0x7f070069
int dimen design_bottom_navigation_margin 0x7f07006a
int dimen design_bottom_navigation_shadow_height 0x7f07006b
int dimen design_bottom_navigation_text_size 0x7f07006c
int dimen design_bottom_sheet_elevation 0x7f07006d
int dimen design_bottom_sheet_modal_elevation 0x7f07006e
int dimen design_bottom_sheet_peek_height_min 0x7f07006f
int dimen design_fab_border_width 0x7f070070
int dimen design_fab_elevation 0x7f070071
int dimen design_fab_image_size 0x7f070072
int dimen design_fab_size_mini 0x7f070073
int dimen design_fab_size_normal 0x7f070074
int dimen design_fab_translation_z_hovered_focused 0x7f070075
int dimen design_fab_translation_z_pressed 0x7f070076
int dimen design_navigation_elevation 0x7f070077
int dimen design_navigation_icon_padding 0x7f070078
int dimen design_navigation_icon_size 0x7f070079
int dimen design_navigation_item_horizontal_padding 0x7f07007a
int dimen design_navigation_item_icon_padding 0x7f07007b
int dimen design_navigation_max_width 0x7f07007c
int dimen design_navigation_padding_bottom 0x7f07007d
int dimen design_navigation_separator_vertical_padding 0x7f07007e
int dimen design_snackbar_action_inline_max_width 0x7f07007f
int dimen design_snackbar_action_text_color_alpha 0x7f070080
int dimen design_snackbar_background_corner_radius 0x7f070081
int dimen design_snackbar_elevation 0x7f070082
int dimen design_snackbar_extra_spacing_horizontal 0x7f070083
int dimen design_snackbar_max_width 0x7f070084
int dimen design_snackbar_min_width 0x7f070085
int dimen design_snackbar_padding_horizontal 0x7f070086
int dimen design_snackbar_padding_vertical 0x7f070087
int dimen design_snackbar_padding_vertical_2lines 0x7f070088
int dimen design_snackbar_text_size 0x7f070089
int dimen design_tab_max_width 0x7f07008a
int dimen design_tab_scrollable_min_width 0x7f07008b
int dimen design_tab_text_size 0x7f07008c
int dimen design_tab_text_size_2line 0x7f07008d
int dimen design_textinput_caption_translate_y 0x7f07008e
int dimen disabled_alpha_material_dark 0x7f07008f
int dimen disabled_alpha_material_light 0x7f070090
int dimen exo_error_message_height 0x7f070091
int dimen exo_error_message_margin_bottom 0x7f070092
int dimen exo_error_message_text_padding_horizontal 0x7f070093
int dimen exo_error_message_text_padding_vertical 0x7f070094
int dimen exo_error_message_text_size 0x7f070095
int dimen exo_icon_horizontal_margin 0x7f070096
int dimen exo_icon_padding 0x7f070097
int dimen exo_icon_padding_bottom 0x7f070098
int dimen exo_icon_size 0x7f070099
int dimen exo_icon_text_size 0x7f07009a
int dimen exo_media_button_height 0x7f07009b
int dimen exo_media_button_width 0x7f07009c
int dimen exo_setting_width 0x7f07009d
int dimen exo_settings_height 0x7f07009e
int dimen exo_settings_icon_size 0x7f07009f
int dimen exo_settings_main_text_size 0x7f0700a0
int dimen exo_settings_offset 0x7f0700a1
int dimen exo_settings_sub_text_size 0x7f0700a2
int dimen exo_settings_text_height 0x7f0700a3
int dimen exo_small_icon_height 0x7f0700a4
int dimen exo_small_icon_horizontal_margin 0x7f0700a5
int dimen exo_small_icon_padding_horizontal 0x7f0700a6
int dimen exo_small_icon_padding_vertical 0x7f0700a7
int dimen exo_small_icon_width 0x7f0700a8
int dimen exo_styled_bottom_bar_height 0x7f0700a9
int dimen exo_styled_bottom_bar_margin_top 0x7f0700aa
int dimen exo_styled_bottom_bar_time_padding 0x7f0700ab
int dimen exo_styled_controls_padding 0x7f0700ac
int dimen exo_styled_minimal_controls_margin_bottom 0x7f0700ad
int dimen exo_styled_progress_bar_height 0x7f0700ae
int dimen exo_styled_progress_dragged_thumb_size 0x7f0700af
int dimen exo_styled_progress_enabled_thumb_size 0x7f0700b0
int dimen exo_styled_progress_layout_height 0x7f0700b1
int dimen exo_styled_progress_margin_bottom 0x7f0700b2
int dimen exo_styled_progress_touch_target_height 0x7f0700b3
int dimen fastscroll_default_thickness 0x7f0700b4
int dimen fastscroll_margin 0x7f0700b5
int dimen fastscroll_minimum_range 0x7f0700b6
int dimen highlight_alpha_material_colored 0x7f0700b7
int dimen highlight_alpha_material_dark 0x7f0700b8
int dimen highlight_alpha_material_light 0x7f0700b9
int dimen hint_alpha_material_dark 0x7f0700ba
int dimen hint_alpha_material_light 0x7f0700bb
int dimen hint_pressed_alpha_material_dark 0x7f0700bc
int dimen hint_pressed_alpha_material_light 0x7f0700bd
int dimen item_touch_helper_max_drag_scroll_per_frame 0x7f0700be
int dimen item_touch_helper_swipe_escape_max_velocity 0x7f0700bf
int dimen item_touch_helper_swipe_escape_velocity 0x7f0700c0
int dimen lb_action_1_line_height 0x7f0700c1
int dimen lb_action_2_lines_height 0x7f0700c2
int dimen lb_action_button_corner_radius 0x7f0700c3
int dimen lb_action_icon_margin 0x7f0700c4
int dimen lb_action_padding_horizontal 0x7f0700c5
int dimen lb_action_text_size 0x7f0700c6
int dimen lb_action_with_icon_padding_end 0x7f0700c7
int dimen lb_action_with_icon_padding_start 0x7f0700c8
int dimen lb_basic_card_content_text_size 0x7f0700c9
int dimen lb_basic_card_info_badge_margin 0x7f0700ca
int dimen lb_basic_card_info_badge_size 0x7f0700cb
int dimen lb_basic_card_info_height 0x7f0700cc
int dimen lb_basic_card_info_height_no_content 0x7f0700cd
int dimen lb_basic_card_info_padding_bottom 0x7f0700ce
int dimen lb_basic_card_info_padding_horizontal 0x7f0700cf
int dimen lb_basic_card_info_padding_top 0x7f0700d0
int dimen lb_basic_card_info_text_margin 0x7f0700d1
int dimen lb_basic_card_main_height 0x7f0700d2
int dimen lb_basic_card_main_width 0x7f0700d3
int dimen lb_basic_card_title_text_size 0x7f0700d4
int dimen lb_browse_expanded_row_no_hovercard_bottom_padding 0x7f0700d5
int dimen lb_browse_expanded_selected_row_top_padding 0x7f0700d6
int dimen lb_browse_header_description_text_size 0x7f0700d7
int dimen lb_browse_header_fading_length 0x7f0700d8
int dimen lb_browse_header_height 0x7f0700d9
int dimen lb_browse_header_padding_end 0x7f0700da
int dimen lb_browse_header_select_duration 0x7f0700db
int dimen lb_browse_header_select_scale 0x7f0700dc
int dimen lb_browse_header_text_size 0x7f0700dd
int dimen lb_browse_headers_vertical_spacing 0x7f0700de
int dimen lb_browse_headers_width 0x7f0700df
int dimen lb_browse_headers_z 0x7f0700e0
int dimen lb_browse_item_horizontal_spacing 0x7f0700e1
int dimen lb_browse_item_vertical_spacing 0x7f0700e2
int dimen lb_browse_padding_bottom 0x7f0700e3
int dimen lb_browse_padding_end 0x7f0700e4
int dimen lb_browse_padding_start 0x7f0700e5
int dimen lb_browse_padding_top 0x7f0700e6
int dimen lb_browse_row_hovercard_description_font_size 0x7f0700e7
int dimen lb_browse_row_hovercard_max_width 0x7f0700e8
int dimen lb_browse_row_hovercard_title_font_size 0x7f0700e9
int dimen lb_browse_rows_fading_edge 0x7f0700ea
int dimen lb_browse_rows_margin_start 0x7f0700eb
int dimen lb_browse_rows_margin_top 0x7f0700ec
int dimen lb_browse_section_header_text_size 0x7f0700ed
int dimen lb_browse_selected_row_top_padding 0x7f0700ee
int dimen lb_browse_title_height 0x7f0700ef
int dimen lb_browse_title_icon_height 0x7f0700f0
int dimen lb_browse_title_icon_max_width 0x7f0700f1
int dimen lb_browse_title_text_size 0x7f0700f2
int dimen lb_control_button_diameter 0x7f0700f3
int dimen lb_control_button_height 0x7f0700f4
int dimen lb_control_button_secondary_diameter 0x7f0700f5
int dimen lb_control_button_secondary_height 0x7f0700f6
int dimen lb_control_button_text_size 0x7f0700f7
int dimen lb_control_icon_height 0x7f0700f8
int dimen lb_control_icon_width 0x7f0700f9
int dimen lb_details_cover_drawable_parallax_movement 0x7f0700fa
int dimen lb_details_description_body_line_spacing 0x7f0700fb
int dimen lb_details_description_body_text_size 0x7f0700fc
int dimen lb_details_description_subtitle_text_size 0x7f0700fd
int dimen lb_details_description_title_baseline 0x7f0700fe
int dimen lb_details_description_title_line_spacing 0x7f0700ff
int dimen lb_details_description_title_padding_adjust_bottom 0x7f070100
int dimen lb_details_description_title_padding_adjust_top 0x7f070101
int dimen lb_details_description_title_resized_text_size 0x7f070102
int dimen lb_details_description_title_text_size 0x7f070103
int dimen lb_details_description_under_subtitle_baseline_margin 0x7f070104
int dimen lb_details_description_under_title_baseline_margin 0x7f070105
int dimen lb_details_overview_action_items_spacing 0x7f070106
int dimen lb_details_overview_action_select_duration 0x7f070107
int dimen lb_details_overview_actions_fade_size 0x7f070108
int dimen lb_details_overview_actions_height 0x7f070109
int dimen lb_details_overview_actions_padding_end 0x7f07010a
int dimen lb_details_overview_actions_padding_start 0x7f07010b
int dimen lb_details_overview_description_margin_bottom 0x7f07010c
int dimen lb_details_overview_description_margin_end 0x7f07010d
int dimen lb_details_overview_description_margin_start 0x7f07010e
int dimen lb_details_overview_description_margin_top 0x7f07010f
int dimen lb_details_overview_height_large 0x7f070110
int dimen lb_details_overview_height_small 0x7f070111
int dimen lb_details_overview_image_margin_horizontal 0x7f070112
int dimen lb_details_overview_image_margin_vertical 0x7f070113
int dimen lb_details_overview_margin_bottom 0x7f070114
int dimen lb_details_overview_margin_end 0x7f070115
int dimen lb_details_overview_margin_start 0x7f070116
int dimen lb_details_overview_z 0x7f070117
int dimen lb_details_rows_align_top 0x7f070118
int dimen lb_details_v2_actions_height 0x7f070119
int dimen lb_details_v2_align_pos_for_actions 0x7f07011a
int dimen lb_details_v2_align_pos_for_description 0x7f07011b
int dimen lb_details_v2_blank_height 0x7f07011c
int dimen lb_details_v2_card_height 0x7f07011d
int dimen lb_details_v2_description_margin_end 0x7f07011e
int dimen lb_details_v2_description_margin_start 0x7f07011f
int dimen lb_details_v2_description_margin_top 0x7f070120
int dimen lb_details_v2_left 0x7f070121
int dimen lb_details_v2_logo_margin_start 0x7f070122
int dimen lb_details_v2_logo_max_height 0x7f070123
int dimen lb_details_v2_logo_max_width 0x7f070124
int dimen lb_error_image_max_height 0x7f070125
int dimen lb_error_message_max_width 0x7f070126
int dimen lb_error_message_text_size 0x7f070127
int dimen lb_error_under_image_baseline_margin 0x7f070128
int dimen lb_error_under_message_baseline_margin 0x7f070129
int dimen lb_guidedactions_elevation 0x7f07012a
int dimen lb_guidedactions_item_bottom_padding 0x7f07012b
int dimen lb_guidedactions_item_checkmark_diameter 0x7f07012c
int dimen lb_guidedactions_item_delimiter_padding 0x7f07012d
int dimen lb_guidedactions_item_description_font_size 0x7f07012e
int dimen lb_guidedactions_item_disabled_chevron_alpha 0x7f07012f
int dimen lb_guidedactions_item_disabled_description_text_alpha 0x7f070130
int dimen lb_guidedactions_item_disabled_text_alpha 0x7f070131
int dimen lb_guidedactions_item_enabled_chevron_alpha 0x7f070132
int dimen lb_guidedactions_item_end_padding 0x7f070133
int dimen lb_guidedactions_item_icon_height 0x7f070134
int dimen lb_guidedactions_item_icon_width 0x7f070135
int dimen lb_guidedactions_item_space_between_title_and_description 0x7f070136
int dimen lb_guidedactions_item_start_padding 0x7f070137
int dimen lb_guidedactions_item_text_width 0x7f070138
int dimen lb_guidedactions_item_text_width_no_icon 0x7f070139
int dimen lb_guidedactions_item_title_font_size 0x7f07013a
int dimen lb_guidedactions_item_top_padding 0x7f07013b
int dimen lb_guidedactions_item_unselected_description_text_alpha 0x7f07013c
int dimen lb_guidedactions_item_unselected_text_alpha 0x7f07013d
int dimen lb_guidedactions_list_padding_end 0x7f07013e
int dimen lb_guidedactions_list_padding_start 0x7f07013f
int dimen lb_guidedactions_list_vertical_spacing 0x7f070140
int dimen lb_guidedactions_section_shadow_width 0x7f070141
int dimen lb_guidedactions_sublist_bottom_margin 0x7f070142
int dimen lb_guidedactions_sublist_padding_bottom 0x7f070143
int dimen lb_guidedactions_sublist_padding_top 0x7f070144
int dimen lb_guidedactions_vertical_padding 0x7f070145
int dimen lb_guidedactions_width_weight 0x7f070146
int dimen lb_guidedactions_width_weight_two_panels 0x7f070147
int dimen lb_guidedbuttonactions_width_weight 0x7f070148
int dimen lb_guidedstep_height_weight 0x7f070149
int dimen lb_guidedstep_height_weight_translucent 0x7f07014a
int dimen lb_guidedstep_keyline 0x7f07014b
int dimen lb_guidedstep_slide_ime_distance 0x7f07014c
int dimen lb_list_row_height 0x7f07014d
int dimen lb_material_shadow_details_z 0x7f07014e
int dimen lb_material_shadow_focused_z 0x7f07014f
int dimen lb_material_shadow_normal_z 0x7f070150
int dimen lb_onboarding_content_margin_bottom 0x7f070151
int dimen lb_onboarding_content_margin_top 0x7f070152
int dimen lb_onboarding_content_width 0x7f070153
int dimen lb_onboarding_header_height 0x7f070154
int dimen lb_onboarding_header_margin_top 0x7f070155
int dimen lb_onboarding_navigation_height 0x7f070156
int dimen lb_onboarding_start_button_height 0x7f070157
int dimen lb_onboarding_start_button_margin_bottom 0x7f070158
int dimen lb_onboarding_start_button_translation_offset 0x7f070159
int dimen lb_page_indicator_arrow_gap 0x7f07015a
int dimen lb_page_indicator_arrow_radius 0x7f07015b
int dimen lb_page_indicator_arrow_shadow_offset 0x7f07015c
int dimen lb_page_indicator_arrow_shadow_radius 0x7f07015d
int dimen lb_page_indicator_dot_gap 0x7f07015e
int dimen lb_page_indicator_dot_radius 0x7f07015f
int dimen lb_playback_controls_card_height 0x7f070160
int dimen lb_playback_controls_child_margin_bigger 0x7f070161
int dimen lb_playback_controls_child_margin_biggest 0x7f070162
int dimen lb_playback_controls_child_margin_default 0x7f070163
int dimen lb_playback_controls_margin_bottom 0x7f070164
int dimen lb_playback_controls_margin_end 0x7f070165
int dimen lb_playback_controls_margin_start 0x7f070166
int dimen lb_playback_controls_padding_bottom 0x7f070167
int dimen lb_playback_controls_time_text_size 0x7f070168
int dimen lb_playback_controls_z 0x7f070169
int dimen lb_playback_current_time_margin_start 0x7f07016a
int dimen lb_playback_description_margin_end 0x7f07016b
int dimen lb_playback_description_margin_start 0x7f07016c
int dimen lb_playback_description_margin_top 0x7f07016d
int dimen lb_playback_major_fade_translate_y 0x7f07016e
int dimen lb_playback_media_item_radio_icon_size 0x7f07016f
int dimen lb_playback_media_radio_width_with_padding 0x7f070170
int dimen lb_playback_media_row_details_selector_width 0x7f070171
int dimen lb_playback_media_row_horizontal_padding 0x7f070172
int dimen lb_playback_media_row_radio_selector_width 0x7f070173
int dimen lb_playback_media_row_selector_round_rect_radius 0x7f070174
int dimen lb_playback_media_row_separator_height 0x7f070175
int dimen lb_playback_minor_fade_translate_y 0x7f070176
int dimen lb_playback_now_playing_bar_height 0x7f070177
int dimen lb_playback_now_playing_bar_left_margin 0x7f070178
int dimen lb_playback_now_playing_bar_margin 0x7f070179
int dimen lb_playback_now_playing_bar_top_margin 0x7f07017a
int dimen lb_playback_now_playing_bar_width 0x7f07017b
int dimen lb_playback_now_playing_view_size 0x7f07017c
int dimen lb_playback_other_rows_center_to_bottom 0x7f07017d
int dimen lb_playback_play_icon_size 0x7f07017e
int dimen lb_playback_time_padding_top 0x7f07017f
int dimen lb_playback_total_time_margin_end 0x7f070180
int dimen lb_playback_transport_control_info_margin_bottom 0x7f070181
int dimen lb_playback_transport_control_row_padding_bottom 0x7f070182
int dimen lb_playback_transport_controlbar_margin_start 0x7f070183
int dimen lb_playback_transport_hero_thumbs_height 0x7f070184
int dimen lb_playback_transport_hero_thumbs_width 0x7f070185
int dimen lb_playback_transport_image_height 0x7f070186
int dimen lb_playback_transport_image_margin_end 0x7f070187
int dimen lb_playback_transport_progressbar_active_bar_height 0x7f070188
int dimen lb_playback_transport_progressbar_active_radius 0x7f070189
int dimen lb_playback_transport_progressbar_bar_height 0x7f07018a
int dimen lb_playback_transport_progressbar_height 0x7f07018b
int dimen lb_playback_transport_thumbs_bottom_margin 0x7f07018c
int dimen lb_playback_transport_thumbs_height 0x7f07018d
int dimen lb_playback_transport_thumbs_margin 0x7f07018e
int dimen lb_playback_transport_thumbs_width 0x7f07018f
int dimen lb_playback_transport_time_margin 0x7f070190
int dimen lb_playback_transport_time_margin_top 0x7f070191
int dimen lb_preference_category_height 0x7f070192
int dimen lb_preference_category_text_size 0x7f070193
int dimen lb_preference_decor_elevation 0x7f070194
int dimen lb_preference_decor_title_container_elevation 0x7f070195
int dimen lb_preference_decor_title_margin_end 0x7f070196
int dimen lb_preference_decor_title_margin_start 0x7f070197
int dimen lb_preference_decor_title_margin_top 0x7f070198
int dimen lb_preference_decor_title_text_height 0x7f070199
int dimen lb_preference_decor_title_text_size 0x7f07019a
int dimen lb_preference_item_icon_margin_end 0x7f07019b
int dimen lb_preference_item_icon_size 0x7f07019c
int dimen lb_preference_item_padding_end 0x7f07019d
int dimen lb_preference_item_padding_start 0x7f07019e
int dimen lb_preference_item_primary_text_margin_bottom 0x7f07019f
int dimen lb_preference_item_primary_text_size 0x7f0701a0
int dimen lb_preference_item_secondary_text_size 0x7f0701a1
int dimen lb_preference_item_text_space_bottom 0x7f0701a2
int dimen lb_preference_item_text_space_top 0x7f0701a3
int dimen lb_preference_seekbar_padding_end 0x7f0701a4
int dimen lb_preference_seekbar_padding_start 0x7f0701a5
int dimen lb_preference_seekbar_value_width 0x7f0701a6
int dimen lb_rounded_rect_corner_radius 0x7f0701a7
int dimen lb_search_bar_edit_text_margin_start 0x7f0701a8
int dimen lb_search_bar_height 0x7f0701a9
int dimen lb_search_bar_hint_margin_start 0x7f0701aa
int dimen lb_search_bar_icon_height 0x7f0701ab
int dimen lb_search_bar_icon_margin_start 0x7f0701ac
int dimen lb_search_bar_icon_width 0x7f0701ad
int dimen lb_search_bar_inner_margin_bottom 0x7f0701ae
int dimen lb_search_bar_inner_margin_top 0x7f0701af
int dimen lb_search_bar_items_height 0x7f0701b0
int dimen lb_search_bar_items_layout_margin_top 0x7f0701b1
int dimen lb_search_bar_items_margin_start 0x7f0701b2
int dimen lb_search_bar_items_width 0x7f0701b3
int dimen lb_search_bar_padding_start 0x7f0701b4
int dimen lb_search_bar_padding_top 0x7f0701b5
int dimen lb_search_bar_speech_orb_margin_start 0x7f0701b6
int dimen lb_search_bar_speech_orb_size 0x7f0701b7
int dimen lb_search_bar_text_size 0x7f0701b8
int dimen lb_search_bar_unfocused_text_size 0x7f0701b9
int dimen lb_search_browse_row_padding_start 0x7f0701ba
int dimen lb_search_browse_rows_align_top 0x7f0701bb
int dimen lb_search_orb_focused_z 0x7f0701bc
int dimen lb_search_orb_margin_bottom 0x7f0701bd
int dimen lb_search_orb_margin_end 0x7f0701be
int dimen lb_search_orb_margin_start 0x7f0701bf
int dimen lb_search_orb_margin_top 0x7f0701c0
int dimen lb_search_orb_size 0x7f0701c1
int dimen lb_search_orb_unfocused_z 0x7f0701c2
int dimen lb_settings_pane_width 0x7f0701c3
int dimen lb_vertical_grid_padding_bottom 0x7f0701c4
int dimen material_bottom_sheet_max_width 0x7f0701c5
int dimen material_clock_display_padding 0x7f0701c6
int dimen material_clock_face_margin_top 0x7f0701c7
int dimen material_clock_hand_center_dot_radius 0x7f0701c8
int dimen material_clock_hand_padding 0x7f0701c9
int dimen material_clock_hand_stroke_width 0x7f0701ca
int dimen material_clock_number_text_size 0x7f0701cb
int dimen material_clock_period_toggle_height 0x7f0701cc
int dimen material_clock_period_toggle_margin_left 0x7f0701cd
int dimen material_clock_period_toggle_width 0x7f0701ce
int dimen material_clock_size 0x7f0701cf
int dimen material_cursor_inset_bottom 0x7f0701d0
int dimen material_cursor_inset_top 0x7f0701d1
int dimen material_cursor_width 0x7f0701d2
int dimen material_emphasis_disabled 0x7f0701d3
int dimen material_emphasis_high_type 0x7f0701d4
int dimen material_emphasis_medium 0x7f0701d5
int dimen material_filled_edittext_font_1_3_padding_bottom 0x7f0701d6
int dimen material_filled_edittext_font_1_3_padding_top 0x7f0701d7
int dimen material_filled_edittext_font_2_0_padding_bottom 0x7f0701d8
int dimen material_filled_edittext_font_2_0_padding_top 0x7f0701d9
int dimen material_font_1_3_box_collapsed_padding_top 0x7f0701da
int dimen material_font_2_0_box_collapsed_padding_top 0x7f0701db
int dimen material_helper_text_default_padding_top 0x7f0701dc
int dimen material_helper_text_font_1_3_padding_horizontal 0x7f0701dd
int dimen material_helper_text_font_1_3_padding_top 0x7f0701de
int dimen material_input_text_to_prefix_suffix_padding 0x7f0701df
int dimen material_text_view_test_line_height 0x7f0701e0
int dimen material_text_view_test_line_height_override 0x7f0701e1
int dimen material_textinput_default_width 0x7f0701e2
int dimen material_textinput_max_width 0x7f0701e3
int dimen material_textinput_min_width 0x7f0701e4
int dimen material_time_picker_minimum_screen_height 0x7f0701e5
int dimen material_time_picker_minimum_screen_width 0x7f0701e6
int dimen material_timepicker_dialog_buttons_margin_top 0x7f0701e7
int dimen mtrl_alert_dialog_background_inset_bottom 0x7f0701e8
int dimen mtrl_alert_dialog_background_inset_end 0x7f0701e9
int dimen mtrl_alert_dialog_background_inset_start 0x7f0701ea
int dimen mtrl_alert_dialog_background_inset_top 0x7f0701eb
int dimen mtrl_alert_dialog_picker_background_inset 0x7f0701ec
int dimen mtrl_badge_horizontal_edge_offset 0x7f0701ed
int dimen mtrl_badge_long_text_horizontal_padding 0x7f0701ee
int dimen mtrl_badge_radius 0x7f0701ef
int dimen mtrl_badge_text_horizontal_edge_offset 0x7f0701f0
int dimen mtrl_badge_text_size 0x7f0701f1
int dimen mtrl_badge_toolbar_action_menu_item_horizontal_offset 0x7f0701f2
int dimen mtrl_badge_toolbar_action_menu_item_vertical_offset 0x7f0701f3
int dimen mtrl_badge_with_text_radius 0x7f0701f4
int dimen mtrl_bottomappbar_fabOffsetEndMode 0x7f0701f5
int dimen mtrl_bottomappbar_fab_bottom_margin 0x7f0701f6
int dimen mtrl_bottomappbar_fab_cradle_margin 0x7f0701f7
int dimen mtrl_bottomappbar_fab_cradle_rounded_corner_radius 0x7f0701f8
int dimen mtrl_bottomappbar_fab_cradle_vertical_offset 0x7f0701f9
int dimen mtrl_bottomappbar_height 0x7f0701fa
int dimen mtrl_btn_corner_radius 0x7f0701fb
int dimen mtrl_btn_dialog_btn_min_width 0x7f0701fc
int dimen mtrl_btn_disabled_elevation 0x7f0701fd
int dimen mtrl_btn_disabled_z 0x7f0701fe
int dimen mtrl_btn_elevation 0x7f0701ff
int dimen mtrl_btn_focused_z 0x7f070200
int dimen mtrl_btn_hovered_z 0x7f070201
int dimen mtrl_btn_icon_btn_padding_left 0x7f070202
int dimen mtrl_btn_icon_padding 0x7f070203
int dimen mtrl_btn_inset 0x7f070204
int dimen mtrl_btn_letter_spacing 0x7f070205
int dimen mtrl_btn_max_width 0x7f070206
int dimen mtrl_btn_padding_bottom 0x7f070207
int dimen mtrl_btn_padding_left 0x7f070208
int dimen mtrl_btn_padding_right 0x7f070209
int dimen mtrl_btn_padding_top 0x7f07020a
int dimen mtrl_btn_pressed_z 0x7f07020b
int dimen mtrl_btn_snackbar_margin_horizontal 0x7f07020c
int dimen mtrl_btn_stroke_size 0x7f07020d
int dimen mtrl_btn_text_btn_icon_padding 0x7f07020e
int dimen mtrl_btn_text_btn_padding_left 0x7f07020f
int dimen mtrl_btn_text_btn_padding_right 0x7f070210
int dimen mtrl_btn_text_size 0x7f070211
int dimen mtrl_btn_z 0x7f070212
int dimen mtrl_calendar_action_confirm_button_min_width 0x7f070213
int dimen mtrl_calendar_action_height 0x7f070214
int dimen mtrl_calendar_action_padding 0x7f070215
int dimen mtrl_calendar_bottom_padding 0x7f070216
int dimen mtrl_calendar_content_padding 0x7f070217
int dimen mtrl_calendar_day_corner 0x7f070218
int dimen mtrl_calendar_day_height 0x7f070219
int dimen mtrl_calendar_day_horizontal_padding 0x7f07021a
int dimen mtrl_calendar_day_today_stroke 0x7f07021b
int dimen mtrl_calendar_day_vertical_padding 0x7f07021c
int dimen mtrl_calendar_day_width 0x7f07021d
int dimen mtrl_calendar_days_of_week_height 0x7f07021e
int dimen mtrl_calendar_dialog_background_inset 0x7f07021f
int dimen mtrl_calendar_header_content_padding 0x7f070220
int dimen mtrl_calendar_header_content_padding_fullscreen 0x7f070221
int dimen mtrl_calendar_header_divider_thickness 0x7f070222
int dimen mtrl_calendar_header_height 0x7f070223
int dimen mtrl_calendar_header_height_fullscreen 0x7f070224
int dimen mtrl_calendar_header_selection_line_height 0x7f070225
int dimen mtrl_calendar_header_text_padding 0x7f070226
int dimen mtrl_calendar_header_toggle_margin_bottom 0x7f070227
int dimen mtrl_calendar_header_toggle_margin_top 0x7f070228
int dimen mtrl_calendar_landscape_header_width 0x7f070229
int dimen mtrl_calendar_maximum_default_fullscreen_minor_axis 0x7f07022a
int dimen mtrl_calendar_month_horizontal_padding 0x7f07022b
int dimen mtrl_calendar_month_vertical_padding 0x7f07022c
int dimen mtrl_calendar_navigation_bottom_padding 0x7f07022d
int dimen mtrl_calendar_navigation_height 0x7f07022e
int dimen mtrl_calendar_navigation_top_padding 0x7f07022f
int dimen mtrl_calendar_pre_l_text_clip_padding 0x7f070230
int dimen mtrl_calendar_selection_baseline_to_top_fullscreen 0x7f070231
int dimen mtrl_calendar_selection_text_baseline_to_bottom 0x7f070232
int dimen mtrl_calendar_selection_text_baseline_to_bottom_fullscreen 0x7f070233
int dimen mtrl_calendar_selection_text_baseline_to_top 0x7f070234
int dimen mtrl_calendar_text_input_padding_top 0x7f070235
int dimen mtrl_calendar_title_baseline_to_top 0x7f070236
int dimen mtrl_calendar_title_baseline_to_top_fullscreen 0x7f070237
int dimen mtrl_calendar_year_corner 0x7f070238
int dimen mtrl_calendar_year_height 0x7f070239
int dimen mtrl_calendar_year_horizontal_padding 0x7f07023a
int dimen mtrl_calendar_year_vertical_padding 0x7f07023b
int dimen mtrl_calendar_year_width 0x7f07023c
int dimen mtrl_card_checked_icon_margin 0x7f07023d
int dimen mtrl_card_checked_icon_size 0x7f07023e
int dimen mtrl_card_corner_radius 0x7f07023f
int dimen mtrl_card_dragged_z 0x7f070240
int dimen mtrl_card_elevation 0x7f070241
int dimen mtrl_card_spacing 0x7f070242
int dimen mtrl_chip_pressed_translation_z 0x7f070243
int dimen mtrl_chip_text_size 0x7f070244
int dimen mtrl_edittext_rectangle_top_offset 0x7f070245
int dimen mtrl_exposed_dropdown_menu_popup_elevation 0x7f070246
int dimen mtrl_exposed_dropdown_menu_popup_vertical_offset 0x7f070247
int dimen mtrl_exposed_dropdown_menu_popup_vertical_padding 0x7f070248
int dimen mtrl_extended_fab_bottom_padding 0x7f070249
int dimen mtrl_extended_fab_corner_radius 0x7f07024a
int dimen mtrl_extended_fab_disabled_elevation 0x7f07024b
int dimen mtrl_extended_fab_disabled_translation_z 0x7f07024c
int dimen mtrl_extended_fab_elevation 0x7f07024d
int dimen mtrl_extended_fab_end_padding 0x7f07024e
int dimen mtrl_extended_fab_end_padding_icon 0x7f07024f
int dimen mtrl_extended_fab_icon_size 0x7f070250
int dimen mtrl_extended_fab_icon_text_spacing 0x7f070251
int dimen mtrl_extended_fab_min_height 0x7f070252
int dimen mtrl_extended_fab_min_width 0x7f070253
int dimen mtrl_extended_fab_start_padding 0x7f070254
int dimen mtrl_extended_fab_start_padding_icon 0x7f070255
int dimen mtrl_extended_fab_top_padding 0x7f070256
int dimen mtrl_extended_fab_translation_z_base 0x7f070257
int dimen mtrl_extended_fab_translation_z_hovered_focused 0x7f070258
int dimen mtrl_extended_fab_translation_z_pressed 0x7f070259
int dimen mtrl_fab_elevation 0x7f07025a
int dimen mtrl_fab_min_touch_target 0x7f07025b
int dimen mtrl_fab_translation_z_hovered_focused 0x7f07025c
int dimen mtrl_fab_translation_z_pressed 0x7f07025d
int dimen mtrl_high_ripple_default_alpha 0x7f07025e
int dimen mtrl_high_ripple_focused_alpha 0x7f07025f
int dimen mtrl_high_ripple_hovered_alpha 0x7f070260
int dimen mtrl_high_ripple_pressed_alpha 0x7f070261
int dimen mtrl_large_touch_target 0x7f070262
int dimen mtrl_low_ripple_default_alpha 0x7f070263
int dimen mtrl_low_ripple_focused_alpha 0x7f070264
int dimen mtrl_low_ripple_hovered_alpha 0x7f070265
int dimen mtrl_low_ripple_pressed_alpha 0x7f070266
int dimen mtrl_min_touch_target_size 0x7f070267
int dimen mtrl_navigation_bar_item_default_icon_size 0x7f070268
int dimen mtrl_navigation_bar_item_default_margin 0x7f070269
int dimen mtrl_navigation_elevation 0x7f07026a
int dimen mtrl_navigation_item_horizontal_padding 0x7f07026b
int dimen mtrl_navigation_item_icon_padding 0x7f07026c
int dimen mtrl_navigation_item_icon_size 0x7f07026d
int dimen mtrl_navigation_item_shape_horizontal_margin 0x7f07026e
int dimen mtrl_navigation_item_shape_vertical_margin 0x7f07026f
int dimen mtrl_navigation_rail_active_text_size 0x7f070270
int dimen mtrl_navigation_rail_compact_width 0x7f070271
int dimen mtrl_navigation_rail_default_width 0x7f070272
int dimen mtrl_navigation_rail_elevation 0x7f070273
int dimen mtrl_navigation_rail_icon_margin 0x7f070274
int dimen mtrl_navigation_rail_icon_size 0x7f070275
int dimen mtrl_navigation_rail_margin 0x7f070276
int dimen mtrl_navigation_rail_text_bottom_margin 0x7f070277
int dimen mtrl_navigation_rail_text_size 0x7f070278
int dimen mtrl_progress_circular_inset 0x7f070279
int dimen mtrl_progress_circular_inset_extra_small 0x7f07027a
int dimen mtrl_progress_circular_inset_medium 0x7f07027b
int dimen mtrl_progress_circular_inset_small 0x7f07027c
int dimen mtrl_progress_circular_radius 0x7f07027d
int dimen mtrl_progress_circular_size 0x7f07027e
int dimen mtrl_progress_circular_size_extra_small 0x7f07027f
int dimen mtrl_progress_circular_size_medium 0x7f070280
int dimen mtrl_progress_circular_size_small 0x7f070281
int dimen mtrl_progress_circular_track_thickness_extra_small 0x7f070282
int dimen mtrl_progress_circular_track_thickness_medium 0x7f070283
int dimen mtrl_progress_circular_track_thickness_small 0x7f070284
int dimen mtrl_progress_indicator_full_rounded_corner_radius 0x7f070285
int dimen mtrl_progress_track_thickness 0x7f070286
int dimen mtrl_shape_corner_size_large_component 0x7f070287
int dimen mtrl_shape_corner_size_medium_component 0x7f070288
int dimen mtrl_shape_corner_size_small_component 0x7f070289
int dimen mtrl_slider_halo_radius 0x7f07028a
int dimen mtrl_slider_label_padding 0x7f07028b
int dimen mtrl_slider_label_radius 0x7f07028c
int dimen mtrl_slider_label_square_side 0x7f07028d
int dimen mtrl_slider_thumb_elevation 0x7f07028e
int dimen mtrl_slider_thumb_radius 0x7f07028f
int dimen mtrl_slider_track_height 0x7f070290
int dimen mtrl_slider_track_side_padding 0x7f070291
int dimen mtrl_slider_track_top 0x7f070292
int dimen mtrl_slider_widget_height 0x7f070293
int dimen mtrl_snackbar_action_text_color_alpha 0x7f070294
int dimen mtrl_snackbar_background_corner_radius 0x7f070295
int dimen mtrl_snackbar_background_overlay_color_alpha 0x7f070296
int dimen mtrl_snackbar_margin 0x7f070297
int dimen mtrl_snackbar_message_margin_horizontal 0x7f070298
int dimen mtrl_snackbar_padding_horizontal 0x7f070299
int dimen mtrl_switch_thumb_elevation 0x7f07029a
int dimen mtrl_textinput_box_corner_radius_medium 0x7f07029b
int dimen mtrl_textinput_box_corner_radius_small 0x7f07029c
int dimen mtrl_textinput_box_label_cutout_padding 0x7f07029d
int dimen mtrl_textinput_box_stroke_width_default 0x7f07029e
int dimen mtrl_textinput_box_stroke_width_focused 0x7f07029f
int dimen mtrl_textinput_counter_margin_start 0x7f0702a0
int dimen mtrl_textinput_end_icon_margin_start 0x7f0702a1
int dimen mtrl_textinput_outline_box_expanded_padding 0x7f0702a2
int dimen mtrl_textinput_start_icon_margin_end 0x7f0702a3
int dimen mtrl_toolbar_default_height 0x7f0702a4
int dimen mtrl_tooltip_arrowSize 0x7f0702a5
int dimen mtrl_tooltip_cornerSize 0x7f0702a6
int dimen mtrl_tooltip_minHeight 0x7f0702a7
int dimen mtrl_tooltip_minWidth 0x7f0702a8
int dimen mtrl_tooltip_padding 0x7f0702a9
int dimen mtrl_transition_shared_axis_slide_distance 0x7f0702aa
int dimen notification_action_icon_size 0x7f0702ab
int dimen notification_action_text_size 0x7f0702ac
int dimen notification_big_circle_margin 0x7f0702ad
int dimen notification_content_margin_start 0x7f0702ae
int dimen notification_large_icon_height 0x7f0702af
int dimen notification_large_icon_width 0x7f0702b0
int dimen notification_main_column_padding_top 0x7f0702b1
int dimen notification_media_narrow_margin 0x7f0702b2
int dimen notification_right_icon_size 0x7f0702b3
int dimen notification_right_side_padding_top 0x7f0702b4
int dimen notification_small_icon_background_padding 0x7f0702b5
int dimen notification_small_icon_size_as_large 0x7f0702b6
int dimen notification_subtext_size 0x7f0702b7
int dimen notification_top_pad 0x7f0702b8
int dimen notification_top_pad_large_text 0x7f0702b9
int dimen picker_column_horizontal_padding 0x7f0702ba
int dimen picker_item_height 0x7f0702bb
int dimen picker_item_spacing 0x7f0702bc
int dimen picker_separator_horizontal_padding 0x7f0702bd
int dimen preference_dropdown_padding_start 0x7f0702be
int dimen preference_icon_minWidth 0x7f0702bf
int dimen preference_seekbar_padding_horizontal 0x7f0702c0
int dimen preference_seekbar_padding_vertical 0x7f0702c1
int dimen preference_seekbar_value_minWidth 0x7f0702c2
int dimen preferences_detail_width 0x7f0702c3
int dimen preferences_header_width 0x7f0702c4
int dimen sliding_pane_detail_pane_width 0x7f0702c5
int dimen test_mtrl_calendar_day_cornerSize 0x7f0702c6
int dimen test_navigation_bar_active_item_max_width 0x7f0702c7
int dimen test_navigation_bar_active_item_min_width 0x7f0702c8
int dimen test_navigation_bar_active_text_size 0x7f0702c9
int dimen test_navigation_bar_elevation 0x7f0702ca
int dimen test_navigation_bar_height 0x7f0702cb
int dimen test_navigation_bar_icon_size 0x7f0702cc
int dimen test_navigation_bar_item_max_width 0x7f0702cd
int dimen test_navigation_bar_item_min_width 0x7f0702ce
int dimen test_navigation_bar_label_padding 0x7f0702cf
int dimen test_navigation_bar_shadow_height 0x7f0702d0
int dimen test_navigation_bar_text_size 0x7f0702d1
int dimen tooltip_corner_radius 0x7f0702d2
int dimen tooltip_horizontal_padding 0x7f0702d3
int dimen tooltip_margin 0x7f0702d4
int dimen tooltip_precise_anchor_extra_offset 0x7f0702d5
int dimen tooltip_precise_anchor_threshold 0x7f0702d6
int dimen tooltip_vertical_padding 0x7f0702d7
int dimen tooltip_y_offset_non_touch 0x7f0702d8
int dimen tooltip_y_offset_touch 0x7f0702d9
int drawable abc_ab_share_pack_mtrl_alpha 0x7f080007
int drawable abc_action_bar_item_background_material 0x7f080008
int drawable abc_btn_borderless_material 0x7f080009
int drawable abc_btn_check_material 0x7f08000a
int drawable abc_btn_check_material_anim 0x7f08000b
int drawable abc_btn_check_to_on_mtrl_000 0x7f08000c
int drawable abc_btn_check_to_on_mtrl_015 0x7f08000d
int drawable abc_btn_colored_material 0x7f08000e
int drawable abc_btn_default_mtrl_shape 0x7f08000f
int drawable abc_btn_radio_material 0x7f080010
int drawable abc_btn_radio_material_anim 0x7f080011
int drawable abc_btn_radio_to_on_mtrl_000 0x7f080012
int drawable abc_btn_radio_to_on_mtrl_015 0x7f080013
int drawable abc_btn_switch_to_on_mtrl_00001 0x7f080014
int drawable abc_btn_switch_to_on_mtrl_00012 0x7f080015
int drawable abc_cab_background_internal_bg 0x7f080016
int drawable abc_cab_background_top_material 0x7f080017
int drawable abc_cab_background_top_mtrl_alpha 0x7f080018
int drawable abc_control_background_material 0x7f080019
int drawable abc_dialog_material_background 0x7f08001a
int drawable abc_edit_text_material 0x7f08001b
int drawable abc_ic_ab_back_material 0x7f08001c
int drawable abc_ic_arrow_drop_right_black_24dp 0x7f08001d
int drawable abc_ic_clear_material 0x7f08001e
int drawable abc_ic_commit_search_api_mtrl_alpha 0x7f08001f
int drawable abc_ic_go_search_api_material 0x7f080020
int drawable abc_ic_menu_copy_mtrl_am_alpha 0x7f080021
int drawable abc_ic_menu_cut_mtrl_alpha 0x7f080022
int drawable abc_ic_menu_overflow_material 0x7f080023
int drawable abc_ic_menu_paste_mtrl_am_alpha 0x7f080024
int drawable abc_ic_menu_selectall_mtrl_alpha 0x7f080025
int drawable abc_ic_menu_share_mtrl_alpha 0x7f080026
int drawable abc_ic_search_api_material 0x7f080027
int drawable abc_ic_voice_search_api_material 0x7f080028
int drawable abc_item_background_holo_dark 0x7f080029
int drawable abc_item_background_holo_light 0x7f08002a
int drawable abc_list_divider_material 0x7f08002b
int drawable abc_list_divider_mtrl_alpha 0x7f08002c
int drawable abc_list_focused_holo 0x7f08002d
int drawable abc_list_longpressed_holo 0x7f08002e
int drawable abc_list_pressed_holo_dark 0x7f08002f
int drawable abc_list_pressed_holo_light 0x7f080030
int drawable abc_list_selector_background_transition_holo_dark 0x7f080031
int drawable abc_list_selector_background_transition_holo_light 0x7f080032
int drawable abc_list_selector_disabled_holo_dark 0x7f080033
int drawable abc_list_selector_disabled_holo_light 0x7f080034
int drawable abc_list_selector_holo_dark 0x7f080035
int drawable abc_list_selector_holo_light 0x7f080036
int drawable abc_menu_hardkey_panel_mtrl_mult 0x7f080037
int drawable abc_popup_background_mtrl_mult 0x7f080038
int drawable abc_ratingbar_indicator_material 0x7f080039
int drawable abc_ratingbar_material 0x7f08003a
int drawable abc_ratingbar_small_material 0x7f08003b
int drawable abc_scrubber_control_off_mtrl_alpha 0x7f08003c
int drawable abc_scrubber_control_to_pressed_mtrl_000 0x7f08003d
int drawable abc_scrubber_control_to_pressed_mtrl_005 0x7f08003e
int drawable abc_scrubber_primary_mtrl_alpha 0x7f08003f
int drawable abc_scrubber_track_mtrl_alpha 0x7f080040
int drawable abc_seekbar_thumb_material 0x7f080041
int drawable abc_seekbar_tick_mark_material 0x7f080042
int drawable abc_seekbar_track_material 0x7f080043
int drawable abc_spinner_mtrl_am_alpha 0x7f080044
int drawable abc_spinner_textfield_background_material 0x7f080045
int drawable abc_star_black_48dp 0x7f080046
int drawable abc_star_half_black_48dp 0x7f080047
int drawable abc_switch_thumb_material 0x7f080048
int drawable abc_switch_track_mtrl_alpha 0x7f080049
int drawable abc_tab_indicator_material 0x7f08004a
int drawable abc_tab_indicator_mtrl_alpha 0x7f08004b
int drawable abc_text_cursor_material 0x7f08004c
int drawable abc_text_select_handle_left_mtrl 0x7f08004d
int drawable abc_text_select_handle_middle_mtrl 0x7f08004e
int drawable abc_text_select_handle_right_mtrl 0x7f08004f
int drawable abc_textfield_activated_mtrl_alpha 0x7f080050
int drawable abc_textfield_default_mtrl_alpha 0x7f080051
int drawable abc_textfield_search_activated_mtrl_alpha 0x7f080052
int drawable abc_textfield_search_default_mtrl_alpha 0x7f080053
int drawable abc_textfield_search_material 0x7f080054
int drawable abc_vector_test 0x7f080055
int drawable avd_hide_password 0x7f080056
int drawable avd_show_password 0x7f080057
int drawable btn_checkbox_checked_mtrl 0x7f080058
int drawable btn_checkbox_checked_to_unchecked_mtrl_animation 0x7f080059
int drawable btn_checkbox_unchecked_mtrl 0x7f08005a
int drawable btn_checkbox_unchecked_to_checked_mtrl_animation 0x7f08005b
int drawable btn_radio_off_mtrl 0x7f08005c
int drawable btn_radio_off_to_on_mtrl_animation 0x7f08005d
int drawable btn_radio_on_mtrl 0x7f08005e
int drawable btn_radio_on_to_off_mtrl_animation 0x7f08005f
int drawable common_full_open_on_phone 0x7f080060
int drawable common_google_signin_btn_icon_dark 0x7f080061
int drawable common_google_signin_btn_icon_dark_focused 0x7f080062
int drawable common_google_signin_btn_icon_dark_normal 0x7f080063
int drawable common_google_signin_btn_icon_dark_normal_background 0x7f080064
int drawable common_google_signin_btn_icon_disabled 0x7f080065
int drawable common_google_signin_btn_icon_light 0x7f080066
int drawable common_google_signin_btn_icon_light_focused 0x7f080067
int drawable common_google_signin_btn_icon_light_normal 0x7f080068
int drawable common_google_signin_btn_icon_light_normal_background 0x7f080069
int drawable common_google_signin_btn_text_dark 0x7f08006a
int drawable common_google_signin_btn_text_dark_focused 0x7f08006b
int drawable common_google_signin_btn_text_dark_normal 0x7f08006c
int drawable common_google_signin_btn_text_dark_normal_background 0x7f08006d
int drawable common_google_signin_btn_text_disabled 0x7f08006e
int drawable common_google_signin_btn_text_light 0x7f08006f
int drawable common_google_signin_btn_text_light_focused 0x7f080070
int drawable common_google_signin_btn_text_light_normal 0x7f080071
int drawable common_google_signin_btn_text_light_normal_background 0x7f080072
int drawable design_fab_background 0x7f080073
int drawable design_ic_visibility 0x7f080074
int drawable design_ic_visibility_off 0x7f080075
int drawable design_password_eye 0x7f080076
int drawable design_snackbar_background 0x7f080077
int drawable exo_edit_mode_logo 0x7f080078
int drawable exo_ic_audiotrack 0x7f080079
int drawable exo_ic_check 0x7f08007a
int drawable exo_ic_chevron_left 0x7f08007b
int drawable exo_ic_chevron_right 0x7f08007c
int drawable exo_ic_default_album_image 0x7f08007d
int drawable exo_ic_forward 0x7f08007e
int drawable exo_ic_fullscreen_enter 0x7f08007f
int drawable exo_ic_fullscreen_exit 0x7f080080
int drawable exo_ic_pause_circle_filled 0x7f080081
int drawable exo_ic_play_circle_filled 0x7f080082
int drawable exo_ic_rewind 0x7f080083
int drawable exo_ic_settings 0x7f080084
int drawable exo_ic_skip_next 0x7f080085
int drawable exo_ic_skip_previous 0x7f080086
int drawable exo_ic_speed 0x7f080087
int drawable exo_ic_subtitle_off 0x7f080088
int drawable exo_ic_subtitle_on 0x7f080089
int drawable exo_icon_circular_play 0x7f08008a
int drawable exo_icon_fastforward 0x7f08008b
int drawable exo_icon_fullscreen_enter 0x7f08008c
int drawable exo_icon_fullscreen_exit 0x7f08008d
int drawable exo_icon_next 0x7f08008e
int drawable exo_icon_pause 0x7f08008f
int drawable exo_icon_play 0x7f080090
int drawable exo_icon_previous 0x7f080091
int drawable exo_icon_repeat_all 0x7f080092
int drawable exo_icon_repeat_off 0x7f080093
int drawable exo_icon_repeat_one 0x7f080094
int drawable exo_icon_rewind 0x7f080095
int drawable exo_icon_shuffle_off 0x7f080096
int drawable exo_icon_shuffle_on 0x7f080097
int drawable exo_icon_stop 0x7f080098
int drawable exo_icon_vr 0x7f080099
int drawable exo_legacy_controls_fastforward 0x7f08009a
int drawable exo_legacy_controls_fullscreen_enter 0x7f08009b
int drawable exo_legacy_controls_fullscreen_exit 0x7f08009c
int drawable exo_legacy_controls_next 0x7f08009d
int drawable exo_legacy_controls_pause 0x7f08009e
int drawable exo_legacy_controls_play 0x7f08009f
int drawable exo_legacy_controls_previous 0x7f0800a0
int drawable exo_legacy_controls_repeat_all 0x7f0800a1
int drawable exo_legacy_controls_repeat_off 0x7f0800a2
int drawable exo_legacy_controls_repeat_one 0x7f0800a3
int drawable exo_legacy_controls_rewind 0x7f0800a4
int drawable exo_legacy_controls_shuffle_off 0x7f0800a5
int drawable exo_legacy_controls_shuffle_on 0x7f0800a6
int drawable exo_legacy_controls_vr 0x7f0800a7
int drawable exo_notification_fastforward 0x7f0800a8
int drawable exo_notification_next 0x7f0800a9
int drawable exo_notification_pause 0x7f0800aa
int drawable exo_notification_play 0x7f0800ab
int drawable exo_notification_previous 0x7f0800ac
int drawable exo_notification_rewind 0x7f0800ad
int drawable exo_notification_small_icon 0x7f0800ae
int drawable exo_notification_stop 0x7f0800af
int drawable exo_rounded_rectangle 0x7f0800b0
int drawable exo_styled_controls_audiotrack 0x7f0800b1
int drawable exo_styled_controls_check 0x7f0800b2
int drawable exo_styled_controls_fastforward 0x7f0800b3
int drawable exo_styled_controls_fullscreen_enter 0x7f0800b4
int drawable exo_styled_controls_fullscreen_exit 0x7f0800b5
int drawable exo_styled_controls_next 0x7f0800b6
int drawable exo_styled_controls_overflow_hide 0x7f0800b7
int drawable exo_styled_controls_overflow_show 0x7f0800b8
int drawable exo_styled_controls_pause 0x7f0800b9
int drawable exo_styled_controls_play 0x7f0800ba
int drawable exo_styled_controls_previous 0x7f0800bb
int drawable exo_styled_controls_repeat_all 0x7f0800bc
int drawable exo_styled_controls_repeat_off 0x7f0800bd
int drawable exo_styled_controls_repeat_one 0x7f0800be
int drawable exo_styled_controls_rewind 0x7f0800bf
int drawable exo_styled_controls_settings 0x7f0800c0
int drawable exo_styled_controls_shuffle_off 0x7f0800c1
int drawable exo_styled_controls_shuffle_on 0x7f0800c2
int drawable exo_styled_controls_speed 0x7f0800c3
int drawable exo_styled_controls_subtitle_off 0x7f0800c4
int drawable exo_styled_controls_subtitle_on 0x7f0800c5
int drawable exo_styled_controls_vr 0x7f0800c6
int drawable googleg_disabled_color_18 0x7f0800c7
int drawable googleg_standard_color_18 0x7f0800c8
int drawable ic_arrow_down_24dp 0x7f0800c9
int drawable ic_call_answer 0x7f0800ca
int drawable ic_call_answer_low 0x7f0800cb
int drawable ic_call_answer_video 0x7f0800cc
int drawable ic_call_answer_video_low 0x7f0800cd
int drawable ic_call_decline 0x7f0800ce
int drawable ic_call_decline_low 0x7f0800cf
int drawable ic_clock_black_24dp 0x7f0800d0
int drawable ic_download 0x7f0800d1
int drawable ic_download_24 0x7f0800d2
int drawable ic_download_done 0x7f0800d3
int drawable ic_error 0x7f0800d4
int drawable ic_keyboard_black_24dp 0x7f0800d5
int drawable ic_launcher_background 0x7f0800d6
int drawable ic_launcher_foreground 0x7f0800d7
int drawable ic_mtrl_checked_circle 0x7f0800d8
int drawable ic_mtrl_chip_checked_black 0x7f0800d9
int drawable ic_mtrl_chip_checked_circle 0x7f0800da
int drawable ic_mtrl_chip_close_circle 0x7f0800db
int drawable ic_notification 0x7f0800dc
int drawable lb_action_bg 0x7f0800dd
int drawable lb_action_bg_focused 0x7f0800de
int drawable lb_background 0x7f0800df
int drawable lb_card_foreground 0x7f0800e0
int drawable lb_card_shadow_focused 0x7f0800e1
int drawable lb_card_shadow_normal 0x7f0800e2
int drawable lb_control_button_primary 0x7f0800e3
int drawable lb_control_button_secondary 0x7f0800e4
int drawable lb_headers_right_fading 0x7f0800e5
int drawable lb_ic_actions_right_arrow 0x7f0800e6
int drawable lb_ic_cc 0x7f0800e7
int drawable lb_ic_fast_forward 0x7f0800e8
int drawable lb_ic_fast_rewind 0x7f0800e9
int drawable lb_ic_guidedactions_item_chevron 0x7f0800ea
int drawable lb_ic_hq 0x7f0800eb
int drawable lb_ic_in_app_search 0x7f0800ec
int drawable lb_ic_loop 0x7f0800ed
int drawable lb_ic_loop_one 0x7f0800ee
int drawable lb_ic_more 0x7f0800ef
int drawable lb_ic_nav_arrow 0x7f0800f0
int drawable lb_ic_pause 0x7f0800f1
int drawable lb_ic_pip 0x7f0800f2
int drawable lb_ic_play 0x7f0800f3
int drawable lb_ic_play_fit 0x7f0800f4
int drawable lb_ic_playback_loop 0x7f0800f5
int drawable lb_ic_replay 0x7f0800f6
int drawable lb_ic_sad_cloud 0x7f0800f7
int drawable lb_ic_search_mic 0x7f0800f8
int drawable lb_ic_search_mic_out 0x7f0800f9
int drawable lb_ic_shuffle 0x7f0800fa
int drawable lb_ic_skip_next 0x7f0800fb
int drawable lb_ic_skip_previous 0x7f0800fc
int drawable lb_ic_stop 0x7f0800fd
int drawable lb_ic_thumb_down 0x7f0800fe
int drawable lb_ic_thumb_down_outline 0x7f0800ff
int drawable lb_ic_thumb_up 0x7f080100
int drawable lb_ic_thumb_up_outline 0x7f080101
int drawable lb_in_app_search_bg 0x7f080102
int drawable lb_in_app_search_shadow_focused 0x7f080103
int drawable lb_in_app_search_shadow_normal 0x7f080104
int drawable lb_onboarding_start_button_background 0x7f080105
int drawable lb_playback_now_playing_bar 0x7f080106
int drawable lb_playback_progress_bar 0x7f080107
int drawable lb_search_orb 0x7f080108
int drawable lb_selectable_item_rounded_rect 0x7f080109
int drawable lb_speech_orb 0x7f08010a
int drawable lb_text_dot_one 0x7f08010b
int drawable lb_text_dot_one_small 0x7f08010c
int drawable lb_text_dot_two 0x7f08010d
int drawable lb_text_dot_two_small 0x7f08010e
int drawable material_cursor_drawable 0x7f08010f
int drawable material_ic_calendar_black_24dp 0x7f080110
int drawable material_ic_clear_black_24dp 0x7f080111
int drawable material_ic_edit_black_24dp 0x7f080112
int drawable material_ic_keyboard_arrow_left_black_24dp 0x7f080113
int drawable material_ic_keyboard_arrow_next_black_24dp 0x7f080114
int drawable material_ic_keyboard_arrow_previous_black_24dp 0x7f080115
int drawable material_ic_keyboard_arrow_right_black_24dp 0x7f080116
int drawable material_ic_menu_arrow_down_black_24dp 0x7f080117
int drawable material_ic_menu_arrow_up_black_24dp 0x7f080118
int drawable media3_icon_circular_play 0x7f080119
int drawable media3_notification_pause 0x7f08011a
int drawable media3_notification_play 0x7f08011b
int drawable media3_notification_seek_back 0x7f08011c
int drawable media3_notification_seek_forward 0x7f08011d
int drawable media3_notification_seek_to_next 0x7f08011e
int drawable media3_notification_seek_to_previous 0x7f08011f
int drawable media3_notification_small_icon 0x7f080120
int drawable media_session_service_notification_ic_music_note 0x7f080121
int drawable mtrl_dialog_background 0x7f080122
int drawable mtrl_dropdown_arrow 0x7f080123
int drawable mtrl_ic_arrow_drop_down 0x7f080124
int drawable mtrl_ic_arrow_drop_up 0x7f080125
int drawable mtrl_ic_cancel 0x7f080126
int drawable mtrl_ic_error 0x7f080127
int drawable mtrl_navigation_bar_item_background 0x7f080128
int drawable mtrl_popupmenu_background 0x7f080129
int drawable mtrl_popupmenu_background_dark 0x7f08012a
int drawable mtrl_tabs_default_indicator 0x7f08012b
int drawable navigation_empty_icon 0x7f08012c
int drawable notification_action_background 0x7f08012d
int drawable notification_bg 0x7f08012e
int drawable notification_bg_low 0x7f08012f
int drawable notification_bg_low_normal 0x7f080130
int drawable notification_bg_low_pressed 0x7f080131
int drawable notification_bg_normal 0x7f080132
int drawable notification_bg_normal_pressed 0x7f080133
int drawable notification_icon_background 0x7f080134
int drawable notification_oversize_large_icon_bg 0x7f080135
int drawable notification_template_icon_bg 0x7f080136
int drawable notification_template_icon_low_bg 0x7f080137
int drawable notification_tile_bg 0x7f080138
int drawable notify_panel_notification_icon_bg 0x7f080139
int drawable preference_list_divider_material 0x7f08013a
int drawable test_custom_background 0x7f08013b
int drawable test_level_drawable 0x7f08013c
int drawable tooltip_frame_dark 0x7f08013d
int drawable tooltip_frame_light 0x7f08013e
int drawable tv_banner 0x7f08013f
int font roboto_medium_numbers 0x7f090000
int fraction lb_browse_header_unselect_alpha 0x7f0a0000
int fraction lb_browse_rows_scale 0x7f0a0001
int fraction lb_focus_zoom_factor_large 0x7f0a0002
int fraction lb_focus_zoom_factor_medium 0x7f0a0003
int fraction lb_focus_zoom_factor_small 0x7f0a0004
int fraction lb_focus_zoom_factor_xsmall 0x7f0a0005
int fraction lb_search_bar_speech_orb_max_level_zoom 0x7f0a0006
int fraction lb_search_orb_focused_zoom 0x7f0a0007
int fraction lb_view_active_level 0x7f0a0008
int fraction lb_view_dimmed_level 0x7f0a0009
int id ALT 0x7f0b0000
int id BOTTOM_END 0x7f0b0001
int id BOTTOM_START 0x7f0b0002
int id CTRL 0x7f0b0003
int id Content 0x7f0b0004
int id FUNCTION 0x7f0b0005
int id IconOnLeft 0x7f0b0006
int id IconOnRight 0x7f0b0007
int id ImageOnly 0x7f0b0008
int id META 0x7f0b0009
int id NO_DEBUG 0x7f0b000a
int id SHIFT 0x7f0b000b
int id SHOW_ALL 0x7f0b000c
int id SHOW_PATH 0x7f0b000d
int id SHOW_PROGRESS 0x7f0b000e
int id SYM 0x7f0b000f
int id TOP_END 0x7f0b0010
int id TOP_START 0x7f0b0011
int id Title 0x7f0b0012
int id accelerate 0x7f0b0013
int id accessibility_action_clickable_span 0x7f0b0014
int id accessibility_custom_action_0 0x7f0b0015
int id accessibility_custom_action_1 0x7f0b0016
int id accessibility_custom_action_10 0x7f0b0017
int id accessibility_custom_action_11 0x7f0b0018
int id accessibility_custom_action_12 0x7f0b0019
int id accessibility_custom_action_13 0x7f0b001a
int id accessibility_custom_action_14 0x7f0b001b
int id accessibility_custom_action_15 0x7f0b001c
int id accessibility_custom_action_16 0x7f0b001d
int id accessibility_custom_action_17 0x7f0b001e
int id accessibility_custom_action_18 0x7f0b001f
int id accessibility_custom_action_19 0x7f0b0020
int id accessibility_custom_action_2 0x7f0b0021
int id accessibility_custom_action_20 0x7f0b0022
int id accessibility_custom_action_21 0x7f0b0023
int id accessibility_custom_action_22 0x7f0b0024
int id accessibility_custom_action_23 0x7f0b0025
int id accessibility_custom_action_24 0x7f0b0026
int id accessibility_custom_action_25 0x7f0b0027
int id accessibility_custom_action_26 0x7f0b0028
int id accessibility_custom_action_27 0x7f0b0029
int id accessibility_custom_action_28 0x7f0b002a
int id accessibility_custom_action_29 0x7f0b002b
int id accessibility_custom_action_3 0x7f0b002c
int id accessibility_custom_action_30 0x7f0b002d
int id accessibility_custom_action_31 0x7f0b002e
int id accessibility_custom_action_4 0x7f0b002f
int id accessibility_custom_action_5 0x7f0b0030
int id accessibility_custom_action_6 0x7f0b0031
int id accessibility_custom_action_7 0x7f0b0032
int id accessibility_custom_action_8 0x7f0b0033
int id accessibility_custom_action_9 0x7f0b0034
int id action0 0x7f0b0035
int id actionDown 0x7f0b0036
int id actionDownUp 0x7f0b0037
int id actionIcon 0x7f0b0038
int id actionUp 0x7f0b0039
int id action_bar 0x7f0b003a
int id action_bar_activity_content 0x7f0b003b
int id action_bar_container 0x7f0b003c
int id action_bar_root 0x7f0b003d
int id action_bar_spinner 0x7f0b003e
int id action_bar_subtitle 0x7f0b003f
int id action_bar_title 0x7f0b0040
int id action_container 0x7f0b0041
int id action_context_bar 0x7f0b0042
int id action_divider 0x7f0b0043
int id action_fragment 0x7f0b0044
int id action_fragment_background 0x7f0b0045
int id action_fragment_root 0x7f0b0046
int id action_image 0x7f0b0047
int id action_menu_divider 0x7f0b0048
int id action_menu_presenter 0x7f0b0049
int id action_mode_bar 0x7f0b004a
int id action_mode_bar_stub 0x7f0b004b
int id action_mode_close_button 0x7f0b004c
int id action_text 0x7f0b004d
int id actions 0x7f0b004e
int id activated 0x7f0b004f
int id activity_chooser_view_content 0x7f0b0050
int id add 0x7f0b0051
int id adjust_height 0x7f0b0052
int id adjust_width 0x7f0b0053
int id alertTitle 0x7f0b0054
int id aligned 0x7f0b0055
int id all 0x7f0b0056
int id allStates 0x7f0b0057
int id always 0x7f0b0058
int id androidx_compose_ui_view_composition_context 0x7f0b0059
int id androidx_window_activity_scope 0x7f0b005a
int id animateToEnd 0x7f0b005b
int id animateToStart 0x7f0b005c
int id antiClockwise 0x7f0b005d
int id anticipate 0x7f0b005e
int id arc 0x7f0b005f
int id asConfigured 0x7f0b0060
int id async 0x7f0b0061
int id auto 0x7f0b0062
int id autoComplete 0x7f0b0063
int id autoCompleteToEnd 0x7f0b0064
int id autoCompleteToStart 0x7f0b0065
int id background 0x7f0b0066
int id background_container 0x7f0b0067
int id background_imagein 0x7f0b0068
int id background_imageout 0x7f0b0069
int id bar1 0x7f0b006a
int id bar2 0x7f0b006b
int id bar3 0x7f0b006c
int id barrier 0x7f0b006d
int id baseline 0x7f0b006e
int id beginOnFirstDraw 0x7f0b006f
int id beginning 0x7f0b0070
int id bestChoice 0x7f0b0071
int id blocking 0x7f0b0072
int id bottom 0x7f0b0073
int id bottom_spacer 0x7f0b0074
int id bounce 0x7f0b0075
int id bounceBoth 0x7f0b0076
int id bounceEnd 0x7f0b0077
int id bounceStart 0x7f0b0078
int id browse_container_dock 0x7f0b0079
int id browse_dummy 0x7f0b007a
int id browse_frame 0x7f0b007b
int id browse_grid 0x7f0b007c
int id browse_grid_dock 0x7f0b007d
int id browse_headers 0x7f0b007e
int id browse_headers_dock 0x7f0b007f
int id browse_headers_root 0x7f0b0080
int id browse_title_group 0x7f0b0081
int id button 0x7f0b0082
int id buttonPanel 0x7f0b0083
int id button_start 0x7f0b0084
int id cache_measures 0x7f0b0085
int id callMeasure 0x7f0b0086
int id cancel_action 0x7f0b0087
int id cancel_button 0x7f0b0088
int id carryVelocity 0x7f0b0089
int id center 0x7f0b008a
int id center_horizontal 0x7f0b008b
int id center_vertical 0x7f0b008c
int id chain 0x7f0b008d
int id chain2 0x7f0b008e
int id chains 0x7f0b008f
int id checkbox 0x7f0b0090
int id checked 0x7f0b0091
int id chip 0x7f0b0092
int id chip1 0x7f0b0093
int id chip2 0x7f0b0094
int id chip3 0x7f0b0095
int id chip_group 0x7f0b0096
int id chronometer 0x7f0b0097
int id circle_center 0x7f0b0098
int id clear_text 0x7f0b0099
int id clip_horizontal 0x7f0b009a
int id clip_vertical 0x7f0b009b
int id clockwise 0x7f0b009c
int id closest 0x7f0b009d
int id coil_request_manager 0x7f0b009e
int id collapseActionView 0x7f0b009f
int id column 0x7f0b00a0
int id compose_view_saveable_id_tag 0x7f0b00a1
int id confirm_button 0x7f0b00a2
int id constraint 0x7f0b00a3
int id consume_window_insets_tag 0x7f0b00a4
int id container 0x7f0b00a5
int id container_list 0x7f0b00a6
int id content 0x7f0b00a7
int id contentPanel 0x7f0b00a8
int id content_container 0x7f0b00a9
int id content_fragment 0x7f0b00aa
int id content_frame 0x7f0b00ab
int id content_text 0x7f0b00ac
int id contiguous 0x7f0b00ad
int id continuousVelocity 0x7f0b00ae
int id control_bar 0x7f0b00af
int id controls_card 0x7f0b00b0
int id controls_card_right_panel 0x7f0b00b1
int id controls_container 0x7f0b00b2
int id controls_dock 0x7f0b00b3
int id coordinator 0x7f0b00b4
int id cos 0x7f0b00b5
int id counterclockwise 0x7f0b00b6
int id currentState 0x7f0b00b7
int id current_time 0x7f0b00b8
int id custom 0x7f0b00b9
int id customPanel 0x7f0b00ba
int id cut 0x7f0b00bb
int id dark 0x7f0b00bc
int id dataBinding 0x7f0b00bd
int id date_picker_actions 0x7f0b00be
int id decelerate 0x7f0b00bf
int id decelerateAndComplete 0x7f0b00c0
int id decor_content_parent 0x7f0b00c1
int id decor_title 0x7f0b00c2
int id decor_title_container 0x7f0b00c3
int id default_activity_button 0x7f0b00c4
int id deltaRelative 0x7f0b00c5
int id dependency_ordering 0x7f0b00c6
int id description 0x7f0b00c7
int id description_dock 0x7f0b00c8
int id design_bottom_sheet 0x7f0b00c9
int id design_menu_item_action_area 0x7f0b00ca
int id design_menu_item_action_area_stub 0x7f0b00cb
int id design_menu_item_text 0x7f0b00cc
int id design_navigation_view 0x7f0b00cd
int id details_background_view 0x7f0b00ce
int id details_fragment_root 0x7f0b00cf
int id details_frame 0x7f0b00d0
int id details_overview 0x7f0b00d1
int id details_overview_actions 0x7f0b00d2
int id details_overview_actions_background 0x7f0b00d3
int id details_overview_description 0x7f0b00d4
int id details_overview_image 0x7f0b00d5
int id details_overview_right_panel 0x7f0b00d6
int id details_root 0x7f0b00d7
int id details_rows_dock 0x7f0b00d8
int id dialog_button 0x7f0b00d9
int id dimensions 0x7f0b00da
int id direct 0x7f0b00db
int id disableHome 0x7f0b00dc
int id disableIntraAutoTransition 0x7f0b00dd
int id disablePostScroll 0x7f0b00de
int id disableScroll 0x7f0b00df
int id disjoint 0x7f0b00e0
int id dragAnticlockwise 0x7f0b00e1
int id dragClockwise 0x7f0b00e2
int id dragDown 0x7f0b00e3
int id dragEnd 0x7f0b00e4
int id dragLeft 0x7f0b00e5
int id dragRight 0x7f0b00e6
int id dragStart 0x7f0b00e7
int id dragUp 0x7f0b00e8
int id dropdown_menu 0x7f0b00e9
int id dummy 0x7f0b00ea
int id easeIn 0x7f0b00eb
int id easeInOut 0x7f0b00ec
int id easeOut 0x7f0b00ed
int id east 0x7f0b00ee
int id edit_query 0x7f0b00ef
int id edit_text_id 0x7f0b00f0
int id elastic 0x7f0b00f1
int id end 0x7f0b00f2
int id endToStart 0x7f0b00f3
int id end_padder 0x7f0b00f4
int id enterAlways 0x7f0b00f5
int id enterAlwaysCollapsed 0x7f0b00f6
int id error_frame 0x7f0b00f7
int id exitUntilCollapsed 0x7f0b00f8
int id exo_ad_overlay 0x7f0b00f9
int id exo_artwork 0x7f0b00fa
int id exo_audio_track 0x7f0b00fb
int id exo_basic_controls 0x7f0b00fc
int id exo_bottom_bar 0x7f0b00fd
int id exo_buffering 0x7f0b00fe
int id exo_center_controls 0x7f0b00ff
int id exo_check 0x7f0b0100
int id exo_content_frame 0x7f0b0101
int id exo_controller 0x7f0b0102
int id exo_controller_placeholder 0x7f0b0103
int id exo_controls_background 0x7f0b0104
int id exo_duration 0x7f0b0105
int id exo_error_message 0x7f0b0106
int id exo_extra_controls 0x7f0b0107
int id exo_extra_controls_scroll_view 0x7f0b0108
int id exo_ffwd 0x7f0b0109
int id exo_ffwd_with_amount 0x7f0b010a
int id exo_fullscreen 0x7f0b010b
int id exo_icon 0x7f0b010c
int id exo_main_text 0x7f0b010d
int id exo_minimal_controls 0x7f0b010e
int id exo_minimal_fullscreen 0x7f0b010f
int id exo_next 0x7f0b0110
int id exo_overflow_hide 0x7f0b0111
int id exo_overflow_show 0x7f0b0112
int id exo_overlay 0x7f0b0113
int id exo_pause 0x7f0b0114
int id exo_play 0x7f0b0115
int id exo_play_pause 0x7f0b0116
int id exo_playback_speed 0x7f0b0117
int id exo_position 0x7f0b0118
int id exo_prev 0x7f0b0119
int id exo_progress 0x7f0b011a
int id exo_progress_placeholder 0x7f0b011b
int id exo_repeat_toggle 0x7f0b011c
int id exo_rew 0x7f0b011d
int id exo_rew_with_amount 0x7f0b011e
int id exo_settings 0x7f0b011f
int id exo_settings_listview 0x7f0b0120
int id exo_shuffle 0x7f0b0121
int id exo_shutter 0x7f0b0122
int id exo_sub_text 0x7f0b0123
int id exo_subtitle 0x7f0b0124
int id exo_subtitles 0x7f0b0125
int id exo_text 0x7f0b0126
int id exo_time 0x7f0b0127
int id exo_track_selection_view 0x7f0b0128
int id exo_vr 0x7f0b0129
int id expand_activities_button 0x7f0b012a
int id expanded_menu 0x7f0b012b
int id extra 0x7f0b012c
int id extra_badge 0x7f0b012d
int id fade 0x7f0b012e
int id fade_out_edge 0x7f0b012f
int id fill 0x7f0b0130
int id fill_horizontal 0x7f0b0131
int id fill_vertical 0x7f0b0132
int id filled 0x7f0b0133
int id fit 0x7f0b0134
int id fitToContents 0x7f0b0135
int id fixed 0x7f0b0136
int id fixed_height 0x7f0b0137
int id fixed_width 0x7f0b0138
int id flip 0x7f0b0139
int id floating 0x7f0b013a
int id foreground_container 0x7f0b013b
int id forever 0x7f0b013c
int id fragment_container_view_tag 0x7f0b013d
int id frost 0x7f0b013e
int id ghost_view 0x7f0b013f
int id ghost_view_holder 0x7f0b0140
int id gone 0x7f0b0141
int id graph 0x7f0b0142
int id graph_wrap 0x7f0b0143
int id grid_frame 0x7f0b0144
int id group_divider 0x7f0b0145
int id grouping 0x7f0b0146
int id groups 0x7f0b0147
int id guidance_breadcrumb 0x7f0b0148
int id guidance_container 0x7f0b0149
int id guidance_description 0x7f0b014a
int id guidance_icon 0x7f0b014b
int id guidance_title 0x7f0b014c
int id guidedactions_activator_item 0x7f0b014d
int id guidedactions_content 0x7f0b014e
int id guidedactions_content2 0x7f0b014f
int id guidedactions_item_checkmark 0x7f0b0150
int id guidedactions_item_chevron 0x7f0b0151
int id guidedactions_item_content 0x7f0b0152
int id guidedactions_item_description 0x7f0b0153
int id guidedactions_item_icon 0x7f0b0154
int id guidedactions_item_title 0x7f0b0155
int id guidedactions_list 0x7f0b0156
int id guidedactions_list2 0x7f0b0157
int id guidedactions_list_background 0x7f0b0158
int id guidedactions_list_background2 0x7f0b0159
int id guidedactions_root 0x7f0b015a
int id guidedactions_root2 0x7f0b015b
int id guidedactions_sub_list 0x7f0b015c
int id guidedactions_sub_list_background 0x7f0b015d
int id guidedstep_background 0x7f0b015e
int id guidedstep_background_view_root 0x7f0b015f
int id guidedstep_root 0x7f0b0160
int id guideline 0x7f0b0161
int id header_title 0x7f0b0162
int id hide_ime_id 0x7f0b0163
int id hide_in_inspector_tag 0x7f0b0164
int id hideable 0x7f0b0165
int id home 0x7f0b0166
int id homeAsUp 0x7f0b0167
int id honorRequest 0x7f0b0168
int id horizontal_only 0x7f0b0169
int id hovercard_panel 0x7f0b016a
int id icon 0x7f0b016b
int id icon_frame 0x7f0b016c
int id icon_group 0x7f0b016d
int id icon_only 0x7f0b016e
int id ifRoom 0x7f0b016f
int id ignore 0x7f0b0170
int id ignoreRequest 0x7f0b0171
int id image 0x7f0b0172
int id immediateStop 0x7f0b0173
int id included 0x7f0b0174
int id info 0x7f0b0175
int id infoOver 0x7f0b0176
int id infoUnder 0x7f0b0177
int id infoUnderWithExtra 0x7f0b0178
int id info_field 0x7f0b0179
int id initial 0x7f0b017a
int id inspection_slot_table_set 0x7f0b017b
int id invisible 0x7f0b017c
int id inward 0x7f0b017d
int id is_pooling_container_tag 0x7f0b017e
int id italic 0x7f0b017f
int id item_touch_helper_previous_elevation 0x7f0b0180
int id jumpToEnd 0x7f0b0181
int id jumpToStart 0x7f0b0182
int id label 0x7f0b0183
int id labeled 0x7f0b0184
int id layout 0x7f0b0185
int id lb_action_button 0x7f0b0186
int id lb_control_closed_captioning 0x7f0b0187
int id lb_control_fast_forward 0x7f0b0188
int id lb_control_fast_rewind 0x7f0b0189
int id lb_control_high_quality 0x7f0b018a
int id lb_control_more_actions 0x7f0b018b
int id lb_control_picture_in_picture 0x7f0b018c
int id lb_control_play_pause 0x7f0b018d
int id lb_control_repeat 0x7f0b018e
int id lb_control_shuffle 0x7f0b018f
int id lb_control_skip_next 0x7f0b0190
int id lb_control_skip_previous 0x7f0b0191
int id lb_control_thumbs_down 0x7f0b0192
int id lb_control_thumbs_up 0x7f0b0193
int id lb_details_description_body 0x7f0b0194
int id lb_details_description_subtitle 0x7f0b0195
int id lb_details_description_title 0x7f0b0196
int id lb_focus_animator 0x7f0b0197
int id lb_guidedstep_background 0x7f0b0198
int id lb_parallax_source 0x7f0b0199
int id lb_results_frame 0x7f0b019a
int id lb_row_container_header_dock 0x7f0b019b
int id lb_search_bar 0x7f0b019c
int id lb_search_bar_badge 0x7f0b019d
int id lb_search_bar_items 0x7f0b019e
int id lb_search_bar_speech_orb 0x7f0b019f
int id lb_search_frame 0x7f0b01a0
int id lb_search_text_editor 0x7f0b01a1
int id lb_shadow_focused 0x7f0b01a2
int id lb_shadow_impl 0x7f0b01a3
int id lb_shadow_normal 0x7f0b01a4
int id lb_slide_transition_value 0x7f0b01a5
int id left 0x7f0b01a6
int id leftToRight 0x7f0b01a7
int id legacy 0x7f0b01a8
int id light 0x7f0b01a9
int id line1 0x7f0b01aa
int id line3 0x7f0b01ab
int id linear 0x7f0b01ac
int id list 0x7f0b01ad
int id listMode 0x7f0b01ae
int id list_item 0x7f0b01af
int id locale 0x7f0b01b0
int id logo 0x7f0b01b1
int id ltr 0x7f0b01b2
int id main 0x7f0b01b3
int id mainOnly 0x7f0b01b4
int id main_browse_fragment 0x7f0b01b5
int id main_frame 0x7f0b01b6
int id main_icon 0x7f0b01b7
int id main_image 0x7f0b01b8
int id masked 0x7f0b01b9
int id match_constraint 0x7f0b01ba
int id match_parent 0x7f0b01bb
int id material_clock_display 0x7f0b01bc
int id material_clock_face 0x7f0b01bd
int id material_clock_hand 0x7f0b01be
int id material_clock_period_am_button 0x7f0b01bf
int id material_clock_period_pm_button 0x7f0b01c0
int id material_clock_period_toggle 0x7f0b01c1
int id material_hour_text_input 0x7f0b01c2
int id material_hour_tv 0x7f0b01c3
int id material_label 0x7f0b01c4
int id material_minute_text_input 0x7f0b01c5
int id material_minute_tv 0x7f0b01c6
int id material_textinput_timepicker 0x7f0b01c7
int id material_timepicker_cancel_button 0x7f0b01c8
int id material_timepicker_container 0x7f0b01c9
int id material_timepicker_edit_text 0x7f0b01ca
int id material_timepicker_mode_button 0x7f0b01cb
int id material_timepicker_ok_button 0x7f0b01cc
int id material_timepicker_view 0x7f0b01cd
int id material_value_index 0x7f0b01ce
int id maxLines 0x7f0b01cf
int id mediaItemActionsContainer 0x7f0b01d0
int id mediaItemDetails 0x7f0b01d1
int id mediaItemDuration 0x7f0b01d2
int id mediaItemName 0x7f0b01d3
int id mediaItemNumberViewFlipper 0x7f0b01d4
int id mediaItemRow 0x7f0b01d5
int id mediaListHeader 0x7f0b01d6
int id mediaRowSelector 0x7f0b01d7
int id mediaRowSeparator 0x7f0b01d8
int id media_actions 0x7f0b01d9
int id media_controller_compat_view_tag 0x7f0b01da
int id message 0x7f0b01db
int id middle 0x7f0b01dc
int id mini 0x7f0b01dd
int id month_grid 0x7f0b01de
int id month_navigation_bar 0x7f0b01df
int id month_navigation_fragment_toggle 0x7f0b01e0
int id month_navigation_next 0x7f0b01e1
int id month_navigation_previous 0x7f0b01e2
int id month_title 0x7f0b01e3
int id more_actions_dock 0x7f0b01e4
int id motion_base 0x7f0b01e5
int id mtrl_anchor_parent 0x7f0b01e6
int id mtrl_calendar_day_selector_frame 0x7f0b01e7
int id mtrl_calendar_days_of_week 0x7f0b01e8
int id mtrl_calendar_frame 0x7f0b01e9
int id mtrl_calendar_main_pane 0x7f0b01ea
int id mtrl_calendar_months 0x7f0b01eb
int id mtrl_calendar_selection_frame 0x7f0b01ec
int id mtrl_calendar_text_input_frame 0x7f0b01ed
int id mtrl_calendar_year_selector_frame 0x7f0b01ee
int id mtrl_card_checked_layer_id 0x7f0b01ef
int id mtrl_child_content_container 0x7f0b01f0
int id mtrl_internal_children_alpha_tag 0x7f0b01f1
int id mtrl_motion_snapshot_view 0x7f0b01f2
int id mtrl_picker_fullscreen 0x7f0b01f3
int id mtrl_picker_header 0x7f0b01f4
int id mtrl_picker_header_selection_text 0x7f0b01f5
int id mtrl_picker_header_title_and_selection 0x7f0b01f6
int id mtrl_picker_header_toggle 0x7f0b01f7
int id mtrl_picker_text_input_date 0x7f0b01f8
int id mtrl_picker_text_input_range_end 0x7f0b01f9
int id mtrl_picker_text_input_range_start 0x7f0b01fa
int id mtrl_picker_title_text 0x7f0b01fb
int id mtrl_view_tag_bottom_padding 0x7f0b01fc
int id multiply 0x7f0b01fd
int id nav_controller_view_tag 0x7f0b01fe
int id nav_host_fragment_container 0x7f0b01ff
int id navigation_bar_item_icon_view 0x7f0b0200
int id navigation_bar_item_labels_group 0x7f0b0201
int id navigation_bar_item_large_label_view 0x7f0b0202
int id navigation_bar_item_small_label_view 0x7f0b0203
int id navigation_header_container 0x7f0b0204
int id navigator_container 0x7f0b0205
int id never 0x7f0b0206
int id neverCompleteToEnd 0x7f0b0207
int id neverCompleteToStart 0x7f0b0208
int id noScroll 0x7f0b0209
int id noState 0x7f0b020a
int id none 0x7f0b020b
int id normal 0x7f0b020c
int id north 0x7f0b020d
int id notification_background 0x7f0b020e
int id notification_main_column 0x7f0b020f
int id notification_main_column_container 0x7f0b0210
int id off 0x7f0b0211
int id on 0x7f0b0212
int id onAttachStateChangeListener 0x7f0b0213
int id onDateChanged 0x7f0b0214
int id onInterceptTouchReturnSwipe 0x7f0b0215
int id onboarding_fragment_root 0x7f0b0216
int id one 0x7f0b0217
int id outline 0x7f0b0218
int id outward 0x7f0b0219
int id overshoot 0x7f0b021a
int id packed 0x7f0b021b
int id page_container 0x7f0b021c
int id page_indicator 0x7f0b021d
int id parallax 0x7f0b021e
int id parent 0x7f0b021f
int id parentPanel 0x7f0b0220
int id parentRelative 0x7f0b0221
int id parent_matrix 0x7f0b0222
int id password_toggle 0x7f0b0223
int id path 0x7f0b0224
int id pathRelative 0x7f0b0225
int id paused 0x7f0b0226
int id peekHeight 0x7f0b0227
int id percent 0x7f0b0228
int id picker 0x7f0b0229
int id pin 0x7f0b022a
int id playback_controls_dock 0x7f0b022b
int id playback_fragment_background 0x7f0b022c
int id playback_fragment_root 0x7f0b022d
int id playback_progress 0x7f0b022e
int id playing 0x7f0b022f
int id pooling_container_listener_holder_tag 0x7f0b0230
int id position 0x7f0b0231
int id postLayout 0x7f0b0232
int id preferences_detail 0x7f0b0233
int id preferences_header 0x7f0b0234
int id preferences_sliding_pane_layout 0x7f0b0235
int id progress_circular 0x7f0b0236
int id progress_horizontal 0x7f0b0237
int id radio 0x7f0b0238
int id ratio 0x7f0b0239
int id rectangles 0x7f0b023a
int id recycler_view 0x7f0b023b
int id report_drawn 0x7f0b023c
int id reverseSawtooth 0x7f0b023d
int id right 0x7f0b023e
int id rightToLeft 0x7f0b023f
int id right_icon 0x7f0b0240
int id right_side 0x7f0b0241
int id rounded 0x7f0b0242
int id row_content 0x7f0b0243
int id row_header 0x7f0b0244
int id row_header_description 0x7f0b0245
int id row_index_key 0x7f0b0246
int id rtl 0x7f0b0247
int id save_non_transition_alpha 0x7f0b0248
int id save_overlay_view 0x7f0b0249
int id sawtooth 0x7f0b024a
int id scale 0x7f0b024b
int id scale_frame 0x7f0b024c
int id screen 0x7f0b024d
int id scroll 0x7f0b024e
int id scrollIndicatorDown 0x7f0b024f
int id scrollIndicatorUp 0x7f0b0250
int id scrollView 0x7f0b0251
int id scrollable 0x7f0b0252
int id search_badge 0x7f0b0253
int id search_bar 0x7f0b0254
int id search_button 0x7f0b0255
int id search_close_btn 0x7f0b0256
int id search_edit_frame 0x7f0b0257
int id search_go_btn 0x7f0b0258
int id search_mag_icon 0x7f0b0259
int id search_orb 0x7f0b025a
int id search_plate 0x7f0b025b
int id search_src_text 0x7f0b025c
int id search_voice_btn 0x7f0b025d
int id secondary_controls_dock 0x7f0b025e
int id seekbar 0x7f0b025f
int id seekbar_value 0x7f0b0260
int id select_dialog_listview 0x7f0b0261
int id selected 0x7f0b0262
int id selection_type 0x7f0b0263
int id separate_time 0x7f0b0264
int id separator 0x7f0b0265
int id settings_dialog_container 0x7f0b0266
int id settings_preference_fragment_container 0x7f0b0267
int id sharedValueSet 0x7f0b0268
int id sharedValueUnset 0x7f0b0269
int id shortcut 0x7f0b026a
int id showCustom 0x7f0b026b
int id showHome 0x7f0b026c
int id showTitle 0x7f0b026d
int id sin 0x7f0b026e
int id skipCollapsed 0x7f0b026f
int id skipped 0x7f0b0270
int id slide 0x7f0b0271
int id sliding_pane_detail_container 0x7f0b0272
int id sliding_pane_layout 0x7f0b0273
int id snackbar_action 0x7f0b0274
int id snackbar_text 0x7f0b0275
int id snap 0x7f0b0276
int id snapMargins 0x7f0b0277
int id south 0x7f0b0278
int id spacer 0x7f0b0279
int id special_effects_controller_view_tag 0x7f0b027a
int id spherical_gl_surface_view 0x7f0b027b
int id spinner 0x7f0b027c
int id spline 0x7f0b027d
int id split_action_bar 0x7f0b027e
int id spread 0x7f0b027f
int id spread_inside 0x7f0b0280
int id spring 0x7f0b0281
int id square 0x7f0b0282
int id src_atop 0x7f0b0283
int id src_in 0x7f0b0284
int id src_over 0x7f0b0285
int id standard 0x7f0b0286
int id start 0x7f0b0287
int id startHorizontal 0x7f0b0288
int id startToEnd 0x7f0b0289
int id startVertical 0x7f0b028a
int id staticLayout 0x7f0b028b
int id staticPostLayout 0x7f0b028c
int id status_bar_latest_event_content 0x7f0b028d
int id stop 0x7f0b028e
int id stretch 0x7f0b028f
int id submenuarrow 0x7f0b0290
int id submit_area 0x7f0b0291
int id supportScrollUp 0x7f0b0292
int id surface_view 0x7f0b0293
int id switchWidget 0x7f0b0294
int id tabMode 0x7f0b0295
int id tag_accessibility_actions 0x7f0b0296
int id tag_accessibility_clickable_spans 0x7f0b0297
int id tag_accessibility_heading 0x7f0b0298
int id tag_accessibility_pane_title 0x7f0b0299
int id tag_on_apply_window_listener 0x7f0b029a
int id tag_on_receive_content_listener 0x7f0b029b
int id tag_on_receive_content_mime_types 0x7f0b029c
int id tag_screen_reader_focusable 0x7f0b029d
int id tag_state_description 0x7f0b029e
int id tag_transition_group 0x7f0b029f
int id tag_unhandled_key_event_manager 0x7f0b02a0
int id tag_unhandled_key_listeners 0x7f0b02a1
int id tag_window_insets_animation_callback 0x7f0b02a2
int id test_checkbox_android_button_tint 0x7f0b02a3
int id test_checkbox_app_button_tint 0x7f0b02a4
int id test_radiobutton_android_button_tint 0x7f0b02a5
int id test_radiobutton_app_button_tint 0x7f0b02a6
int id text 0x7f0b02a7
int id text2 0x7f0b02a8
int id textEnd 0x7f0b02a9
int id textSpacerNoButtons 0x7f0b02aa
int id textSpacerNoTitle 0x7f0b02ab
int id textStart 0x7f0b02ac
int id textTop 0x7f0b02ad
int id textWatcher 0x7f0b02ae
int id text_input_end_icon 0x7f0b02af
int id text_input_error_icon 0x7f0b02b0
int id text_input_start_icon 0x7f0b02b1
int id textinput_counter 0x7f0b02b2
int id textinput_error 0x7f0b02b3
int id textinput_helper_text 0x7f0b02b4
int id textinput_placeholder 0x7f0b02b5
int id textinput_prefix_text 0x7f0b02b6
int id textinput_suffix_text 0x7f0b02b7
int id texture_view 0x7f0b02b8
int id thumbs_row 0x7f0b02b9
int id time 0x7f0b02ba
int id title 0x7f0b02bb
int id titleDividerNoCustom 0x7f0b02bc
int id title_badge 0x7f0b02bd
int id title_orb 0x7f0b02be
int id title_template 0x7f0b02bf
int id title_text 0x7f0b02c0
int id toggle 0x7f0b02c1
int id top 0x7f0b02c2
int id topPanel 0x7f0b02c3
int id total_time 0x7f0b02c4
int id touch_outside 0x7f0b02c5
int id transitionPosition 0x7f0b02c6
int id transitionToEnd 0x7f0b02c7
int id transitionToStart 0x7f0b02c8
int id transition_current_scene 0x7f0b02c9
int id transition_layout_save 0x7f0b02ca
int id transition_position 0x7f0b02cb
int id transition_scene_layoutid_cache 0x7f0b02cc
int id transition_transform 0x7f0b02cd
int id transport_row 0x7f0b02ce
int id triangle 0x7f0b02cf
int id unchecked 0x7f0b02d0
int id uniform 0x7f0b02d1
int id unlabeled 0x7f0b02d2
int id up 0x7f0b02d3
int id useLogo 0x7f0b02d4
int id vertical_only 0x7f0b02d5
int id video_decoder_gl_surface_view 0x7f0b02d6
int id video_surface 0x7f0b02d7
int id video_surface_container 0x7f0b02d8
int id view_offset_helper 0x7f0b02d9
int id view_transition 0x7f0b02da
int id view_tree_lifecycle_owner 0x7f0b02db
int id view_tree_on_back_pressed_dispatcher_owner 0x7f0b02dc
int id view_tree_saved_state_registry_owner 0x7f0b02dd
int id view_tree_view_model_store_owner 0x7f0b02de
int id visible 0x7f0b02df
int id visible_removing_fragment_view_tag 0x7f0b02e0
int id west 0x7f0b02e1
int id when_playing 0x7f0b02e2
int id wide 0x7f0b02e3
int id withText 0x7f0b02e4
int id withinBounds 0x7f0b02e5
int id wrap 0x7f0b02e6
int id wrap_content 0x7f0b02e7
int id wrap_content_constrained 0x7f0b02e8
int id wrapped_composition_tag 0x7f0b02e9
int id x_left 0x7f0b02ea
int id x_right 0x7f0b02eb
int id zero_corner_chip 0x7f0b02ec
int id zoom 0x7f0b02ed
int integer abc_config_activityDefaultDur 0x7f0c0000
int integer abc_config_activityShortDur 0x7f0c0001
int integer app_bar_elevation_anim_duration 0x7f0c0002
int integer bottom_sheet_slide_duration 0x7f0c0003
int integer cancel_button_image_alpha 0x7f0c0004
int integer config_navAnimTime 0x7f0c0005
int integer config_tooltipAnimTime 0x7f0c0006
int integer design_snackbar_text_max_lines 0x7f0c0007
int integer design_tab_indicator_anim_duration_ms 0x7f0c0008
int integer exo_media_button_opacity_percentage_disabled 0x7f0c0009
int integer exo_media_button_opacity_percentage_enabled 0x7f0c000a
int integer google_play_services_version 0x7f0c000b
int integer hide_password_duration 0x7f0c000c
int integer lb_browse_headers_transition_delay 0x7f0c000d
int integer lb_browse_headers_transition_duration 0x7f0c000e
int integer lb_browse_rows_anim_duration 0x7f0c000f
int integer lb_card_activated_animation_duration 0x7f0c0010
int integer lb_card_selected_animation_delay 0x7f0c0011
int integer lb_card_selected_animation_duration 0x7f0c0012
int integer lb_details_description_body_max_lines 0x7f0c0013
int integer lb_details_description_body_min_lines 0x7f0c0014
int integer lb_details_description_subtitle_max_lines 0x7f0c0015
int integer lb_details_description_title_max_lines 0x7f0c0016
int integer lb_error_message_max_lines 0x7f0c0017
int integer lb_guidedactions_item_animation_duration 0x7f0c0018
int integer lb_guidedactions_item_description_min_lines 0x7f0c0019
int integer lb_guidedactions_item_title_max_lines 0x7f0c001a
int integer lb_guidedactions_item_title_min_lines 0x7f0c001b
int integer lb_guidedstep_activity_background_fade_duration_ms 0x7f0c001c
int integer lb_onboarding_header_description_delay 0x7f0c001d
int integer lb_onboarding_header_title_delay 0x7f0c001e
int integer lb_playback_bg_fade_in_ms 0x7f0c001f
int integer lb_playback_bg_fade_out_ms 0x7f0c0020
int integer lb_playback_controls_fade_in_ms 0x7f0c0021
int integer lb_playback_controls_fade_out_ms 0x7f0c0022
int integer lb_playback_controls_show_time_ms 0x7f0c0023
int integer lb_playback_controls_tickle_timeout_ms 0x7f0c0024
int integer lb_playback_description_fade_in_ms 0x7f0c0025
int integer lb_playback_description_fade_out_ms 0x7f0c0026
int integer lb_playback_rows_fade_delay_ms 0x7f0c0027
int integer lb_playback_rows_fade_in_ms 0x7f0c0028
int integer lb_playback_rows_fade_out_ms 0x7f0c0029
int integer lb_search_bar_speech_mode_background_alpha 0x7f0c002a
int integer lb_search_bar_text_mode_background_alpha 0x7f0c002b
int integer lb_search_orb_pulse_duration_ms 0x7f0c002c
int integer lb_search_orb_scale_duration_ms 0x7f0c002d
int integer material_motion_duration_long_1 0x7f0c002e
int integer material_motion_duration_long_2 0x7f0c002f
int integer material_motion_duration_medium_1 0x7f0c0030
int integer material_motion_duration_medium_2 0x7f0c0031
int integer material_motion_duration_short_1 0x7f0c0032
int integer material_motion_duration_short_2 0x7f0c0033
int integer material_motion_path 0x7f0c0034
int integer mtrl_badge_max_character_count 0x7f0c0035
int integer mtrl_btn_anim_delay_ms 0x7f0c0036
int integer mtrl_btn_anim_duration_ms 0x7f0c0037
int integer mtrl_calendar_header_orientation 0x7f0c0038
int integer mtrl_calendar_selection_text_lines 0x7f0c0039
int integer mtrl_calendar_year_selector_span 0x7f0c003a
int integer mtrl_card_anim_delay_ms 0x7f0c003b
int integer mtrl_card_anim_duration_ms 0x7f0c003c
int integer mtrl_chip_anim_duration 0x7f0c003d
int integer mtrl_tab_indicator_anim_duration_ms 0x7f0c003e
int integer preferences_detail_pane_weight 0x7f0c003f
int integer preferences_header_pane_weight 0x7f0c0040
int integer show_password_duration 0x7f0c0041
int integer slideEdgeEnd 0x7f0c0042
int integer slideEdgeStart 0x7f0c0043
int integer status_bar_notification_info_maxnum 0x7f0c0044
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_0 0x7f0d0000
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_1 0x7f0d0001
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0 0x7f0d0002
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1 0x7f0d0003
int interpolator btn_radio_to_off_mtrl_animation_interpolator_0 0x7f0d0004
int interpolator btn_radio_to_on_mtrl_animation_interpolator_0 0x7f0d0005
int interpolator fast_out_slow_in 0x7f0d0006
int interpolator mtrl_fast_out_linear_in 0x7f0d0007
int interpolator mtrl_fast_out_slow_in 0x7f0d0008
int interpolator mtrl_linear 0x7f0d0009
int interpolator mtrl_linear_out_slow_in 0x7f0d000a
int layout abc_action_bar_title_item 0x7f0e0000
int layout abc_action_bar_up_container 0x7f0e0001
int layout abc_action_menu_item_layout 0x7f0e0002
int layout abc_action_menu_layout 0x7f0e0003
int layout abc_action_mode_bar 0x7f0e0004
int layout abc_action_mode_close_item_material 0x7f0e0005
int layout abc_activity_chooser_view 0x7f0e0006
int layout abc_activity_chooser_view_list_item 0x7f0e0007
int layout abc_alert_dialog_button_bar_material 0x7f0e0008
int layout abc_alert_dialog_material 0x7f0e0009
int layout abc_alert_dialog_title_material 0x7f0e000a
int layout abc_cascading_menu_item_layout 0x7f0e000b
int layout abc_dialog_title_material 0x7f0e000c
int layout abc_expanded_menu_layout 0x7f0e000d
int layout abc_list_menu_item_checkbox 0x7f0e000e
int layout abc_list_menu_item_icon 0x7f0e000f
int layout abc_list_menu_item_layout 0x7f0e0010
int layout abc_list_menu_item_radio 0x7f0e0011
int layout abc_popup_menu_header_item_layout 0x7f0e0012
int layout abc_popup_menu_item_layout 0x7f0e0013
int layout abc_screen_content_include 0x7f0e0014
int layout abc_screen_simple 0x7f0e0015
int layout abc_screen_simple_overlay_action_mode 0x7f0e0016
int layout abc_screen_toolbar 0x7f0e0017
int layout abc_search_dropdown_item_icons_2line 0x7f0e0018
int layout abc_search_view 0x7f0e0019
int layout abc_select_dialog_material 0x7f0e001a
int layout abc_tooltip 0x7f0e001b
int layout activity_tv_main 0x7f0e001c
int layout custom_dialog 0x7f0e001d
int layout design_bottom_navigation_item 0x7f0e001e
int layout design_bottom_sheet_dialog 0x7f0e001f
int layout design_layout_snackbar 0x7f0e0020
int layout design_layout_snackbar_include 0x7f0e0021
int layout design_layout_tab_icon 0x7f0e0022
int layout design_layout_tab_text 0x7f0e0023
int layout design_menu_item_action_area 0x7f0e0024
int layout design_navigation_item 0x7f0e0025
int layout design_navigation_item_header 0x7f0e0026
int layout design_navigation_item_separator 0x7f0e0027
int layout design_navigation_item_subheader 0x7f0e0028
int layout design_navigation_menu 0x7f0e0029
int layout design_navigation_menu_item 0x7f0e002a
int layout design_text_input_end_icon 0x7f0e002b
int layout design_text_input_start_icon 0x7f0e002c
int layout exo_legacy_player_control_view 0x7f0e002d
int layout exo_list_divider 0x7f0e002e
int layout exo_player_control_ffwd_button 0x7f0e002f
int layout exo_player_control_rewind_button 0x7f0e0030
int layout exo_player_control_view 0x7f0e0031
int layout exo_player_view 0x7f0e0032
int layout exo_styled_settings_list 0x7f0e0033
int layout exo_styled_settings_list_item 0x7f0e0034
int layout exo_styled_sub_settings_list_item 0x7f0e0035
int layout exo_track_selection_dialog 0x7f0e0036
int layout expand_button 0x7f0e0037
int layout image_frame 0x7f0e0038
int layout ime_base_split_test_activity 0x7f0e0039
int layout ime_secondary_split_test_activity 0x7f0e003a
int layout lb_action_1_line 0x7f0e003b
int layout lb_action_2_lines 0x7f0e003c
int layout lb_background_window 0x7f0e003d
int layout lb_browse_fragment 0x7f0e003e
int layout lb_browse_title 0x7f0e003f
int layout lb_control_bar 0x7f0e0040
int layout lb_control_button_primary 0x7f0e0041
int layout lb_control_button_secondary 0x7f0e0042
int layout lb_details_description 0x7f0e0043
int layout lb_details_fragment 0x7f0e0044
int layout lb_details_overview 0x7f0e0045
int layout lb_divider 0x7f0e0046
int layout lb_error_fragment 0x7f0e0047
int layout lb_fullwidth_details_overview 0x7f0e0048
int layout lb_fullwidth_details_overview_logo 0x7f0e0049
int layout lb_guidance 0x7f0e004a
int layout lb_guidedactions 0x7f0e004b
int layout lb_guidedactions_datepicker_item 0x7f0e004c
int layout lb_guidedactions_item 0x7f0e004d
int layout lb_guidedbuttonactions 0x7f0e004e
int layout lb_guidedstep_background 0x7f0e004f
int layout lb_guidedstep_fragment 0x7f0e0050
int layout lb_header 0x7f0e0051
int layout lb_headers_fragment 0x7f0e0052
int layout lb_image_card_view 0x7f0e0053
int layout lb_image_card_view_themed_badge_left 0x7f0e0054
int layout lb_image_card_view_themed_badge_right 0x7f0e0055
int layout lb_image_card_view_themed_content 0x7f0e0056
int layout lb_image_card_view_themed_title 0x7f0e0057
int layout lb_list_row 0x7f0e0058
int layout lb_list_row_hovercard 0x7f0e0059
int layout lb_media_item_number_view_flipper 0x7f0e005a
int layout lb_media_list_header 0x7f0e005b
int layout lb_onboarding_fragment 0x7f0e005c
int layout lb_picker 0x7f0e005d
int layout lb_picker_column 0x7f0e005e
int layout lb_picker_item 0x7f0e005f
int layout lb_picker_separator 0x7f0e0060
int layout lb_playback_controls 0x7f0e0061
int layout lb_playback_controls_row 0x7f0e0062
int layout lb_playback_fragment 0x7f0e0063
int layout lb_playback_now_playing_bars 0x7f0e0064
int layout lb_playback_transport_controls 0x7f0e0065
int layout lb_playback_transport_controls_row 0x7f0e0066
int layout lb_row_container 0x7f0e0067
int layout lb_row_header 0x7f0e0068
int layout lb_row_media_item 0x7f0e0069
int layout lb_row_media_item_action 0x7f0e006a
int layout lb_rows_fragment 0x7f0e006b
int layout lb_search_bar 0x7f0e006c
int layout lb_search_fragment 0x7f0e006d
int layout lb_search_orb 0x7f0e006e
int layout lb_section_header 0x7f0e006f
int layout lb_shadow 0x7f0e0070
int layout lb_speech_orb 0x7f0e0071
int layout lb_title_view 0x7f0e0072
int layout lb_vertical_grid 0x7f0e0073
int layout lb_vertical_grid_fragment 0x7f0e0074
int layout lb_video_surface 0x7f0e0075
int layout leanback_list_preference_fragment 0x7f0e0076
int layout leanback_list_preference_item_multi 0x7f0e0077
int layout leanback_list_preference_item_single 0x7f0e0078
int layout leanback_preference 0x7f0e0079
int layout leanback_preference_category 0x7f0e007a
int layout leanback_preference_fragment 0x7f0e007b
int layout leanback_preference_information 0x7f0e007c
int layout leanback_preference_widget_seekbar 0x7f0e007d
int layout leanback_preferences_list 0x7f0e007e
int layout leanback_settings_fragment 0x7f0e007f
int layout material_chip_input_combo 0x7f0e0080
int layout material_clock_display 0x7f0e0081
int layout material_clock_display_divider 0x7f0e0082
int layout material_clock_period_toggle 0x7f0e0083
int layout material_clock_period_toggle_land 0x7f0e0084
int layout material_clockface_textview 0x7f0e0085
int layout material_clockface_view 0x7f0e0086
int layout material_radial_view_group 0x7f0e0087
int layout material_textinput_timepicker 0x7f0e0088
int layout material_time_chip 0x7f0e0089
int layout material_time_input 0x7f0e008a
int layout material_timepicker 0x7f0e008b
int layout material_timepicker_dialog 0x7f0e008c
int layout material_timepicker_textinput_display 0x7f0e008d
int layout mtrl_alert_dialog 0x7f0e008e
int layout mtrl_alert_dialog_actions 0x7f0e008f
int layout mtrl_alert_dialog_title 0x7f0e0090
int layout mtrl_alert_select_dialog_item 0x7f0e0091
int layout mtrl_alert_select_dialog_multichoice 0x7f0e0092
int layout mtrl_alert_select_dialog_singlechoice 0x7f0e0093
int layout mtrl_calendar_day 0x7f0e0094
int layout mtrl_calendar_day_of_week 0x7f0e0095
int layout mtrl_calendar_days_of_week 0x7f0e0096
int layout mtrl_calendar_horizontal 0x7f0e0097
int layout mtrl_calendar_month 0x7f0e0098
int layout mtrl_calendar_month_labeled 0x7f0e0099
int layout mtrl_calendar_month_navigation 0x7f0e009a
int layout mtrl_calendar_months 0x7f0e009b
int layout mtrl_calendar_vertical 0x7f0e009c
int layout mtrl_calendar_year 0x7f0e009d
int layout mtrl_layout_snackbar 0x7f0e009e
int layout mtrl_layout_snackbar_include 0x7f0e009f
int layout mtrl_navigation_rail_item 0x7f0e00a0
int layout mtrl_picker_actions 0x7f0e00a1
int layout mtrl_picker_dialog 0x7f0e00a2
int layout mtrl_picker_fullscreen 0x7f0e00a3
int layout mtrl_picker_header_dialog 0x7f0e00a4
int layout mtrl_picker_header_fullscreen 0x7f0e00a5
int layout mtrl_picker_header_selection_text 0x7f0e00a6
int layout mtrl_picker_header_title_text 0x7f0e00a7
int layout mtrl_picker_header_toggle 0x7f0e00a8
int layout mtrl_picker_text_input_date 0x7f0e00a9
int layout mtrl_picker_text_input_date_range 0x7f0e00aa
int layout notification_action 0x7f0e00ab
int layout notification_action_tombstone 0x7f0e00ac
int layout notification_media_action 0x7f0e00ad
int layout notification_media_cancel_action 0x7f0e00ae
int layout notification_template_big_media 0x7f0e00af
int layout notification_template_big_media_custom 0x7f0e00b0
int layout notification_template_big_media_narrow 0x7f0e00b1
int layout notification_template_big_media_narrow_custom 0x7f0e00b2
int layout notification_template_custom_big 0x7f0e00b3
int layout notification_template_icon_group 0x7f0e00b4
int layout notification_template_lines_media 0x7f0e00b5
int layout notification_template_media 0x7f0e00b6
int layout notification_template_media_custom 0x7f0e00b7
int layout notification_template_part_chronometer 0x7f0e00b8
int layout notification_template_part_time 0x7f0e00b9
int layout preference 0x7f0e00ba
int layout preference_category 0x7f0e00bb
int layout preference_category_material 0x7f0e00bc
int layout preference_dialog_edittext 0x7f0e00bd
int layout preference_dropdown 0x7f0e00be
int layout preference_dropdown_material 0x7f0e00bf
int layout preference_information 0x7f0e00c0
int layout preference_information_material 0x7f0e00c1
int layout preference_list_fragment 0x7f0e00c2
int layout preference_material 0x7f0e00c3
int layout preference_recyclerview 0x7f0e00c4
int layout preference_widget_checkbox 0x7f0e00c5
int layout preference_widget_seekbar 0x7f0e00c6
int layout preference_widget_seekbar_material 0x7f0e00c7
int layout preference_widget_switch 0x7f0e00c8
int layout preference_widget_switch_compat 0x7f0e00c9
int layout select_dialog_item_material 0x7f0e00ca
int layout select_dialog_multichoice_material 0x7f0e00cb
int layout select_dialog_singlechoice_material 0x7f0e00cc
int layout support_simple_spinner_dropdown_item 0x7f0e00cd
int layout test_action_chip 0x7f0e00ce
int layout test_chip_zero_corner_radius 0x7f0e00cf
int layout test_design_checkbox 0x7f0e00d0
int layout test_design_radiobutton 0x7f0e00d1
int layout test_navigation_bar_item_layout 0x7f0e00d2
int layout test_reflow_chipgroup 0x7f0e00d3
int layout test_toolbar 0x7f0e00d4
int layout test_toolbar_custom_background 0x7f0e00d5
int layout test_toolbar_elevation 0x7f0e00d6
int layout test_toolbar_surface 0x7f0e00d7
int layout text_view_with_line_height_from_appearance 0x7f0e00d8
int layout text_view_with_line_height_from_layout 0x7f0e00d9
int layout text_view_with_line_height_from_style 0x7f0e00da
int layout text_view_with_theme_line_height 0x7f0e00db
int layout text_view_without_line_height 0x7f0e00dc
int layout video_surface_fragment 0x7f0e00dd
int mipmap ic_launcher 0x7f0f0000
int mipmap ic_launcher_foreground 0x7f0f0001
int mipmap ic_launcher_round 0x7f0f0002
int plurals exo_controls_fastforward_by_amount_description 0x7f100000
int plurals exo_controls_rewind_by_amount_description 0x7f100001
int plurals mtrl_badge_content_description 0x7f100002
int raw firebase_common_keep 0x7f110000
int raw lb_voice_failure 0x7f110001
int raw lb_voice_no_input 0x7f110002
int raw lb_voice_open 0x7f110003
int raw lb_voice_success 0x7f110004
int string abc_action_bar_home_description 0x7f120000
int string abc_action_bar_up_description 0x7f120001
int string abc_action_menu_overflow_description 0x7f120002
int string abc_action_mode_done 0x7f120003
int string abc_activity_chooser_view_see_all 0x7f120004
int string abc_activitychooserview_choose_application 0x7f120005
int string abc_capital_off 0x7f120006
int string abc_capital_on 0x7f120007
int string abc_menu_alt_shortcut_label 0x7f120008
int string abc_menu_ctrl_shortcut_label 0x7f120009
int string abc_menu_delete_shortcut_label 0x7f12000a
int string abc_menu_enter_shortcut_label 0x7f12000b
int string abc_menu_function_shortcut_label 0x7f12000c
int string abc_menu_meta_shortcut_label 0x7f12000d
int string abc_menu_shift_shortcut_label 0x7f12000e
int string abc_menu_space_shortcut_label 0x7f12000f
int string abc_menu_sym_shortcut_label 0x7f120010
int string abc_prepend_shortcut_label 0x7f120011
int string abc_search_hint 0x7f120012
int string abc_searchview_description_clear 0x7f120013
int string abc_searchview_description_query 0x7f120014
int string abc_searchview_description_search 0x7f120015
int string abc_searchview_description_submit 0x7f120016
int string abc_searchview_description_voice 0x7f120017
int string abc_shareactionprovider_share_with 0x7f120018
int string abc_shareactionprovider_share_with_application 0x7f120019
int string abc_toolbar_collapse_description 0x7f12001a
int string androidx_startup 0x7f12001b
int string app_name 0x7f12001c
int string appbar_scrolling_view_behavior 0x7f12001d
int string authentication_required 0x7f12001e
int string bottom_sheet_behavior 0x7f12001f
int string bottom_sheet_collapse_description 0x7f120020
int string bottom_sheet_dismiss_description 0x7f120021
int string bottom_sheet_drag_handle_description 0x7f120022
int string bottom_sheet_expand_description 0x7f120023
int string bottomsheet_action_expand_halfway 0x7f120024
int string call_notification_answer_action 0x7f120025
int string call_notification_answer_video_action 0x7f120026
int string call_notification_decline_action 0x7f120027
int string call_notification_hang_up_action 0x7f120028
int string call_notification_incoming_text 0x7f120029
int string call_notification_ongoing_text 0x7f12002a
int string call_notification_screening_text 0x7f12002b
int string cancel 0x7f12002c
int string character_counter_content_description 0x7f12002d
int string character_counter_overflowed_content_description 0x7f12002e
int string character_counter_pattern 0x7f12002f
int string chip_text 0x7f120030
int string clear_text_end_icon_content_description 0x7f120031
int string close_drawer 0x7f120032
int string close_sheet 0x7f120033
int string collapsed 0x7f120034
int string common_google_play_services_enable_button 0x7f120035
int string common_google_play_services_enable_text 0x7f120036
int string common_google_play_services_enable_title 0x7f120037
int string common_google_play_services_install_button 0x7f120038
int string common_google_play_services_install_text 0x7f120039
int string common_google_play_services_install_title 0x7f12003a
int string common_google_play_services_notification_channel_name 0x7f12003b
int string common_google_play_services_notification_ticker 0x7f12003c
int string common_google_play_services_unknown_issue 0x7f12003d
int string common_google_play_services_unsupported_text 0x7f12003e
int string common_google_play_services_update_button 0x7f12003f
int string common_google_play_services_update_text 0x7f120040
int string common_google_play_services_update_title 0x7f120041
int string common_google_play_services_updating_text 0x7f120042
int string common_google_play_services_wear_update_text 0x7f120043
int string common_open_on_phone 0x7f120044
int string common_signin_button_text 0x7f120045
int string common_signin_button_text_long 0x7f120046
int string copy 0x7f120047
int string date_input_headline 0x7f120048
int string date_input_headline_description 0x7f120049
int string date_input_invalid_for_pattern 0x7f12004a
int string date_input_invalid_not_allowed 0x7f12004b
int string date_input_invalid_year_range 0x7f12004c
int string date_input_label 0x7f12004d
int string date_input_no_input_description 0x7f12004e
int string date_input_title 0x7f12004f
int string date_picker_headline 0x7f120050
int string date_picker_headline_description 0x7f120051
int string date_picker_navigate_to_year_description 0x7f120052
int string date_picker_no_selection_description 0x7f120053
int string date_picker_scroll_to_earlier_years 0x7f120054
int string date_picker_scroll_to_later_years 0x7f120055
int string date_picker_switch_to_calendar_mode 0x7f120056
int string date_picker_switch_to_day_selection 0x7f120057
int string date_picker_switch_to_input_mode 0x7f120058
int string date_picker_switch_to_next_month 0x7f120059
int string date_picker_switch_to_previous_month 0x7f12005a
int string date_picker_switch_to_year_selection 0x7f12005b
int string date_picker_title 0x7f12005c
int string date_picker_today_description 0x7f12005d
int string date_picker_year_picker_pane_title 0x7f12005e
int string date_range_input_invalid_range_input 0x7f12005f
int string date_range_input_title 0x7f120060
int string date_range_picker_day_in_range 0x7f120061
int string date_range_picker_end_headline 0x7f120062
int string date_range_picker_scroll_to_next_month 0x7f120063
int string date_range_picker_scroll_to_previous_month 0x7f120064
int string date_range_picker_start_headline 0x7f120065
int string date_range_picker_title 0x7f120066
int string default_error_message 0x7f120067
int string default_notification_channel_name 0x7f120068
int string default_popup_window_title 0x7f120069
int string default_web_client_id 0x7f12006a
int string dest_title 0x7f12006b
int string dialog 0x7f12006c
int string download_cancel 0x7f12006d
int string download_completed 0x7f12006e
int string download_failed 0x7f12006f
int string download_pause 0x7f120070
int string download_resume 0x7f120071
int string download_start 0x7f120072
int string dropdown_menu 0x7f120073
int string error 0x7f120074
int string error_icon_content_description 0x7f120075
int string exo_controls_cc_disabled_description 0x7f120076
int string exo_controls_cc_enabled_description 0x7f120077
int string exo_controls_custom_playback_speed 0x7f120078
int string exo_controls_fastforward_description 0x7f120079
int string exo_controls_fullscreen_enter_description 0x7f12007a
int string exo_controls_fullscreen_exit_description 0x7f12007b
int string exo_controls_hide 0x7f12007c
int string exo_controls_next_description 0x7f12007d
int string exo_controls_overflow_hide_description 0x7f12007e
int string exo_controls_overflow_show_description 0x7f12007f
int string exo_controls_pause_description 0x7f120080
int string exo_controls_play_description 0x7f120081
int string exo_controls_playback_speed 0x7f120082
int string exo_controls_previous_description 0x7f120083
int string exo_controls_repeat_all_description 0x7f120084
int string exo_controls_repeat_off_description 0x7f120085
int string exo_controls_repeat_one_description 0x7f120086
int string exo_controls_rewind_description 0x7f120087
int string exo_controls_seek_bar_description 0x7f120088
int string exo_controls_settings_description 0x7f120089
int string exo_controls_show 0x7f12008a
int string exo_controls_shuffle_off_description 0x7f12008b
int string exo_controls_shuffle_on_description 0x7f12008c
int string exo_controls_stop_description 0x7f12008d
int string exo_controls_time_placeholder 0x7f12008e
int string exo_controls_vr_description 0x7f12008f
int string exo_download_completed 0x7f120090
int string exo_download_description 0x7f120091
int string exo_download_downloading 0x7f120092
int string exo_download_failed 0x7f120093
int string exo_download_notification_channel_name 0x7f120094
int string exo_download_paused 0x7f120095
int string exo_download_paused_for_network 0x7f120096
int string exo_download_paused_for_wifi 0x7f120097
int string exo_download_removing 0x7f120098
int string exo_item_list 0x7f120099
int string exo_track_bitrate 0x7f12009a
int string exo_track_mono 0x7f12009b
int string exo_track_resolution 0x7f12009c
int string exo_track_role_alternate 0x7f12009d
int string exo_track_role_closed_captions 0x7f12009e
int string exo_track_role_commentary 0x7f12009f
int string exo_track_role_supplementary 0x7f1200a0
int string exo_track_selection_auto 0x7f1200a1
int string exo_track_selection_none 0x7f1200a2
int string exo_track_selection_title_audio 0x7f1200a3
int string exo_track_selection_title_text 0x7f1200a4
int string exo_track_selection_title_video 0x7f1200a5
int string exo_track_stereo 0x7f1200a6
int string exo_track_surround 0x7f1200a7
int string exo_track_surround_5_point_1 0x7f1200a8
int string exo_track_surround_7_point_1 0x7f1200a9
int string exo_track_unknown 0x7f1200aa
int string expand_button_title 0x7f1200ab
int string expanded 0x7f1200ac
int string exposed_dropdown_menu_content_description 0x7f1200ad
int string fab_transformation_scrim_behavior 0x7f1200ae
int string fab_transformation_sheet_behavior 0x7f1200af
int string fcm_fallback_notification_channel_label 0x7f1200b0
int string gcm_defaultSenderId 0x7f1200b1
int string google_api_key 0x7f1200b2
int string google_app_id 0x7f1200b3
int string google_crash_reporting_api_key 0x7f1200b4
int string google_storage_bucket 0x7f1200b5
int string hide_bottom_view_on_scroll_behavior 0x7f1200b6
int string icon_content_description 0x7f1200b7
int string in_progress 0x7f1200b8
int string indeterminate 0x7f1200b9
int string item_view_role_description 0x7f1200ba
int string lb_control_display_fast_forward_multiplier 0x7f1200bb
int string lb_control_display_rewind_multiplier 0x7f1200bc
int string lb_guidedaction_continue_title 0x7f1200bd
int string lb_guidedaction_finish_title 0x7f1200be
int string lb_media_player_error 0x7f1200bf
int string lb_navigation_menu_contentDescription 0x7f1200c0
int string lb_onboarding_accessibility_next 0x7f1200c1
int string lb_onboarding_get_started 0x7f1200c2
int string lb_playback_controls_closed_captioning_disable 0x7f1200c3
int string lb_playback_controls_closed_captioning_enable 0x7f1200c4
int string lb_playback_controls_fast_forward 0x7f1200c5
int string lb_playback_controls_fast_forward_multiplier 0x7f1200c6
int string lb_playback_controls_hidden 0x7f1200c7
int string lb_playback_controls_high_quality_disable 0x7f1200c8
int string lb_playback_controls_high_quality_enable 0x7f1200c9
int string lb_playback_controls_more_actions 0x7f1200ca
int string lb_playback_controls_pause 0x7f1200cb
int string lb_playback_controls_picture_in_picture 0x7f1200cc
int string lb_playback_controls_play 0x7f1200cd
int string lb_playback_controls_repeat_all 0x7f1200ce
int string lb_playback_controls_repeat_none 0x7f1200cf
int string lb_playback_controls_repeat_one 0x7f1200d0
int string lb_playback_controls_rewind 0x7f1200d1
int string lb_playback_controls_rewind_multiplier 0x7f1200d2
int string lb_playback_controls_shown 0x7f1200d3
int string lb_playback_controls_shuffle_disable 0x7f1200d4
int string lb_playback_controls_shuffle_enable 0x7f1200d5
int string lb_playback_controls_skip_next 0x7f1200d6
int string lb_playback_controls_skip_previous 0x7f1200d7
int string lb_playback_controls_thumb_down 0x7f1200d8
int string lb_playback_controls_thumb_down_outline 0x7f1200d9
int string lb_playback_controls_thumb_up 0x7f1200da
int string lb_playback_controls_thumb_up_outline 0x7f1200db
int string lb_playback_time_separator 0x7f1200dc
int string lb_search_bar_hint 0x7f1200dd
int string lb_search_bar_hint_speech 0x7f1200de
int string lb_search_bar_hint_with_title 0x7f1200df
int string lb_search_bar_hint_with_title_speech 0x7f1200e0
int string loading 0x7f1200e1
int string m3c_bottom_sheet_pane_title 0x7f1200e2
int string material_clock_display_divider 0x7f1200e3
int string material_clock_toggle_content_description 0x7f1200e4
int string material_hour_selection 0x7f1200e5
int string material_hour_suffix 0x7f1200e6
int string material_minute_selection 0x7f1200e7
int string material_minute_suffix 0x7f1200e8
int string material_motion_easing_accelerated 0x7f1200e9
int string material_motion_easing_decelerated 0x7f1200ea
int string material_motion_easing_emphasized 0x7f1200eb
int string material_motion_easing_linear 0x7f1200ec
int string material_motion_easing_standard 0x7f1200ed
int string material_slider_range_end 0x7f1200ee
int string material_slider_range_start 0x7f1200ef
int string material_timepicker_am 0x7f1200f0
int string material_timepicker_clock_mode_description 0x7f1200f1
int string material_timepicker_hour 0x7f1200f2
int string material_timepicker_minute 0x7f1200f3
int string material_timepicker_pm 0x7f1200f4
int string material_timepicker_select_time 0x7f1200f5
int string material_timepicker_text_input_mode_description 0x7f1200f6
int string media3_controls_pause_description 0x7f1200f7
int string media3_controls_play_description 0x7f1200f8
int string media3_controls_seek_back_description 0x7f1200f9
int string media3_controls_seek_forward_description 0x7f1200fa
int string media3_controls_seek_to_next_description 0x7f1200fb
int string media3_controls_seek_to_previous_description 0x7f1200fc
int string mtrl_badge_numberless_content_description 0x7f1200fd
int string mtrl_chip_close_icon_content_description 0x7f1200fe
int string mtrl_exceed_max_badge_number_content_description 0x7f1200ff
int string mtrl_exceed_max_badge_number_suffix 0x7f120100
int string mtrl_picker_a11y_next_month 0x7f120101
int string mtrl_picker_a11y_prev_month 0x7f120102
int string mtrl_picker_announce_current_selection 0x7f120103
int string mtrl_picker_cancel 0x7f120104
int string mtrl_picker_confirm 0x7f120105
int string mtrl_picker_date_header_selected 0x7f120106
int string mtrl_picker_date_header_title 0x7f120107
int string mtrl_picker_date_header_unselected 0x7f120108
int string mtrl_picker_day_of_week_column_header 0x7f120109
int string mtrl_picker_invalid_format 0x7f12010a
int string mtrl_picker_invalid_format_example 0x7f12010b
int string mtrl_picker_invalid_format_use 0x7f12010c
int string mtrl_picker_invalid_range 0x7f12010d
int string mtrl_picker_navigate_to_year_description 0x7f12010e
int string mtrl_picker_out_of_range 0x7f12010f
int string mtrl_picker_range_header_only_end_selected 0x7f120110
int string mtrl_picker_range_header_only_start_selected 0x7f120111
int string mtrl_picker_range_header_selected 0x7f120112
int string mtrl_picker_range_header_title 0x7f120113
int string mtrl_picker_range_header_unselected 0x7f120114
int string mtrl_picker_save 0x7f120115
int string mtrl_picker_text_input_date_hint 0x7f120116
int string mtrl_picker_text_input_date_range_end_hint 0x7f120117
int string mtrl_picker_text_input_date_range_start_hint 0x7f120118
int string mtrl_picker_text_input_day_abbr 0x7f120119
int string mtrl_picker_text_input_month_abbr 0x7f12011a
int string mtrl_picker_text_input_year_abbr 0x7f12011b
int string mtrl_picker_toggle_to_calendar_input_mode 0x7f12011c
int string mtrl_picker_toggle_to_day_selection 0x7f12011d
int string mtrl_picker_toggle_to_text_input_mode 0x7f12011e
int string mtrl_picker_toggle_to_year_selection 0x7f12011f
int string nav_app_bar_navigate_up_description 0x7f120120
int string nav_app_bar_open_drawer_description 0x7f120121
int string nav_downloads 0x7f120122
int string nav_favorites 0x7f120123
int string nav_home 0x7f120124
int string nav_live_tv 0x7f120125
int string nav_movies 0x7f120126
int string nav_series 0x7f120127
int string nav_settings 0x7f120128
int string navigation_menu 0x7f120129
int string no_internet 0x7f12012a
int string not_selected 0x7f12012b
int string not_set 0x7f12012c
int string off 0x7f12012d
int string ok 0x7f12012e
int string on 0x7f12012f
int string orb_search_action 0x7f120130
int string password_toggle_content_description 0x7f120131
int string path_password_eye 0x7f120132
int string path_password_eye_mask_strike_through 0x7f120133
int string path_password_eye_mask_visible 0x7f120134
int string path_password_strike_through 0x7f120135
int string player_audio 0x7f120136
int string player_exit_fullscreen 0x7f120137
int string player_fullscreen 0x7f120138
int string player_next 0x7f120139
int string player_pause 0x7f12013a
int string player_play 0x7f12013b
int string player_previous 0x7f12013c
int string player_quality 0x7f12013d
int string player_settings 0x7f12013e
int string player_speed 0x7f12013f
int string player_subtitle 0x7f120140
int string preference_copied 0x7f120141
int string premium_required 0x7f120142
int string project_id 0x7f120143
int string range_end 0x7f120144
int string range_start 0x7f120145
int string retry 0x7f120146
int string search 0x7f120147
int string search_bar_search 0x7f120148
int string search_menu_title 0x7f120149
int string selected 0x7f12014a
int string snackbar_dismiss 0x7f12014b
int string status_bar_notification_info_overflow 0x7f12014c
int string suggestions_available 0x7f12014d
int string summary_collapsed_preference_list 0x7f12014e
int string switch_role 0x7f12014f
int string tab 0x7f120150
int string template_percent 0x7f120151
int string time_picker_am 0x7f120152
int string time_picker_hour 0x7f120153
int string time_picker_hour_24h_suffix 0x7f120154
int string time_picker_hour_selection 0x7f120155
int string time_picker_hour_suffix 0x7f120156
int string time_picker_hour_text_field 0x7f120157
int string time_picker_minute 0x7f120158
int string time_picker_minute_selection 0x7f120159
int string time_picker_minute_suffix 0x7f12015a
int string time_picker_minute_text_field 0x7f12015b
int string time_picker_period_toggle_description 0x7f12015c
int string time_picker_pm 0x7f12015d
int string tooltip_long_press_label 0x7f12015e
int string tooltip_pane_description 0x7f12015f
int string tv_app_description 0x7f120160
int string v7_preference_off 0x7f120161
int string v7_preference_on 0x7f120162
int style AlertDialog_AppCompat 0x7f130000
int style AlertDialog_AppCompat_Light 0x7f130001
int style AndroidThemeColorAccentYellow 0x7f130002
int style Animation_AppCompat_Dialog 0x7f130003
int style Animation_AppCompat_DropDownUp 0x7f130004
int style Animation_AppCompat_Tooltip 0x7f130005
int style Animation_Design_BottomSheetDialog 0x7f130006
int style Animation_MaterialComponents_BottomSheetDialog 0x7f130007
int style Base_AlertDialog_AppCompat 0x7f130008
int style Base_AlertDialog_AppCompat_Light 0x7f130009
int style Base_Animation_AppCompat_Dialog 0x7f13000a
int style Base_Animation_AppCompat_DropDownUp 0x7f13000b
int style Base_Animation_AppCompat_Tooltip 0x7f13000c
int style Base_CardView 0x7f13000d
int style Base_DialogWindowTitle_AppCompat 0x7f13000e
int style Base_DialogWindowTitleBackground_AppCompat 0x7f13000f
int style Base_MaterialAlertDialog_MaterialComponents_Title_Icon 0x7f130010
int style Base_MaterialAlertDialog_MaterialComponents_Title_Panel 0x7f130011
int style Base_MaterialAlertDialog_MaterialComponents_Title_Text 0x7f130012
int style Base_TextAppearance_AppCompat 0x7f130013
int style Base_TextAppearance_AppCompat_Body1 0x7f130014
int style Base_TextAppearance_AppCompat_Body2 0x7f130015
int style Base_TextAppearance_AppCompat_Button 0x7f130016
int style Base_TextAppearance_AppCompat_Caption 0x7f130017
int style Base_TextAppearance_AppCompat_Display1 0x7f130018
int style Base_TextAppearance_AppCompat_Display2 0x7f130019
int style Base_TextAppearance_AppCompat_Display3 0x7f13001a
int style Base_TextAppearance_AppCompat_Display4 0x7f13001b
int style Base_TextAppearance_AppCompat_Headline 0x7f13001c
int style Base_TextAppearance_AppCompat_Inverse 0x7f13001d
int style Base_TextAppearance_AppCompat_Large 0x7f13001e
int style Base_TextAppearance_AppCompat_Large_Inverse 0x7f13001f
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f130020
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f130021
int style Base_TextAppearance_AppCompat_Medium 0x7f130022
int style Base_TextAppearance_AppCompat_Medium_Inverse 0x7f130023
int style Base_TextAppearance_AppCompat_Menu 0x7f130024
int style Base_TextAppearance_AppCompat_SearchResult 0x7f130025
int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x7f130026
int style Base_TextAppearance_AppCompat_SearchResult_Title 0x7f130027
int style Base_TextAppearance_AppCompat_Small 0x7f130028
int style Base_TextAppearance_AppCompat_Small_Inverse 0x7f130029
int style Base_TextAppearance_AppCompat_Subhead 0x7f13002a
int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x7f13002b
int style Base_TextAppearance_AppCompat_Title 0x7f13002c
int style Base_TextAppearance_AppCompat_Title_Inverse 0x7f13002d
int style Base_TextAppearance_AppCompat_Tooltip 0x7f13002e
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f13002f
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f130030
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f130031
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f130032
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f130033
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f130034
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f130035
int style Base_TextAppearance_AppCompat_Widget_Button 0x7f130036
int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f130037
int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x7f130038
int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x7f130039
int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x7f13003a
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f13003b
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f13003c
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f13003d
int style Base_TextAppearance_AppCompat_Widget_Switch 0x7f13003e
int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f13003f
int style Base_TextAppearance_MaterialComponents_Badge 0x7f130040
int style Base_TextAppearance_MaterialComponents_Button 0x7f130041
int style Base_TextAppearance_MaterialComponents_Headline6 0x7f130042
int style Base_TextAppearance_MaterialComponents_Subtitle2 0x7f130043
int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f130044
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f130045
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f130046
int style Base_Theme_AppCompat 0x7f130047
int style Base_Theme_AppCompat_CompactMenu 0x7f130048
int style Base_Theme_AppCompat_Dialog 0x7f130049
int style Base_Theme_AppCompat_Dialog_Alert 0x7f13004a
int style Base_Theme_AppCompat_Dialog_FixedSize 0x7f13004b
int style Base_Theme_AppCompat_Dialog_MinWidth 0x7f13004c
int style Base_Theme_AppCompat_DialogWhenLarge 0x7f13004d
int style Base_Theme_AppCompat_Light 0x7f13004e
int style Base_Theme_AppCompat_Light_DarkActionBar 0x7f13004f
int style Base_Theme_AppCompat_Light_Dialog 0x7f130050
int style Base_Theme_AppCompat_Light_Dialog_Alert 0x7f130051
int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x7f130052
int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x7f130053
int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x7f130054
int style Base_Theme_MaterialComponents 0x7f130055
int style Base_Theme_MaterialComponents_Bridge 0x7f130056
int style Base_Theme_MaterialComponents_CompactMenu 0x7f130057
int style Base_Theme_MaterialComponents_Dialog 0x7f130058
int style Base_Theme_MaterialComponents_Dialog_Alert 0x7f130059
int style Base_Theme_MaterialComponents_Dialog_Bridge 0x7f13005a
int style Base_Theme_MaterialComponents_Dialog_FixedSize 0x7f13005b
int style Base_Theme_MaterialComponents_Dialog_MinWidth 0x7f13005c
int style Base_Theme_MaterialComponents_DialogWhenLarge 0x7f13005d
int style Base_Theme_MaterialComponents_Light 0x7f13005e
int style Base_Theme_MaterialComponents_Light_Bridge 0x7f13005f
int style Base_Theme_MaterialComponents_Light_DarkActionBar 0x7f130060
int style Base_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f130061
int style Base_Theme_MaterialComponents_Light_Dialog 0x7f130062
int style Base_Theme_MaterialComponents_Light_Dialog_Alert 0x7f130063
int style Base_Theme_MaterialComponents_Light_Dialog_Bridge 0x7f130064
int style Base_Theme_MaterialComponents_Light_Dialog_FixedSize 0x7f130065
int style Base_Theme_MaterialComponents_Light_Dialog_MinWidth 0x7f130066
int style Base_Theme_MaterialComponents_Light_DialogWhenLarge 0x7f130067
int style Base_ThemeOverlay_AppCompat 0x7f130068
int style Base_ThemeOverlay_AppCompat_ActionBar 0x7f130069
int style Base_ThemeOverlay_AppCompat_Dark 0x7f13006a
int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x7f13006b
int style Base_ThemeOverlay_AppCompat_Dialog 0x7f13006c
int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x7f13006d
int style Base_ThemeOverlay_AppCompat_Light 0x7f13006e
int style Base_ThemeOverlay_MaterialComponents_Dialog 0x7f13006f
int style Base_ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f130070
int style Base_ThemeOverlay_MaterialComponents_Dialog_Alert_Framework 0x7f130071
int style Base_ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework 0x7f130072
int style Base_ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f130073
int style Base_V14_Theme_MaterialComponents 0x7f130074
int style Base_V14_Theme_MaterialComponents_Bridge 0x7f130075
int style Base_V14_Theme_MaterialComponents_Dialog 0x7f130076
int style Base_V14_Theme_MaterialComponents_Dialog_Bridge 0x7f130077
int style Base_V14_Theme_MaterialComponents_Light 0x7f130078
int style Base_V14_Theme_MaterialComponents_Light_Bridge 0x7f130079
int style Base_V14_Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f13007a
int style Base_V14_Theme_MaterialComponents_Light_Dialog 0x7f13007b
int style Base_V14_Theme_MaterialComponents_Light_Dialog_Bridge 0x7f13007c
int style Base_V14_ThemeOverlay_MaterialComponents_BottomSheetDialog 0x7f13007d
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog 0x7f13007e
int style Base_V14_ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f13007f
int style Base_V14_ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f130080
int style Base_V21_Theme_AppCompat 0x7f130081
int style Base_V21_Theme_AppCompat_Dialog 0x7f130082
int style Base_V21_Theme_AppCompat_Light 0x7f130083
int style Base_V21_Theme_AppCompat_Light_Dialog 0x7f130084
int style Base_V21_Theme_MaterialComponents 0x7f130085
int style Base_V21_Theme_MaterialComponents_Dialog 0x7f130086
int style Base_V21_Theme_MaterialComponents_Light 0x7f130087
int style Base_V21_Theme_MaterialComponents_Light_Dialog 0x7f130088
int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x7f130089
int style Base_V21_ThemeOverlay_MaterialComponents_BottomSheetDialog 0x7f13008a
int style Base_V22_Theme_AppCompat 0x7f13008b
int style Base_V22_Theme_AppCompat_Light 0x7f13008c
int style Base_V23_Theme_AppCompat 0x7f13008d
int style Base_V23_Theme_AppCompat_Light 0x7f13008e
int style Base_V26_Theme_AppCompat 0x7f13008f
int style Base_V26_Theme_AppCompat_Light 0x7f130090
int style Base_V26_Widget_AppCompat_Toolbar 0x7f130091
int style Base_V28_Theme_AppCompat 0x7f130092
int style Base_V28_Theme_AppCompat_Light 0x7f130093
int style Base_V7_Theme_AppCompat 0x7f130094
int style Base_V7_Theme_AppCompat_Dialog 0x7f130095
int style Base_V7_Theme_AppCompat_Light 0x7f130096
int style Base_V7_Theme_AppCompat_Light_Dialog 0x7f130097
int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x7f130098
int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x7f130099
int style Base_V7_Widget_AppCompat_EditText 0x7f13009a
int style Base_V7_Widget_AppCompat_Toolbar 0x7f13009b
int style Base_Widget_AppCompat_ActionBar 0x7f13009c
int style Base_Widget_AppCompat_ActionBar_Solid 0x7f13009d
int style Base_Widget_AppCompat_ActionBar_TabBar 0x7f13009e
int style Base_Widget_AppCompat_ActionBar_TabText 0x7f13009f
int style Base_Widget_AppCompat_ActionBar_TabView 0x7f1300a0
int style Base_Widget_AppCompat_ActionButton 0x7f1300a1
int style Base_Widget_AppCompat_ActionButton_CloseMode 0x7f1300a2
int style Base_Widget_AppCompat_ActionButton_Overflow 0x7f1300a3
int style Base_Widget_AppCompat_ActionMode 0x7f1300a4
int style Base_Widget_AppCompat_ActivityChooserView 0x7f1300a5
int style Base_Widget_AppCompat_AutoCompleteTextView 0x7f1300a6
int style Base_Widget_AppCompat_Button 0x7f1300a7
int style Base_Widget_AppCompat_Button_Borderless 0x7f1300a8
int style Base_Widget_AppCompat_Button_Borderless_Colored 0x7f1300a9
int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f1300aa
int style Base_Widget_AppCompat_Button_Colored 0x7f1300ab
int style Base_Widget_AppCompat_Button_Small 0x7f1300ac
int style Base_Widget_AppCompat_ButtonBar 0x7f1300ad
int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x7f1300ae
int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x7f1300af
int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x7f1300b0
int style Base_Widget_AppCompat_CompoundButton_Switch 0x7f1300b1
int style Base_Widget_AppCompat_DrawerArrowToggle 0x7f1300b2
int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x7f1300b3
int style Base_Widget_AppCompat_DropDownItem_Spinner 0x7f1300b4
int style Base_Widget_AppCompat_EditText 0x7f1300b5
int style Base_Widget_AppCompat_ImageButton 0x7f1300b6
int style Base_Widget_AppCompat_Light_ActionBar 0x7f1300b7
int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x7f1300b8
int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x7f1300b9
int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x7f1300ba
int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f1300bb
int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x7f1300bc
int style Base_Widget_AppCompat_Light_PopupMenu 0x7f1300bd
int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x7f1300be
int style Base_Widget_AppCompat_ListMenuView 0x7f1300bf
int style Base_Widget_AppCompat_ListPopupWindow 0x7f1300c0
int style Base_Widget_AppCompat_ListView 0x7f1300c1
int style Base_Widget_AppCompat_ListView_DropDown 0x7f1300c2
int style Base_Widget_AppCompat_ListView_Menu 0x7f1300c3
int style Base_Widget_AppCompat_PopupMenu 0x7f1300c4
int style Base_Widget_AppCompat_PopupMenu_Overflow 0x7f1300c5
int style Base_Widget_AppCompat_PopupWindow 0x7f1300c6
int style Base_Widget_AppCompat_ProgressBar 0x7f1300c7
int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x7f1300c8
int style Base_Widget_AppCompat_RatingBar 0x7f1300c9
int style Base_Widget_AppCompat_RatingBar_Indicator 0x7f1300ca
int style Base_Widget_AppCompat_RatingBar_Small 0x7f1300cb
int style Base_Widget_AppCompat_SearchView 0x7f1300cc
int style Base_Widget_AppCompat_SearchView_ActionBar 0x7f1300cd
int style Base_Widget_AppCompat_SeekBar 0x7f1300ce
int style Base_Widget_AppCompat_SeekBar_Discrete 0x7f1300cf
int style Base_Widget_AppCompat_Spinner 0x7f1300d0
int style Base_Widget_AppCompat_Spinner_Underlined 0x7f1300d1
int style Base_Widget_AppCompat_TextView 0x7f1300d2
int style Base_Widget_AppCompat_TextView_SpinnerItem 0x7f1300d3
int style Base_Widget_AppCompat_Toolbar 0x7f1300d4
int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x7f1300d5
int style Base_Widget_Design_TabLayout 0x7f1300d6
int style Base_Widget_MaterialComponents_AutoCompleteTextView 0x7f1300d7
int style Base_Widget_MaterialComponents_CheckedTextView 0x7f1300d8
int style Base_Widget_MaterialComponents_Chip 0x7f1300d9
int style Base_Widget_MaterialComponents_MaterialCalendar_NavigationButton 0x7f1300da
int style Base_Widget_MaterialComponents_PopupMenu 0x7f1300db
int style Base_Widget_MaterialComponents_PopupMenu_ContextMenu 0x7f1300dc
int style Base_Widget_MaterialComponents_PopupMenu_ListPopupWindow 0x7f1300dd
int style Base_Widget_MaterialComponents_PopupMenu_Overflow 0x7f1300de
int style Base_Widget_MaterialComponents_Slider 0x7f1300df
int style Base_Widget_MaterialComponents_Snackbar 0x7f1300e0
int style Base_Widget_MaterialComponents_TextInputEditText 0x7f1300e1
int style Base_Widget_MaterialComponents_TextInputLayout 0x7f1300e2
int style Base_Widget_MaterialComponents_TextView 0x7f1300e3
int style BasePreferenceThemeOverlay 0x7f1300e4
int style CardView 0x7f1300e5
int style CardView_Dark 0x7f1300e6
int style CardView_Light 0x7f1300e7
int style DialogWindowTheme 0x7f1300e8
int style EmptyTheme 0x7f1300e9
int style ExoMediaButton 0x7f1300ea
int style ExoMediaButton_FastForward 0x7f1300eb
int style ExoMediaButton_Next 0x7f1300ec
int style ExoMediaButton_Pause 0x7f1300ed
int style ExoMediaButton_Play 0x7f1300ee
int style ExoMediaButton_Previous 0x7f1300ef
int style ExoMediaButton_Rewind 0x7f1300f0
int style ExoMediaButton_VR 0x7f1300f1
int style ExoStyledControls 0x7f1300f2
int style ExoStyledControls_Button 0x7f1300f3
int style ExoStyledControls_Button_Bottom 0x7f1300f4
int style ExoStyledControls_Button_Bottom_AudioTrack 0x7f1300f5
int style ExoStyledControls_Button_Bottom_CC 0x7f1300f6
int style ExoStyledControls_Button_Bottom_FullScreen 0x7f1300f7
int style ExoStyledControls_Button_Bottom_OverflowHide 0x7f1300f8
int style ExoStyledControls_Button_Bottom_OverflowShow 0x7f1300f9
int style ExoStyledControls_Button_Bottom_PlaybackSpeed 0x7f1300fa
int style ExoStyledControls_Button_Bottom_RepeatToggle 0x7f1300fb
int style ExoStyledControls_Button_Bottom_Settings 0x7f1300fc
int style ExoStyledControls_Button_Bottom_Shuffle 0x7f1300fd
int style ExoStyledControls_Button_Bottom_VR 0x7f1300fe
int style ExoStyledControls_Button_Center 0x7f1300ff
int style ExoStyledControls_Button_Center_FfwdWithAmount 0x7f130100
int style ExoStyledControls_Button_Center_Next 0x7f130101
int style ExoStyledControls_Button_Center_PlayPause 0x7f130102
int style ExoStyledControls_Button_Center_Previous 0x7f130103
int style ExoStyledControls_Button_Center_RewWithAmount 0x7f130104
int style ExoStyledControls_TimeBar 0x7f130105
int style ExoStyledControls_TimeText 0x7f130106
int style ExoStyledControls_TimeText_Duration 0x7f130107
int style ExoStyledControls_TimeText_Position 0x7f130108
int style ExoStyledControls_TimeText_Separator 0x7f130109
int style FloatingDialogTheme 0x7f13010a
int style FloatingDialogWindowTheme 0x7f13010b
int style LeanbackPreference 0x7f13010c
int style LeanbackPreference_Category 0x7f13010d
int style LeanbackPreference_CheckBoxPreference 0x7f13010e
int style LeanbackPreference_DialogPreference 0x7f13010f
int style LeanbackPreference_DialogPreference_EditTextPreference 0x7f130110
int style LeanbackPreference_Information 0x7f130111
int style LeanbackPreference_PreferenceScreen 0x7f130112
int style LeanbackPreference_SeekBarPreference 0x7f130113
int style LeanbackPreference_SwitchPreference 0x7f130114
int style LeanbackPreference_SwitchPreferenceCompat 0x7f130115
int style MaterialAlertDialog_MaterialComponents 0x7f130116
int style MaterialAlertDialog_MaterialComponents_Body_Text 0x7f130117
int style MaterialAlertDialog_MaterialComponents_Picker_Date_Calendar 0x7f130118
int style MaterialAlertDialog_MaterialComponents_Picker_Date_Spinner 0x7f130119
int style MaterialAlertDialog_MaterialComponents_Title_Icon 0x7f13011a
int style MaterialAlertDialog_MaterialComponents_Title_Icon_CenterStacked 0x7f13011b
int style MaterialAlertDialog_MaterialComponents_Title_Panel 0x7f13011c
int style MaterialAlertDialog_MaterialComponents_Title_Panel_CenterStacked 0x7f13011d
int style MaterialAlertDialog_MaterialComponents_Title_Text 0x7f13011e
int style MaterialAlertDialog_MaterialComponents_Title_Text_CenterStacked 0x7f13011f
int style Platform_AppCompat 0x7f130120
int style Platform_AppCompat_Light 0x7f130121
int style Platform_MaterialComponents 0x7f130122
int style Platform_MaterialComponents_Dialog 0x7f130123
int style Platform_MaterialComponents_Light 0x7f130124
int style Platform_MaterialComponents_Light_Dialog 0x7f130125
int style Platform_ThemeOverlay_AppCompat 0x7f130126
int style Platform_ThemeOverlay_AppCompat_Dark 0x7f130127
int style Platform_ThemeOverlay_AppCompat_Light 0x7f130128
int style Platform_V21_AppCompat 0x7f130129
int style Platform_V21_AppCompat_Light 0x7f13012a
int style Platform_V25_AppCompat 0x7f13012b
int style Platform_V25_AppCompat_Light 0x7f13012c
int style Platform_Widget_AppCompat_Spinner 0x7f13012d
int style Preference 0x7f13012e
int style Preference_Category 0x7f13012f
int style Preference_Category_Material 0x7f130130
int style Preference_CheckBoxPreference 0x7f130131
int style Preference_CheckBoxPreference_Material 0x7f130132
int style Preference_DialogPreference 0x7f130133
int style Preference_DialogPreference_EditTextPreference 0x7f130134
int style Preference_DialogPreference_EditTextPreference_Material 0x7f130135
int style Preference_DialogPreference_Material 0x7f130136
int style Preference_DropDown 0x7f130137
int style Preference_DropDown_Material 0x7f130138
int style Preference_Information 0x7f130139
int style Preference_Information_Material 0x7f13013a
int style Preference_Material 0x7f13013b
int style Preference_PreferenceScreen 0x7f13013c
int style Preference_PreferenceScreen_Material 0x7f13013d
int style Preference_SeekBarPreference 0x7f13013e
int style Preference_SeekBarPreference_Material 0x7f13013f
int style Preference_SwitchPreference 0x7f130140
int style Preference_SwitchPreference_Material 0x7f130141
int style Preference_SwitchPreferenceCompat 0x7f130142
int style Preference_SwitchPreferenceCompat_Material 0x7f130143
int style PreferenceCategoryTitleTextStyle 0x7f130144
int style PreferenceFragment 0x7f130145
int style PreferenceFragment_Leanback 0x7f130146
int style PreferenceFragment_Material 0x7f130147
int style PreferenceFragmentList 0x7f130148
int style PreferenceFragmentList_Leanback 0x7f130149
int style PreferenceFragmentList_Material 0x7f13014a
int style PreferenceSummaryTextStyle 0x7f13014b
int style PreferenceThemeOverlay 0x7f13014c
int style PreferenceThemeOverlay_v14 0x7f13014d
int style PreferenceThemeOverlay_v14_Leanback 0x7f13014e
int style PreferenceThemeOverlay_v14_Material 0x7f13014f
int style PreferenceThemeOverlayLeanback 0x7f130150
int style RtlOverlay_DialogWindowTitle_AppCompat 0x7f130151
int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x7f130152
int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x7f130153
int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x7f130154
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x7f130155
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x7f130156
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x7f130157
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x7f130158
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x7f130159
int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x7f13015a
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x7f13015b
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x7f13015c
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x7f13015d
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x7f13015e
int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x7f13015f
int style RtlUnderlay_Widget_AppCompat_ActionButton 0x7f130160
int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x7f130161
int style ShapeAppearance_MaterialComponents 0x7f130162
int style ShapeAppearance_MaterialComponents_LargeComponent 0x7f130163
int style ShapeAppearance_MaterialComponents_MediumComponent 0x7f130164
int style ShapeAppearance_MaterialComponents_SmallComponent 0x7f130165
int style ShapeAppearance_MaterialComponents_Test 0x7f130166
int style ShapeAppearance_MaterialComponents_Tooltip 0x7f130167
int style ShapeAppearanceOverlay 0x7f130168
int style ShapeAppearanceOverlay_BottomLeftDifferentCornerSize 0x7f130169
int style ShapeAppearanceOverlay_BottomRightCut 0x7f13016a
int style ShapeAppearanceOverlay_Cut 0x7f13016b
int style ShapeAppearanceOverlay_DifferentCornerSize 0x7f13016c
int style ShapeAppearanceOverlay_MaterialComponents_BottomSheet 0x7f13016d
int style ShapeAppearanceOverlay_MaterialComponents_Chip 0x7f13016e
int style ShapeAppearanceOverlay_MaterialComponents_ExtendedFloatingActionButton 0x7f13016f
int style ShapeAppearanceOverlay_MaterialComponents_FloatingActionButton 0x7f130170
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day 0x7f130171
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Window_Fullscreen 0x7f130172
int style ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Year 0x7f130173
int style ShapeAppearanceOverlay_MaterialComponents_TextInputLayout_FilledBox 0x7f130174
int style ShapeAppearanceOverlay_TopLeftCut 0x7f130175
int style ShapeAppearanceOverlay_TopRightDifferentCornerSize 0x7f130176
int style Test_ShapeAppearanceOverlay_MaterialComponents_MaterialCalendar_Day 0x7f130177
int style Test_Theme_MaterialComponents_MaterialCalendar 0x7f130178
int style Test_Widget_MaterialComponents_MaterialCalendar 0x7f130179
int style Test_Widget_MaterialComponents_MaterialCalendar_Day 0x7f13017a
int style Test_Widget_MaterialComponents_MaterialCalendar_Day_Selected 0x7f13017b
int style TestStyleWithLineHeight 0x7f13017c
int style TestStyleWithLineHeightAppearance 0x7f13017d
int style TestStyleWithThemeLineHeightAttribute 0x7f13017e
int style TestStyleWithoutLineHeight 0x7f13017f
int style TestThemeWithLineHeight 0x7f130180
int style TestThemeWithLineHeightDisabled 0x7f130181
int style TextAppearance_AppCompat 0x7f130182
int style TextAppearance_AppCompat_Body1 0x7f130183
int style TextAppearance_AppCompat_Body2 0x7f130184
int style TextAppearance_AppCompat_Button 0x7f130185
int style TextAppearance_AppCompat_Caption 0x7f130186
int style TextAppearance_AppCompat_Display1 0x7f130187
int style TextAppearance_AppCompat_Display2 0x7f130188
int style TextAppearance_AppCompat_Display3 0x7f130189
int style TextAppearance_AppCompat_Display4 0x7f13018a
int style TextAppearance_AppCompat_Headline 0x7f13018b
int style TextAppearance_AppCompat_Inverse 0x7f13018c
int style TextAppearance_AppCompat_Large 0x7f13018d
int style TextAppearance_AppCompat_Large_Inverse 0x7f13018e
int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x7f13018f
int style TextAppearance_AppCompat_Light_SearchResult_Title 0x7f130190
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f130191
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f130192
int style TextAppearance_AppCompat_Medium 0x7f130193
int style TextAppearance_AppCompat_Medium_Inverse 0x7f130194
int style TextAppearance_AppCompat_Menu 0x7f130195
int style TextAppearance_AppCompat_SearchResult_Subtitle 0x7f130196
int style TextAppearance_AppCompat_SearchResult_Title 0x7f130197
int style TextAppearance_AppCompat_Small 0x7f130198
int style TextAppearance_AppCompat_Small_Inverse 0x7f130199
int style TextAppearance_AppCompat_Subhead 0x7f13019a
int style TextAppearance_AppCompat_Subhead_Inverse 0x7f13019b
int style TextAppearance_AppCompat_Title 0x7f13019c
int style TextAppearance_AppCompat_Title_Inverse 0x7f13019d
int style TextAppearance_AppCompat_Tooltip 0x7f13019e
int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f13019f
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f1301a0
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f1301a1
int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f1301a2
int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f1301a3
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f1301a4
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x7f1301a5
int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f1301a6
int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x7f1301a7
int style TextAppearance_AppCompat_Widget_Button 0x7f1301a8
int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f1301a9
int style TextAppearance_AppCompat_Widget_Button_Colored 0x7f1301aa
int style TextAppearance_AppCompat_Widget_Button_Inverse 0x7f1301ab
int style TextAppearance_AppCompat_Widget_DropDownItem 0x7f1301ac
int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f1301ad
int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f1301ae
int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f1301af
int style TextAppearance_AppCompat_Widget_Switch 0x7f1301b0
int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f1301b1
int style TextAppearance_Compat_Notification 0x7f1301b2
int style TextAppearance_Compat_Notification_Info 0x7f1301b3
int style TextAppearance_Compat_Notification_Info_Media 0x7f1301b4
int style TextAppearance_Compat_Notification_Line2 0x7f1301b5
int style TextAppearance_Compat_Notification_Line2_Media 0x7f1301b6
int style TextAppearance_Compat_Notification_Media 0x7f1301b7
int style TextAppearance_Compat_Notification_Time 0x7f1301b8
int style TextAppearance_Compat_Notification_Time_Media 0x7f1301b9
int style TextAppearance_Compat_Notification_Title 0x7f1301ba
int style TextAppearance_Compat_Notification_Title_Media 0x7f1301bb
int style TextAppearance_Design_CollapsingToolbar_Expanded 0x7f1301bc
int style TextAppearance_Design_Counter 0x7f1301bd
int style TextAppearance_Design_Counter_Overflow 0x7f1301be
int style TextAppearance_Design_Error 0x7f1301bf
int style TextAppearance_Design_HelperText 0x7f1301c0
int style TextAppearance_Design_Hint 0x7f1301c1
int style TextAppearance_Design_Placeholder 0x7f1301c2
int style TextAppearance_Design_Prefix 0x7f1301c3
int style TextAppearance_Design_Snackbar_Message 0x7f1301c4
int style TextAppearance_Design_Suffix 0x7f1301c5
int style TextAppearance_Design_Tab 0x7f1301c6
int style TextAppearance_Leanback 0x7f1301c7
int style TextAppearance_Leanback_DetailsActionButton 0x7f1301c8
int style TextAppearance_Leanback_DetailsDescriptionBody 0x7f1301c9
int style TextAppearance_Leanback_DetailsDescriptionSubtitle 0x7f1301ca
int style TextAppearance_Leanback_DetailsDescriptionTitle 0x7f1301cb
int style TextAppearance_Leanback_ErrorMessage 0x7f1301cc
int style TextAppearance_Leanback_Header 0x7f1301cd
int style TextAppearance_Leanback_Header_Section 0x7f1301ce
int style TextAppearance_Leanback_ImageCardView 0x7f1301cf
int style TextAppearance_Leanback_ImageCardView_Content 0x7f1301d0
int style TextAppearance_Leanback_ImageCardView_Title 0x7f1301d1
int style TextAppearance_Leanback_PlaybackControlLabel 0x7f1301d2
int style TextAppearance_Leanback_PlaybackControlsTime 0x7f1301d3
int style TextAppearance_Leanback_PlaybackMediaItemDuration 0x7f1301d4
int style TextAppearance_Leanback_PlaybackMediaItemName 0x7f1301d5
int style TextAppearance_Leanback_PlaybackMediaItemNumber 0x7f1301d6
int style TextAppearance_Leanback_PlaybackMediaListHeaderTitle 0x7f1301d7
int style TextAppearance_Leanback_Row_Header 0x7f1301d8
int style TextAppearance_Leanback_Row_Header_Description 0x7f1301d9
int style TextAppearance_Leanback_Row_HoverCardDescription 0x7f1301da
int style TextAppearance_Leanback_Row_HoverCardTitle 0x7f1301db
int style TextAppearance_Leanback_SearchTextEdit 0x7f1301dc
int style TextAppearance_Leanback_Title 0x7f1301dd
int style TextAppearance_LeanbackBase 0x7f1301de
int style TextAppearance_MaterialComponents_Badge 0x7f1301df
int style TextAppearance_MaterialComponents_Body1 0x7f1301e0
int style TextAppearance_MaterialComponents_Body2 0x7f1301e1
int style TextAppearance_MaterialComponents_Button 0x7f1301e2
int style TextAppearance_MaterialComponents_Caption 0x7f1301e3
int style TextAppearance_MaterialComponents_Chip 0x7f1301e4
int style TextAppearance_MaterialComponents_Headline1 0x7f1301e5
int style TextAppearance_MaterialComponents_Headline2 0x7f1301e6
int style TextAppearance_MaterialComponents_Headline3 0x7f1301e7
int style TextAppearance_MaterialComponents_Headline4 0x7f1301e8
int style TextAppearance_MaterialComponents_Headline5 0x7f1301e9
int style TextAppearance_MaterialComponents_Headline6 0x7f1301ea
int style TextAppearance_MaterialComponents_Overline 0x7f1301eb
int style TextAppearance_MaterialComponents_Subtitle1 0x7f1301ec
int style TextAppearance_MaterialComponents_Subtitle2 0x7f1301ed
int style TextAppearance_MaterialComponents_TimePicker_Title 0x7f1301ee
int style TextAppearance_MaterialComponents_Tooltip 0x7f1301ef
int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f1301f0
int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f1301f1
int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f1301f2
int style Theme_AppCompat 0x7f1301f3
int style Theme_AppCompat_CompactMenu 0x7f1301f4
int style Theme_AppCompat_DayNight 0x7f1301f5
int style Theme_AppCompat_DayNight_DarkActionBar 0x7f1301f6
int style Theme_AppCompat_DayNight_Dialog 0x7f1301f7
int style Theme_AppCompat_DayNight_Dialog_Alert 0x7f1301f8
int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x7f1301f9
int style Theme_AppCompat_DayNight_DialogWhenLarge 0x7f1301fa
int style Theme_AppCompat_DayNight_NoActionBar 0x7f1301fb
int style Theme_AppCompat_Dialog 0x7f1301fc
int style Theme_AppCompat_Dialog_Alert 0x7f1301fd
int style Theme_AppCompat_Dialog_MinWidth 0x7f1301fe
int style Theme_AppCompat_DialogWhenLarge 0x7f1301ff
int style Theme_AppCompat_Empty 0x7f130200
int style Theme_AppCompat_Light 0x7f130201
int style Theme_AppCompat_Light_DarkActionBar 0x7f130202
int style Theme_AppCompat_Light_Dialog 0x7f130203
int style Theme_AppCompat_Light_Dialog_Alert 0x7f130204
int style Theme_AppCompat_Light_Dialog_MinWidth 0x7f130205
int style Theme_AppCompat_Light_DialogWhenLarge 0x7f130206
int style Theme_AppCompat_Light_NoActionBar 0x7f130207
int style Theme_AppCompat_NoActionBar 0x7f130208
int style Theme_Cinepix 0x7f130209
int style Theme_Cinepix_Fullscreen 0x7f13020a
int style Theme_Cinepix_Leanback 0x7f13020b
int style Theme_Design 0x7f13020c
int style Theme_Design_BottomSheetDialog 0x7f13020d
int style Theme_Design_Light 0x7f13020e
int style Theme_Design_Light_BottomSheetDialog 0x7f13020f
int style Theme_Design_Light_NoActionBar 0x7f130210
int style Theme_Design_NoActionBar 0x7f130211
int style Theme_Leanback 0x7f130212
int style Theme_Leanback_Browse 0x7f130213
int style Theme_Leanback_Details 0x7f130214
int style Theme_Leanback_Details_NoSharedElementTransition 0x7f130215
int style Theme_Leanback_GuidedStep 0x7f130216
int style Theme_Leanback_GuidedStep_Half 0x7f130217
int style Theme_Leanback_GuidedStep_HalfBase 0x7f130218
int style Theme_Leanback_GuidedStepBase 0x7f130219
int style Theme_Leanback_Onboarding 0x7f13021a
int style Theme_Leanback_VerticalGrid 0x7f13021b
int style Theme_LeanbackBase 0x7f13021c
int style Theme_MaterialComponents 0x7f13021d
int style Theme_MaterialComponents_BottomSheetDialog 0x7f13021e
int style Theme_MaterialComponents_Bridge 0x7f13021f
int style Theme_MaterialComponents_CompactMenu 0x7f130220
int style Theme_MaterialComponents_DayNight 0x7f130221
int style Theme_MaterialComponents_DayNight_BottomSheetDialog 0x7f130222
int style Theme_MaterialComponents_DayNight_Bridge 0x7f130223
int style Theme_MaterialComponents_DayNight_DarkActionBar 0x7f130224
int style Theme_MaterialComponents_DayNight_DarkActionBar_Bridge 0x7f130225
int style Theme_MaterialComponents_DayNight_Dialog 0x7f130226
int style Theme_MaterialComponents_DayNight_Dialog_Alert 0x7f130227
int style Theme_MaterialComponents_DayNight_Dialog_Alert_Bridge 0x7f130228
int style Theme_MaterialComponents_DayNight_Dialog_Bridge 0x7f130229
int style Theme_MaterialComponents_DayNight_Dialog_FixedSize 0x7f13022a
int style Theme_MaterialComponents_DayNight_Dialog_FixedSize_Bridge 0x7f13022b
int style Theme_MaterialComponents_DayNight_Dialog_MinWidth 0x7f13022c
int style Theme_MaterialComponents_DayNight_Dialog_MinWidth_Bridge 0x7f13022d
int style Theme_MaterialComponents_DayNight_DialogWhenLarge 0x7f13022e
int style Theme_MaterialComponents_DayNight_NoActionBar 0x7f13022f
int style Theme_MaterialComponents_DayNight_NoActionBar_Bridge 0x7f130230
int style Theme_MaterialComponents_Dialog 0x7f130231
int style Theme_MaterialComponents_Dialog_Alert 0x7f130232
int style Theme_MaterialComponents_Dialog_Alert_Bridge 0x7f130233
int style Theme_MaterialComponents_Dialog_Bridge 0x7f130234
int style Theme_MaterialComponents_Dialog_FixedSize 0x7f130235
int style Theme_MaterialComponents_Dialog_FixedSize_Bridge 0x7f130236
int style Theme_MaterialComponents_Dialog_MinWidth 0x7f130237
int style Theme_MaterialComponents_Dialog_MinWidth_Bridge 0x7f130238
int style Theme_MaterialComponents_DialogWhenLarge 0x7f130239
int style Theme_MaterialComponents_Light 0x7f13023a
int style Theme_MaterialComponents_Light_BarSize 0x7f13023b
int style Theme_MaterialComponents_Light_BottomSheetDialog 0x7f13023c
int style Theme_MaterialComponents_Light_Bridge 0x7f13023d
int style Theme_MaterialComponents_Light_DarkActionBar 0x7f13023e
int style Theme_MaterialComponents_Light_DarkActionBar_Bridge 0x7f13023f
int style Theme_MaterialComponents_Light_Dialog 0x7f130240
int style Theme_MaterialComponents_Light_Dialog_Alert 0x7f130241
int style Theme_MaterialComponents_Light_Dialog_Alert_Bridge 0x7f130242
int style Theme_MaterialComponents_Light_Dialog_Bridge 0x7f130243
int style Theme_MaterialComponents_Light_Dialog_FixedSize 0x7f130244
int style Theme_MaterialComponents_Light_Dialog_FixedSize_Bridge 0x7f130245
int style Theme_MaterialComponents_Light_Dialog_MinWidth 0x7f130246
int style Theme_MaterialComponents_Light_Dialog_MinWidth_Bridge 0x7f130247
int style Theme_MaterialComponents_Light_DialogWhenLarge 0x7f130248
int style Theme_MaterialComponents_Light_LargeTouch 0x7f130249
int style Theme_MaterialComponents_Light_NoActionBar 0x7f13024a
int style Theme_MaterialComponents_Light_NoActionBar_Bridge 0x7f13024b
int style Theme_MaterialComponents_NoActionBar 0x7f13024c
int style Theme_MaterialComponents_NoActionBar_Bridge 0x7f13024d
int style ThemeOverlay_AppCompat 0x7f13024e
int style ThemeOverlay_AppCompat_ActionBar 0x7f13024f
int style ThemeOverlay_AppCompat_Dark 0x7f130250
int style ThemeOverlay_AppCompat_Dark_ActionBar 0x7f130251
int style ThemeOverlay_AppCompat_DayNight 0x7f130252
int style ThemeOverlay_AppCompat_DayNight_ActionBar 0x7f130253
int style ThemeOverlay_AppCompat_Dialog 0x7f130254
int style ThemeOverlay_AppCompat_Dialog_Alert 0x7f130255
int style ThemeOverlay_AppCompat_Light 0x7f130256
int style ThemeOverlay_Design_TextInputEditText 0x7f130257
int style ThemeOverlay_MaterialComponents 0x7f130258
int style ThemeOverlay_MaterialComponents_ActionBar 0x7f130259
int style ThemeOverlay_MaterialComponents_ActionBar_Primary 0x7f13025a
int style ThemeOverlay_MaterialComponents_ActionBar_Surface 0x7f13025b
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView 0x7f13025c
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox 0x7f13025d
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_FilledBox_Dense 0x7f13025e
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox 0x7f13025f
int style ThemeOverlay_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense 0x7f130260
int style ThemeOverlay_MaterialComponents_BottomAppBar_Primary 0x7f130261
int style ThemeOverlay_MaterialComponents_BottomAppBar_Surface 0x7f130262
int style ThemeOverlay_MaterialComponents_BottomSheetDialog 0x7f130263
int style ThemeOverlay_MaterialComponents_Dark 0x7f130264
int style ThemeOverlay_MaterialComponents_Dark_ActionBar 0x7f130265
int style ThemeOverlay_MaterialComponents_DayNight_BottomSheetDialog 0x7f130266
int style ThemeOverlay_MaterialComponents_Dialog 0x7f130267
int style ThemeOverlay_MaterialComponents_Dialog_Alert 0x7f130268
int style ThemeOverlay_MaterialComponents_Dialog_Alert_Framework 0x7f130269
int style ThemeOverlay_MaterialComponents_Light 0x7f13026a
int style ThemeOverlay_MaterialComponents_Light_Dialog_Alert_Framework 0x7f13026b
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog 0x7f13026c
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Centered 0x7f13026d
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date 0x7f13026e
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Calendar 0x7f13026f
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text 0x7f130270
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Header_Text_Day 0x7f130271
int style ThemeOverlay_MaterialComponents_MaterialAlertDialog_Picker_Date_Spinner 0x7f130272
int style ThemeOverlay_MaterialComponents_MaterialCalendar 0x7f130273
int style ThemeOverlay_MaterialComponents_MaterialCalendar_Fullscreen 0x7f130274
int style ThemeOverlay_MaterialComponents_TextInputEditText 0x7f130275
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox 0x7f130276
int style ThemeOverlay_MaterialComponents_TextInputEditText_FilledBox_Dense 0x7f130277
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox 0x7f130278
int style ThemeOverlay_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x7f130279
int style ThemeOverlay_MaterialComponents_TimePicker 0x7f13027a
int style ThemeOverlay_MaterialComponents_TimePicker_Display 0x7f13027b
int style ThemeOverlay_MaterialComponents_Toolbar_Primary 0x7f13027c
int style ThemeOverlay_MaterialComponents_Toolbar_Surface 0x7f13027d
int style ThemeOverlayColorAccentRed 0x7f13027e
int style Widget_AppCompat_ActionBar 0x7f13027f
int style Widget_AppCompat_ActionBar_Solid 0x7f130280
int style Widget_AppCompat_ActionBar_TabBar 0x7f130281
int style Widget_AppCompat_ActionBar_TabText 0x7f130282
int style Widget_AppCompat_ActionBar_TabView 0x7f130283
int style Widget_AppCompat_ActionButton 0x7f130284
int style Widget_AppCompat_ActionButton_CloseMode 0x7f130285
int style Widget_AppCompat_ActionButton_Overflow 0x7f130286
int style Widget_AppCompat_ActionMode 0x7f130287
int style Widget_AppCompat_ActivityChooserView 0x7f130288
int style Widget_AppCompat_AutoCompleteTextView 0x7f130289
int style Widget_AppCompat_Button 0x7f13028a
int style Widget_AppCompat_Button_Borderless 0x7f13028b
int style Widget_AppCompat_Button_Borderless_Colored 0x7f13028c
int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f13028d
int style Widget_AppCompat_Button_Colored 0x7f13028e
int style Widget_AppCompat_Button_Small 0x7f13028f
int style Widget_AppCompat_ButtonBar 0x7f130290
int style Widget_AppCompat_ButtonBar_AlertDialog 0x7f130291
int style Widget_AppCompat_CompoundButton_CheckBox 0x7f130292
int style Widget_AppCompat_CompoundButton_RadioButton 0x7f130293
int style Widget_AppCompat_CompoundButton_Switch 0x7f130294
int style Widget_AppCompat_DrawerArrowToggle 0x7f130295
int style Widget_AppCompat_DropDownItem_Spinner 0x7f130296
int style Widget_AppCompat_EditText 0x7f130297
int style Widget_AppCompat_ImageButton 0x7f130298
int style Widget_AppCompat_Light_ActionBar 0x7f130299
int style Widget_AppCompat_Light_ActionBar_Solid 0x7f13029a
int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x7f13029b
int style Widget_AppCompat_Light_ActionBar_TabBar 0x7f13029c
int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x7f13029d
int style Widget_AppCompat_Light_ActionBar_TabText 0x7f13029e
int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f13029f
int style Widget_AppCompat_Light_ActionBar_TabView 0x7f1302a0
int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x7f1302a1
int style Widget_AppCompat_Light_ActionButton 0x7f1302a2
int style Widget_AppCompat_Light_ActionButton_CloseMode 0x7f1302a3
int style Widget_AppCompat_Light_ActionButton_Overflow 0x7f1302a4
int style Widget_AppCompat_Light_ActionMode_Inverse 0x7f1302a5
int style Widget_AppCompat_Light_ActivityChooserView 0x7f1302a6
int style Widget_AppCompat_Light_AutoCompleteTextView 0x7f1302a7
int style Widget_AppCompat_Light_DropDownItem_Spinner 0x7f1302a8
int style Widget_AppCompat_Light_ListPopupWindow 0x7f1302a9
int style Widget_AppCompat_Light_ListView_DropDown 0x7f1302aa
int style Widget_AppCompat_Light_PopupMenu 0x7f1302ab
int style Widget_AppCompat_Light_PopupMenu_Overflow 0x7f1302ac
int style Widget_AppCompat_Light_SearchView 0x7f1302ad
int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x7f1302ae
int style Widget_AppCompat_ListMenuView 0x7f1302af
int style Widget_AppCompat_ListPopupWindow 0x7f1302b0
int style Widget_AppCompat_ListView 0x7f1302b1
int style Widget_AppCompat_ListView_DropDown 0x7f1302b2
int style Widget_AppCompat_ListView_Menu 0x7f1302b3
int style Widget_AppCompat_PopupMenu 0x7f1302b4
int style Widget_AppCompat_PopupMenu_Overflow 0x7f1302b5
int style Widget_AppCompat_PopupWindow 0x7f1302b6
int style Widget_AppCompat_ProgressBar 0x7f1302b7
int style Widget_AppCompat_ProgressBar_Horizontal 0x7f1302b8
int style Widget_AppCompat_RatingBar 0x7f1302b9
int style Widget_AppCompat_RatingBar_Indicator 0x7f1302ba
int style Widget_AppCompat_RatingBar_Small 0x7f1302bb
int style Widget_AppCompat_SearchView 0x7f1302bc
int style Widget_AppCompat_SearchView_ActionBar 0x7f1302bd
int style Widget_AppCompat_SeekBar 0x7f1302be
int style Widget_AppCompat_SeekBar_Discrete 0x7f1302bf
int style Widget_AppCompat_Spinner 0x7f1302c0
int style Widget_AppCompat_Spinner_DropDown 0x7f1302c1
int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x7f1302c2
int style Widget_AppCompat_Spinner_Underlined 0x7f1302c3
int style Widget_AppCompat_TextView 0x7f1302c4
int style Widget_AppCompat_TextView_SpinnerItem 0x7f1302c5
int style Widget_AppCompat_Toolbar 0x7f1302c6
int style Widget_AppCompat_Toolbar_Button_Navigation 0x7f1302c7
int style Widget_Compat_NotificationActionContainer 0x7f1302c8
int style Widget_Compat_NotificationActionText 0x7f1302c9
int style Widget_Design_AppBarLayout 0x7f1302ca
int style Widget_Design_BottomNavigationView 0x7f1302cb
int style Widget_Design_BottomSheet_Modal 0x7f1302cc
int style Widget_Design_CollapsingToolbar 0x7f1302cd
int style Widget_Design_FloatingActionButton 0x7f1302ce
int style Widget_Design_NavigationView 0x7f1302cf
int style Widget_Design_ScrimInsetsFrameLayout 0x7f1302d0
int style Widget_Design_Snackbar 0x7f1302d1
int style Widget_Design_TabLayout 0x7f1302d2
int style Widget_Design_TextInputEditText 0x7f1302d3
int style Widget_Design_TextInputLayout 0x7f1302d4
int style Widget_Leanback 0x7f1302d5
int style Widget_Leanback_BaseCardViewStyle 0x7f1302d6
int style Widget_Leanback_DetailsActionButtonStyle 0x7f1302d7
int style Widget_Leanback_DetailsActionButtonStyleBase 0x7f1302d8
int style Widget_Leanback_DetailsDescriptionBodyStyle 0x7f1302d9
int style Widget_Leanback_DetailsDescriptionSubtitleStyle 0x7f1302da
int style Widget_Leanback_DetailsDescriptionTitleStyle 0x7f1302db
int style Widget_Leanback_ErrorMessageStyle 0x7f1302dc
int style Widget_Leanback_GridItems 0x7f1302dd
int style Widget_Leanback_GridItems_VerticalGridView 0x7f1302de
int style Widget_Leanback_GuidanceBreadcrumbStyle 0x7f1302df
int style Widget_Leanback_GuidanceContainerStyle 0x7f1302e0
int style Widget_Leanback_GuidanceDescriptionStyle 0x7f1302e1
int style Widget_Leanback_GuidanceIconStyle 0x7f1302e2
int style Widget_Leanback_GuidanceTitleStyle 0x7f1302e3
int style Widget_Leanback_GuidedActionItemCheckmarkStyle 0x7f1302e4
int style Widget_Leanback_GuidedActionItemChevronStyle 0x7f1302e5
int style Widget_Leanback_GuidedActionItemContainerStyle 0x7f1302e6
int style Widget_Leanback_GuidedActionItemContentStyle 0x7f1302e7
int style Widget_Leanback_GuidedActionItemDescriptionStyle 0x7f1302e8
int style Widget_Leanback_GuidedActionItemIconStyle 0x7f1302e9
int style Widget_Leanback_GuidedActionItemTitleStyle 0x7f1302ea
int style Widget_Leanback_GuidedActionsContainerStyle 0x7f1302eb
int style Widget_Leanback_GuidedActionsListStyle 0x7f1302ec
int style Widget_Leanback_GuidedActionsSelectorStyle 0x7f1302ed
int style Widget_Leanback_GuidedButtonActionsListStyle 0x7f1302ee
int style Widget_Leanback_GuidedSubActionsListStyle 0x7f1302ef
int style Widget_Leanback_Header 0x7f1302f0
int style Widget_Leanback_Header_Section 0x7f1302f1
int style Widget_Leanback_Headers 0x7f1302f2
int style Widget_Leanback_Headers_VerticalGridView 0x7f1302f3
int style Widget_Leanback_ImageCardView 0x7f1302f4
int style Widget_Leanback_ImageCardView_BadgeStyle 0x7f1302f5
int style Widget_Leanback_ImageCardView_ContentStyle 0x7f1302f6
int style Widget_Leanback_ImageCardView_ImageStyle 0x7f1302f7
int style Widget_Leanback_ImageCardView_InfoAreaStyle 0x7f1302f8
int style Widget_Leanback_ImageCardView_TitleStyle 0x7f1302f9
int style Widget_Leanback_ImageCardViewStyle 0x7f1302fa
int style Widget_Leanback_OnboardingDescriptionStyle 0x7f1302fb
int style Widget_Leanback_OnboardingHeaderStyle 0x7f1302fc
int style Widget_Leanback_OnboardingLogoStyle 0x7f1302fd
int style Widget_Leanback_OnboardingMainIconStyle 0x7f1302fe
int style Widget_Leanback_OnboardingNavigatorContainerStyle 0x7f1302ff
int style Widget_Leanback_OnboardingPageIndicatorStyle 0x7f130300
int style Widget_Leanback_OnboardingStartButtonStyle 0x7f130301
int style Widget_Leanback_OnboardingStartButtonStyleBase 0x7f130302
int style Widget_Leanback_OnboardingTitleStyle 0x7f130303
int style Widget_Leanback_PlaybackControlLabelStyle 0x7f130304
int style Widget_Leanback_PlaybackControlsActionIconsStyle 0x7f130305
int style Widget_Leanback_PlaybackControlsButtonStyle 0x7f130306
int style Widget_Leanback_PlaybackControlsTimeStyle 0x7f130307
int style Widget_Leanback_PlaybackMediaItemDetailsStyle 0x7f130308
int style Widget_Leanback_PlaybackMediaItemDurationStyle 0x7f130309
int style Widget_Leanback_PlaybackMediaItemNameStyle 0x7f13030a
int style Widget_Leanback_PlaybackMediaItemNumberStyle 0x7f13030b
int style Widget_Leanback_PlaybackMediaItemNumberViewFlipperStyle 0x7f13030c
int style Widget_Leanback_PlaybackMediaItemRowStyle 0x7f13030d
int style Widget_Leanback_PlaybackMediaItemSeparatorStyle 0x7f13030e
int style Widget_Leanback_PlaybackMediaListHeaderStyle 0x7f13030f
int style Widget_Leanback_PlaybackMediaListHeaderTitleStyle 0x7f130310
int style Widget_Leanback_PlaybackRow 0x7f130311
int style Widget_Leanback_Row 0x7f130312
int style Widget_Leanback_Row_Header 0x7f130313
int style Widget_Leanback_Row_Header_Description 0x7f130314
int style Widget_Leanback_Row_HeaderDock 0x7f130315
int style Widget_Leanback_Row_HorizontalGridView 0x7f130316
int style Widget_Leanback_Row_HoverCardDescription 0x7f130317
int style Widget_Leanback_Row_HoverCardTitle 0x7f130318
int style Widget_Leanback_Rows 0x7f130319
int style Widget_Leanback_Rows_VerticalGridView 0x7f13031a
int style Widget_Leanback_SearchOrbViewStyle 0x7f13031b
int style Widget_Leanback_Title 0x7f13031c
int style Widget_Leanback_Title_Icon 0x7f13031d
int style Widget_Leanback_Title_Text 0x7f13031e
int style Widget_Leanback_TitleView 0x7f13031f
int style Widget_LeanbackBase 0x7f130320
int style Widget_MaterialComponents_ActionBar_Primary 0x7f130321
int style Widget_MaterialComponents_ActionBar_PrimarySurface 0x7f130322
int style Widget_MaterialComponents_ActionBar_Solid 0x7f130323
int style Widget_MaterialComponents_ActionBar_Surface 0x7f130324
int style Widget_MaterialComponents_AppBarLayout_Primary 0x7f130325
int style Widget_MaterialComponents_AppBarLayout_PrimarySurface 0x7f130326
int style Widget_MaterialComponents_AppBarLayout_Surface 0x7f130327
int style Widget_MaterialComponents_AutoCompleteTextView_FilledBox 0x7f130328
int style Widget_MaterialComponents_AutoCompleteTextView_FilledBox_Dense 0x7f130329
int style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox 0x7f13032a
int style Widget_MaterialComponents_AutoCompleteTextView_OutlinedBox_Dense 0x7f13032b
int style Widget_MaterialComponents_Badge 0x7f13032c
int style Widget_MaterialComponents_BottomAppBar 0x7f13032d
int style Widget_MaterialComponents_BottomAppBar_Colored 0x7f13032e
int style Widget_MaterialComponents_BottomAppBar_PrimarySurface 0x7f13032f
int style Widget_MaterialComponents_BottomNavigationView 0x7f130330
int style Widget_MaterialComponents_BottomNavigationView_Colored 0x7f130331
int style Widget_MaterialComponents_BottomNavigationView_PrimarySurface 0x7f130332
int style Widget_MaterialComponents_BottomSheet 0x7f130333
int style Widget_MaterialComponents_BottomSheet_Modal 0x7f130334
int style Widget_MaterialComponents_Button 0x7f130335
int style Widget_MaterialComponents_Button_Icon 0x7f130336
int style Widget_MaterialComponents_Button_OutlinedButton 0x7f130337
int style Widget_MaterialComponents_Button_OutlinedButton_Icon 0x7f130338
int style Widget_MaterialComponents_Button_TextButton 0x7f130339
int style Widget_MaterialComponents_Button_TextButton_Dialog 0x7f13033a
int style Widget_MaterialComponents_Button_TextButton_Dialog_Flush 0x7f13033b
int style Widget_MaterialComponents_Button_TextButton_Dialog_Icon 0x7f13033c
int style Widget_MaterialComponents_Button_TextButton_Icon 0x7f13033d
int style Widget_MaterialComponents_Button_TextButton_Snackbar 0x7f13033e
int style Widget_MaterialComponents_Button_UnelevatedButton 0x7f13033f
int style Widget_MaterialComponents_Button_UnelevatedButton_Icon 0x7f130340
int style Widget_MaterialComponents_CardView 0x7f130341
int style Widget_MaterialComponents_CheckedTextView 0x7f130342
int style Widget_MaterialComponents_Chip_Action 0x7f130343
int style Widget_MaterialComponents_Chip_Choice 0x7f130344
int style Widget_MaterialComponents_Chip_Entry 0x7f130345
int style Widget_MaterialComponents_Chip_Filter 0x7f130346
int style Widget_MaterialComponents_ChipGroup 0x7f130347
int style Widget_MaterialComponents_CircularProgressIndicator 0x7f130348
int style Widget_MaterialComponents_CircularProgressIndicator_ExtraSmall 0x7f130349
int style Widget_MaterialComponents_CircularProgressIndicator_Medium 0x7f13034a
int style Widget_MaterialComponents_CircularProgressIndicator_Small 0x7f13034b
int style Widget_MaterialComponents_CollapsingToolbar 0x7f13034c
int style Widget_MaterialComponents_CompoundButton_CheckBox 0x7f13034d
int style Widget_MaterialComponents_CompoundButton_RadioButton 0x7f13034e
int style Widget_MaterialComponents_CompoundButton_Switch 0x7f13034f
int style Widget_MaterialComponents_ExtendedFloatingActionButton 0x7f130350
int style Widget_MaterialComponents_ExtendedFloatingActionButton_Icon 0x7f130351
int style Widget_MaterialComponents_FloatingActionButton 0x7f130352
int style Widget_MaterialComponents_Light_ActionBar_Solid 0x7f130353
int style Widget_MaterialComponents_LinearProgressIndicator 0x7f130354
int style Widget_MaterialComponents_MaterialButtonToggleGroup 0x7f130355
int style Widget_MaterialComponents_MaterialCalendar 0x7f130356
int style Widget_MaterialComponents_MaterialCalendar_Day 0x7f130357
int style Widget_MaterialComponents_MaterialCalendar_Day_Invalid 0x7f130358
int style Widget_MaterialComponents_MaterialCalendar_Day_Selected 0x7f130359
int style Widget_MaterialComponents_MaterialCalendar_Day_Today 0x7f13035a
int style Widget_MaterialComponents_MaterialCalendar_DayTextView 0x7f13035b
int style Widget_MaterialComponents_MaterialCalendar_Fullscreen 0x7f13035c
int style Widget_MaterialComponents_MaterialCalendar_HeaderCancelButton 0x7f13035d
int style Widget_MaterialComponents_MaterialCalendar_HeaderConfirmButton 0x7f13035e
int style Widget_MaterialComponents_MaterialCalendar_HeaderDivider 0x7f13035f
int style Widget_MaterialComponents_MaterialCalendar_HeaderLayout 0x7f130360
int style Widget_MaterialComponents_MaterialCalendar_HeaderSelection 0x7f130361
int style Widget_MaterialComponents_MaterialCalendar_HeaderSelection_Fullscreen 0x7f130362
int style Widget_MaterialComponents_MaterialCalendar_HeaderTitle 0x7f130363
int style Widget_MaterialComponents_MaterialCalendar_HeaderToggleButton 0x7f130364
int style Widget_MaterialComponents_MaterialCalendar_Item 0x7f130365
int style Widget_MaterialComponents_MaterialCalendar_MonthNavigationButton 0x7f130366
int style Widget_MaterialComponents_MaterialCalendar_MonthTextView 0x7f130367
int style Widget_MaterialComponents_MaterialCalendar_Year 0x7f130368
int style Widget_MaterialComponents_MaterialCalendar_Year_Selected 0x7f130369
int style Widget_MaterialComponents_MaterialCalendar_Year_Today 0x7f13036a
int style Widget_MaterialComponents_MaterialCalendar_YearNavigationButton 0x7f13036b
int style Widget_MaterialComponents_NavigationRailView 0x7f13036c
int style Widget_MaterialComponents_NavigationRailView_Colored 0x7f13036d
int style Widget_MaterialComponents_NavigationRailView_Colored_Compact 0x7f13036e
int style Widget_MaterialComponents_NavigationRailView_Compact 0x7f13036f
int style Widget_MaterialComponents_NavigationRailView_PrimarySurface 0x7f130370
int style Widget_MaterialComponents_NavigationView 0x7f130371
int style Widget_MaterialComponents_PopupMenu 0x7f130372
int style Widget_MaterialComponents_PopupMenu_ContextMenu 0x7f130373
int style Widget_MaterialComponents_PopupMenu_ListPopupWindow 0x7f130374
int style Widget_MaterialComponents_PopupMenu_Overflow 0x7f130375
int style Widget_MaterialComponents_ProgressIndicator 0x7f130376
int style Widget_MaterialComponents_ShapeableImageView 0x7f130377
int style Widget_MaterialComponents_Slider 0x7f130378
int style Widget_MaterialComponents_Snackbar 0x7f130379
int style Widget_MaterialComponents_Snackbar_FullWidth 0x7f13037a
int style Widget_MaterialComponents_Snackbar_TextView 0x7f13037b
int style Widget_MaterialComponents_TabLayout 0x7f13037c
int style Widget_MaterialComponents_TabLayout_Colored 0x7f13037d
int style Widget_MaterialComponents_TabLayout_PrimarySurface 0x7f13037e
int style Widget_MaterialComponents_TextInputEditText_FilledBox 0x7f13037f
int style Widget_MaterialComponents_TextInputEditText_FilledBox_Dense 0x7f130380
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox 0x7f130381
int style Widget_MaterialComponents_TextInputEditText_OutlinedBox_Dense 0x7f130382
int style Widget_MaterialComponents_TextInputLayout_FilledBox 0x7f130383
int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense 0x7f130384
int style Widget_MaterialComponents_TextInputLayout_FilledBox_Dense_ExposedDropdownMenu 0x7f130385
int style Widget_MaterialComponents_TextInputLayout_FilledBox_ExposedDropdownMenu 0x7f130386
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox 0x7f130387
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense 0x7f130388
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_Dense_ExposedDropdownMenu 0x7f130389
int style Widget_MaterialComponents_TextInputLayout_OutlinedBox_ExposedDropdownMenu 0x7f13038a
int style Widget_MaterialComponents_TextView 0x7f13038b
int style Widget_MaterialComponents_TimePicker 0x7f13038c
int style Widget_MaterialComponents_TimePicker_Button 0x7f13038d
int style Widget_MaterialComponents_TimePicker_Clock 0x7f13038e
int style Widget_MaterialComponents_TimePicker_Display 0x7f13038f
int style Widget_MaterialComponents_TimePicker_Display_TextInputEditText 0x7f130390
int style Widget_MaterialComponents_TimePicker_ImageButton 0x7f130391
int style Widget_MaterialComponents_TimePicker_ImageButton_ShapeAppearance 0x7f130392
int style Widget_MaterialComponents_Toolbar 0x7f130393
int style Widget_MaterialComponents_Toolbar_Primary 0x7f130394
int style Widget_MaterialComponents_Toolbar_PrimarySurface 0x7f130395
int style Widget_MaterialComponents_Toolbar_Surface 0x7f130396
int style Widget_MaterialComponents_Tooltip 0x7f130397
int style Widget_Support_CoordinatorLayout 0x7f130398
int[] styleable ActionBar { 0x7f040052, 0x7f040059, 0x7f04005a, 0x7f040119, 0x7f04011a, 0x7f04011b, 0x7f04011c, 0x7f04011d, 0x7f04011e, 0x7f040145, 0x7f04016f, 0x7f040170, 0x7f04018e, 0x7f04022b, 0x7f040232, 0x7f04023b, 0x7f04023c, 0x7f040240, 0x7f04025a, 0x7f040271, 0x7f0402ee, 0x7f040349, 0x7f0403ae, 0x7f0403c1, 0x7f0403c2, 0x7f04045b, 0x7f04045f, 0x7f0404d5, 0x7f0404e1 }
int styleable ActionBar_background 0
int styleable ActionBar_backgroundSplit 1
int styleable ActionBar_backgroundStacked 2
int styleable ActionBar_contentInsetEnd 3
int styleable ActionBar_contentInsetEndWithActions 4
int styleable ActionBar_contentInsetLeft 5
int styleable ActionBar_contentInsetRight 6
int styleable ActionBar_contentInsetStart 7
int styleable ActionBar_contentInsetStartWithNavigation 8
int styleable ActionBar_customNavigationLayout 9
int styleable ActionBar_displayOptions 10
int styleable ActionBar_divider 11
int styleable ActionBar_elevation 12
int styleable ActionBar_height 13
int styleable ActionBar_hideOnContentScroll 14
int styleable ActionBar_homeAsUpIndicator 15
int styleable ActionBar_homeLayout 16
int styleable ActionBar_icon 17
int styleable ActionBar_indeterminateProgressStyle 18
int styleable ActionBar_itemPadding 19
int styleable ActionBar_logo 20
int styleable ActionBar_navigationMode 21
int styleable ActionBar_popupTheme 22
int styleable ActionBar_progressBarPadding 23
int styleable ActionBar_progressBarStyle 24
int styleable ActionBar_subtitle 25
int styleable ActionBar_subtitleTextStyle 26
int styleable ActionBar_title 27
int styleable ActionBar_titleTextStyle 28
int[] styleable ActionBarLayout { 0x010100b3 }
int styleable ActionBarLayout_android_layout_gravity 0
int[] styleable ActionMenuItemView { 0x0101013f }
int styleable ActionMenuItemView_android_minWidth 0
int[] styleable ActionMenuView { }
int[] styleable ActionMode { 0x7f040052, 0x7f040059, 0x7f0400ef, 0x7f04022b, 0x7f04045f, 0x7f0404e1 }
int styleable ActionMode_background 0
int styleable ActionMode_backgroundSplit 1
int styleable ActionMode_closeItemLayout 2
int styleable ActionMode_height 3
int styleable ActionMode_subtitleTextStyle 4
int styleable ActionMode_titleTextStyle 5
int[] styleable ActivityChooserView { 0x7f0401aa, 0x7f040262 }
int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
int styleable ActivityChooserView_initialActivityCount 1
int[] styleable ActivityFilter { 0x7f040028, 0x7f04002a }
int styleable ActivityFilter_activityAction 0
int styleable ActivityFilter_activityName 1
int[] styleable ActivityNavigator { 0x01010003, 0x7f040002, 0x7f040149, 0x7f04014a, 0x7f04048c }
int styleable ActivityNavigator_android_name 0
int styleable ActivityNavigator_action 1
int styleable ActivityNavigator_data 2
int styleable ActivityNavigator_dataPattern 3
int styleable ActivityNavigator_targetPackage 4
int[] styleable ActivityRule { 0x7f040039 }
int styleable ActivityRule_alwaysExpand 0
int[] styleable AlertDialog { 0x010100f2, 0x7f04009a, 0x7f04009b, 0x7f0402e3, 0x7f0402e4, 0x7f040344, 0x7f04041f, 0x7f04042c }
int styleable AlertDialog_android_layout 0
int styleable AlertDialog_buttonIconDimen 1
int styleable AlertDialog_buttonPanelSideLayout 2
int styleable AlertDialog_listItemLayout 3
int styleable AlertDialog_listLayout 4
int styleable AlertDialog_multiChoiceItemLayout 5
int styleable AlertDialog_showTitle 6
int styleable AlertDialog_singleChoiceItemLayout 7
int[] styleable AnimatedStateListDrawableCompat { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable AnimatedStateListDrawableCompat_android_dither 0
int styleable AnimatedStateListDrawableCompat_android_visible 1
int styleable AnimatedStateListDrawableCompat_android_variablePadding 2
int styleable AnimatedStateListDrawableCompat_android_constantSize 3
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 4
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 5
int[] styleable AnimatedStateListDrawableItem { 0x010100d0, 0x01010199 }
int styleable AnimatedStateListDrawableItem_android_id 0
int styleable AnimatedStateListDrawableItem_android_drawable 1
int[] styleable AnimatedStateListDrawableTransition { 0x01010199, 0x01010449, 0x0101044a, 0x0101044b }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_toId 1
int styleable AnimatedStateListDrawableTransition_android_fromId 2
int styleable AnimatedStateListDrawableTransition_android_reversible 3
int[] styleable AppBarLayout { 0x010100d4, 0x0101048f, 0x01010540, 0x7f04018e, 0x7f0401ab, 0x7f0402d9, 0x7f0402da, 0x7f040455 }
int styleable AppBarLayout_android_background 0
int styleable AppBarLayout_android_touchscreenBlocksFocus 1
int styleable AppBarLayout_android_keyboardNavigationCluster 2
int styleable AppBarLayout_elevation 3
int styleable AppBarLayout_expanded 4
int styleable AppBarLayout_liftOnScroll 5
int styleable AppBarLayout_liftOnScrollTargetViewId 6
int styleable AppBarLayout_statusBarForeground 7
int[] styleable AppBarLayoutStates { 0x7f04044f, 0x7f040450, 0x7f040452, 0x7f040453 }
int styleable AppBarLayoutStates_state_collapsed 0
int styleable AppBarLayoutStates_state_collapsible 1
int styleable AppBarLayoutStates_state_liftable 2
int styleable AppBarLayoutStates_state_lifted 3
int[] styleable AppBarLayout_Layout { 0x7f0402d2, 0x7f0402d3 }
int styleable AppBarLayout_Layout_layout_scrollFlags 0
int styleable AppBarLayout_Layout_layout_scrollInterpolator 1
int[] styleable AppCompatEmojiHelper { }
int[] styleable AppCompatImageView { 0x01010119, 0x7f040445, 0x7f0404d3, 0x7f0404d4 }
int styleable AppCompatImageView_android_src 0
int styleable AppCompatImageView_srcCompat 1
int styleable AppCompatImageView_tint 2
int styleable AppCompatImageView_tintMode 3
int[] styleable AppCompatSeekBar { 0x01010142, 0x7f0404ce, 0x7f0404cf, 0x7f0404d0 }
int styleable AppCompatSeekBar_android_thumb 0
int styleable AppCompatSeekBar_tickMark 1
int styleable AppCompatSeekBar_tickMarkTint 2
int styleable AppCompatSeekBar_tickMarkTintMode 3
int[] styleable AppCompatTextHelper { 0x01010034, 0x0101016d, 0x0101016e, 0x0101016f, 0x01010170, 0x01010392, 0x01010393 }
int styleable AppCompatTextHelper_android_textAppearance 0
int styleable AppCompatTextHelper_android_drawableTop 1
int styleable AppCompatTextHelper_android_drawableBottom 2
int styleable AppCompatTextHelper_android_drawableLeft 3
int styleable AppCompatTextHelper_android_drawableRight 4
int styleable AppCompatTextHelper_android_drawableStart 5
int styleable AppCompatTextHelper_android_drawableEnd 6
int[] styleable AppCompatTextView { 0x01010034, 0x7f04004b, 0x7f04004c, 0x7f04004d, 0x7f04004e, 0x7f04004f, 0x7f04017b, 0x7f04017c, 0x7f04017d, 0x7f04017e, 0x7f040180, 0x7f040181, 0x7f040182, 0x7f040183, 0x7f040191, 0x7f0401c6, 0x7f0401e0, 0x7f0401e9, 0x7f04028b, 0x7f0402dc, 0x7f040490, 0x7f0404b2 }
int styleable AppCompatTextView_android_textAppearance 0
int styleable AppCompatTextView_autoSizeMaxTextSize 1
int styleable AppCompatTextView_autoSizeMinTextSize 2
int styleable AppCompatTextView_autoSizePresetSizes 3
int styleable AppCompatTextView_autoSizeStepGranularity 4
int styleable AppCompatTextView_autoSizeTextType 5
int styleable AppCompatTextView_drawableBottomCompat 6
int styleable AppCompatTextView_drawableEndCompat 7
int styleable AppCompatTextView_drawableLeftCompat 8
int styleable AppCompatTextView_drawableRightCompat 9
int styleable AppCompatTextView_drawableStartCompat 10
int styleable AppCompatTextView_drawableTint 11
int styleable AppCompatTextView_drawableTintMode 12
int styleable AppCompatTextView_drawableTopCompat 13
int styleable AppCompatTextView_emojiCompatEnabled 14
int styleable AppCompatTextView_firstBaselineToTopHeight 15
int styleable AppCompatTextView_fontFamily 16
int styleable AppCompatTextView_fontVariationSettings 17
int styleable AppCompatTextView_lastBaselineToBottomHeight 18
int styleable AppCompatTextView_lineHeight 19
int styleable AppCompatTextView_textAllCaps 20
int styleable AppCompatTextView_textLocale 21
int[] styleable AppCompatTheme { 0x01010057, 0x010100ae, 0x7f040003, 0x7f040004, 0x7f040005, 0x7f040006, 0x7f040007, 0x7f040008, 0x7f040009, 0x7f04000a, 0x7f04000b, 0x7f04000c, 0x7f04000d, 0x7f04000e, 0x7f04000f, 0x7f040011, 0x7f040012, 0x7f040013, 0x7f040014, 0x7f040015, 0x7f040016, 0x7f040017, 0x7f040018, 0x7f040019, 0x7f04001a, 0x7f04001b, 0x7f04001c, 0x7f04001d, 0x7f04001e, 0x7f04001f, 0x7f040020, 0x7f040021, 0x7f040022, 0x7f040023, 0x7f040029, 0x7f04002e, 0x7f04002f, 0x7f040030, 0x7f040031, 0x7f04004a, 0x7f040076, 0x7f040093, 0x7f040094, 0x7f040095, 0x7f040096, 0x7f040097, 0x7f04009d, 0x7f04009e, 0x7f0400bb, 0x7f0400c4, 0x7f0400f8, 0x7f0400f9, 0x7f0400fa, 0x7f0400fb, 0x7f0400fc, 0x7f0400fd, 0x7f0400fe, 0x7f040105, 0x7f040106, 0x7f04010d, 0x7f040128, 0x7f040166, 0x7f04016b, 0x7f04016c, 0x7f040171, 0x7f040173, 0x7f040186, 0x7f040187, 0x7f04018a, 0x7f04018b, 0x7f04018d, 0x7f04023b, 0x7f04024e, 0x7f0402df, 0x7f0402e0, 0x7f0402e1, 0x7f0402e2, 0x7f0402e5, 0x7f0402e6, 0x7f0402e7, 0x7f0402e8, 0x7f0402e9, 0x7f0402ea, 0x7f0402eb, 0x7f0402ec, 0x7f0402ed, 0x7f040374, 0x7f040375, 0x7f040376, 0x7f0403ad, 0x7f0403af, 0x7f0403c9, 0x7f0403cb, 0x7f0403cc, 0x7f0403cd, 0x7f040401, 0x7f040407, 0x7f040409, 0x7f04040a, 0x7f040439, 0x7f04043a, 0x7f04046c, 0x7f04049b, 0x7f04049d, 0x7f04049e, 0x7f04049f, 0x7f0404a1, 0x7f0404a2, 0x7f0404a3, 0x7f0404a4, 0x7f0404ac, 0x7f0404ad, 0x7f0404e3, 0x7f0404e4, 0x7f0404e5, 0x7f0404e6, 0x7f04050d, 0x7f04051c, 0x7f04051d, 0x7f04051e, 0x7f04051f, 0x7f040520, 0x7f040521, 0x7f040522, 0x7f040523, 0x7f040524, 0x7f040525 }
int styleable AppCompatTheme_android_windowIsFloating 0
int styleable AppCompatTheme_android_windowAnimationStyle 1
int styleable AppCompatTheme_actionBarDivider 2
int styleable AppCompatTheme_actionBarItemBackground 3
int styleable AppCompatTheme_actionBarPopupTheme 4
int styleable AppCompatTheme_actionBarSize 5
int styleable AppCompatTheme_actionBarSplitStyle 6
int styleable AppCompatTheme_actionBarStyle 7
int styleable AppCompatTheme_actionBarTabBarStyle 8
int styleable AppCompatTheme_actionBarTabStyle 9
int styleable AppCompatTheme_actionBarTabTextStyle 10
int styleable AppCompatTheme_actionBarTheme 11
int styleable AppCompatTheme_actionBarWidgetTheme 12
int styleable AppCompatTheme_actionButtonStyle 13
int styleable AppCompatTheme_actionDropDownStyle 14
int styleable AppCompatTheme_actionMenuTextAppearance 15
int styleable AppCompatTheme_actionMenuTextColor 16
int styleable AppCompatTheme_actionModeBackground 17
int styleable AppCompatTheme_actionModeCloseButtonStyle 18
int styleable AppCompatTheme_actionModeCloseContentDescription 19
int styleable AppCompatTheme_actionModeCloseDrawable 20
int styleable AppCompatTheme_actionModeCopyDrawable 21
int styleable AppCompatTheme_actionModeCutDrawable 22
int styleable AppCompatTheme_actionModeFindDrawable 23
int styleable AppCompatTheme_actionModePasteDrawable 24
int styleable AppCompatTheme_actionModePopupWindowStyle 25
int styleable AppCompatTheme_actionModeSelectAllDrawable 26
int styleable AppCompatTheme_actionModeShareDrawable 27
int styleable AppCompatTheme_actionModeSplitBackground 28
int styleable AppCompatTheme_actionModeStyle 29
int styleable AppCompatTheme_actionModeTheme 30
int styleable AppCompatTheme_actionModeWebSearchDrawable 31
int styleable AppCompatTheme_actionOverflowButtonStyle 32
int styleable AppCompatTheme_actionOverflowMenuStyle 33
int styleable AppCompatTheme_activityChooserViewStyle 34
int styleable AppCompatTheme_alertDialogButtonGroupStyle 35
int styleable AppCompatTheme_alertDialogCenterButtons 36
int styleable AppCompatTheme_alertDialogStyle 37
int styleable AppCompatTheme_alertDialogTheme 38
int styleable AppCompatTheme_autoCompleteTextViewStyle 39
int styleable AppCompatTheme_borderlessButtonStyle 40
int styleable AppCompatTheme_buttonBarButtonStyle 41
int styleable AppCompatTheme_buttonBarNegativeButtonStyle 42
int styleable AppCompatTheme_buttonBarNeutralButtonStyle 43
int styleable AppCompatTheme_buttonBarPositiveButtonStyle 44
int styleable AppCompatTheme_buttonBarStyle 45
int styleable AppCompatTheme_buttonStyle 46
int styleable AppCompatTheme_buttonStyleSmall 47
int styleable AppCompatTheme_checkboxStyle 48
int styleable AppCompatTheme_checkedTextViewStyle 49
int styleable AppCompatTheme_colorAccent 50
int styleable AppCompatTheme_colorBackgroundFloating 51
int styleable AppCompatTheme_colorButtonNormal 52
int styleable AppCompatTheme_colorControlActivated 53
int styleable AppCompatTheme_colorControlHighlight 54
int styleable AppCompatTheme_colorControlNormal 55
int styleable AppCompatTheme_colorError 56
int styleable AppCompatTheme_colorPrimary 57
int styleable AppCompatTheme_colorPrimaryDark 58
int styleable AppCompatTheme_colorSwitchThumbNormal 59
int styleable AppCompatTheme_controlBackground 60
int styleable AppCompatTheme_dialogCornerRadius 61
int styleable AppCompatTheme_dialogPreferredPadding 62
int styleable AppCompatTheme_dialogTheme 63
int styleable AppCompatTheme_dividerHorizontal 64
int styleable AppCompatTheme_dividerVertical 65
int styleable AppCompatTheme_dropDownListViewStyle 66
int styleable AppCompatTheme_dropdownListPreferredItemHeight 67
int styleable AppCompatTheme_editTextBackground 68
int styleable AppCompatTheme_editTextColor 69
int styleable AppCompatTheme_editTextStyle 70
int styleable AppCompatTheme_homeAsUpIndicator 71
int styleable AppCompatTheme_imageButtonStyle 72
int styleable AppCompatTheme_listChoiceBackgroundIndicator 73
int styleable AppCompatTheme_listChoiceIndicatorMultipleAnimated 74
int styleable AppCompatTheme_listChoiceIndicatorSingleAnimated 75
int styleable AppCompatTheme_listDividerAlertDialog 76
int styleable AppCompatTheme_listMenuViewStyle 77
int styleable AppCompatTheme_listPopupWindowStyle 78
int styleable AppCompatTheme_listPreferredItemHeight 79
int styleable AppCompatTheme_listPreferredItemHeightLarge 80
int styleable AppCompatTheme_listPreferredItemHeightSmall 81
int styleable AppCompatTheme_listPreferredItemPaddingEnd 82
int styleable AppCompatTheme_listPreferredItemPaddingLeft 83
int styleable AppCompatTheme_listPreferredItemPaddingRight 84
int styleable AppCompatTheme_listPreferredItemPaddingStart 85
int styleable AppCompatTheme_panelBackground 86
int styleable AppCompatTheme_panelMenuListTheme 87
int styleable AppCompatTheme_panelMenuListWidth 88
int styleable AppCompatTheme_popupMenuStyle 89
int styleable AppCompatTheme_popupWindowStyle 90
int styleable AppCompatTheme_radioButtonStyle 91
int styleable AppCompatTheme_ratingBarStyle 92
int styleable AppCompatTheme_ratingBarStyleIndicator 93
int styleable AppCompatTheme_ratingBarStyleSmall 94
int styleable AppCompatTheme_searchViewStyle 95
int styleable AppCompatTheme_seekBarStyle 96
int styleable AppCompatTheme_selectableItemBackground 97
int styleable AppCompatTheme_selectableItemBackgroundBorderless 98
int styleable AppCompatTheme_spinnerDropDownItemStyle 99
int styleable AppCompatTheme_spinnerStyle 100
int styleable AppCompatTheme_switchStyle 101
int styleable AppCompatTheme_textAppearanceLargePopupMenu 102
int styleable AppCompatTheme_textAppearanceListItem 103
int styleable AppCompatTheme_textAppearanceListItemSecondary 104
int styleable AppCompatTheme_textAppearanceListItemSmall 105
int styleable AppCompatTheme_textAppearancePopupMenuHeader 106
int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 107
int styleable AppCompatTheme_textAppearanceSearchResultTitle 108
int styleable AppCompatTheme_textAppearanceSmallPopupMenu 109
int styleable AppCompatTheme_textColorAlertDialogListItem 110
int styleable AppCompatTheme_textColorSearchUrl 111
int styleable AppCompatTheme_toolbarNavigationButtonStyle 112
int styleable AppCompatTheme_toolbarStyle 113
int styleable AppCompatTheme_tooltipForegroundColor 114
int styleable AppCompatTheme_tooltipFrameBackground 115
int styleable AppCompatTheme_viewInflaterClass 116
int styleable AppCompatTheme_windowActionBar 117
int styleable AppCompatTheme_windowActionBarOverlay 118
int styleable AppCompatTheme_windowActionModeOverlay 119
int styleable AppCompatTheme_windowFixedHeightMajor 120
int styleable AppCompatTheme_windowFixedHeightMinor 121
int styleable AppCompatTheme_windowFixedWidthMajor 122
int styleable AppCompatTheme_windowFixedWidthMinor 123
int styleable AppCompatTheme_windowMinWidthMajor 124
int styleable AppCompatTheme_windowMinWidthMinor 125
int styleable AppCompatTheme_windowNoTitle 126
int[] styleable AspectRatioFrameLayout { 0x7f0403db }
int styleable AspectRatioFrameLayout_resize_mode 0
int[] styleable BackgroundStyle { 0x0101030e, 0x7f040409 }
int styleable BackgroundStyle_android_selectableItemBackground 0
int styleable BackgroundStyle_selectableItemBackground 1
int[] styleable Badge { 0x7f040053, 0x7f04005d, 0x7f04005f, 0x7f04023e, 0x7f040310, 0x7f040351, 0x7f04050c }
int styleable Badge_backgroundColor 0
int styleable Badge_badgeGravity 1
int styleable Badge_badgeTextColor 2
int styleable Badge_horizontalOffset 3
int styleable Badge_maxCharacterCount 4
int styleable Badge_number 5
int styleable Badge_verticalOffset 6
int[] styleable BaseProgressIndicator { 0x01010139, 0x7f040230, 0x7f04025b, 0x7f04031d, 0x7f040417, 0x7f040419, 0x7f0404ee, 0x7f0404f1, 0x7f0404f3 }
int styleable BaseProgressIndicator_android_indeterminate 0
int styleable BaseProgressIndicator_hideAnimationBehavior 1
int styleable BaseProgressIndicator_indicatorColor 2
int styleable BaseProgressIndicator_minHideDelay 3
int styleable BaseProgressIndicator_showAnimationBehavior 4
int styleable BaseProgressIndicator_showDelay 5
int styleable BaseProgressIndicator_trackColor 6
int styleable BaseProgressIndicator_trackCornerRadius 7
int styleable BaseProgressIndicator_trackThickness 8
int[] styleable BottomAppBar { 0x7f04005b, 0x7f04018e, 0x7f0401b7, 0x7f0401b8, 0x7f0401b9, 0x7f0401ba, 0x7f0401bb, 0x7f040233, 0x7f04036d, 0x7f04036f, 0x7f040370 }
int styleable BottomAppBar_backgroundTint 0
int styleable BottomAppBar_elevation 1
int styleable BottomAppBar_fabAlignmentMode 2
int styleable BottomAppBar_fabAnimationMode 3
int styleable BottomAppBar_fabCradleMargin 4
int styleable BottomAppBar_fabCradleRoundedCornerRadius 5
int styleable BottomAppBar_fabCradleVerticalOffset 6
int styleable BottomAppBar_hideOnScroll 7
int styleable BottomAppBar_paddingBottomSystemWindowInsets 8
int styleable BottomAppBar_paddingLeftSystemWindowInsets 9
int styleable BottomAppBar_paddingRightSystemWindowInsets 10
int[] styleable BottomNavigationView { 0x7f04026c }
int styleable BottomNavigationView_itemHorizontalTranslationEnabled 0
int[] styleable BottomSheetBehavior_Layout { 0x0101011f, 0x01010440, 0x7f04005b, 0x7f040069, 0x7f04006a, 0x7f04006b, 0x7f04006c, 0x7f04006d, 0x7f04006f, 0x7f040070, 0x7f040071, 0x7f0401ef, 0x7f04036d, 0x7f04036f, 0x7f040370, 0x7f040373, 0x7f040410, 0x7f040413 }
int styleable BottomSheetBehavior_Layout_android_maxWidth 0
int styleable BottomSheetBehavior_Layout_android_elevation 1
int styleable BottomSheetBehavior_Layout_backgroundTint 2
int styleable BottomSheetBehavior_Layout_behavior_draggable 3
int styleable BottomSheetBehavior_Layout_behavior_expandedOffset 4
int styleable BottomSheetBehavior_Layout_behavior_fitToContents 5
int styleable BottomSheetBehavior_Layout_behavior_halfExpandedRatio 6
int styleable BottomSheetBehavior_Layout_behavior_hideable 7
int styleable BottomSheetBehavior_Layout_behavior_peekHeight 8
int styleable BottomSheetBehavior_Layout_behavior_saveFlags 9
int styleable BottomSheetBehavior_Layout_behavior_skipCollapsed 10
int styleable BottomSheetBehavior_Layout_gestureInsetBottomIgnored 11
int styleable BottomSheetBehavior_Layout_paddingBottomSystemWindowInsets 12
int styleable BottomSheetBehavior_Layout_paddingLeftSystemWindowInsets 13
int styleable BottomSheetBehavior_Layout_paddingRightSystemWindowInsets 14
int styleable BottomSheetBehavior_Layout_paddingTopSystemWindowInsets 15
int styleable BottomSheetBehavior_Layout_shapeAppearance 16
int styleable BottomSheetBehavior_Layout_shapeAppearanceOverlay 17
int[] styleable ButtonBarLayout { 0x7f040035 }
int styleable ButtonBarLayout_allowStacking 0
int[] styleable Capability { 0x7f0403c8, 0x7f040415 }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable CardView { 0x0101013f, 0x01010140, 0x7f0400a2, 0x7f0400a3, 0x7f0400a4, 0x7f0400a7, 0x7f0400a8, 0x7f0400aa, 0x7f04011f, 0x7f040120, 0x7f040122, 0x7f040123, 0x7f040125 }
int styleable CardView_android_minWidth 0
int styleable CardView_android_minHeight 1
int styleable CardView_cardBackgroundColor 2
int styleable CardView_cardCornerRadius 3
int styleable CardView_cardElevation 4
int styleable CardView_cardMaxElevation 5
int styleable CardView_cardPreventCornerOverlap 6
int styleable CardView_cardUseCompatPadding 7
int styleable CardView_contentPadding 8
int styleable CardView_contentPaddingBottom 9
int styleable CardView_contentPaddingLeft 10
int styleable CardView_contentPaddingRight 11
int styleable CardView_contentPaddingTop 12
int[] styleable Carousel { 0x7f0400ac, 0x7f0400ad, 0x7f0400ae, 0x7f0400af, 0x7f0400b0, 0x7f0400b1, 0x7f0400b2, 0x7f0400b3, 0x7f0400b4, 0x7f0400b5 }
int styleable Carousel_carousel_backwardTransition 0
int styleable Carousel_carousel_emptyViewsBehavior 1
int styleable Carousel_carousel_firstView 2
int styleable Carousel_carousel_forwardTransition 3
int styleable Carousel_carousel_infinite 4
int styleable Carousel_carousel_nextState 5
int styleable Carousel_carousel_previousState 6
int styleable Carousel_carousel_touchUpMode 7
int styleable Carousel_carousel_touchUp_dampeningFactor 8
int styleable Carousel_carousel_touchUp_velocityThreshold 9
int[] styleable CheckBoxPreference { 0x010101ef, 0x010101f0, 0x010101f1, 0x7f04016e, 0x7f040465, 0x7f040466 }
int styleable CheckBoxPreference_android_summaryOn 0
int styleable CheckBoxPreference_android_summaryOff 1
int styleable CheckBoxPreference_android_disableDependentsState 2
int styleable CheckBoxPreference_disableDependentsState 3
int styleable CheckBoxPreference_summaryOff 4
int styleable CheckBoxPreference_summaryOn 5
int[] styleable CheckedTextView { 0x01010108, 0x7f0400b8, 0x7f0400b9, 0x7f0400ba }
int styleable CheckedTextView_android_checkMark 0
int styleable CheckedTextView_checkMarkCompat 1
int styleable CheckedTextView_checkMarkTint 2
int styleable CheckedTextView_checkMarkTintMode 3
int[] styleable Chip { 0x01010034, 0x01010095, 0x01010098, 0x010100ab, 0x0101011f, 0x0101014f, 0x010101e5, 0x7f0400be, 0x7f0400bf, 0x7f0400c2, 0x7f0400c3, 0x7f0400c5, 0x7f0400c6, 0x7f0400c7, 0x7f0400c9, 0x7f0400ca, 0x7f0400cb, 0x7f0400cc, 0x7f0400cd, 0x7f0400ce, 0x7f0400cf, 0x7f0400d4, 0x7f0400d5, 0x7f0400d6, 0x7f0400d8, 0x7f0400e8, 0x7f0400e9, 0x7f0400ea, 0x7f0400eb, 0x7f0400ec, 0x7f0400ed, 0x7f0400ee, 0x7f04019d, 0x7f040231, 0x7f040241, 0x7f040246, 0x7f0403e2, 0x7f040410, 0x7f040413, 0x7f04041b, 0x7f0404ae, 0x7f0404b7 }
int styleable Chip_android_textAppearance 0
int styleable Chip_android_textSize 1
int styleable Chip_android_textColor 2
int styleable Chip_android_ellipsize 3
int styleable Chip_android_maxWidth 4
int styleable Chip_android_text 5
int styleable Chip_android_checkable 6
int styleable Chip_checkedIcon 7
int styleable Chip_checkedIconEnabled 8
int styleable Chip_checkedIconTint 9
int styleable Chip_checkedIconVisible 10
int styleable Chip_chipBackgroundColor 11
int styleable Chip_chipCornerRadius 12
int styleable Chip_chipEndPadding 13
int styleable Chip_chipIcon 14
int styleable Chip_chipIconEnabled 15
int styleable Chip_chipIconSize 16
int styleable Chip_chipIconTint 17
int styleable Chip_chipIconVisible 18
int styleable Chip_chipMinHeight 19
int styleable Chip_chipMinTouchTargetSize 20
int styleable Chip_chipStartPadding 21
int styleable Chip_chipStrokeColor 22
int styleable Chip_chipStrokeWidth 23
int styleable Chip_chipSurfaceColor 24
int styleable Chip_closeIcon 25
int styleable Chip_closeIconEnabled 26
int styleable Chip_closeIconEndPadding 27
int styleable Chip_closeIconSize 28
int styleable Chip_closeIconStartPadding 29
int styleable Chip_closeIconTint 30
int styleable Chip_closeIconVisible 31
int styleable Chip_ensureMinTouchTargetSize 32
int styleable Chip_hideMotionSpec 33
int styleable Chip_iconEndPadding 34
int styleable Chip_iconStartPadding 35
int styleable Chip_rippleColor 36
int styleable Chip_shapeAppearance 37
int styleable Chip_shapeAppearanceOverlay 38
int styleable Chip_showMotionSpec 39
int styleable Chip_textEndPadding 40
int styleable Chip_textStartPadding 41
int[] styleable ChipGroup { 0x7f0400bd, 0x7f0400d0, 0x7f0400d1, 0x7f0400d2, 0x7f04040d, 0x7f04042d, 0x7f04042f }
int styleable ChipGroup_checkedChip 0
int styleable ChipGroup_chipSpacing 1
int styleable ChipGroup_chipSpacingHorizontal 2
int styleable ChipGroup_chipSpacingVertical 3
int styleable ChipGroup_selectionRequired 4
int styleable ChipGroup_singleLine 5
int styleable ChipGroup_singleSelection 6
int[] styleable CircularProgressIndicator { 0x7f04025c, 0x7f04025e, 0x7f04025f }
int styleable CircularProgressIndicator_indicatorDirectionCircular 0
int styleable CircularProgressIndicator_indicatorInset 1
int styleable CircularProgressIndicator_indicatorSize 2
int[] styleable ClockFaceView { 0x7f0400e4, 0x7f0400e7 }
int styleable ClockFaceView_clockFaceBackgroundColor 0
int styleable ClockFaceView_clockNumberTextColor 1
int[] styleable ClockHandView { 0x7f0400e5, 0x7f040308, 0x7f04040e }
int styleable ClockHandView_clockHandColor 0
int styleable ClockHandView_materialCircleRadius 1
int styleable ClockHandView_selectorSize 2
int[] styleable CollapsingToolbarLayout { 0x7f0400f4, 0x7f0400f5, 0x7f040126, 0x7f0401ad, 0x7f0401ae, 0x7f0401af, 0x7f0401b0, 0x7f0401b1, 0x7f0401b2, 0x7f0401b3, 0x7f040313, 0x7f0403f2, 0x7f0403f4, 0x7f040456, 0x7f0404d5, 0x7f0404d7, 0x7f0404d8, 0x7f0404e2 }
int styleable CollapsingToolbarLayout_collapsedTitleGravity 0
int styleable CollapsingToolbarLayout_collapsedTitleTextAppearance 1
int styleable CollapsingToolbarLayout_contentScrim 2
int styleable CollapsingToolbarLayout_expandedTitleGravity 3
int styleable CollapsingToolbarLayout_expandedTitleMargin 4
int styleable CollapsingToolbarLayout_expandedTitleMarginBottom 5
int styleable CollapsingToolbarLayout_expandedTitleMarginEnd 6
int styleable CollapsingToolbarLayout_expandedTitleMarginStart 7
int styleable CollapsingToolbarLayout_expandedTitleMarginTop 8
int styleable CollapsingToolbarLayout_expandedTitleTextAppearance 9
int styleable CollapsingToolbarLayout_maxLines 10
int styleable CollapsingToolbarLayout_scrimAnimationDuration 11
int styleable CollapsingToolbarLayout_scrimVisibleHeightTrigger 12
int styleable CollapsingToolbarLayout_statusBarScrim 13
int styleable CollapsingToolbarLayout_title 14
int styleable CollapsingToolbarLayout_titleCollapseMode 15
int styleable CollapsingToolbarLayout_titleEnabled 16
int styleable CollapsingToolbarLayout_toolbarId 17
int[] styleable CollapsingToolbarLayout_Layout { 0x7f040294, 0x7f040295 }
int styleable CollapsingToolbarLayout_Layout_layout_collapseMode 0
int styleable CollapsingToolbarLayout_Layout_layout_collapseParallaxMultiplier 1
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f040036, 0x7f040287 }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable CompoundButton { 0x01010107, 0x7f040098, 0x7f04009f, 0x7f0400a0 }
int styleable CompoundButton_android_button 0
int styleable CompoundButton_buttonCompat 1
int styleable CompoundButton_buttonTint 2
int styleable CompoundButton_buttonTintMode 3
int[] styleable Constraint { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f04003a, 0x7f04003b, 0x7f040063, 0x7f040064, 0x7f040065, 0x7f0400b6, 0x7f040114, 0x7f040115, 0x7f04017a, 0x7f0401c8, 0x7f0401c9, 0x7f0401ca, 0x7f0401cb, 0x7f0401cc, 0x7f0401cd, 0x7f0401ce, 0x7f0401cf, 0x7f0401d0, 0x7f0401d1, 0x7f0401d2, 0x7f0401d3, 0x7f0401d4, 0x7f0401d6, 0x7f0401d7, 0x7f0401d8, 0x7f0401d9, 0x7f0401da, 0x7f040225, 0x7f040296, 0x7f040297, 0x7f040298, 0x7f040299, 0x7f04029a, 0x7f04029b, 0x7f04029c, 0x7f04029d, 0x7f04029e, 0x7f04029f, 0x7f0402a0, 0x7f0402a1, 0x7f0402a2, 0x7f0402a3, 0x7f0402a4, 0x7f0402a5, 0x7f0402a6, 0x7f0402a7, 0x7f0402a8, 0x7f0402a9, 0x7f0402aa, 0x7f0402ab, 0x7f0402ac, 0x7f0402ad, 0x7f0402ae, 0x7f0402af, 0x7f0402b0, 0x7f0402b1, 0x7f0402b2, 0x7f0402b3, 0x7f0402b4, 0x7f0402b5, 0x7f0402b6, 0x7f0402b7, 0x7f0402b8, 0x7f0402b9, 0x7f0402ba, 0x7f0402bb, 0x7f0402bc, 0x7f0402bd, 0x7f0402be, 0x7f0402bf, 0x7f0402c0, 0x7f0402c1, 0x7f0402c2, 0x7f0402c3, 0x7f0402c5, 0x7f0402c6, 0x7f0402c7, 0x7f0402c8, 0x7f0402c9, 0x7f0402ca, 0x7f0402cb, 0x7f0402cc, 0x7f0402cd, 0x7f0402d0, 0x7f0402d5, 0x7f04033e, 0x7f04033f, 0x7f04037c, 0x7f040386, 0x7f0403a6, 0x7f0403c3, 0x7f0403c4, 0x7f0403c5, 0x7f0404f6, 0x7f0404f8, 0x7f0404fa, 0x7f040512 }
int styleable Constraint_android_orientation 0
int styleable Constraint_android_id 1
int styleable Constraint_android_visibility 2
int styleable Constraint_android_layout_width 3
int styleable Constraint_android_layout_height 4
int styleable Constraint_android_layout_marginLeft 5
int styleable Constraint_android_layout_marginTop 6
int styleable Constraint_android_layout_marginRight 7
int styleable Constraint_android_layout_marginBottom 8
int styleable Constraint_android_maxWidth 9
int styleable Constraint_android_maxHeight 10
int styleable Constraint_android_minWidth 11
int styleable Constraint_android_minHeight 12
int styleable Constraint_android_alpha 13
int styleable Constraint_android_transformPivotX 14
int styleable Constraint_android_transformPivotY 15
int styleable Constraint_android_translationX 16
int styleable Constraint_android_translationY 17
int styleable Constraint_android_scaleX 18
int styleable Constraint_android_scaleY 19
int styleable Constraint_android_rotation 20
int styleable Constraint_android_rotationX 21
int styleable Constraint_android_rotationY 22
int styleable Constraint_android_layout_marginStart 23
int styleable Constraint_android_layout_marginEnd 24
int styleable Constraint_android_translationZ 25
int styleable Constraint_android_elevation 26
int styleable Constraint_animateCircleAngleTo 27
int styleable Constraint_animateRelativeTo 28
int styleable Constraint_barrierAllowsGoneWidgets 29
int styleable Constraint_barrierDirection 30
int styleable Constraint_barrierMargin 31
int styleable Constraint_chainUseRtl 32
int styleable Constraint_constraint_referenced_ids 33
int styleable Constraint_constraint_referenced_tags 34
int styleable Constraint_drawPath 35
int styleable Constraint_flow_firstHorizontalBias 36
int styleable Constraint_flow_firstHorizontalStyle 37
int styleable Constraint_flow_firstVerticalBias 38
int styleable Constraint_flow_firstVerticalStyle 39
int styleable Constraint_flow_horizontalAlign 40
int styleable Constraint_flow_horizontalBias 41
int styleable Constraint_flow_horizontalGap 42
int styleable Constraint_flow_horizontalStyle 43
int styleable Constraint_flow_lastHorizontalBias 44
int styleable Constraint_flow_lastHorizontalStyle 45
int styleable Constraint_flow_lastVerticalBias 46
int styleable Constraint_flow_lastVerticalStyle 47
int styleable Constraint_flow_maxElementsWrap 48
int styleable Constraint_flow_verticalAlign 49
int styleable Constraint_flow_verticalBias 50
int styleable Constraint_flow_verticalGap 51
int styleable Constraint_flow_verticalStyle 52
int styleable Constraint_flow_wrapMode 53
int styleable Constraint_guidelineUseRtl 54
int styleable Constraint_layout_constrainedHeight 55
int styleable Constraint_layout_constrainedWidth 56
int styleable Constraint_layout_constraintBaseline_creator 57
int styleable Constraint_layout_constraintBaseline_toBaselineOf 58
int styleable Constraint_layout_constraintBaseline_toBottomOf 59
int styleable Constraint_layout_constraintBaseline_toTopOf 60
int styleable Constraint_layout_constraintBottom_creator 61
int styleable Constraint_layout_constraintBottom_toBottomOf 62
int styleable Constraint_layout_constraintBottom_toTopOf 63
int styleable Constraint_layout_constraintCircle 64
int styleable Constraint_layout_constraintCircleAngle 65
int styleable Constraint_layout_constraintCircleRadius 66
int styleable Constraint_layout_constraintDimensionRatio 67
int styleable Constraint_layout_constraintEnd_toEndOf 68
int styleable Constraint_layout_constraintEnd_toStartOf 69
int styleable Constraint_layout_constraintGuide_begin 70
int styleable Constraint_layout_constraintGuide_end 71
int styleable Constraint_layout_constraintGuide_percent 72
int styleable Constraint_layout_constraintHeight 73
int styleable Constraint_layout_constraintHeight_default 74
int styleable Constraint_layout_constraintHeight_max 75
int styleable Constraint_layout_constraintHeight_min 76
int styleable Constraint_layout_constraintHeight_percent 77
int styleable Constraint_layout_constraintHorizontal_bias 78
int styleable Constraint_layout_constraintHorizontal_chainStyle 79
int styleable Constraint_layout_constraintHorizontal_weight 80
int styleable Constraint_layout_constraintLeft_creator 81
int styleable Constraint_layout_constraintLeft_toLeftOf 82
int styleable Constraint_layout_constraintLeft_toRightOf 83
int styleable Constraint_layout_constraintRight_creator 84
int styleable Constraint_layout_constraintRight_toLeftOf 85
int styleable Constraint_layout_constraintRight_toRightOf 86
int styleable Constraint_layout_constraintStart_toEndOf 87
int styleable Constraint_layout_constraintStart_toStartOf 88
int styleable Constraint_layout_constraintTag 89
int styleable Constraint_layout_constraintTop_creator 90
int styleable Constraint_layout_constraintTop_toBottomOf 91
int styleable Constraint_layout_constraintTop_toTopOf 92
int styleable Constraint_layout_constraintVertical_bias 93
int styleable Constraint_layout_constraintVertical_chainStyle 94
int styleable Constraint_layout_constraintVertical_weight 95
int styleable Constraint_layout_constraintWidth 96
int styleable Constraint_layout_constraintWidth_default 97
int styleable Constraint_layout_constraintWidth_max 98
int styleable Constraint_layout_constraintWidth_min 99
int styleable Constraint_layout_constraintWidth_percent 100
int styleable Constraint_layout_editor_absoluteX 101
int styleable Constraint_layout_editor_absoluteY 102
int styleable Constraint_layout_goneMarginBaseline 103
int styleable Constraint_layout_goneMarginBottom 104
int styleable Constraint_layout_goneMarginEnd 105
int styleable Constraint_layout_goneMarginLeft 106
int styleable Constraint_layout_goneMarginRight 107
int styleable Constraint_layout_goneMarginStart 108
int styleable Constraint_layout_goneMarginTop 109
int styleable Constraint_layout_marginBaseline 110
int styleable Constraint_layout_wrapBehaviorInParent 111
int styleable Constraint_motionProgress 112
int styleable Constraint_motionStagger 113
int styleable Constraint_pathMotionArc 114
int styleable Constraint_pivotAnchor 115
int styleable Constraint_polarRelativeTo 116
int styleable Constraint_quantizeMotionInterpolator 117
int styleable Constraint_quantizeMotionPhase 118
int styleable Constraint_quantizeMotionSteps 119
int styleable Constraint_transformPivotTarget 120
int styleable Constraint_transitionEasing 121
int styleable Constraint_transitionPathRotate 122
int styleable Constraint_visibilityMode 123
int[] styleable ConstraintLayout_Layout { 0x010100c4, 0x010100d5, 0x010100d6, 0x010100d7, 0x010100d8, 0x010100d9, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f6, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x010103b3, 0x010103b4, 0x010103b5, 0x010103b6, 0x01010440, 0x0101053b, 0x0101053c, 0x7f040063, 0x7f040064, 0x7f040065, 0x7f0400b6, 0x7f0400dc, 0x7f0400dd, 0x7f0400de, 0x7f0400df, 0x7f0400e0, 0x7f040111, 0x7f040114, 0x7f040115, 0x7f0401c8, 0x7f0401c9, 0x7f0401ca, 0x7f0401cb, 0x7f0401cc, 0x7f0401cd, 0x7f0401ce, 0x7f0401cf, 0x7f0401d0, 0x7f0401d1, 0x7f0401d2, 0x7f0401d3, 0x7f0401d4, 0x7f0401d6, 0x7f0401d7, 0x7f0401d8, 0x7f0401d9, 0x7f0401da, 0x7f040225, 0x7f04028e, 0x7f040296, 0x7f040297, 0x7f040298, 0x7f040299, 0x7f04029a, 0x7f04029b, 0x7f04029c, 0x7f04029d, 0x7f04029e, 0x7f04029f, 0x7f0402a0, 0x7f0402a1, 0x7f0402a2, 0x7f0402a3, 0x7f0402a4, 0x7f0402a5, 0x7f0402a6, 0x7f0402a7, 0x7f0402a8, 0x7f0402a9, 0x7f0402aa, 0x7f0402ab, 0x7f0402ac, 0x7f0402ad, 0x7f0402ae, 0x7f0402af, 0x7f0402b0, 0x7f0402b1, 0x7f0402b2, 0x7f0402b3, 0x7f0402b4, 0x7f0402b5, 0x7f0402b6, 0x7f0402b7, 0x7f0402b8, 0x7f0402b9, 0x7f0402ba, 0x7f0402bb, 0x7f0402bc, 0x7f0402bd, 0x7f0402be, 0x7f0402bf, 0x7f0402c0, 0x7f0402c1, 0x7f0402c2, 0x7f0402c3, 0x7f0402c5, 0x7f0402c6, 0x7f0402c7, 0x7f0402c8, 0x7f0402c9, 0x7f0402ca, 0x7f0402cb, 0x7f0402cc, 0x7f0402cd, 0x7f0402d0, 0x7f0402d1, 0x7f0402d5 }
int styleable ConstraintLayout_Layout_android_orientation 0
int styleable ConstraintLayout_Layout_android_padding 1
int styleable ConstraintLayout_Layout_android_paddingLeft 2
int styleable ConstraintLayout_Layout_android_paddingTop 3
int styleable ConstraintLayout_Layout_android_paddingRight 4
int styleable ConstraintLayout_Layout_android_paddingBottom 5
int styleable ConstraintLayout_Layout_android_visibility 6
int styleable ConstraintLayout_Layout_android_layout_width 7
int styleable ConstraintLayout_Layout_android_layout_height 8
int styleable ConstraintLayout_Layout_android_layout_margin 9
int styleable ConstraintLayout_Layout_android_layout_marginLeft 10
int styleable ConstraintLayout_Layout_android_layout_marginTop 11
int styleable ConstraintLayout_Layout_android_layout_marginRight 12
int styleable ConstraintLayout_Layout_android_layout_marginBottom 13
int styleable ConstraintLayout_Layout_android_maxWidth 14
int styleable ConstraintLayout_Layout_android_maxHeight 15
int styleable ConstraintLayout_Layout_android_minWidth 16
int styleable ConstraintLayout_Layout_android_minHeight 17
int styleable ConstraintLayout_Layout_android_paddingStart 18
int styleable ConstraintLayout_Layout_android_paddingEnd 19
int styleable ConstraintLayout_Layout_android_layout_marginStart 20
int styleable ConstraintLayout_Layout_android_layout_marginEnd 21
int styleable ConstraintLayout_Layout_android_elevation 22
int styleable ConstraintLayout_Layout_android_layout_marginHorizontal 23
int styleable ConstraintLayout_Layout_android_layout_marginVertical 24
int styleable ConstraintLayout_Layout_barrierAllowsGoneWidgets 25
int styleable ConstraintLayout_Layout_barrierDirection 26
int styleable ConstraintLayout_Layout_barrierMargin 27
int styleable ConstraintLayout_Layout_chainUseRtl 28
int styleable ConstraintLayout_Layout_circularflow_angles 29
int styleable ConstraintLayout_Layout_circularflow_defaultAngle 30
int styleable ConstraintLayout_Layout_circularflow_defaultRadius 31
int styleable ConstraintLayout_Layout_circularflow_radiusInDP 32
int styleable ConstraintLayout_Layout_circularflow_viewCenter 33
int styleable ConstraintLayout_Layout_constraintSet 34
int styleable ConstraintLayout_Layout_constraint_referenced_ids 35
int styleable ConstraintLayout_Layout_constraint_referenced_tags 36
int styleable ConstraintLayout_Layout_flow_firstHorizontalBias 37
int styleable ConstraintLayout_Layout_flow_firstHorizontalStyle 38
int styleable ConstraintLayout_Layout_flow_firstVerticalBias 39
int styleable ConstraintLayout_Layout_flow_firstVerticalStyle 40
int styleable ConstraintLayout_Layout_flow_horizontalAlign 41
int styleable ConstraintLayout_Layout_flow_horizontalBias 42
int styleable ConstraintLayout_Layout_flow_horizontalGap 43
int styleable ConstraintLayout_Layout_flow_horizontalStyle 44
int styleable ConstraintLayout_Layout_flow_lastHorizontalBias 45
int styleable ConstraintLayout_Layout_flow_lastHorizontalStyle 46
int styleable ConstraintLayout_Layout_flow_lastVerticalBias 47
int styleable ConstraintLayout_Layout_flow_lastVerticalStyle 48
int styleable ConstraintLayout_Layout_flow_maxElementsWrap 49
int styleable ConstraintLayout_Layout_flow_verticalAlign 50
int styleable ConstraintLayout_Layout_flow_verticalBias 51
int styleable ConstraintLayout_Layout_flow_verticalGap 52
int styleable ConstraintLayout_Layout_flow_verticalStyle 53
int styleable ConstraintLayout_Layout_flow_wrapMode 54
int styleable ConstraintLayout_Layout_guidelineUseRtl 55
int styleable ConstraintLayout_Layout_layoutDescription 56
int styleable ConstraintLayout_Layout_layout_constrainedHeight 57
int styleable ConstraintLayout_Layout_layout_constrainedWidth 58
int styleable ConstraintLayout_Layout_layout_constraintBaseline_creator 59
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf 60
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toBottomOf 61
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toTopOf 62
int styleable ConstraintLayout_Layout_layout_constraintBottom_creator 63
int styleable ConstraintLayout_Layout_layout_constraintBottom_toBottomOf 64
int styleable ConstraintLayout_Layout_layout_constraintBottom_toTopOf 65
int styleable ConstraintLayout_Layout_layout_constraintCircle 66
int styleable ConstraintLayout_Layout_layout_constraintCircleAngle 67
int styleable ConstraintLayout_Layout_layout_constraintCircleRadius 68
int styleable ConstraintLayout_Layout_layout_constraintDimensionRatio 69
int styleable ConstraintLayout_Layout_layout_constraintEnd_toEndOf 70
int styleable ConstraintLayout_Layout_layout_constraintEnd_toStartOf 71
int styleable ConstraintLayout_Layout_layout_constraintGuide_begin 72
int styleable ConstraintLayout_Layout_layout_constraintGuide_end 73
int styleable ConstraintLayout_Layout_layout_constraintGuide_percent 74
int styleable ConstraintLayout_Layout_layout_constraintHeight 75
int styleable ConstraintLayout_Layout_layout_constraintHeight_default 76
int styleable ConstraintLayout_Layout_layout_constraintHeight_max 77
int styleable ConstraintLayout_Layout_layout_constraintHeight_min 78
int styleable ConstraintLayout_Layout_layout_constraintHeight_percent 79
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_bias 80
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle 81
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_weight 82
int styleable ConstraintLayout_Layout_layout_constraintLeft_creator 83
int styleable ConstraintLayout_Layout_layout_constraintLeft_toLeftOf 84
int styleable ConstraintLayout_Layout_layout_constraintLeft_toRightOf 85
int styleable ConstraintLayout_Layout_layout_constraintRight_creator 86
int styleable ConstraintLayout_Layout_layout_constraintRight_toLeftOf 87
int styleable ConstraintLayout_Layout_layout_constraintRight_toRightOf 88
int styleable ConstraintLayout_Layout_layout_constraintStart_toEndOf 89
int styleable ConstraintLayout_Layout_layout_constraintStart_toStartOf 90
int styleable ConstraintLayout_Layout_layout_constraintTag 91
int styleable ConstraintLayout_Layout_layout_constraintTop_creator 92
int styleable ConstraintLayout_Layout_layout_constraintTop_toBottomOf 93
int styleable ConstraintLayout_Layout_layout_constraintTop_toTopOf 94
int styleable ConstraintLayout_Layout_layout_constraintVertical_bias 95
int styleable ConstraintLayout_Layout_layout_constraintVertical_chainStyle 96
int styleable ConstraintLayout_Layout_layout_constraintVertical_weight 97
int styleable ConstraintLayout_Layout_layout_constraintWidth 98
int styleable ConstraintLayout_Layout_layout_constraintWidth_default 99
int styleable ConstraintLayout_Layout_layout_constraintWidth_max 100
int styleable ConstraintLayout_Layout_layout_constraintWidth_min 101
int styleable ConstraintLayout_Layout_layout_constraintWidth_percent 102
int styleable ConstraintLayout_Layout_layout_editor_absoluteX 103
int styleable ConstraintLayout_Layout_layout_editor_absoluteY 104
int styleable ConstraintLayout_Layout_layout_goneMarginBaseline 105
int styleable ConstraintLayout_Layout_layout_goneMarginBottom 106
int styleable ConstraintLayout_Layout_layout_goneMarginEnd 107
int styleable ConstraintLayout_Layout_layout_goneMarginLeft 108
int styleable ConstraintLayout_Layout_layout_goneMarginRight 109
int styleable ConstraintLayout_Layout_layout_goneMarginStart 110
int styleable ConstraintLayout_Layout_layout_goneMarginTop 111
int styleable ConstraintLayout_Layout_layout_marginBaseline 112
int styleable ConstraintLayout_Layout_layout_optimizationLevel 113
int styleable ConstraintLayout_Layout_layout_wrapBehaviorInParent 114
int[] styleable ConstraintLayout_ReactiveGuide { 0x7f0403ce, 0x7f0403cf, 0x7f0403d0, 0x7f0403d1 }
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_animateChange 0
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_applyToAllConstraintSets 1
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_applyToConstraintSet 2
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_valueId 3
int[] styleable ConstraintLayout_placeholder { 0x7f040117, 0x7f04038b }
int styleable ConstraintLayout_placeholder_content 0
int styleable ConstraintLayout_placeholder_placeholder_emptyVisibility 1
int[] styleable ConstraintOverride { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f04003a, 0x7f04003b, 0x7f040063, 0x7f040064, 0x7f040065, 0x7f0400b6, 0x7f040114, 0x7f04017a, 0x7f0401c8, 0x7f0401c9, 0x7f0401ca, 0x7f0401cb, 0x7f0401cc, 0x7f0401cd, 0x7f0401ce, 0x7f0401cf, 0x7f0401d0, 0x7f0401d1, 0x7f0401d2, 0x7f0401d3, 0x7f0401d4, 0x7f0401d6, 0x7f0401d7, 0x7f0401d8, 0x7f0401d9, 0x7f0401da, 0x7f040225, 0x7f040296, 0x7f040297, 0x7f040298, 0x7f04029c, 0x7f0402a0, 0x7f0402a1, 0x7f0402a2, 0x7f0402a5, 0x7f0402a6, 0x7f0402a7, 0x7f0402a8, 0x7f0402a9, 0x7f0402aa, 0x7f0402ab, 0x7f0402ac, 0x7f0402ad, 0x7f0402ae, 0x7f0402af, 0x7f0402b0, 0x7f0402b3, 0x7f0402b8, 0x7f0402b9, 0x7f0402bc, 0x7f0402bd, 0x7f0402be, 0x7f0402bf, 0x7f0402c0, 0x7f0402c1, 0x7f0402c2, 0x7f0402c3, 0x7f0402c5, 0x7f0402c6, 0x7f0402c7, 0x7f0402c8, 0x7f0402c9, 0x7f0402ca, 0x7f0402cb, 0x7f0402cc, 0x7f0402cd, 0x7f0402d0, 0x7f0402d5, 0x7f04033e, 0x7f04033f, 0x7f040340, 0x7f04037c, 0x7f040386, 0x7f0403a6, 0x7f0403c3, 0x7f0403c4, 0x7f0403c5, 0x7f0404f6, 0x7f0404f8, 0x7f0404fa, 0x7f040512 }
int styleable ConstraintOverride_android_orientation 0
int styleable ConstraintOverride_android_id 1
int styleable ConstraintOverride_android_visibility 2
int styleable ConstraintOverride_android_layout_width 3
int styleable ConstraintOverride_android_layout_height 4
int styleable ConstraintOverride_android_layout_marginLeft 5
int styleable ConstraintOverride_android_layout_marginTop 6
int styleable ConstraintOverride_android_layout_marginRight 7
int styleable ConstraintOverride_android_layout_marginBottom 8
int styleable ConstraintOverride_android_maxWidth 9
int styleable ConstraintOverride_android_maxHeight 10
int styleable ConstraintOverride_android_minWidth 11
int styleable ConstraintOverride_android_minHeight 12
int styleable ConstraintOverride_android_alpha 13
int styleable ConstraintOverride_android_transformPivotX 14
int styleable ConstraintOverride_android_transformPivotY 15
int styleable ConstraintOverride_android_translationX 16
int styleable ConstraintOverride_android_translationY 17
int styleable ConstraintOverride_android_scaleX 18
int styleable ConstraintOverride_android_scaleY 19
int styleable ConstraintOverride_android_rotation 20
int styleable ConstraintOverride_android_rotationX 21
int styleable ConstraintOverride_android_rotationY 22
int styleable ConstraintOverride_android_layout_marginStart 23
int styleable ConstraintOverride_android_layout_marginEnd 24
int styleable ConstraintOverride_android_translationZ 25
int styleable ConstraintOverride_android_elevation 26
int styleable ConstraintOverride_animateCircleAngleTo 27
int styleable ConstraintOverride_animateRelativeTo 28
int styleable ConstraintOverride_barrierAllowsGoneWidgets 29
int styleable ConstraintOverride_barrierDirection 30
int styleable ConstraintOverride_barrierMargin 31
int styleable ConstraintOverride_chainUseRtl 32
int styleable ConstraintOverride_constraint_referenced_ids 33
int styleable ConstraintOverride_drawPath 34
int styleable ConstraintOverride_flow_firstHorizontalBias 35
int styleable ConstraintOverride_flow_firstHorizontalStyle 36
int styleable ConstraintOverride_flow_firstVerticalBias 37
int styleable ConstraintOverride_flow_firstVerticalStyle 38
int styleable ConstraintOverride_flow_horizontalAlign 39
int styleable ConstraintOverride_flow_horizontalBias 40
int styleable ConstraintOverride_flow_horizontalGap 41
int styleable ConstraintOverride_flow_horizontalStyle 42
int styleable ConstraintOverride_flow_lastHorizontalBias 43
int styleable ConstraintOverride_flow_lastHorizontalStyle 44
int styleable ConstraintOverride_flow_lastVerticalBias 45
int styleable ConstraintOverride_flow_lastVerticalStyle 46
int styleable ConstraintOverride_flow_maxElementsWrap 47
int styleable ConstraintOverride_flow_verticalAlign 48
int styleable ConstraintOverride_flow_verticalBias 49
int styleable ConstraintOverride_flow_verticalGap 50
int styleable ConstraintOverride_flow_verticalStyle 51
int styleable ConstraintOverride_flow_wrapMode 52
int styleable ConstraintOverride_guidelineUseRtl 53
int styleable ConstraintOverride_layout_constrainedHeight 54
int styleable ConstraintOverride_layout_constrainedWidth 55
int styleable ConstraintOverride_layout_constraintBaseline_creator 56
int styleable ConstraintOverride_layout_constraintBottom_creator 57
int styleable ConstraintOverride_layout_constraintCircleAngle 58
int styleable ConstraintOverride_layout_constraintCircleRadius 59
int styleable ConstraintOverride_layout_constraintDimensionRatio 60
int styleable ConstraintOverride_layout_constraintGuide_begin 61
int styleable ConstraintOverride_layout_constraintGuide_end 62
int styleable ConstraintOverride_layout_constraintGuide_percent 63
int styleable ConstraintOverride_layout_constraintHeight 64
int styleable ConstraintOverride_layout_constraintHeight_default 65
int styleable ConstraintOverride_layout_constraintHeight_max 66
int styleable ConstraintOverride_layout_constraintHeight_min 67
int styleable ConstraintOverride_layout_constraintHeight_percent 68
int styleable ConstraintOverride_layout_constraintHorizontal_bias 69
int styleable ConstraintOverride_layout_constraintHorizontal_chainStyle 70
int styleable ConstraintOverride_layout_constraintHorizontal_weight 71
int styleable ConstraintOverride_layout_constraintLeft_creator 72
int styleable ConstraintOverride_layout_constraintRight_creator 73
int styleable ConstraintOverride_layout_constraintTag 74
int styleable ConstraintOverride_layout_constraintTop_creator 75
int styleable ConstraintOverride_layout_constraintVertical_bias 76
int styleable ConstraintOverride_layout_constraintVertical_chainStyle 77
int styleable ConstraintOverride_layout_constraintVertical_weight 78
int styleable ConstraintOverride_layout_constraintWidth 79
int styleable ConstraintOverride_layout_constraintWidth_default 80
int styleable ConstraintOverride_layout_constraintWidth_max 81
int styleable ConstraintOverride_layout_constraintWidth_min 82
int styleable ConstraintOverride_layout_constraintWidth_percent 83
int styleable ConstraintOverride_layout_editor_absoluteX 84
int styleable ConstraintOverride_layout_editor_absoluteY 85
int styleable ConstraintOverride_layout_goneMarginBaseline 86
int styleable ConstraintOverride_layout_goneMarginBottom 87
int styleable ConstraintOverride_layout_goneMarginEnd 88
int styleable ConstraintOverride_layout_goneMarginLeft 89
int styleable ConstraintOverride_layout_goneMarginRight 90
int styleable ConstraintOverride_layout_goneMarginStart 91
int styleable ConstraintOverride_layout_goneMarginTop 92
int styleable ConstraintOverride_layout_marginBaseline 93
int styleable ConstraintOverride_layout_wrapBehaviorInParent 94
int styleable ConstraintOverride_motionProgress 95
int styleable ConstraintOverride_motionStagger 96
int styleable ConstraintOverride_motionTarget 97
int styleable ConstraintOverride_pathMotionArc 98
int styleable ConstraintOverride_pivotAnchor 99
int styleable ConstraintOverride_polarRelativeTo 100
int styleable ConstraintOverride_quantizeMotionInterpolator 101
int styleable ConstraintOverride_quantizeMotionPhase 102
int styleable ConstraintOverride_quantizeMotionSteps 103
int styleable ConstraintOverride_transformPivotTarget 104
int styleable ConstraintOverride_transitionEasing 105
int styleable ConstraintOverride_transitionPathRotate 106
int styleable ConstraintOverride_visibilityMode 107
int[] styleable ConstraintSet { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x010101b5, 0x010101b6, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f04003a, 0x7f04003b, 0x7f040063, 0x7f040064, 0x7f040065, 0x7f0400b6, 0x7f040110, 0x7f040114, 0x7f040115, 0x7f040160, 0x7f04017a, 0x7f0401c8, 0x7f0401c9, 0x7f0401ca, 0x7f0401cb, 0x7f0401cc, 0x7f0401cd, 0x7f0401ce, 0x7f0401cf, 0x7f0401d0, 0x7f0401d1, 0x7f0401d2, 0x7f0401d3, 0x7f0401d4, 0x7f0401d6, 0x7f0401d7, 0x7f0401d8, 0x7f0401d9, 0x7f0401da, 0x7f040225, 0x7f040296, 0x7f040297, 0x7f040298, 0x7f040299, 0x7f04029a, 0x7f04029b, 0x7f04029c, 0x7f04029d, 0x7f04029e, 0x7f04029f, 0x7f0402a0, 0x7f0402a1, 0x7f0402a2, 0x7f0402a3, 0x7f0402a4, 0x7f0402a5, 0x7f0402a6, 0x7f0402a7, 0x7f0402a9, 0x7f0402aa, 0x7f0402ab, 0x7f0402ac, 0x7f0402ad, 0x7f0402ae, 0x7f0402af, 0x7f0402b0, 0x7f0402b1, 0x7f0402b2, 0x7f0402b3, 0x7f0402b4, 0x7f0402b5, 0x7f0402b6, 0x7f0402b7, 0x7f0402b8, 0x7f0402b9, 0x7f0402ba, 0x7f0402bb, 0x7f0402bc, 0x7f0402bd, 0x7f0402be, 0x7f0402c0, 0x7f0402c1, 0x7f0402c2, 0x7f0402c3, 0x7f0402c5, 0x7f0402c6, 0x7f0402c7, 0x7f0402c8, 0x7f0402c9, 0x7f0402ca, 0x7f0402cb, 0x7f0402cc, 0x7f0402cd, 0x7f0402d0, 0x7f0402d5, 0x7f04033e, 0x7f04033f, 0x7f04037c, 0x7f040386, 0x7f0403a6, 0x7f0403c5, 0x7f0404f8, 0x7f0404fa }
int styleable ConstraintSet_android_orientation 0
int styleable ConstraintSet_android_id 1
int styleable ConstraintSet_android_visibility 2
int styleable ConstraintSet_android_layout_width 3
int styleable ConstraintSet_android_layout_height 4
int styleable ConstraintSet_android_layout_marginLeft 5
int styleable ConstraintSet_android_layout_marginTop 6
int styleable ConstraintSet_android_layout_marginRight 7
int styleable ConstraintSet_android_layout_marginBottom 8
int styleable ConstraintSet_android_maxWidth 9
int styleable ConstraintSet_android_maxHeight 10
int styleable ConstraintSet_android_minWidth 11
int styleable ConstraintSet_android_minHeight 12
int styleable ConstraintSet_android_pivotX 13
int styleable ConstraintSet_android_pivotY 14
int styleable ConstraintSet_android_alpha 15
int styleable ConstraintSet_android_transformPivotX 16
int styleable ConstraintSet_android_transformPivotY 17
int styleable ConstraintSet_android_translationX 18
int styleable ConstraintSet_android_translationY 19
int styleable ConstraintSet_android_scaleX 20
int styleable ConstraintSet_android_scaleY 21
int styleable ConstraintSet_android_rotation 22
int styleable ConstraintSet_android_rotationX 23
int styleable ConstraintSet_android_rotationY 24
int styleable ConstraintSet_android_layout_marginStart 25
int styleable ConstraintSet_android_layout_marginEnd 26
int styleable ConstraintSet_android_translationZ 27
int styleable ConstraintSet_android_elevation 28
int styleable ConstraintSet_animateCircleAngleTo 29
int styleable ConstraintSet_animateRelativeTo 30
int styleable ConstraintSet_barrierAllowsGoneWidgets 31
int styleable ConstraintSet_barrierDirection 32
int styleable ConstraintSet_barrierMargin 33
int styleable ConstraintSet_chainUseRtl 34
int styleable ConstraintSet_constraintRotate 35
int styleable ConstraintSet_constraint_referenced_ids 36
int styleable ConstraintSet_constraint_referenced_tags 37
int styleable ConstraintSet_deriveConstraintsFrom 38
int styleable ConstraintSet_drawPath 39
int styleable ConstraintSet_flow_firstHorizontalBias 40
int styleable ConstraintSet_flow_firstHorizontalStyle 41
int styleable ConstraintSet_flow_firstVerticalBias 42
int styleable ConstraintSet_flow_firstVerticalStyle 43
int styleable ConstraintSet_flow_horizontalAlign 44
int styleable ConstraintSet_flow_horizontalBias 45
int styleable ConstraintSet_flow_horizontalGap 46
int styleable ConstraintSet_flow_horizontalStyle 47
int styleable ConstraintSet_flow_lastHorizontalBias 48
int styleable ConstraintSet_flow_lastHorizontalStyle 49
int styleable ConstraintSet_flow_lastVerticalBias 50
int styleable ConstraintSet_flow_lastVerticalStyle 51
int styleable ConstraintSet_flow_maxElementsWrap 52
int styleable ConstraintSet_flow_verticalAlign 53
int styleable ConstraintSet_flow_verticalBias 54
int styleable ConstraintSet_flow_verticalGap 55
int styleable ConstraintSet_flow_verticalStyle 56
int styleable ConstraintSet_flow_wrapMode 57
int styleable ConstraintSet_guidelineUseRtl 58
int styleable ConstraintSet_layout_constrainedHeight 59
int styleable ConstraintSet_layout_constrainedWidth 60
int styleable ConstraintSet_layout_constraintBaseline_creator 61
int styleable ConstraintSet_layout_constraintBaseline_toBaselineOf 62
int styleable ConstraintSet_layout_constraintBaseline_toBottomOf 63
int styleable ConstraintSet_layout_constraintBaseline_toTopOf 64
int styleable ConstraintSet_layout_constraintBottom_creator 65
int styleable ConstraintSet_layout_constraintBottom_toBottomOf 66
int styleable ConstraintSet_layout_constraintBottom_toTopOf 67
int styleable ConstraintSet_layout_constraintCircle 68
int styleable ConstraintSet_layout_constraintCircleAngle 69
int styleable ConstraintSet_layout_constraintCircleRadius 70
int styleable ConstraintSet_layout_constraintDimensionRatio 71
int styleable ConstraintSet_layout_constraintEnd_toEndOf 72
int styleable ConstraintSet_layout_constraintEnd_toStartOf 73
int styleable ConstraintSet_layout_constraintGuide_begin 74
int styleable ConstraintSet_layout_constraintGuide_end 75
int styleable ConstraintSet_layout_constraintGuide_percent 76
int styleable ConstraintSet_layout_constraintHeight_default 77
int styleable ConstraintSet_layout_constraintHeight_max 78
int styleable ConstraintSet_layout_constraintHeight_min 79
int styleable ConstraintSet_layout_constraintHeight_percent 80
int styleable ConstraintSet_layout_constraintHorizontal_bias 81
int styleable ConstraintSet_layout_constraintHorizontal_chainStyle 82
int styleable ConstraintSet_layout_constraintHorizontal_weight 83
int styleable ConstraintSet_layout_constraintLeft_creator 84
int styleable ConstraintSet_layout_constraintLeft_toLeftOf 85
int styleable ConstraintSet_layout_constraintLeft_toRightOf 86
int styleable ConstraintSet_layout_constraintRight_creator 87
int styleable ConstraintSet_layout_constraintRight_toLeftOf 88
int styleable ConstraintSet_layout_constraintRight_toRightOf 89
int styleable ConstraintSet_layout_constraintStart_toEndOf 90
int styleable ConstraintSet_layout_constraintStart_toStartOf 91
int styleable ConstraintSet_layout_constraintTag 92
int styleable ConstraintSet_layout_constraintTop_creator 93
int styleable ConstraintSet_layout_constraintTop_toBottomOf 94
int styleable ConstraintSet_layout_constraintTop_toTopOf 95
int styleable ConstraintSet_layout_constraintVertical_bias 96
int styleable ConstraintSet_layout_constraintVertical_chainStyle 97
int styleable ConstraintSet_layout_constraintVertical_weight 98
int styleable ConstraintSet_layout_constraintWidth_default 99
int styleable ConstraintSet_layout_constraintWidth_max 100
int styleable ConstraintSet_layout_constraintWidth_min 101
int styleable ConstraintSet_layout_constraintWidth_percent 102
int styleable ConstraintSet_layout_editor_absoluteX 103
int styleable ConstraintSet_layout_editor_absoluteY 104
int styleable ConstraintSet_layout_goneMarginBaseline 105
int styleable ConstraintSet_layout_goneMarginBottom 106
int styleable ConstraintSet_layout_goneMarginEnd 107
int styleable ConstraintSet_layout_goneMarginLeft 108
int styleable ConstraintSet_layout_goneMarginRight 109
int styleable ConstraintSet_layout_goneMarginStart 110
int styleable ConstraintSet_layout_goneMarginTop 111
int styleable ConstraintSet_layout_marginBaseline 112
int styleable ConstraintSet_layout_wrapBehaviorInParent 113
int styleable ConstraintSet_motionProgress 114
int styleable ConstraintSet_motionStagger 115
int styleable ConstraintSet_pathMotionArc 116
int styleable ConstraintSet_pivotAnchor 117
int styleable ConstraintSet_polarRelativeTo 118
int styleable ConstraintSet_quantizeMotionSteps 119
int styleable ConstraintSet_transitionEasing 120
int styleable ConstraintSet_transitionPathRotate 121
int[] styleable CoordinatorLayout { 0x7f040286, 0x7f040454 }
int styleable CoordinatorLayout_keylines 0
int styleable CoordinatorLayout_statusBarBackground 1
int[] styleable CoordinatorLayout_Layout { 0x010100b3, 0x7f040291, 0x7f040292, 0x7f040293, 0x7f0402c4, 0x7f0402ce, 0x7f0402cf }
int styleable CoordinatorLayout_Layout_android_layout_gravity 0
int styleable CoordinatorLayout_Layout_layout_anchor 1
int styleable CoordinatorLayout_Layout_layout_anchorGravity 2
int styleable CoordinatorLayout_Layout_layout_behavior 3
int styleable CoordinatorLayout_Layout_layout_dodgeInsetEdges 4
int styleable CoordinatorLayout_Layout_layout_insetEdge 5
int styleable CoordinatorLayout_Layout_layout_keyline 6
int[] styleable CustomAttribute { 0x7f040048, 0x7f04013f, 0x7f040140, 0x7f040141, 0x7f040142, 0x7f040143, 0x7f040144, 0x7f040146, 0x7f040147, 0x7f040148, 0x7f040319 }
int styleable CustomAttribute_attributeName 0
int styleable CustomAttribute_customBoolean 1
int styleable CustomAttribute_customColorDrawableValue 2
int styleable CustomAttribute_customColorValue 3
int styleable CustomAttribute_customDimension 4
int styleable CustomAttribute_customFloatValue 5
int styleable CustomAttribute_customIntegerValue 6
int styleable CustomAttribute_customPixelDimension 7
int styleable CustomAttribute_customReference 8
int styleable CustomAttribute_customStringValue 9
int styleable CustomAttribute_methodName 10
int[] styleable DefaultTimeBar { 0x7f04002b, 0x7f04002c, 0x7f040061, 0x7f040062, 0x7f040092, 0x7f0403a3, 0x7f0403a4, 0x7f0403f5, 0x7f0403f6, 0x7f0403f7, 0x7f0403f8, 0x7f0403f9, 0x7f0404ec, 0x7f040500 }
int styleable DefaultTimeBar_ad_marker_color 0
int styleable DefaultTimeBar_ad_marker_width 1
int styleable DefaultTimeBar_bar_gravity 2
int styleable DefaultTimeBar_bar_height 3
int styleable DefaultTimeBar_buffered_color 4
int styleable DefaultTimeBar_played_ad_marker_color 5
int styleable DefaultTimeBar_played_color 6
int styleable DefaultTimeBar_scrubber_color 7
int styleable DefaultTimeBar_scrubber_disabled_size 8
int styleable DefaultTimeBar_scrubber_dragged_size 9
int styleable DefaultTimeBar_scrubber_drawable 10
int styleable DefaultTimeBar_scrubber_enabled_size 11
int styleable DefaultTimeBar_touch_target_height 12
int styleable DefaultTimeBar_unplayed_color 13
int[] styleable DialogFragmentNavigator { 0x01010003 }
int styleable DialogFragmentNavigator_android_name 0
int[] styleable DialogPreference { 0x010101f2, 0x010101f3, 0x010101f4, 0x010101f5, 0x010101f6, 0x010101f7, 0x7f040167, 0x7f040168, 0x7f040169, 0x7f04016d, 0x7f04034c, 0x7f0403b0 }
int styleable DialogPreference_android_dialogTitle 0
int styleable DialogPreference_android_dialogMessage 1
int styleable DialogPreference_android_dialogIcon 2
int styleable DialogPreference_android_positiveButtonText 3
int styleable DialogPreference_android_negativeButtonText 4
int styleable DialogPreference_android_dialogLayout 5
int styleable DialogPreference_dialogIcon 6
int styleable DialogPreference_dialogLayout 7
int styleable DialogPreference_dialogMessage 8
int styleable DialogPreference_dialogTitle 9
int styleable DialogPreference_negativeButtonText 10
int styleable DialogPreference_positiveButtonText 11
int[] styleable DrawerArrowToggle { 0x7f040044, 0x7f040046, 0x7f040060, 0x7f0400f7, 0x7f04017f, 0x7f0401ee, 0x7f040438, 0x7f0404be }
int styleable DrawerArrowToggle_arrowHeadLength 0
int styleable DrawerArrowToggle_arrowShaftLength 1
int styleable DrawerArrowToggle_barLength 2
int styleable DrawerArrowToggle_color 3
int styleable DrawerArrowToggle_drawableSize 4
int styleable DrawerArrowToggle_gapBetweenBars 5
int styleable DrawerArrowToggle_spinBars 6
int styleable DrawerArrowToggle_thickness 7
int[] styleable DrawerLayout { 0x7f04018e }
int styleable DrawerLayout_elevation 0
int[] styleable EditTextPreference { 0x7f040507 }
int styleable EditTextPreference_useSimpleSummaryProvider 0
int[] styleable ExtendedFloatingActionButton { 0x7f0400f3, 0x7f04018e, 0x7f0401b4, 0x7f040231, 0x7f04041b, 0x7f040429 }
int styleable ExtendedFloatingActionButton_collapsedSize 0
int styleable ExtendedFloatingActionButton_elevation 1
int styleable ExtendedFloatingActionButton_extendMotionSpec 2
int styleable ExtendedFloatingActionButton_hideMotionSpec 3
int styleable ExtendedFloatingActionButton_showMotionSpec 4
int styleable ExtendedFloatingActionButton_shrinkMotionSpec 5
int[] styleable ExtendedFloatingActionButton_Behavior_Layout { 0x7f040067, 0x7f040068 }
int styleable ExtendedFloatingActionButton_Behavior_Layout_behavior_autoHide 0
int styleable ExtendedFloatingActionButton_Behavior_Layout_behavior_autoShrink 1
int[] styleable FloatingActionButton { 0x0101000e, 0x7f04005b, 0x7f04005c, 0x7f040075, 0x7f04018e, 0x7f04019d, 0x7f0401bc, 0x7f0401bd, 0x7f040231, 0x7f04023f, 0x7f040312, 0x7f0403bf, 0x7f0403e2, 0x7f040410, 0x7f040413, 0x7f04041b, 0x7f040504 }
int styleable FloatingActionButton_android_enabled 0
int styleable FloatingActionButton_backgroundTint 1
int styleable FloatingActionButton_backgroundTintMode 2
int styleable FloatingActionButton_borderWidth 3
int styleable FloatingActionButton_elevation 4
int styleable FloatingActionButton_ensureMinTouchTargetSize 5
int styleable FloatingActionButton_fabCustomSize 6
int styleable FloatingActionButton_fabSize 7
int styleable FloatingActionButton_hideMotionSpec 8
int styleable FloatingActionButton_hoveredFocusedTranslationZ 9
int styleable FloatingActionButton_maxImageSize 10
int styleable FloatingActionButton_pressedTranslationZ 11
int styleable FloatingActionButton_rippleColor 12
int styleable FloatingActionButton_shapeAppearance 13
int styleable FloatingActionButton_shapeAppearanceOverlay 14
int styleable FloatingActionButton_showMotionSpec 15
int styleable FloatingActionButton_useCompatPadding 16
int[] styleable FloatingActionButton_Behavior_Layout { 0x7f040067 }
int styleable FloatingActionButton_Behavior_Layout_behavior_autoHide 0
int[] styleable FlowLayout { 0x7f04027a, 0x7f0402dd }
int styleable FlowLayout_itemSpacing 0
int styleable FlowLayout_lineSpacing 1
int[] styleable FontFamily { 0x7f0401e1, 0x7f0401e2, 0x7f0401e3, 0x7f0401e4, 0x7f0401e5, 0x7f0401e6, 0x7f0401e7 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f0401df, 0x7f0401e8, 0x7f0401e9, 0x7f0401ea, 0x7f0404ff }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable ForegroundLinearLayout { 0x01010109, 0x01010200, 0x7f0401eb }
int styleable ForegroundLinearLayout_android_foreground 0
int styleable ForegroundLinearLayout_android_foregroundGravity 1
int styleable ForegroundLinearLayout_foregroundInsidePadding 2
int[] styleable Fragment { 0x01010003, 0x010100d0, 0x010100d1 }
int styleable Fragment_android_name 0
int styleable Fragment_android_id 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x01010003, 0x010100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable FragmentNavigator { 0x01010003 }
int styleable FragmentNavigator_android_name 0
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable ImageFilterView { 0x7f040038, 0x7f040072, 0x7f040086, 0x7f040127, 0x7f04013c, 0x7f040255, 0x7f040256, 0x7f040257, 0x7f040258, 0x7f040368, 0x7f0403e4, 0x7f0403e5, 0x7f0403ef, 0x7f040514 }
int styleable ImageFilterView_altSrc 0
int styleable ImageFilterView_blendSrc 1
int styleable ImageFilterView_brightness 2
int styleable ImageFilterView_contrast 3
int styleable ImageFilterView_crossfade 4
int styleable ImageFilterView_imagePanX 5
int styleable ImageFilterView_imagePanY 6
int styleable ImageFilterView_imageRotate 7
int styleable ImageFilterView_imageZoom 8
int styleable ImageFilterView_overlay 9
int styleable ImageFilterView_round 10
int styleable ImageFilterView_roundPercent 11
int styleable ImageFilterView_saturation 12
int styleable ImageFilterView_warmth 13
int[] styleable Insets { 0x7f04036d, 0x7f04036f, 0x7f040370, 0x7f040373 }
int styleable Insets_paddingBottomSystemWindowInsets 0
int styleable Insets_paddingLeftSystemWindowInsets 1
int styleable Insets_paddingRightSystemWindowInsets 2
int styleable Insets_paddingTopSystemWindowInsets 3
int[] styleable KeyAttribute { 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f04013e, 0x7f0401ed, 0x7f04033e, 0x7f040340, 0x7f0404f6, 0x7f0404f8, 0x7f0404fa }
int styleable KeyAttribute_android_alpha 0
int styleable KeyAttribute_android_transformPivotX 1
int styleable KeyAttribute_android_transformPivotY 2
int styleable KeyAttribute_android_translationX 3
int styleable KeyAttribute_android_translationY 4
int styleable KeyAttribute_android_scaleX 5
int styleable KeyAttribute_android_scaleY 6
int styleable KeyAttribute_android_rotation 7
int styleable KeyAttribute_android_rotationX 8
int styleable KeyAttribute_android_rotationY 9
int styleable KeyAttribute_android_translationZ 10
int styleable KeyAttribute_android_elevation 11
int styleable KeyAttribute_curveFit 12
int styleable KeyAttribute_framePosition 13
int styleable KeyAttribute_motionProgress 14
int styleable KeyAttribute_motionTarget 15
int styleable KeyAttribute_transformPivotTarget 16
int styleable KeyAttribute_transitionEasing 17
int styleable KeyAttribute_transitionPathRotate 18
int[] styleable KeyCycle { 0x0101031f, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f04013e, 0x7f0401ed, 0x7f04033e, 0x7f040340, 0x7f0404f8, 0x7f0404fa, 0x7f040516, 0x7f040517, 0x7f040518, 0x7f040519, 0x7f04051a }
int styleable KeyCycle_android_alpha 0
int styleable KeyCycle_android_translationX 1
int styleable KeyCycle_android_translationY 2
int styleable KeyCycle_android_scaleX 3
int styleable KeyCycle_android_scaleY 4
int styleable KeyCycle_android_rotation 5
int styleable KeyCycle_android_rotationX 6
int styleable KeyCycle_android_rotationY 7
int styleable KeyCycle_android_translationZ 8
int styleable KeyCycle_android_elevation 9
int styleable KeyCycle_curveFit 10
int styleable KeyCycle_framePosition 11
int styleable KeyCycle_motionProgress 12
int styleable KeyCycle_motionTarget 13
int styleable KeyCycle_transitionEasing 14
int styleable KeyCycle_transitionPathRotate 15
int styleable KeyCycle_waveOffset 16
int styleable KeyCycle_wavePeriod 17
int styleable KeyCycle_wavePhase 18
int styleable KeyCycle_waveShape 19
int styleable KeyCycle_waveVariesBy 20
int[] styleable KeyFrame { }
int[] styleable KeyFramesAcceleration { }
int[] styleable KeyFramesVelocity { }
int[] styleable KeyPosition { 0x7f04013e, 0x7f04017a, 0x7f0401ed, 0x7f040284, 0x7f040340, 0x7f04037c, 0x7f04037f, 0x7f040380, 0x7f040381, 0x7f040382, 0x7f040430, 0x7f0404f8 }
int styleable KeyPosition_curveFit 0
int styleable KeyPosition_drawPath 1
int styleable KeyPosition_framePosition 2
int styleable KeyPosition_keyPositionType 3
int styleable KeyPosition_motionTarget 4
int styleable KeyPosition_pathMotionArc 5
int styleable KeyPosition_percentHeight 6
int styleable KeyPosition_percentWidth 7
int styleable KeyPosition_percentX 8
int styleable KeyPosition_percentY 9
int styleable KeyPosition_sizePercent 10
int styleable KeyPosition_transitionEasing 11
int[] styleable KeyTimeCycle { 0x0101031f, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f04013e, 0x7f0401ed, 0x7f04033e, 0x7f040340, 0x7f0404f8, 0x7f0404fa, 0x7f040515, 0x7f040516, 0x7f040517, 0x7f040518, 0x7f040519 }
int styleable KeyTimeCycle_android_alpha 0
int styleable KeyTimeCycle_android_translationX 1
int styleable KeyTimeCycle_android_translationY 2
int styleable KeyTimeCycle_android_scaleX 3
int styleable KeyTimeCycle_android_scaleY 4
int styleable KeyTimeCycle_android_rotation 5
int styleable KeyTimeCycle_android_rotationX 6
int styleable KeyTimeCycle_android_rotationY 7
int styleable KeyTimeCycle_android_translationZ 8
int styleable KeyTimeCycle_android_elevation 9
int styleable KeyTimeCycle_curveFit 10
int styleable KeyTimeCycle_framePosition 11
int styleable KeyTimeCycle_motionProgress 12
int styleable KeyTimeCycle_motionTarget 13
int styleable KeyTimeCycle_transitionEasing 14
int styleable KeyTimeCycle_transitionPathRotate 15
int styleable KeyTimeCycle_waveDecay 16
int styleable KeyTimeCycle_waveOffset 17
int styleable KeyTimeCycle_wavePeriod 18
int styleable KeyTimeCycle_wavePhase 19
int styleable KeyTimeCycle_waveShape 20
int[] styleable KeyTrigger { 0x7f0401ed, 0x7f040340, 0x7f040341, 0x7f040342, 0x7f040355, 0x7f040357, 0x7f040358, 0x7f0404fc, 0x7f0404fd, 0x7f0404fe, 0x7f04050f, 0x7f040510, 0x7f040511 }
int styleable KeyTrigger_framePosition 0
int styleable KeyTrigger_motionTarget 1
int styleable KeyTrigger_motion_postLayoutCollision 2
int styleable KeyTrigger_motion_triggerOnCollision 3
int styleable KeyTrigger_onCross 4
int styleable KeyTrigger_onNegativeCross 5
int styleable KeyTrigger_onPositiveCross 6
int styleable KeyTrigger_triggerId 7
int styleable KeyTrigger_triggerReceiver 8
int styleable KeyTrigger_triggerSlack 9
int styleable KeyTrigger_viewTransitionOnCross 10
int styleable KeyTrigger_viewTransitionOnNegativeCross 11
int styleable KeyTrigger_viewTransitionOnPositiveCross 12
int[] styleable Layout { 0x010100c4, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x010103b5, 0x010103b6, 0x7f040063, 0x7f040064, 0x7f040065, 0x7f0400b6, 0x7f040114, 0x7f040115, 0x7f040225, 0x7f040296, 0x7f040297, 0x7f040298, 0x7f040299, 0x7f04029a, 0x7f04029b, 0x7f04029c, 0x7f04029d, 0x7f04029e, 0x7f04029f, 0x7f0402a0, 0x7f0402a1, 0x7f0402a2, 0x7f0402a3, 0x7f0402a4, 0x7f0402a5, 0x7f0402a6, 0x7f0402a7, 0x7f0402a8, 0x7f0402a9, 0x7f0402aa, 0x7f0402ab, 0x7f0402ac, 0x7f0402ad, 0x7f0402ae, 0x7f0402af, 0x7f0402b0, 0x7f0402b1, 0x7f0402b2, 0x7f0402b3, 0x7f0402b4, 0x7f0402b5, 0x7f0402b6, 0x7f0402b7, 0x7f0402b9, 0x7f0402ba, 0x7f0402bb, 0x7f0402bc, 0x7f0402bd, 0x7f0402be, 0x7f0402bf, 0x7f0402c0, 0x7f0402c1, 0x7f0402c2, 0x7f0402c3, 0x7f0402c5, 0x7f0402c6, 0x7f0402c7, 0x7f0402c8, 0x7f0402c9, 0x7f0402ca, 0x7f0402cb, 0x7f0402cc, 0x7f0402cd, 0x7f0402d0, 0x7f0402d5, 0x7f040311, 0x7f040315, 0x7f04031c, 0x7f040320 }
int styleable Layout_android_orientation 0
int styleable Layout_android_layout_width 1
int styleable Layout_android_layout_height 2
int styleable Layout_android_layout_marginLeft 3
int styleable Layout_android_layout_marginTop 4
int styleable Layout_android_layout_marginRight 5
int styleable Layout_android_layout_marginBottom 6
int styleable Layout_android_layout_marginStart 7
int styleable Layout_android_layout_marginEnd 8
int styleable Layout_barrierAllowsGoneWidgets 9
int styleable Layout_barrierDirection 10
int styleable Layout_barrierMargin 11
int styleable Layout_chainUseRtl 12
int styleable Layout_constraint_referenced_ids 13
int styleable Layout_constraint_referenced_tags 14
int styleable Layout_guidelineUseRtl 15
int styleable Layout_layout_constrainedHeight 16
int styleable Layout_layout_constrainedWidth 17
int styleable Layout_layout_constraintBaseline_creator 18
int styleable Layout_layout_constraintBaseline_toBaselineOf 19
int styleable Layout_layout_constraintBaseline_toBottomOf 20
int styleable Layout_layout_constraintBaseline_toTopOf 21
int styleable Layout_layout_constraintBottom_creator 22
int styleable Layout_layout_constraintBottom_toBottomOf 23
int styleable Layout_layout_constraintBottom_toTopOf 24
int styleable Layout_layout_constraintCircle 25
int styleable Layout_layout_constraintCircleAngle 26
int styleable Layout_layout_constraintCircleRadius 27
int styleable Layout_layout_constraintDimensionRatio 28
int styleable Layout_layout_constraintEnd_toEndOf 29
int styleable Layout_layout_constraintEnd_toStartOf 30
int styleable Layout_layout_constraintGuide_begin 31
int styleable Layout_layout_constraintGuide_end 32
int styleable Layout_layout_constraintGuide_percent 33
int styleable Layout_layout_constraintHeight 34
int styleable Layout_layout_constraintHeight_default 35
int styleable Layout_layout_constraintHeight_max 36
int styleable Layout_layout_constraintHeight_min 37
int styleable Layout_layout_constraintHeight_percent 38
int styleable Layout_layout_constraintHorizontal_bias 39
int styleable Layout_layout_constraintHorizontal_chainStyle 40
int styleable Layout_layout_constraintHorizontal_weight 41
int styleable Layout_layout_constraintLeft_creator 42
int styleable Layout_layout_constraintLeft_toLeftOf 43
int styleable Layout_layout_constraintLeft_toRightOf 44
int styleable Layout_layout_constraintRight_creator 45
int styleable Layout_layout_constraintRight_toLeftOf 46
int styleable Layout_layout_constraintRight_toRightOf 47
int styleable Layout_layout_constraintStart_toEndOf 48
int styleable Layout_layout_constraintStart_toStartOf 49
int styleable Layout_layout_constraintTop_creator 50
int styleable Layout_layout_constraintTop_toBottomOf 51
int styleable Layout_layout_constraintTop_toTopOf 52
int styleable Layout_layout_constraintVertical_bias 53
int styleable Layout_layout_constraintVertical_chainStyle 54
int styleable Layout_layout_constraintVertical_weight 55
int styleable Layout_layout_constraintWidth 56
int styleable Layout_layout_constraintWidth_default 57
int styleable Layout_layout_constraintWidth_max 58
int styleable Layout_layout_constraintWidth_min 59
int styleable Layout_layout_constraintWidth_percent 60
int styleable Layout_layout_editor_absoluteX 61
int styleable Layout_layout_editor_absoluteY 62
int styleable Layout_layout_goneMarginBaseline 63
int styleable Layout_layout_goneMarginBottom 64
int styleable Layout_layout_goneMarginEnd 65
int styleable Layout_layout_goneMarginLeft 66
int styleable Layout_layout_goneMarginRight 67
int styleable Layout_layout_goneMarginStart 68
int styleable Layout_layout_goneMarginTop 69
int styleable Layout_layout_marginBaseline 70
int styleable Layout_layout_wrapBehaviorInParent 71
int styleable Layout_maxHeight 72
int styleable Layout_maxWidth 73
int styleable Layout_minHeight 74
int styleable Layout_minWidth 75
int[] styleable LeanbackGuidedStepTheme { 0x7f0401f2, 0x7f0401f3, 0x7f0401f4, 0x7f0401f5, 0x7f0401f6, 0x7f0401f7, 0x7f0401f8, 0x7f0401f9, 0x7f0401fa, 0x7f0401fb, 0x7f0401fc, 0x7f0401fd, 0x7f0401fe, 0x7f0401ff, 0x7f040200, 0x7f040201, 0x7f040202, 0x7f040203, 0x7f040204, 0x7f040205, 0x7f040206, 0x7f040207, 0x7f040208, 0x7f040209, 0x7f04020a, 0x7f04020b, 0x7f04020c, 0x7f04020d, 0x7f04020e, 0x7f04020f, 0x7f040210, 0x7f040211, 0x7f040212, 0x7f040213, 0x7f040214, 0x7f040215, 0x7f040216, 0x7f040217, 0x7f040218, 0x7f040219, 0x7f04021a, 0x7f04021b, 0x7f04021c, 0x7f04021d, 0x7f04021e, 0x7f04021f, 0x7f040220, 0x7f040221, 0x7f040222, 0x7f040223, 0x7f040224 }
int styleable LeanbackGuidedStepTheme_guidanceBreadcrumbStyle 0
int styleable LeanbackGuidedStepTheme_guidanceContainerStyle 1
int styleable LeanbackGuidedStepTheme_guidanceDescriptionStyle 2
int styleable LeanbackGuidedStepTheme_guidanceEntryAnimation 3
int styleable LeanbackGuidedStepTheme_guidanceIconStyle 4
int styleable LeanbackGuidedStepTheme_guidanceTitleStyle 5
int styleable LeanbackGuidedStepTheme_guidedActionCheckedAnimation 6
int styleable LeanbackGuidedStepTheme_guidedActionContentWidth 7
int styleable LeanbackGuidedStepTheme_guidedActionContentWidthNoIcon 8
int styleable LeanbackGuidedStepTheme_guidedActionContentWidthWeight 9
int styleable LeanbackGuidedStepTheme_guidedActionContentWidthWeightTwoPanels 10
int styleable LeanbackGuidedStepTheme_guidedActionDescriptionMinLines 11
int styleable LeanbackGuidedStepTheme_guidedActionDisabledChevronAlpha 12
int styleable LeanbackGuidedStepTheme_guidedActionEnabledChevronAlpha 13
int styleable LeanbackGuidedStepTheme_guidedActionItemCheckmarkStyle 14
int styleable LeanbackGuidedStepTheme_guidedActionItemChevronStyle 15
int styleable LeanbackGuidedStepTheme_guidedActionItemContainerStyle 16
int styleable LeanbackGuidedStepTheme_guidedActionItemContentStyle 17
int styleable LeanbackGuidedStepTheme_guidedActionItemDescriptionStyle 18
int styleable LeanbackGuidedStepTheme_guidedActionItemIconStyle 19
int styleable LeanbackGuidedStepTheme_guidedActionItemTitleStyle 20
int styleable LeanbackGuidedStepTheme_guidedActionPressedAnimation 21
int styleable LeanbackGuidedStepTheme_guidedActionTitleMaxLines 22
int styleable LeanbackGuidedStepTheme_guidedActionTitleMinLines 23
int styleable LeanbackGuidedStepTheme_guidedActionUncheckedAnimation 24
int styleable LeanbackGuidedStepTheme_guidedActionUnpressedAnimation 25
int styleable LeanbackGuidedStepTheme_guidedActionVerticalPadding 26
int styleable LeanbackGuidedStepTheme_guidedActionsBackground 27
int styleable LeanbackGuidedStepTheme_guidedActionsBackgroundDark 28
int styleable LeanbackGuidedStepTheme_guidedActionsContainerStyle 29
int styleable LeanbackGuidedStepTheme_guidedActionsElevation 30
int styleable LeanbackGuidedStepTheme_guidedActionsEntryAnimation 31
int styleable LeanbackGuidedStepTheme_guidedActionsListStyle 32
int styleable LeanbackGuidedStepTheme_guidedActionsSelectorDrawable 33
int styleable LeanbackGuidedStepTheme_guidedActionsSelectorHideAnimation 34
int styleable LeanbackGuidedStepTheme_guidedActionsSelectorShowAnimation 35
int styleable LeanbackGuidedStepTheme_guidedActionsSelectorStyle 36
int styleable LeanbackGuidedStepTheme_guidedButtonActionsListStyle 37
int styleable LeanbackGuidedStepTheme_guidedButtonActionsWidthWeight 38
int styleable LeanbackGuidedStepTheme_guidedStepBackground 39
int styleable LeanbackGuidedStepTheme_guidedStepEntryAnimation 40
int styleable LeanbackGuidedStepTheme_guidedStepExitAnimation 41
int styleable LeanbackGuidedStepTheme_guidedStepHeightWeight 42
int styleable LeanbackGuidedStepTheme_guidedStepImeAppearingAnimation 43
int styleable LeanbackGuidedStepTheme_guidedStepImeDisappearingAnimation 44
int styleable LeanbackGuidedStepTheme_guidedStepKeyline 45
int styleable LeanbackGuidedStepTheme_guidedStepReentryAnimation 46
int styleable LeanbackGuidedStepTheme_guidedStepReturnAnimation 47
int styleable LeanbackGuidedStepTheme_guidedStepTheme 48
int styleable LeanbackGuidedStepTheme_guidedStepThemeFlag 49
int styleable LeanbackGuidedStepTheme_guidedSubActionsListStyle 50
int[] styleable LeanbackOnboardingTheme { 0x7f04035c, 0x7f04035d, 0x7f04035e, 0x7f04035f, 0x7f040360, 0x7f040361, 0x7f040362, 0x7f040363, 0x7f040364 }
int styleable LeanbackOnboardingTheme_onboardingDescriptionStyle 0
int styleable LeanbackOnboardingTheme_onboardingHeaderStyle 1
int styleable LeanbackOnboardingTheme_onboardingLogoStyle 2
int styleable LeanbackOnboardingTheme_onboardingMainIconStyle 3
int styleable LeanbackOnboardingTheme_onboardingNavigatorContainerStyle 4
int styleable LeanbackOnboardingTheme_onboardingPageIndicatorStyle 5
int styleable LeanbackOnboardingTheme_onboardingStartButtonStyle 6
int styleable LeanbackOnboardingTheme_onboardingTheme 7
int styleable LeanbackOnboardingTheme_onboardingTitleStyle 8
int[] styleable LeanbackTheme { 0x7f040066, 0x7f040087, 0x7f040088, 0x7f040089, 0x7f04008a, 0x7f04008b, 0x7f04008c, 0x7f04008d, 0x7f04008e, 0x7f04008f, 0x7f040090, 0x7f040091, 0x7f040150, 0x7f040151, 0x7f040155, 0x7f040156, 0x7f040157, 0x7f040158, 0x7f040159, 0x7f040162, 0x7f040163, 0x7f040164, 0x7f040165, 0x7f0401a6, 0x7f040229, 0x7f04022a, 0x7f04024f, 0x7f040250, 0x7f040251, 0x7f040252, 0x7f040253, 0x7f040254, 0x7f040281, 0x7f040369, 0x7f04036a, 0x7f04036b, 0x7f04038d, 0x7f04038e, 0x7f04038f, 0x7f040390, 0x7f040391, 0x7f040392, 0x7f040393, 0x7f040394, 0x7f040395, 0x7f040396, 0x7f040397, 0x7f040398, 0x7f040399, 0x7f04039a, 0x7f04039b, 0x7f04039c, 0x7f04039d, 0x7f04039e, 0x7f04039f, 0x7f0403a0, 0x7f0403a1, 0x7f0403a2, 0x7f0403e7, 0x7f0403e8, 0x7f0403e9, 0x7f0403eb, 0x7f0403ec, 0x7f0403ed, 0x7f0403ee, 0x7f040400, 0x7f040404 }
int styleable LeanbackTheme_baseCardViewStyle 0
int styleable LeanbackTheme_browsePaddingBottom 1
int styleable LeanbackTheme_browsePaddingEnd 2
int styleable LeanbackTheme_browsePaddingStart 3
int styleable LeanbackTheme_browsePaddingTop 4
int styleable LeanbackTheme_browseRowsFadingEdgeLength 5
int styleable LeanbackTheme_browseRowsMarginStart 6
int styleable LeanbackTheme_browseRowsMarginTop 7
int styleable LeanbackTheme_browseTitleIconStyle 8
int styleable LeanbackTheme_browseTitleTextStyle 9
int styleable LeanbackTheme_browseTitleViewLayout 10
int styleable LeanbackTheme_browseTitleViewStyle 11
int styleable LeanbackTheme_defaultBrandColor 12
int styleable LeanbackTheme_defaultBrandColorDark 13
int styleable LeanbackTheme_defaultSearchBrightColor 14
int styleable LeanbackTheme_defaultSearchColor 15
int styleable LeanbackTheme_defaultSearchIcon 16
int styleable LeanbackTheme_defaultSearchIconColor 17
int styleable LeanbackTheme_defaultSectionHeaderColor 18
int styleable LeanbackTheme_detailsActionButtonStyle 19
int styleable LeanbackTheme_detailsDescriptionBodyStyle 20
int styleable LeanbackTheme_detailsDescriptionSubtitleStyle 21
int styleable LeanbackTheme_detailsDescriptionTitleStyle 22
int styleable LeanbackTheme_errorMessageStyle 23
int styleable LeanbackTheme_headerStyle 24
int styleable LeanbackTheme_headersVerticalGridStyle 25
int styleable LeanbackTheme_imageCardViewBadgeStyle 26
int styleable LeanbackTheme_imageCardViewContentStyle 27
int styleable LeanbackTheme_imageCardViewImageStyle 28
int styleable LeanbackTheme_imageCardViewInfoAreaStyle 29
int styleable LeanbackTheme_imageCardViewStyle 30
int styleable LeanbackTheme_imageCardViewTitleStyle 31
int styleable LeanbackTheme_itemsVerticalGridStyle 32
int styleable LeanbackTheme_overlayDimActiveLevel 33
int styleable LeanbackTheme_overlayDimDimmedLevel 34
int styleable LeanbackTheme_overlayDimMaskColor 35
int styleable LeanbackTheme_playbackControlButtonLabelStyle 36
int styleable LeanbackTheme_playbackControlsActionIcons 37
int styleable LeanbackTheme_playbackControlsAutoHideTickleTimeout 38
int styleable LeanbackTheme_playbackControlsAutoHideTimeout 39
int styleable LeanbackTheme_playbackControlsButtonStyle 40
int styleable LeanbackTheme_playbackControlsIconHighlightColor 41
int styleable LeanbackTheme_playbackControlsTimeStyle 42
int styleable LeanbackTheme_playbackMediaItemDetailsStyle 43
int styleable LeanbackTheme_playbackMediaItemDurationStyle 44
int styleable LeanbackTheme_playbackMediaItemNameStyle 45
int styleable LeanbackTheme_playbackMediaItemNumberStyle 46
int styleable LeanbackTheme_playbackMediaItemNumberViewFlipperLayout 47
int styleable LeanbackTheme_playbackMediaItemNumberViewFlipperStyle 48
int styleable LeanbackTheme_playbackMediaItemPaddingStart 49
int styleable LeanbackTheme_playbackMediaItemRowStyle 50
int styleable LeanbackTheme_playbackMediaItemSeparatorStyle 51
int styleable LeanbackTheme_playbackMediaListHeaderStyle 52
int styleable LeanbackTheme_playbackMediaListHeaderTitleStyle 53
int styleable LeanbackTheme_playbackPaddingEnd 54
int styleable LeanbackTheme_playbackPaddingStart 55
int styleable LeanbackTheme_playbackProgressPrimaryColor 56
int styleable LeanbackTheme_playbackProgressSecondaryColor 57
int styleable LeanbackTheme_rowHeaderDescriptionStyle 58
int styleable LeanbackTheme_rowHeaderDockStyle 59
int styleable LeanbackTheme_rowHeaderStyle 60
int styleable LeanbackTheme_rowHorizontalGridStyle 61
int styleable LeanbackTheme_rowHoverCardDescriptionStyle 62
int styleable LeanbackTheme_rowHoverCardTitleStyle 63
int styleable LeanbackTheme_rowsVerticalGridStyle 64
int styleable LeanbackTheme_searchOrbViewStyle 65
int styleable LeanbackTheme_sectionHeaderStyle 66
int[] styleable LegacyPlayerControlView { 0x7f04002b, 0x7f04002c, 0x7f040061, 0x7f040062, 0x7f040092, 0x7f040129, 0x7f0403a3, 0x7f0403a4, 0x7f0403d9, 0x7f0403f5, 0x7f0403f6, 0x7f0403f7, 0x7f0403f8, 0x7f0403f9, 0x7f040421, 0x7f040422, 0x7f040423, 0x7f040424, 0x7f040425, 0x7f040427, 0x7f0404d2, 0x7f0404ec, 0x7f040500 }
int styleable LegacyPlayerControlView_ad_marker_color 0
int styleable LegacyPlayerControlView_ad_marker_width 1
int styleable LegacyPlayerControlView_bar_gravity 2
int styleable LegacyPlayerControlView_bar_height 3
int styleable LegacyPlayerControlView_buffered_color 4
int styleable LegacyPlayerControlView_controller_layout_id 5
int styleable LegacyPlayerControlView_played_ad_marker_color 6
int styleable LegacyPlayerControlView_played_color 7
int styleable LegacyPlayerControlView_repeat_toggle_modes 8
int styleable LegacyPlayerControlView_scrubber_color 9
int styleable LegacyPlayerControlView_scrubber_disabled_size 10
int styleable LegacyPlayerControlView_scrubber_dragged_size 11
int styleable LegacyPlayerControlView_scrubber_drawable 12
int styleable LegacyPlayerControlView_scrubber_enabled_size 13
int styleable LegacyPlayerControlView_show_fastforward_button 14
int styleable LegacyPlayerControlView_show_next_button 15
int styleable LegacyPlayerControlView_show_previous_button 16
int styleable LegacyPlayerControlView_show_rewind_button 17
int styleable LegacyPlayerControlView_show_shuffle_button 18
int styleable LegacyPlayerControlView_show_timeout 19
int styleable LegacyPlayerControlView_time_bar_min_update_interval 20
int styleable LegacyPlayerControlView_touch_target_height 21
int styleable LegacyPlayerControlView_unplayed_color 22
int[] styleable LinearLayoutCompat { 0x010100af, 0x010100c4, 0x01010126, 0x01010127, 0x01010128, 0x7f040170, 0x7f040172, 0x7f040316, 0x7f04041a }
int styleable LinearLayoutCompat_android_gravity 0
int styleable LinearLayoutCompat_android_orientation 1
int styleable LinearLayoutCompat_android_baselineAligned 2
int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 3
int styleable LinearLayoutCompat_android_weightSum 4
int styleable LinearLayoutCompat_divider 5
int styleable LinearLayoutCompat_dividerPadding 6
int styleable LinearLayoutCompat_measureWithLargestChild 7
int styleable LinearLayoutCompat_showDividers 8
int[] styleable LinearLayoutCompat_Layout { 0x010100b3, 0x010100f4, 0x010100f5, 0x01010181 }
int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
int styleable LinearLayoutCompat_Layout_android_layout_width 1
int styleable LinearLayoutCompat_Layout_android_layout_height 2
int styleable LinearLayoutCompat_Layout_android_layout_weight 3
int[] styleable LinearProgressIndicator { 0x7f040259, 0x7f04025d }
int styleable LinearProgressIndicator_indeterminateAnimationType 0
int styleable LinearProgressIndicator_indicatorDirectionLinear 1
int[] styleable ListPopupWindow { 0x010102ac, 0x010102ad }
int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
int styleable ListPopupWindow_android_dropDownVerticalOffset 1
int[] styleable ListPreference { 0x010100b2, 0x010101f8, 0x7f04019f, 0x7f0401a0, 0x7f040507 }
int styleable ListPreference_android_entries 0
int styleable ListPreference_android_entryValues 1
int styleable ListPreference_entries 2
int styleable ListPreference_entryValues 3
int styleable ListPreference_useSimpleSummaryProvider 4
int[] styleable LoadingImageView { 0x7f0400d9, 0x7f04024c, 0x7f04024d }
int styleable LoadingImageView_circleCrop 0
int styleable LoadingImageView_imageAspectRatio 1
int styleable LoadingImageView_imageAspectRatioAdjust 2
int[] styleable MaterialAlertDialog { 0x7f040054, 0x7f040055, 0x7f040056, 0x7f040057 }
int styleable MaterialAlertDialog_backgroundInsetBottom 0
int styleable MaterialAlertDialog_backgroundInsetEnd 1
int styleable MaterialAlertDialog_backgroundInsetStart 2
int styleable MaterialAlertDialog_backgroundInsetTop 3
int[] styleable MaterialAlertDialogTheme { 0x7f0402f1, 0x7f0402f2, 0x7f0402f3, 0x7f0402f4, 0x7f0402f5 }
int styleable MaterialAlertDialogTheme_materialAlertDialogBodyTextStyle 0
int styleable MaterialAlertDialogTheme_materialAlertDialogTheme 1
int styleable MaterialAlertDialogTheme_materialAlertDialogTitleIconStyle 2
int styleable MaterialAlertDialogTheme_materialAlertDialogTitlePanelStyle 3
int styleable MaterialAlertDialogTheme_materialAlertDialogTitleTextStyle 4
int[] styleable MaterialAutoCompleteTextView { 0x01010220 }
int styleable MaterialAutoCompleteTextView_android_inputType 0
int[] styleable MaterialButton { 0x010100d4, 0x010101b7, 0x010101b8, 0x010101b9, 0x010101ba, 0x010101e5, 0x7f04005b, 0x7f04005c, 0x7f040130, 0x7f04018e, 0x7f040240, 0x7f040242, 0x7f040243, 0x7f040244, 0x7f040247, 0x7f040248, 0x7f0403e2, 0x7f040410, 0x7f040413, 0x7f040457, 0x7f040458 }
int styleable MaterialButton_android_background 0
int styleable MaterialButton_android_insetLeft 1
int styleable MaterialButton_android_insetRight 2
int styleable MaterialButton_android_insetTop 3
int styleable MaterialButton_android_insetBottom 4
int styleable MaterialButton_android_checkable 5
int styleable MaterialButton_backgroundTint 6
int styleable MaterialButton_backgroundTintMode 7
int styleable MaterialButton_cornerRadius 8
int styleable MaterialButton_elevation 9
int styleable MaterialButton_icon 10
int styleable MaterialButton_iconGravity 11
int styleable MaterialButton_iconPadding 12
int styleable MaterialButton_iconSize 13
int styleable MaterialButton_iconTint 14
int styleable MaterialButton_iconTintMode 15
int styleable MaterialButton_rippleColor 16
int styleable MaterialButton_shapeAppearance 17
int styleable MaterialButton_shapeAppearanceOverlay 18
int styleable MaterialButton_strokeColor 19
int styleable MaterialButton_strokeWidth 20
int[] styleable MaterialButtonToggleGroup { 0x7f0400bc, 0x7f04040d, 0x7f04042f }
int styleable MaterialButtonToggleGroup_checkedButton 0
int styleable MaterialButtonToggleGroup_selectionRequired 1
int styleable MaterialButtonToggleGroup_singleSelection 2
int[] styleable MaterialCalendar { 0x0101020d, 0x7f04014c, 0x7f04014d, 0x7f04014e, 0x7f04014f, 0x7f04034f, 0x7f0403ca, 0x7f040526, 0x7f040527, 0x7f040528 }
int styleable MaterialCalendar_android_windowFullscreen 0
int styleable MaterialCalendar_dayInvalidStyle 1
int styleable MaterialCalendar_daySelectedStyle 2
int styleable MaterialCalendar_dayStyle 3
int styleable MaterialCalendar_dayTodayStyle 4
int styleable MaterialCalendar_nestedScrollable 5
int styleable MaterialCalendar_rangeFillColor 6
int styleable MaterialCalendar_yearSelectedStyle 7
int styleable MaterialCalendar_yearStyle 8
int styleable MaterialCalendar_yearTodayStyle 9
int[] styleable MaterialCalendarItem { 0x010101b7, 0x010101b8, 0x010101b9, 0x010101ba, 0x7f04026a, 0x7f040273, 0x7f040274, 0x7f04027b, 0x7f04027c, 0x7f040280 }
int styleable MaterialCalendarItem_android_insetLeft 0
int styleable MaterialCalendarItem_android_insetRight 1
int styleable MaterialCalendarItem_android_insetTop 2
int styleable MaterialCalendarItem_android_insetBottom 3
int styleable MaterialCalendarItem_itemFillColor 4
int styleable MaterialCalendarItem_itemShapeAppearance 5
int styleable MaterialCalendarItem_itemShapeAppearanceOverlay 6
int styleable MaterialCalendarItem_itemStrokeColor 7
int styleable MaterialCalendarItem_itemStrokeWidth 8
int styleable MaterialCalendarItem_itemTextColor 9
int[] styleable MaterialCardView { 0x010101e5, 0x7f0400a6, 0x7f0400be, 0x7f0400c0, 0x7f0400c1, 0x7f0400c2, 0x7f0403e2, 0x7f040410, 0x7f040413, 0x7f040451, 0x7f040457, 0x7f040458 }
int styleable MaterialCardView_android_checkable 0
int styleable MaterialCardView_cardForegroundColor 1
int styleable MaterialCardView_checkedIcon 2
int styleable MaterialCardView_checkedIconMargin 3
int styleable MaterialCardView_checkedIconSize 4
int styleable MaterialCardView_checkedIconTint 5
int styleable MaterialCardView_rippleColor 6
int styleable MaterialCardView_shapeAppearance 7
int styleable MaterialCardView_shapeAppearanceOverlay 8
int styleable MaterialCardView_state_dragged 9
int styleable MaterialCardView_strokeColor 10
int styleable MaterialCardView_strokeWidth 11
int[] styleable MaterialCheckBox { 0x7f04009f, 0x7f040506 }
int styleable MaterialCheckBox_buttonTint 0
int styleable MaterialCheckBox_useMaterialThemeColors 1
int[] styleable MaterialRadioButton { 0x7f04009f, 0x7f040506 }
int styleable MaterialRadioButton_buttonTint 0
int styleable MaterialRadioButton_useMaterialThemeColors 1
int[] styleable MaterialShape { 0x7f040410, 0x7f040413 }
int styleable MaterialShape_shapeAppearance 0
int styleable MaterialShape_shapeAppearanceOverlay 1
int[] styleable MaterialTextAppearance { 0x010104b6, 0x0101057f, 0x7f0402dc }
int styleable MaterialTextAppearance_android_letterSpacing 0
int styleable MaterialTextAppearance_android_lineHeight 1
int styleable MaterialTextAppearance_lineHeight 2
int[] styleable MaterialTextView { 0x01010034, 0x0101057f, 0x7f0402dc }
int styleable MaterialTextView_android_textAppearance 0
int styleable MaterialTextView_android_lineHeight 1
int styleable MaterialTextView_lineHeight 2
int[] styleable MaterialTimePicker { 0x7f0400e6, 0x7f040285 }
int styleable MaterialTimePicker_clockIcon 0
int styleable MaterialTimePicker_keyboardIcon 1
int[] styleable MaterialToolbar { 0x7f040348, 0x7f04045c, 0x7f0404d6 }
int styleable MaterialToolbar_navigationIconTint 0
int styleable MaterialToolbar_subtitleCentered 1
int styleable MaterialToolbar_titleCentered 2
int[] styleable MenuGroup { 0x0101000e, 0x010100d0, 0x01010194, 0x010101de, 0x010101df, 0x010101e0 }
int styleable MenuGroup_android_enabled 0
int styleable MenuGroup_android_id 1
int styleable MenuGroup_android_visible 2
int styleable MenuGroup_android_menuCategory 3
int styleable MenuGroup_android_orderInCategory 4
int styleable MenuGroup_android_checkableBehavior 5
int[] styleable MenuItem { 0x01010002, 0x0101000e, 0x010100d0, 0x01010106, 0x01010194, 0x010101de, 0x010101df, 0x010101e1, 0x010101e2, 0x010101e3, 0x010101e4, 0x010101e5, 0x0101026f, 0x7f040010, 0x7f040024, 0x7f040026, 0x7f040037, 0x7f040118, 0x7f040247, 0x7f040248, 0x7f040354, 0x7f040418, 0x7f0404e8 }
int styleable MenuItem_android_icon 0
int styleable MenuItem_android_enabled 1
int styleable MenuItem_android_id 2
int styleable MenuItem_android_checked 3
int styleable MenuItem_android_visible 4
int styleable MenuItem_android_menuCategory 5
int styleable MenuItem_android_orderInCategory 6
int styleable MenuItem_android_title 7
int styleable MenuItem_android_titleCondensed 8
int styleable MenuItem_android_alphabeticShortcut 9
int styleable MenuItem_android_numericShortcut 10
int styleable MenuItem_android_checkable 11
int styleable MenuItem_android_onClick 12
int styleable MenuItem_actionLayout 13
int styleable MenuItem_actionProviderClass 14
int styleable MenuItem_actionViewClass 15
int styleable MenuItem_alphabeticModifiers 16
int styleable MenuItem_contentDescription 17
int styleable MenuItem_iconTint 18
int styleable MenuItem_iconTintMode 19
int styleable MenuItem_numericModifiers 20
int styleable MenuItem_showAsAction 21
int styleable MenuItem_tooltipText 22
int[] styleable MenuView { 0x010100ae, 0x0101012c, 0x0101012d, 0x0101012e, 0x0101012f, 0x01010130, 0x01010131, 0x7f0403be, 0x7f040459 }
int styleable MenuView_android_windowAnimationStyle 0
int styleable MenuView_android_itemTextAppearance 1
int styleable MenuView_android_horizontalDivider 2
int styleable MenuView_android_verticalDivider 3
int styleable MenuView_android_headerBackground 4
int styleable MenuView_android_itemBackground 5
int styleable MenuView_android_itemIconDisabledAlpha 6
int styleable MenuView_preserveIconSpacing 7
int styleable MenuView_subMenuArrow 8
int[] styleable MockView { 0x7f040321, 0x7f040322, 0x7f040323, 0x7f040324, 0x7f040325, 0x7f040326 }
int styleable MockView_mock_diagonalsColor 0
int styleable MockView_mock_label 1
int styleable MockView_mock_labelBackgroundColor 2
int styleable MockView_mock_labelColor 3
int styleable MockView_mock_showDiagonals 4
int styleable MockView_mock_showLabel 5
int[] styleable Motion { 0x7f04003a, 0x7f04003b, 0x7f04017a, 0x7f04033d, 0x7f04033f, 0x7f04037c, 0x7f0403c3, 0x7f0403c4, 0x7f0403c5, 0x7f0404f8 }
int styleable Motion_animateCircleAngleTo 0
int styleable Motion_animateRelativeTo 1
int styleable Motion_drawPath 2
int styleable Motion_motionPathRotate 3
int styleable Motion_motionStagger 4
int styleable Motion_pathMotionArc 5
int styleable Motion_quantizeMotionInterpolator 6
int styleable Motion_quantizeMotionPhase 7
int styleable Motion_quantizeMotionSteps 8
int styleable Motion_transitionEasing 9
int[] styleable MotionEffect { 0x7f040333, 0x7f040334, 0x7f040335, 0x7f040336, 0x7f040337, 0x7f040338, 0x7f040339, 0x7f04033a }
int styleable MotionEffect_motionEffect_alpha 0
int styleable MotionEffect_motionEffect_end 1
int styleable MotionEffect_motionEffect_move 2
int styleable MotionEffect_motionEffect_start 3
int styleable MotionEffect_motionEffect_strict 4
int styleable MotionEffect_motionEffect_translationX 5
int styleable MotionEffect_motionEffect_translationY 6
int styleable MotionEffect_motionEffect_viewTransition 7
int[] styleable MotionHelper { 0x7f040356, 0x7f040359 }
int styleable MotionHelper_onHide 0
int styleable MotionHelper_onShow 1
int[] styleable MotionLabel { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x010100af, 0x0101014f, 0x01010164, 0x010103ac, 0x01010535, 0x7f040073, 0x7f040074, 0x7f0403f0, 0x7f0404a7, 0x7f0404a8, 0x7f0404a9, 0x7f0404aa, 0x7f0404ab, 0x7f0404b3, 0x7f0404b4, 0x7f0404b5, 0x7f0404b6, 0x7f0404b8, 0x7f0404b9, 0x7f0404ba, 0x7f0404bb }
int styleable MotionLabel_android_textSize 0
int styleable MotionLabel_android_typeface 1
int styleable MotionLabel_android_textStyle 2
int styleable MotionLabel_android_textColor 3
int styleable MotionLabel_android_gravity 4
int styleable MotionLabel_android_text 5
int styleable MotionLabel_android_shadowRadius 6
int styleable MotionLabel_android_fontFamily 7
int styleable MotionLabel_android_autoSizeTextType 8
int styleable MotionLabel_borderRound 9
int styleable MotionLabel_borderRoundPercent 10
int styleable MotionLabel_scaleFromTextSize 11
int styleable MotionLabel_textBackground 12
int styleable MotionLabel_textBackgroundPanX 13
int styleable MotionLabel_textBackgroundPanY 14
int styleable MotionLabel_textBackgroundRotate 15
int styleable MotionLabel_textBackgroundZoom 16
int styleable MotionLabel_textOutlineColor 17
int styleable MotionLabel_textOutlineThickness 18
int styleable MotionLabel_textPanX 19
int styleable MotionLabel_textPanY 20
int styleable MotionLabel_textureBlurFactor 21
int styleable MotionLabel_textureEffect 22
int styleable MotionLabel_textureHeight 23
int styleable MotionLabel_textureWidth 24
int[] styleable MotionLayout { 0x7f04003f, 0x7f04013d, 0x7f04028e, 0x7f040327, 0x7f04033e, 0x7f04041c }
int styleable MotionLayout_applyMotionScene 0
int styleable MotionLayout_currentState 1
int styleable MotionLayout_layoutDescription 2
int styleable MotionLayout_motionDebug 3
int styleable MotionLayout_motionProgress 4
int styleable MotionLayout_showPaths 5
int[] styleable MotionScene { 0x7f040152, 0x7f04028f }
int styleable MotionScene_defaultDuration 0
int styleable MotionScene_layoutDuringTransition 1
int[] styleable MotionTelltales { 0x7f04048d, 0x7f04048e, 0x7f04048f }
int styleable MotionTelltales_telltales_tailColor 0
int styleable MotionTelltales_telltales_tailScale 1
int styleable MotionTelltales_telltales_velocityMode 2
int[] styleable MultiSelectListPreference { 0x010100b2, 0x010101f8, 0x7f04019f, 0x7f0401a0 }
int styleable MultiSelectListPreference_android_entries 0
int styleable MultiSelectListPreference_android_entryValues 1
int styleable MultiSelectListPreference_entries 2
int styleable MultiSelectListPreference_entryValues 3
int[] styleable NavAction { 0x010100d0, 0x7f040161, 0x7f04019e, 0x7f0401a9, 0x7f04028c, 0x7f0403a7, 0x7f0403a8, 0x7f0403a9, 0x7f0403aa, 0x7f0403ab, 0x7f0403df }
int styleable NavAction_android_id 0
int styleable NavAction_destination 1
int styleable NavAction_enterAnim 2
int styleable NavAction_exitAnim 3
int styleable NavAction_launchSingleTop 4
int styleable NavAction_popEnterAnim 5
int styleable NavAction_popExitAnim 6
int styleable NavAction_popUpTo 7
int styleable NavAction_popUpToInclusive 8
int styleable NavAction_popUpToSaveState 9
int styleable NavAction_restoreState 10
int[] styleable NavArgument { 0x01010003, 0x010101ed, 0x7f040041, 0x7f040350 }
int styleable NavArgument_android_name 0
int styleable NavArgument_android_defaultValue 1
int styleable NavArgument_argType 2
int styleable NavArgument_nullable 3
int[] styleable NavDeepLink { 0x010104ee, 0x7f040002, 0x7f04031a, 0x7f040503 }
int styleable NavDeepLink_android_autoVerify 0
int styleable NavDeepLink_action 1
int styleable NavDeepLink_mimeType 2
int styleable NavDeepLink_uri 3
int[] styleable NavGraphNavigator { 0x7f040448 }
int styleable NavGraphNavigator_startDestination 0
int[] styleable NavHost { 0x7f040345 }
int styleable NavHost_navGraph 0
int[] styleable NavHostFragment { 0x7f040153 }
int styleable NavHostFragment_defaultNavHost 0
int[] styleable NavInclude { 0x7f0401f1 }
int styleable NavInclude_graph 0
int[] styleable NavigationBarView { 0x7f04005b, 0x7f04018e, 0x7f040269, 0x7f04026e, 0x7f04026f, 0x7f040272, 0x7f04027e, 0x7f04027f, 0x7f040280, 0x7f04028a, 0x7f040317 }
int styleable NavigationBarView_backgroundTint 0
int styleable NavigationBarView_elevation 1
int styleable NavigationBarView_itemBackground 2
int styleable NavigationBarView_itemIconSize 3
int styleable NavigationBarView_itemIconTint 4
int styleable NavigationBarView_itemRippleColor 5
int styleable NavigationBarView_itemTextAppearanceActive 6
int styleable NavigationBarView_itemTextAppearanceInactive 7
int styleable NavigationBarView_itemTextColor 8
int styleable NavigationBarView_labelVisibilityMode 9
int styleable NavigationBarView_menu 10
int[] styleable NavigationRailView { 0x7f040228, 0x7f040318 }
int styleable NavigationRailView_headerLayout 0
int styleable NavigationRailView_menuGravity 1
int[] styleable NavigationView { 0x010100d4, 0x010100dd, 0x0101011f, 0x7f04018e, 0x7f040228, 0x7f040269, 0x7f04026b, 0x7f04026d, 0x7f04026e, 0x7f04026f, 0x7f040270, 0x7f040273, 0x7f040274, 0x7f040275, 0x7f040276, 0x7f040277, 0x7f040278, 0x7f040279, 0x7f04027d, 0x7f040280, 0x7f040317, 0x7f040410, 0x7f040413 }
int styleable NavigationView_android_background 0
int styleable NavigationView_android_fitsSystemWindows 1
int styleable NavigationView_android_maxWidth 2
int styleable NavigationView_elevation 3
int styleable NavigationView_headerLayout 4
int styleable NavigationView_itemBackground 5
int styleable NavigationView_itemHorizontalPadding 6
int styleable NavigationView_itemIconPadding 7
int styleable NavigationView_itemIconSize 8
int styleable NavigationView_itemIconTint 9
int styleable NavigationView_itemMaxLines 10
int styleable NavigationView_itemShapeAppearance 11
int styleable NavigationView_itemShapeAppearanceOverlay 12
int styleable NavigationView_itemShapeFillColor 13
int styleable NavigationView_itemShapeInsetBottom 14
int styleable NavigationView_itemShapeInsetEnd 15
int styleable NavigationView_itemShapeInsetStart 16
int styleable NavigationView_itemShapeInsetTop 17
int styleable NavigationView_itemTextAppearance 18
int styleable NavigationView_itemTextColor 19
int styleable NavigationView_menu 20
int styleable NavigationView_shapeAppearance 21
int styleable NavigationView_shapeAppearanceOverlay 22
int[] styleable Navigator { 0x01010001, 0x010100d0, 0x7f0403e6 }
int styleable Navigator_android_label 0
int styleable Navigator_android_id 1
int styleable Navigator_route 2
int[] styleable OnClick { 0x7f0400e3, 0x7f04048b }
int styleable OnClick_clickAction 0
int styleable OnClick_targetId 1
int[] styleable OnSwipe { 0x7f040049, 0x7f040177, 0x7f040178, 0x7f040179, 0x7f0402db, 0x7f04030d, 0x7f040314, 0x7f040343, 0x7f04034d, 0x7f04035b, 0x7f0403e3, 0x7f040440, 0x7f040441, 0x7f040442, 0x7f040443, 0x7f040444, 0x7f0404e9, 0x7f0404ea, 0x7f0404eb }
int styleable OnSwipe_autoCompleteMode 0
int styleable OnSwipe_dragDirection 1
int styleable OnSwipe_dragScale 2
int styleable OnSwipe_dragThreshold 3
int styleable OnSwipe_limitBoundsTo 4
int styleable OnSwipe_maxAcceleration 5
int styleable OnSwipe_maxVelocity 6
int styleable OnSwipe_moveWhenScrollAtTop 7
int styleable OnSwipe_nestedScrollFlags 8
int styleable OnSwipe_onTouchUp 9
int styleable OnSwipe_rotationCenterId 10
int styleable OnSwipe_springBoundary 11
int styleable OnSwipe_springDamping 12
int styleable OnSwipe_springMass 13
int styleable OnSwipe_springStiffness 14
int styleable OnSwipe_springStopThreshold 15
int styleable OnSwipe_touchAnchorId 16
int styleable OnSwipe_touchAnchorSide 17
int styleable OnSwipe_touchRegionId 18
int[] styleable PagingIndicator { 0x7f040042, 0x7f040043, 0x7f040045, 0x7f040174, 0x7f040175, 0x7f040176, 0x7f0402d6 }
int styleable PagingIndicator_arrowBgColor 0
int styleable PagingIndicator_arrowColor 1
int styleable PagingIndicator_arrowRadius 2
int styleable PagingIndicator_dotBgColor 3
int styleable PagingIndicator_dotToArrowGap 4
int styleable PagingIndicator_dotToDotGap 5
int styleable PagingIndicator_lbDotRadius 6
int[] styleable PlayerControlView { 0x7f04002b, 0x7f04002c, 0x7f04003d, 0x7f040061, 0x7f040062, 0x7f040092, 0x7f040129, 0x7f0403a3, 0x7f0403a4, 0x7f0403d9, 0x7f0403f5, 0x7f0403f6, 0x7f0403f7, 0x7f0403f8, 0x7f0403f9, 0x7f040421, 0x7f040422, 0x7f040423, 0x7f040424, 0x7f040425, 0x7f040426, 0x7f040427, 0x7f040428, 0x7f0404d2, 0x7f0404ec, 0x7f040500 }
int styleable PlayerControlView_ad_marker_color 0
int styleable PlayerControlView_ad_marker_width 1
int styleable PlayerControlView_animation_enabled 2
int styleable PlayerControlView_bar_gravity 3
int styleable PlayerControlView_bar_height 4
int styleable PlayerControlView_buffered_color 5
int styleable PlayerControlView_controller_layout_id 6
int styleable PlayerControlView_played_ad_marker_color 7
int styleable PlayerControlView_played_color 8
int styleable PlayerControlView_repeat_toggle_modes 9
int styleable PlayerControlView_scrubber_color 10
int styleable PlayerControlView_scrubber_disabled_size 11
int styleable PlayerControlView_scrubber_dragged_size 12
int styleable PlayerControlView_scrubber_drawable 13
int styleable PlayerControlView_scrubber_enabled_size 14
int styleable PlayerControlView_show_fastforward_button 15
int styleable PlayerControlView_show_next_button 16
int styleable PlayerControlView_show_previous_button 17
int styleable PlayerControlView_show_rewind_button 18
int styleable PlayerControlView_show_shuffle_button 19
int styleable PlayerControlView_show_subtitle_button 20
int styleable PlayerControlView_show_timeout 21
int styleable PlayerControlView_show_vr_button 22
int styleable PlayerControlView_time_bar_min_update_interval 23
int styleable PlayerControlView_touch_target_height 24
int styleable PlayerControlView_unplayed_color 25
int[] styleable PlayerView { 0x7f04002b, 0x7f04002c, 0x7f04003d, 0x7f040047, 0x7f040051, 0x7f040061, 0x7f040062, 0x7f040092, 0x7f040129, 0x7f04015c, 0x7f040234, 0x7f040235, 0x7f040282, 0x7f0403a3, 0x7f0403a4, 0x7f0403a5, 0x7f0403d9, 0x7f0403db, 0x7f0403f5, 0x7f0403f6, 0x7f0403f7, 0x7f0403f8, 0x7f0403f9, 0x7f040420, 0x7f040425, 0x7f040426, 0x7f040427, 0x7f040428, 0x7f04042b, 0x7f040467, 0x7f0404d2, 0x7f0404ec, 0x7f040500, 0x7f040508, 0x7f040509 }
int styleable PlayerView_ad_marker_color 0
int styleable PlayerView_ad_marker_width 1
int styleable PlayerView_animation_enabled 2
int styleable PlayerView_artwork_display_mode 3
int styleable PlayerView_auto_show 4
int styleable PlayerView_bar_gravity 5
int styleable PlayerView_bar_height 6
int styleable PlayerView_buffered_color 7
int styleable PlayerView_controller_layout_id 8
int styleable PlayerView_default_artwork 9
int styleable PlayerView_hide_during_ads 10
int styleable PlayerView_hide_on_touch 11
int styleable PlayerView_keep_content_on_player_reset 12
int styleable PlayerView_played_ad_marker_color 13
int styleable PlayerView_played_color 14
int styleable PlayerView_player_layout_id 15
int styleable PlayerView_repeat_toggle_modes 16
int styleable PlayerView_resize_mode 17
int styleable PlayerView_scrubber_color 18
int styleable PlayerView_scrubber_disabled_size 19
int styleable PlayerView_scrubber_dragged_size 20
int styleable PlayerView_scrubber_drawable 21
int styleable PlayerView_scrubber_enabled_size 22
int styleable PlayerView_show_buffering 23
int styleable PlayerView_show_shuffle_button 24
int styleable PlayerView_show_subtitle_button 25
int styleable PlayerView_show_timeout 26
int styleable PlayerView_show_vr_button 27
int styleable PlayerView_shutter_background_color 28
int styleable PlayerView_surface_type 29
int styleable PlayerView_time_bar_min_update_interval 30
int styleable PlayerView_touch_target_height 31
int styleable PlayerView_unplayed_color 32
int styleable PlayerView_use_artwork 33
int styleable PlayerView_use_controller 34
int[] styleable PopupWindow { 0x01010176, 0x010102c9, 0x7f040367 }
int styleable PopupWindow_android_popupBackground 0
int styleable PopupWindow_android_popupAnimationStyle 1
int styleable PopupWindow_overlapAnchor 2
int[] styleable PopupWindowBackgroundState { 0x7f04044e }
int styleable PopupWindowBackgroundState_state_above_anchor 0
int[] styleable Preference { 0x01010002, 0x0101000d, 0x0101000e, 0x010100f2, 0x010101e1, 0x010101e6, 0x010101e8, 0x010101e9, 0x010101ea, 0x010101eb, 0x010101ec, 0x010101ed, 0x010101ee, 0x010102e3, 0x0101055c, 0x01010561, 0x7f040032, 0x7f040034, 0x7f04015b, 0x7f04015f, 0x7f040192, 0x7f040194, 0x7f0401ec, 0x7f040240, 0x7f040245, 0x7f040268, 0x7f040283, 0x7f04028d, 0x7f040365, 0x7f040384, 0x7f040408, 0x7f040416, 0x7f04042e, 0x7f040464, 0x7f0404d5, 0x7f04051b }
int styleable Preference_android_icon 0
int styleable Preference_android_persistent 1
int styleable Preference_android_enabled 2
int styleable Preference_android_layout 3
int styleable Preference_android_title 4
int styleable Preference_android_selectable 5
int styleable Preference_android_key 6
int styleable Preference_android_summary 7
int styleable Preference_android_order 8
int styleable Preference_android_widgetLayout 9
int styleable Preference_android_dependency 10
int styleable Preference_android_defaultValue 11
int styleable Preference_android_shouldDisableView 12
int styleable Preference_android_fragment 13
int styleable Preference_android_singleLineTitle 14
int styleable Preference_android_iconSpaceReserved 15
int styleable Preference_allowDividerAbove 16
int styleable Preference_allowDividerBelow 17
int styleable Preference_defaultValue 18
int styleable Preference_dependency 19
int styleable Preference_enableCopying 20
int styleable Preference_enabled 21
int styleable Preference_fragment 22
int styleable Preference_icon 23
int styleable Preference_iconSpaceReserved 24
int styleable Preference_isPreferenceVisible 25
int styleable Preference_key 26
int styleable Preference_layout 27
int styleable Preference_order 28
int styleable Preference_persistent 29
int styleable Preference_selectable 30
int styleable Preference_shouldDisableView 31
int styleable Preference_singleLineTitle 32
int styleable Preference_summary 33
int styleable Preference_title 34
int styleable Preference_widgetLayout 35
int[] styleable PreferenceFragment { 0x010100f2, 0x01010129, 0x0101012a, 0x7f040033 }
int styleable PreferenceFragment_android_layout 0
int styleable PreferenceFragment_android_divider 1
int styleable PreferenceFragment_android_dividerHeight 2
int styleable PreferenceFragment_allowDividerAfterLastItem 3
int[] styleable PreferenceFragmentCompat { 0x010100f2, 0x01010129, 0x0101012a, 0x7f040033 }
int styleable PreferenceFragmentCompat_android_layout 0
int styleable PreferenceFragmentCompat_android_divider 1
int styleable PreferenceFragmentCompat_android_dividerHeight 2
int styleable PreferenceFragmentCompat_allowDividerAfterLastItem 3
int[] styleable PreferenceGroup { 0x010101e7, 0x7f040263, 0x7f040366 }
int styleable PreferenceGroup_android_orderingFromXml 0
int styleable PreferenceGroup_initialExpandedChildrenCount 1
int styleable PreferenceGroup_orderingFromXml 2
int[] styleable PreferenceImageView { 0x0101011f, 0x01010120, 0x7f040311, 0x7f040315 }
int styleable PreferenceImageView_android_maxWidth 0
int styleable PreferenceImageView_android_maxHeight 1
int styleable PreferenceImageView_maxHeight 2
int styleable PreferenceImageView_maxWidth 3
int[] styleable PreferenceTheme { 0x7f0400b7, 0x7f04016a, 0x7f040188, 0x7f04018c, 0x7f0403b1, 0x7f0403b2, 0x7f0403b3, 0x7f0403b4, 0x7f0403b5, 0x7f0403b6, 0x7f0403b7, 0x7f0403b8, 0x7f0403b9, 0x7f0403ba, 0x7f040406, 0x7f04046a, 0x7f04046b }
int styleable PreferenceTheme_checkBoxPreferenceStyle 0
int styleable PreferenceTheme_dialogPreferenceStyle 1
int styleable PreferenceTheme_dropdownPreferenceStyle 2
int styleable PreferenceTheme_editTextPreferenceStyle 3
int styleable PreferenceTheme_preferenceCategoryStyle 4
int styleable PreferenceTheme_preferenceCategoryTitleTextAppearance 5
int styleable PreferenceTheme_preferenceCategoryTitleTextColor 6
int styleable PreferenceTheme_preferenceFragmentCompatStyle 7
int styleable PreferenceTheme_preferenceFragmentListStyle 8
int styleable PreferenceTheme_preferenceFragmentStyle 9
int styleable PreferenceTheme_preferenceInformationStyle 10
int styleable PreferenceTheme_preferenceScreenStyle 11
int styleable PreferenceTheme_preferenceStyle 12
int styleable PreferenceTheme_preferenceTheme 13
int styleable PreferenceTheme_seekBarPreferenceStyle 14
int styleable PreferenceTheme_switchPreferenceCompatStyle 15
int styleable PreferenceTheme_switchPreferenceStyle 16
int[] styleable PropertySet { 0x010100dc, 0x0101031f, 0x7f0402b8, 0x7f04033e, 0x7f040512 }
int styleable PropertySet_android_visibility 0
int styleable PropertySet_android_alpha 1
int styleable PropertySet_layout_constraintTag 2
int styleable PropertySet_motionProgress 3
int styleable PropertySet_visibilityMode 4
int[] styleable RadialViewGroup { 0x7f040308 }
int styleable RadialViewGroup_materialCircleRadius 0
int[] styleable RangeSlider { 0x7f04031e, 0x7f04050a }
int styleable RangeSlider_minSeparation 0
int styleable RangeSlider_values 1
int[] styleable RecycleListView { 0x7f04036c, 0x7f040372 }
int styleable RecycleListView_paddingBottomNoButtons 0
int styleable RecycleListView_paddingTopNoTitle 1
int[] styleable RecyclerView { 0x010100c4, 0x010100eb, 0x010100f1, 0x7f0401be, 0x7f0401bf, 0x7f0401c0, 0x7f0401c1, 0x7f0401c2, 0x7f040290, 0x7f0403e0, 0x7f040437, 0x7f040446 }
int styleable RecyclerView_android_orientation 0
int styleable RecyclerView_android_clipToPadding 1
int styleable RecyclerView_android_descendantFocusability 2
int styleable RecyclerView_fastScrollEnabled 3
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 4
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 5
int styleable RecyclerView_fastScrollVerticalThumbDrawable 6
int styleable RecyclerView_fastScrollVerticalTrackDrawable 7
int styleable RecyclerView_layoutManager 8
int styleable RecyclerView_reverseLayout 9
int styleable RecyclerView_spanCount 10
int styleable RecyclerView_stackFromEnd 11
int[] styleable ScrimInsetsFrameLayout { 0x7f040264 }
int styleable ScrimInsetsFrameLayout_insetForeground 0
int[] styleable ScrollingViewBehavior_Layout { 0x7f04006e }
int styleable ScrollingViewBehavior_Layout_behavior_overlapTop 0
int[] styleable SearchView { 0x010100da, 0x0101011f, 0x01010220, 0x01010264, 0x7f0400e8, 0x7f04010f, 0x7f040154, 0x7f0401f0, 0x7f040249, 0x7f04028d, 0x7f0403c6, 0x7f0403c7, 0x7f0403fa, 0x7f0403fb, 0x7f04045a, 0x7f040463, 0x7f040513 }
int styleable SearchView_android_focusable 0
int styleable SearchView_android_maxWidth 1
int styleable SearchView_android_inputType 2
int styleable SearchView_android_imeOptions 3
int styleable SearchView_closeIcon 4
int styleable SearchView_commitIcon 5
int styleable SearchView_defaultQueryHint 6
int styleable SearchView_goIcon 7
int styleable SearchView_iconifiedByDefault 8
int styleable SearchView_layout 9
int styleable SearchView_queryBackground 10
int styleable SearchView_queryHint 11
int styleable SearchView_searchHintIcon 12
int styleable SearchView_searchIcon 13
int styleable SearchView_submitBackground 14
int styleable SearchView_suggestionRowLayout 15
int styleable SearchView_voiceIcon 16
int[] styleable SeekBarPreference { 0x010100f2, 0x01010136, 0x7f04002d, 0x7f04031b, 0x7f040405, 0x7f04041d, 0x7f040502 }
int styleable SeekBarPreference_android_layout 0
int styleable SeekBarPreference_android_max 1
int styleable SeekBarPreference_adjustable 2
int styleable SeekBarPreference_min 3
int styleable SeekBarPreference_seekBarIncrement 4
int styleable SeekBarPreference_showSeekBarValue 5
int styleable SeekBarPreference_updatesContinuously 6
int[] styleable ShapeAppearance { 0x7f04012b, 0x7f04012c, 0x7f04012d, 0x7f04012e, 0x7f04012f, 0x7f040131, 0x7f040132, 0x7f040133, 0x7f040134, 0x7f040135 }
int styleable ShapeAppearance_cornerFamily 0
int styleable ShapeAppearance_cornerFamilyBottomLeft 1
int styleable ShapeAppearance_cornerFamilyBottomRight 2
int styleable ShapeAppearance_cornerFamilyTopLeft 3
int styleable ShapeAppearance_cornerFamilyTopRight 4
int styleable ShapeAppearance_cornerSize 5
int styleable ShapeAppearance_cornerSizeBottomLeft 6
int styleable ShapeAppearance_cornerSizeBottomRight 7
int styleable ShapeAppearance_cornerSizeTopLeft 8
int styleable ShapeAppearance_cornerSizeTopRight 9
int[] styleable ShapeableImageView { 0x7f04011f, 0x7f040120, 0x7f040121, 0x7f040122, 0x7f040123, 0x7f040124, 0x7f040125, 0x7f040410, 0x7f040413, 0x7f040457, 0x7f040458 }
int styleable ShapeableImageView_contentPadding 0
int styleable ShapeableImageView_contentPaddingBottom 1
int styleable ShapeableImageView_contentPaddingEnd 2
int styleable ShapeableImageView_contentPaddingLeft 3
int styleable ShapeableImageView_contentPaddingRight 4
int styleable ShapeableImageView_contentPaddingStart 5
int styleable ShapeableImageView_contentPaddingTop 6
int styleable ShapeableImageView_shapeAppearance 7
int styleable ShapeableImageView_shapeAppearanceOverlay 8
int styleable ShapeableImageView_strokeColor 9
int styleable ShapeableImageView_strokeWidth 10
int[] styleable SignInButton { 0x7f04009c, 0x7f040109, 0x7f0403f1 }
int styleable SignInButton_buttonSize 0
int styleable SignInButton_colorScheme 1
int styleable SignInButton_scopeUris 2
int[] styleable Slider { 0x0101000e, 0x01010024, 0x01010146, 0x010102de, 0x010102df, 0x7f040226, 0x7f040227, 0x7f040288, 0x7f040289, 0x7f0404bf, 0x7f0404c0, 0x7f0404c1, 0x7f0404c2, 0x7f0404c3, 0x7f0404cb, 0x7f0404cc, 0x7f0404cd, 0x7f0404d1, 0x7f0404ee, 0x7f0404ef, 0x7f0404f0, 0x7f0404f2 }
int styleable Slider_android_enabled 0
int styleable Slider_android_value 1
int styleable Slider_android_stepSize 2
int styleable Slider_android_valueFrom 3
int styleable Slider_android_valueTo 4
int styleable Slider_haloColor 5
int styleable Slider_haloRadius 6
int styleable Slider_labelBehavior 7
int styleable Slider_labelStyle 8
int styleable Slider_thumbColor 9
int styleable Slider_thumbElevation 10
int styleable Slider_thumbRadius 11
int styleable Slider_thumbStrokeColor 12
int styleable Slider_thumbStrokeWidth 13
int styleable Slider_tickColor 14
int styleable Slider_tickColorActive 15
int styleable Slider_tickColorInactive 16
int styleable Slider_tickVisible 17
int styleable Slider_trackColor 18
int styleable Slider_trackColorActive 19
int styleable Slider_trackColorInactive 20
int styleable Slider_trackHeight 21
int[] styleable Snackbar { 0x7f040434, 0x7f040435, 0x7f040436 }
int styleable Snackbar_snackbarButtonStyle 0
int styleable Snackbar_snackbarStyle 1
int styleable Snackbar_snackbarTextViewStyle 2
int[] styleable SnackbarLayout { 0x0101011f, 0x7f040025, 0x7f04003c, 0x7f040058, 0x7f04005b, 0x7f04005c, 0x7f04018e, 0x7f04030e }
int styleable SnackbarLayout_android_maxWidth 0
int styleable SnackbarLayout_actionTextColorAlpha 1
int styleable SnackbarLayout_animationMode 2
int styleable SnackbarLayout_backgroundOverlayColorAlpha 3
int styleable SnackbarLayout_backgroundTint 4
int styleable SnackbarLayout_backgroundTintMode 5
int styleable SnackbarLayout_elevation 6
int styleable SnackbarLayout_maxActionInlineWidth 7
int[] styleable Spinner { 0x010100b2, 0x01010176, 0x0101017b, 0x01010262, 0x7f0403ae }
int styleable Spinner_android_entries 0
int styleable Spinner_android_popupBackground 1
int styleable Spinner_android_prompt 2
int styleable Spinner_android_dropDownWidth 3
int styleable Spinner_popupTheme 4
int[] styleable SplitPairFilter { 0x7f0403c0, 0x7f040402, 0x7f040403 }
int styleable SplitPairFilter_primaryActivityName 0
int styleable SplitPairFilter_secondaryActivityAction 1
int styleable SplitPairFilter_secondaryActivityName 2
int[] styleable SplitPairRule { 0x7f0400e1, 0x7f0401c4, 0x7f0401c5, 0x7f04043b, 0x7f04043c, 0x7f04043d, 0x7f04043e }
int styleable SplitPairRule_clearTop 0
int styleable SplitPairRule_finishPrimaryWithSecondary 1
int styleable SplitPairRule_finishSecondaryWithPrimary 2
int styleable SplitPairRule_splitLayoutDirection 3
int styleable SplitPairRule_splitMinSmallestWidth 4
int styleable SplitPairRule_splitMinWidth 5
int styleable SplitPairRule_splitRatio 6
int[] styleable SplitPlaceholderRule { 0x7f040387, 0x7f04043b, 0x7f04043c, 0x7f04043d, 0x7f04043e }
int styleable SplitPlaceholderRule_placeholderActivityName 0
int styleable SplitPlaceholderRule_splitLayoutDirection 1
int styleable SplitPlaceholderRule_splitMinSmallestWidth 2
int styleable SplitPlaceholderRule_splitMinWidth 3
int styleable SplitPlaceholderRule_splitRatio 4
int[] styleable State { 0x010100d0, 0x7f040116 }
int styleable State_android_id 0
int styleable State_constraints 1
int[] styleable StateListDrawable { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable StateListDrawable_android_dither 0
int styleable StateListDrawable_android_visible 1
int styleable StateListDrawable_android_variablePadding 2
int styleable StateListDrawable_android_constantSize 3
int styleable StateListDrawable_android_enterFadeDuration 4
int styleable StateListDrawable_android_exitFadeDuration 5
int[] styleable StateListDrawableItem { 0x01010199 }
int styleable StateListDrawableItem_android_drawable 0
int[] styleable StateSet { 0x7f04015a }
int styleable StateSet_defaultState 0
int[] styleable SwitchCompat { 0x01010124, 0x01010125, 0x01010142, 0x7f04041e, 0x7f04043f, 0x7f040468, 0x7f040469, 0x7f04046d, 0x7f0404c4, 0x7f0404c5, 0x7f0404c6, 0x7f0404ed, 0x7f0404f4, 0x7f0404f5 }
int styleable SwitchCompat_android_textOn 0
int styleable SwitchCompat_android_textOff 1
int styleable SwitchCompat_android_thumb 2
int styleable SwitchCompat_showText 3
int styleable SwitchCompat_splitTrack 4
int styleable SwitchCompat_switchMinWidth 5
int styleable SwitchCompat_switchPadding 6
int styleable SwitchCompat_switchTextAppearance 7
int styleable SwitchCompat_thumbTextPadding 8
int styleable SwitchCompat_thumbTint 9
int styleable SwitchCompat_thumbTintMode 10
int styleable SwitchCompat_track 11
int styleable SwitchCompat_trackTint 12
int styleable SwitchCompat_trackTintMode 13
int[] styleable SwitchMaterial { 0x7f040506 }
int styleable SwitchMaterial_useMaterialThemeColors 0
int[] styleable SwitchPreference { 0x010101ef, 0x010101f0, 0x010101f1, 0x0101036b, 0x0101036c, 0x7f04016e, 0x7f040465, 0x7f040466, 0x7f04046e, 0x7f04046f }
int styleable SwitchPreference_android_summaryOn 0
int styleable SwitchPreference_android_summaryOff 1
int styleable SwitchPreference_android_disableDependentsState 2
int styleable SwitchPreference_android_switchTextOn 3
int styleable SwitchPreference_android_switchTextOff 4
int styleable SwitchPreference_disableDependentsState 5
int styleable SwitchPreference_summaryOff 6
int styleable SwitchPreference_summaryOn 7
int styleable SwitchPreference_switchTextOff 8
int styleable SwitchPreference_switchTextOn 9
int[] styleable SwitchPreferenceCompat { 0x010101ef, 0x010101f0, 0x010101f1, 0x0101036b, 0x0101036c, 0x7f04016e, 0x7f040465, 0x7f040466, 0x7f04046e, 0x7f04046f }
int styleable SwitchPreferenceCompat_android_summaryOn 0
int styleable SwitchPreferenceCompat_android_summaryOff 1
int styleable SwitchPreferenceCompat_android_disableDependentsState 2
int styleable SwitchPreferenceCompat_android_switchTextOn 3
int styleable SwitchPreferenceCompat_android_switchTextOff 4
int styleable SwitchPreferenceCompat_disableDependentsState 5
int styleable SwitchPreferenceCompat_summaryOff 6
int styleable SwitchPreferenceCompat_summaryOn 7
int styleable SwitchPreferenceCompat_switchTextOff 8
int styleable SwitchPreferenceCompat_switchTextOn 9
int[] styleable TabItem { 0x01010002, 0x010100f2, 0x0101014f }
int styleable TabItem_android_icon 0
int styleable TabItem_android_layout 1
int styleable TabItem_android_text 2
int[] styleable TabLayout { 0x7f040470, 0x7f040471, 0x7f040472, 0x7f040473, 0x7f040474, 0x7f040475, 0x7f040476, 0x7f040477, 0x7f040478, 0x7f040479, 0x7f04047a, 0x7f04047b, 0x7f04047c, 0x7f04047d, 0x7f04047e, 0x7f04047f, 0x7f040480, 0x7f040481, 0x7f040482, 0x7f040483, 0x7f040484, 0x7f040485, 0x7f040486, 0x7f040488, 0x7f040489, 0x7f04048a }
int styleable TabLayout_tabBackground 0
int styleable TabLayout_tabContentStart 1
int styleable TabLayout_tabGravity 2
int styleable TabLayout_tabIconTint 3
int styleable TabLayout_tabIconTintMode 4
int styleable TabLayout_tabIndicator 5
int styleable TabLayout_tabIndicatorAnimationDuration 6
int styleable TabLayout_tabIndicatorAnimationMode 7
int styleable TabLayout_tabIndicatorColor 8
int styleable TabLayout_tabIndicatorFullWidth 9
int styleable TabLayout_tabIndicatorGravity 10
int styleable TabLayout_tabIndicatorHeight 11
int styleable TabLayout_tabInlineLabel 12
int styleable TabLayout_tabMaxWidth 13
int styleable TabLayout_tabMinWidth 14
int styleable TabLayout_tabMode 15
int styleable TabLayout_tabPadding 16
int styleable TabLayout_tabPaddingBottom 17
int styleable TabLayout_tabPaddingEnd 18
int styleable TabLayout_tabPaddingStart 19
int styleable TabLayout_tabPaddingTop 20
int styleable TabLayout_tabRippleColor 21
int styleable TabLayout_tabSelectedTextColor 22
int styleable TabLayout_tabTextAppearance 23
int styleable TabLayout_tabTextColor 24
int styleable TabLayout_tabUnboundedRipple 25
int[] styleable TextAppearance { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x0101009a, 0x0101009b, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x01010585, 0x7f0401e0, 0x7f0401e9, 0x7f040490, 0x7f0404b2 }
int styleable TextAppearance_android_textSize 0
int styleable TextAppearance_android_typeface 1
int styleable TextAppearance_android_textStyle 2
int styleable TextAppearance_android_textColor 3
int styleable TextAppearance_android_textColorHint 4
int styleable TextAppearance_android_textColorLink 5
int styleable TextAppearance_android_shadowColor 6
int styleable TextAppearance_android_shadowDx 7
int styleable TextAppearance_android_shadowDy 8
int styleable TextAppearance_android_shadowRadius 9
int styleable TextAppearance_android_fontFamily 10
int styleable TextAppearance_android_textFontWeight 11
int styleable TextAppearance_fontFamily 12
int styleable TextAppearance_fontVariationSettings 13
int styleable TextAppearance_textAllCaps 14
int styleable TextAppearance_textLocale 15
int[] styleable TextEffects { 0x01010095, 0x01010096, 0x01010097, 0x0101014f, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x7f040073, 0x7f040074, 0x7f0404af, 0x7f0404b3, 0x7f0404b4 }
int styleable TextEffects_android_textSize 0
int styleable TextEffects_android_typeface 1
int styleable TextEffects_android_textStyle 2
int styleable TextEffects_android_text 3
int styleable TextEffects_android_shadowColor 4
int styleable TextEffects_android_shadowDx 5
int styleable TextEffects_android_shadowDy 6
int styleable TextEffects_android_shadowRadius 7
int styleable TextEffects_android_fontFamily 8
int styleable TextEffects_borderRound 9
int styleable TextEffects_borderRoundPercent 10
int styleable TextEffects_textFillColor 11
int styleable TextEffects_textOutlineColor 12
int styleable TextEffects_textOutlineThickness 13
int[] styleable TextInputEditText { 0x7f0404b0 }
int styleable TextInputEditText_textInputLayoutFocusedRectEnabled 0
int[] styleable TextInputLayout { 0x0101000e, 0x0101009a, 0x0101011f, 0x0101013f, 0x01010150, 0x7f04007b, 0x7f04007c, 0x7f04007d, 0x7f04007e, 0x7f04007f, 0x7f040080, 0x7f040081, 0x7f040082, 0x7f040083, 0x7f040084, 0x7f040085, 0x7f040136, 0x7f040137, 0x7f040138, 0x7f040139, 0x7f04013a, 0x7f04013b, 0x7f040195, 0x7f040196, 0x7f040197, 0x7f040198, 0x7f040199, 0x7f04019a, 0x7f0401a1, 0x7f0401a2, 0x7f0401a3, 0x7f0401a4, 0x7f0401a5, 0x7f0401a7, 0x7f0401a8, 0x7f0401ac, 0x7f04022c, 0x7f04022d, 0x7f04022e, 0x7f04022f, 0x7f040237, 0x7f040238, 0x7f040239, 0x7f04023a, 0x7f040377, 0x7f040378, 0x7f040379, 0x7f04037a, 0x7f04037b, 0x7f040388, 0x7f040389, 0x7f04038a, 0x7f0403bb, 0x7f0403bc, 0x7f0403bd, 0x7f040410, 0x7f040413, 0x7f040449, 0x7f04044a, 0x7f04044b, 0x7f04044c, 0x7f04044d, 0x7f040460, 0x7f040461, 0x7f040462 }
int styleable TextInputLayout_android_enabled 0
int styleable TextInputLayout_android_textColorHint 1
int styleable TextInputLayout_android_maxWidth 2
int styleable TextInputLayout_android_minWidth 3
int styleable TextInputLayout_android_hint 4
int styleable TextInputLayout_boxBackgroundColor 5
int styleable TextInputLayout_boxBackgroundMode 6
int styleable TextInputLayout_boxCollapsedPaddingTop 7
int styleable TextInputLayout_boxCornerRadiusBottomEnd 8
int styleable TextInputLayout_boxCornerRadiusBottomStart 9
int styleable TextInputLayout_boxCornerRadiusTopEnd 10
int styleable TextInputLayout_boxCornerRadiusTopStart 11
int styleable TextInputLayout_boxStrokeColor 12
int styleable TextInputLayout_boxStrokeErrorColor 13
int styleable TextInputLayout_boxStrokeWidth 14
int styleable TextInputLayout_boxStrokeWidthFocused 15
int styleable TextInputLayout_counterEnabled 16
int styleable TextInputLayout_counterMaxLength 17
int styleable TextInputLayout_counterOverflowTextAppearance 18
int styleable TextInputLayout_counterOverflowTextColor 19
int styleable TextInputLayout_counterTextAppearance 20
int styleable TextInputLayout_counterTextColor 21
int styleable TextInputLayout_endIconCheckable 22
int styleable TextInputLayout_endIconContentDescription 23
int styleable TextInputLayout_endIconDrawable 24
int styleable TextInputLayout_endIconMode 25
int styleable TextInputLayout_endIconTint 26
int styleable TextInputLayout_endIconTintMode 27
int styleable TextInputLayout_errorContentDescription 28
int styleable TextInputLayout_errorEnabled 29
int styleable TextInputLayout_errorIconDrawable 30
int styleable TextInputLayout_errorIconTint 31
int styleable TextInputLayout_errorIconTintMode 32
int styleable TextInputLayout_errorTextAppearance 33
int styleable TextInputLayout_errorTextColor 34
int styleable TextInputLayout_expandedHintEnabled 35
int styleable TextInputLayout_helperText 36
int styleable TextInputLayout_helperTextEnabled 37
int styleable TextInputLayout_helperTextTextAppearance 38
int styleable TextInputLayout_helperTextTextColor 39
int styleable TextInputLayout_hintAnimationEnabled 40
int styleable TextInputLayout_hintEnabled 41
int styleable TextInputLayout_hintTextAppearance 42
int styleable TextInputLayout_hintTextColor 43
int styleable TextInputLayout_passwordToggleContentDescription 44
int styleable TextInputLayout_passwordToggleDrawable 45
int styleable TextInputLayout_passwordToggleEnabled 46
int styleable TextInputLayout_passwordToggleTint 47
int styleable TextInputLayout_passwordToggleTintMode 48
int styleable TextInputLayout_placeholderText 49
int styleable TextInputLayout_placeholderTextAppearance 50
int styleable TextInputLayout_placeholderTextColor 51
int styleable TextInputLayout_prefixText 52
int styleable TextInputLayout_prefixTextAppearance 53
int styleable TextInputLayout_prefixTextColor 54
int styleable TextInputLayout_shapeAppearance 55
int styleable TextInputLayout_shapeAppearanceOverlay 56
int styleable TextInputLayout_startIconCheckable 57
int styleable TextInputLayout_startIconContentDescription 58
int styleable TextInputLayout_startIconDrawable 59
int styleable TextInputLayout_startIconTint 60
int styleable TextInputLayout_startIconTintMode 61
int styleable TextInputLayout_suffixText 62
int styleable TextInputLayout_suffixTextAppearance 63
int styleable TextInputLayout_suffixTextColor 64
int[] styleable ThemeEnforcement { 0x01010034, 0x7f04019b, 0x7f04019c }
int styleable ThemeEnforcement_android_textAppearance 0
int styleable ThemeEnforcement_enforceMaterialTheme 1
int styleable ThemeEnforcement_enforceTextAppearance 2
int[] styleable Toolbar { 0x010100af, 0x01010140, 0x7f040099, 0x7f0400f1, 0x7f0400f2, 0x7f040119, 0x7f04011a, 0x7f04011b, 0x7f04011c, 0x7f04011d, 0x7f04011e, 0x7f0402ee, 0x7f0402ef, 0x7f04030f, 0x7f040317, 0x7f040346, 0x7f040347, 0x7f0403ae, 0x7f04045b, 0x7f04045d, 0x7f04045e, 0x7f0404d5, 0x7f0404d9, 0x7f0404da, 0x7f0404db, 0x7f0404dc, 0x7f0404dd, 0x7f0404de, 0x7f0404df, 0x7f0404e0 }
int styleable Toolbar_android_gravity 0
int styleable Toolbar_android_minHeight 1
int styleable Toolbar_buttonGravity 2
int styleable Toolbar_collapseContentDescription 3
int styleable Toolbar_collapseIcon 4
int styleable Toolbar_contentInsetEnd 5
int styleable Toolbar_contentInsetEndWithActions 6
int styleable Toolbar_contentInsetLeft 7
int styleable Toolbar_contentInsetRight 8
int styleable Toolbar_contentInsetStart 9
int styleable Toolbar_contentInsetStartWithNavigation 10
int styleable Toolbar_logo 11
int styleable Toolbar_logoDescription 12
int styleable Toolbar_maxButtonHeight 13
int styleable Toolbar_menu 14
int styleable Toolbar_navigationContentDescription 15
int styleable Toolbar_navigationIcon 16
int styleable Toolbar_popupTheme 17
int styleable Toolbar_subtitle 18
int styleable Toolbar_subtitleTextAppearance 19
int styleable Toolbar_subtitleTextColor 20
int styleable Toolbar_title 21
int styleable Toolbar_titleMargin 22
int styleable Toolbar_titleMarginBottom 23
int styleable Toolbar_titleMarginEnd 24
int styleable Toolbar_titleMarginStart 25
int styleable Toolbar_titleMarginTop 26
int styleable Toolbar_titleMargins 27
int styleable Toolbar_titleTextAppearance 28
int styleable Toolbar_titleTextColor 29
int[] styleable Tooltip { 0x01010034, 0x010100d5, 0x010100f6, 0x0101013f, 0x01010140, 0x0101014f, 0x7f04005b }
int styleable Tooltip_android_textAppearance 0
int styleable Tooltip_android_padding 1
int styleable Tooltip_android_layout_margin 2
int styleable Tooltip_android_minWidth 3
int styleable Tooltip_android_minHeight 4
int styleable Tooltip_android_text 5
int styleable Tooltip_backgroundTint 6
int[] styleable Transform { 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f0404f6 }
int styleable Transform_android_transformPivotX 0
int styleable Transform_android_transformPivotY 1
int styleable Transform_android_translationX 2
int styleable Transform_android_translationY 3
int styleable Transform_android_scaleX 4
int styleable Transform_android_scaleY 5
int styleable Transform_android_rotation 6
int styleable Transform_android_rotationX 7
int styleable Transform_android_rotationY 8
int styleable Transform_android_translationZ 9
int styleable Transform_android_elevation 10
int styleable Transform_transformPivotTarget 11
int[] styleable Transition { 0x010100d0, 0x7f040050, 0x7f040112, 0x7f040113, 0x7f040189, 0x7f04028f, 0x7f04033b, 0x7f04037c, 0x7f040447, 0x7f0404f7, 0x7f0404f9 }
int styleable Transition_android_id 0
int styleable Transition_autoTransition 1
int styleable Transition_constraintSetEnd 2
int styleable Transition_constraintSetStart 3
int styleable Transition_duration 4
int styleable Transition_layoutDuringTransition 5
int styleable Transition_motionInterpolator 6
int styleable Transition_pathMotionArc 7
int styleable Transition_staggered 8
int styleable Transition_transitionDisable 9
int styleable Transition_transitionFlags 10
int[] styleable Variant { 0x7f040116, 0x7f0403d3, 0x7f0403d4, 0x7f0403d5, 0x7f0403d6 }
int styleable Variant_constraints 0
int styleable Variant_region_heightLessThan 1
int styleable Variant_region_heightMoreThan 2
int styleable Variant_region_widthLessThan 3
int styleable Variant_region_widthMoreThan 4
int[] styleable View { 0x01010000, 0x010100da, 0x7f04036e, 0x7f040371, 0x7f0404bc }
int styleable View_android_theme 0
int styleable View_android_focusable 1
int styleable View_paddingEnd 2
int styleable View_paddingStart 3
int styleable View_theme 4
int[] styleable ViewBackgroundHelper { 0x010100d4, 0x7f04005b, 0x7f04005c }
int styleable ViewBackgroundHelper_android_background 0
int styleable ViewBackgroundHelper_backgroundTint 1
int styleable ViewBackgroundHelper_backgroundTintMode 2
int[] styleable ViewPager2 { 0x010100c4 }
int styleable ViewPager2_android_orientation 0
int[] styleable ViewStubCompat { 0x010100d0, 0x010100f2, 0x010100f3 }
int styleable ViewStubCompat_android_id 0
int styleable ViewStubCompat_android_layout 1
int styleable ViewStubCompat_android_inflatedId 2
int[] styleable ViewTransition { 0x010100d0, 0x7f040000, 0x7f040001, 0x7f0400e2, 0x7f040189, 0x7f04024a, 0x7f04024b, 0x7f04033b, 0x7f040340, 0x7f04035a, 0x7f04037c, 0x7f04040f, 0x7f0404f7, 0x7f040501, 0x7f04050e }
int styleable ViewTransition_android_id 0
int styleable ViewTransition_SharedValue 1
int styleable ViewTransition_SharedValueId 2
int styleable ViewTransition_clearsTag 3
int styleable ViewTransition_duration 4
int styleable ViewTransition_ifTagNotSet 5
int styleable ViewTransition_ifTagSet 6
int styleable ViewTransition_motionInterpolator 7
int styleable ViewTransition_motionTarget 8
int styleable ViewTransition_onStateTransition 9
int styleable ViewTransition_pathMotionArc 10
int styleable ViewTransition_setsTag 11
int styleable ViewTransition_transitionDisable 12
int styleable ViewTransition_upDuration 13
int styleable ViewTransition_viewTransitionMode 14
int[] styleable include { 0x7f040111 }
int styleable include_constraintSet 0
int[] styleable lbBaseCardView { 0x7f040027, 0x7f0400a1, 0x7f0400a5, 0x7f0400a9, 0x7f0401b6, 0x7f040261, 0x7f04040b, 0x7f04040c }
int styleable lbBaseCardView_activatedAnimationDuration 0
int styleable lbBaseCardView_cardBackground 1
int styleable lbBaseCardView_cardForeground 2
int styleable lbBaseCardView_cardType 3
int styleable lbBaseCardView_extraVisibility 4
int styleable lbBaseCardView_infoVisibility 5
int styleable lbBaseCardView_selectedAnimationDelay 6
int styleable lbBaseCardView_selectedAnimationDuration 7
int[] styleable lbBaseCardView_Layout { 0x7f0402d4 }
int styleable lbBaseCardView_Layout_layout_viewType 0
int[] styleable lbBaseGridView { 0x010100af, 0x01010114, 0x01010115, 0x7f0401db, 0x7f0401dc, 0x7f0401dd, 0x7f0401de, 0x7f04023d, 0x7f04050b }
int styleable lbBaseGridView_android_gravity 0
int styleable lbBaseGridView_android_horizontalSpacing 1
int styleable lbBaseGridView_android_verticalSpacing 2
int styleable lbBaseGridView_focusOutEnd 3
int styleable lbBaseGridView_focusOutFront 4
int styleable lbBaseGridView_focusOutSideEnd 5
int styleable lbBaseGridView_focusOutSideStart 6
int styleable lbBaseGridView_horizontalMargin 7
int styleable lbBaseGridView_verticalMargin 8
int[] styleable lbDatePicker { 0x0101033f, 0x01010340, 0x7f04014b }
int styleable lbDatePicker_android_minDate 0
int styleable lbDatePicker_android_maxDate 1
int styleable lbDatePicker_datePickerFormat 2
int[] styleable lbHorizontalGridView { 0x7f040353, 0x7f0403ea }
int styleable lbHorizontalGridView_numberOfRows 0
int styleable lbHorizontalGridView_rowHeight 1
int[] styleable lbImageCardView { 0x7f040260, 0x7f0402d7 }
int styleable lbImageCardView_infoAreaBackground 0
int styleable lbImageCardView_lbImageCardViewType 1
int[] styleable lbPlaybackControlsActionIcons { 0x7f0400f0, 0x7f0401c3, 0x7f040236, 0x7f04037e, 0x7f040385, 0x7f04038c, 0x7f0403d7, 0x7f0403d8, 0x7f0403e1, 0x7f04042a, 0x7f040431, 0x7f040432, 0x7f0404c7, 0x7f0404c8, 0x7f0404c9, 0x7f0404ca }
int styleable lbPlaybackControlsActionIcons_closed_captioning 0
int styleable lbPlaybackControlsActionIcons_fast_forward 1
int styleable lbPlaybackControlsActionIcons_high_quality 2
int styleable lbPlaybackControlsActionIcons_pause 3
int styleable lbPlaybackControlsActionIcons_picture_in_picture 4
int styleable lbPlaybackControlsActionIcons_play 5
int styleable lbPlaybackControlsActionIcons_repeat 6
int styleable lbPlaybackControlsActionIcons_repeat_one 7
int styleable lbPlaybackControlsActionIcons_rewind 8
int styleable lbPlaybackControlsActionIcons_shuffle 9
int styleable lbPlaybackControlsActionIcons_skip_next 10
int styleable lbPlaybackControlsActionIcons_skip_previous 11
int styleable lbPlaybackControlsActionIcons_thumb_down 12
int styleable lbPlaybackControlsActionIcons_thumb_down_outline 13
int styleable lbPlaybackControlsActionIcons_thumb_up 14
int styleable lbPlaybackControlsActionIcons_thumb_up_outline 15
int[] styleable lbResizingTextView { 0x7f0402f0, 0x7f0403da, 0x7f0403dc, 0x7f0403dd, 0x7f0403de }
int styleable lbResizingTextView_maintainLineSpacing 0
int styleable lbResizingTextView_resizeTrigger 1
int styleable lbResizingTextView_resizedPaddingAdjustmentBottom 2
int styleable lbResizingTextView_resizedPaddingAdjustmentTop 3
int styleable lbResizingTextView_resizedTextSize 4
int[] styleable lbSearchOrbView { 0x7f0403fc, 0x7f0403fd, 0x7f0403fe, 0x7f0403ff }
int styleable lbSearchOrbView_searchOrbBrightColor 0
int styleable lbSearchOrbView_searchOrbColor 1
int styleable lbSearchOrbView_searchOrbIcon 2
int styleable lbSearchOrbView_searchOrbIconColor 3
int[] styleable lbSlide { 0x01010141, 0x01010198, 0x010103e2, 0x7f0402d8 }
int styleable lbSlide_android_interpolator 0
int styleable lbSlide_android_duration 1
int styleable lbSlide_android_startDelay 2
int styleable lbSlide_lb_slideEdge 3
int[] styleable lbTimePicker { 0x7f040265, 0x7f040505 }
int styleable lbTimePicker_is24HourFormat 0
int styleable lbTimePicker_useCurrentTime 1
int[] styleable lbVerticalGridView { 0x7f04010e, 0x7f040352 }
int styleable lbVerticalGridView_columnWidth 0
int styleable lbVerticalGridView_numberOfColumns 1
int transition lb_browse_enter_transition 0x7f150000
int transition lb_browse_entrance_transition 0x7f150001
int transition lb_browse_headers_in 0x7f150002
int transition lb_browse_headers_out 0x7f150003
int transition lb_browse_return_transition 0x7f150004
int transition lb_details_enter_transition 0x7f150005
int transition lb_details_return_transition 0x7f150006
int transition lb_enter_transition 0x7f150007
int transition lb_guidedstep_activity_enter 0x7f150008
int transition lb_guidedstep_activity_enter_bottom 0x7f150009
int transition lb_return_transition 0x7f15000a
int transition lb_shared_element_enter_transition 0x7f15000b
int transition lb_shared_element_return_transition 0x7f15000c
int transition lb_title_in 0x7f15000d
int transition lb_title_out 0x7f15000e
int transition lb_vertical_grid_enter_transition 0x7f15000f
int transition lb_vertical_grid_entrance_transition 0x7f150010
int transition lb_vertical_grid_return_transition 0x7f150011
int xml backup_rules 0x7f160000
int xml data_extraction_rules 0x7f160001
int xml file_paths 0x7f160002
int xml ga_ad_services_config 0x7f160003
int xml standalone_badge 0x7f160004
int xml standalone_badge_gravity_bottom_end 0x7f160005
int xml standalone_badge_gravity_bottom_start 0x7f160006
int xml standalone_badge_gravity_top_start 0x7f160007
int xml standalone_badge_offset 0x7f160008
