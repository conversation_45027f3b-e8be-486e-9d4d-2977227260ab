{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-91:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,409,502,631,740,838,928,1054,1179,1282,1413,1570,1691,1811,1921,2017,2153,2243,2344,2447,2553,2645,2762,2880,2998,3115,3216,3321,3446,3573,3693,3815,3902,3985,4088,4223,4380", "endColumns": "106,100,95,92,128,108,97,89,125,124,102,130,156,120,119,109,95,135,89,100,102,105,91,116,117,117,116,100,104,124,126,119,121,86,82,102,134,156,87", "endOffsets": "207,308,404,497,626,735,833,923,1049,1174,1277,1408,1565,1686,1806,1916,2012,2148,2238,2339,2442,2548,2640,2757,2875,2993,3110,3211,3316,3441,3568,3688,3810,3897,3980,4083,4218,4375,4463"}, "to": {"startLines": "177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,273", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14694,14801,14902,14998,15091,15220,15329,15427,15517,15643,15768,15871,16002,16159,16280,16400,16510,16606,16742,16832,16933,17036,17142,17234,17351,17469,17587,17704,17805,17910,18035,18162,18282,18404,18491,18574,18677,18812,23618", "endColumns": "106,100,95,92,128,108,97,89,125,124,102,130,156,120,119,109,95,135,89,100,102,105,91,116,117,117,116,100,104,124,126,119,121,86,82,102,134,156,87", "endOffsets": "14796,14897,14993,15086,15215,15324,15422,15512,15638,15763,15866,15997,16154,16275,16395,16505,16601,16737,16827,16928,17031,17137,17229,17346,17464,17582,17699,17800,17905,18030,18157,18277,18399,18486,18569,18672,18807,18964,23701"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,2759", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,2836"}, "to": {"startLines": "35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,279", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1729,1837,1941,2048,2130,2231,2345,2425,2504,2595,2688,2780,2874,2974,3067,3162,3255,3346,3440,3519,3624,3722,3820,3928,4028,4131,4286,24163", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "1832,1936,2043,2125,2226,2340,2420,2499,2590,2683,2775,2869,2969,3062,3157,3250,3341,3435,3514,3619,3717,3815,3923,4023,4126,4281,4378,24240"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,123,202,275,342,414,489,582", "endColumns": "67,78,72,66,71,74,92,96", "endOffsets": "118,197,270,337,409,484,577,674"}, "to": {"startLines": "62,114,227,228,229,230,231,232", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4383,9951,19871,19944,20011,20083,20158,20251", "endColumns": "67,78,72,66,71,74,92,96", "endOffsets": "4446,10025,19939,20006,20078,20153,20246,20343"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,282,388,500,575,676,797,931,1049,1188,1271,1372,1460,1556,1669,1791,1897,2036,2173,2306,2454,2576,2692,2812,2927,3016,3110,3229,3349,3445,3544,3648,3785,3928,4031,4128,4204,4278,4358,4439,4536,4611,4691,4788,4887,4982,5078,5161,5263,5359,5457,5591,5666,5763", "endColumns": "113,112,105,111,74,100,120,133,117,138,82,100,87,95,112,121,105,138,136,132,147,121,115,119,114,88,93,118,119,95,98,103,136,142,102,96,75,73,79,80,96,74,79,96,98,94,95,82,101,95,97,133,74,96,88", "endOffsets": "164,277,383,495,570,671,792,926,1044,1183,1266,1367,1455,1551,1664,1786,1892,2031,2168,2301,2449,2571,2687,2807,2922,3011,3105,3224,3344,3440,3539,3643,3780,3923,4026,4123,4199,4273,4353,4434,4531,4606,4686,4783,4882,4977,5073,5156,5258,5354,5452,5586,5661,5758,5847"}, "to": {"startLines": "63,64,65,66,80,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,116,171,278,281,283,288,289,290,291,292,293,294,295,296,297,298,299,300,301", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4451,4565,4678,4784,6161,6303,6404,6525,6659,6777,6916,6999,7100,7188,7284,7397,7519,7625,7764,7901,8034,8182,8304,8420,8540,8655,8744,8838,8957,9077,9173,9272,9376,9513,9656,9759,10128,14206,24083,24315,24497,25034,25109,25189,25286,25385,25480,25576,25659,25761,25857,25955,26089,26164,26261", "endColumns": "113,112,105,111,74,100,120,133,117,138,82,100,87,95,112,121,105,138,136,132,147,121,115,119,114,88,93,118,119,95,98,103,136,142,102,96,75,73,79,80,96,74,79,96,98,94,95,82,101,95,97,133,74,96,88", "endOffsets": "4560,4673,4779,4891,6231,6399,6520,6654,6772,6911,6994,7095,7183,7279,7392,7514,7620,7759,7896,8029,8177,8299,8415,8535,8650,8739,8833,8952,9072,9168,9267,9371,9508,9651,9754,9851,10199,14275,24158,24391,24589,25104,25184,25281,25380,25475,25571,25654,25756,25852,25950,26084,26159,26256,26345"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,308,390,471,572,667", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "115,174,241,303,385,466,567,662,746"}, "to": {"startLines": "143,144,145,146,147,148,149,150,151", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "12293,12358,12417,12484,12546,12628,12709,12810,12905", "endColumns": "64,58,66,61,81,80,100,94,83", "endOffsets": "12353,12412,12479,12541,12623,12704,12805,12900,12984"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "68,69,70,71,72,73,74,282", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4980,5073,5175,5270,5373,5476,5578,24396", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "5068,5170,5265,5368,5471,5573,5687,24492"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,110", "endOffsets": "160,271"}, "to": {"startLines": "266,267", "startColumns": "4,4", "startOffsets": "23008,23118", "endColumns": "109,110", "endOffsets": "23113,23224"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,11,19,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,349,849,1314,1393,1471,1547,1641,1733,1807,1872,1964,2054,2124,2188,2251,2320,2428,2537,2652,2718,2801,2873,2945,3037,3128,3192,3255,3308,3379,3434,3495,3553,3627,3691,3755,3815,3880,3944,4006,4072,4124,4181,4252,4323", "endLines": "10,18,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,107,108,114,65,82,71,71,91,90,63,62,52,70,54,60,57,73,63,63,59,64,63,61,65,51,56,70,70,55", "endOffsets": "344,844,1309,1388,1466,1542,1636,1728,1802,1867,1959,2049,2119,2183,2246,2315,2423,2532,2647,2713,2796,2868,2940,3032,3123,3187,3250,3303,3374,3429,3490,3548,3622,3686,3750,3810,3875,3939,4001,4067,4119,4176,4247,4318,4374"}, "to": {"startLines": "2,11,19,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,399,899,10352,10431,10509,10585,10679,10771,10845,10910,11002,11092,11162,11226,11289,11358,11466,11575,11690,11756,11839,11911,11983,12075,12166,12230,12989,13042,13113,13168,13229,13287,13361,13425,13489,13549,13614,13678,13740,13806,13858,13915,13986,14057", "endLines": "10,18,26,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169", "endColumns": "17,12,12,78,77,75,93,91,73,64,91,89,69,63,62,68,107,108,114,65,82,71,71,91,90,63,62,52,70,54,60,57,73,63,63,59,64,63,61,65,51,56,70,70,55", "endOffsets": "394,894,1359,10426,10504,10580,10674,10766,10840,10905,10997,11087,11157,11221,11284,11353,11461,11570,11685,11751,11834,11906,11978,12070,12161,12225,12288,13037,13108,13163,13224,13282,13356,13420,13484,13544,13609,13673,13735,13801,13853,13910,13981,14052,14108"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,172,265,351,484,653,735", "endColumns": "66,92,85,132,168,81,79", "endOffsets": "167,260,346,479,648,730,810"}, "to": {"startLines": "81,170,270,275,284,302,303", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6236,14113,23394,23786,24594,26350,26432", "endColumns": "66,92,85,132,168,81,79", "endOffsets": "6298,14201,23475,23914,24758,26427,26507"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,420,504,605,724,801,864,955,1024,1091,1191,1256,1317,1385,1447,1505,1619,1679,1740,1797,1870,1993,2074,2154,2272,2353,2434,2523,2590,2656,2734,2814,2898,2970,3044,3117,3187,3278,3349,3439,3534,3608,3691,3784,3833,3902,3988,4073,4135,4199,4262,4371,4463,4560,4653", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63", "endColumns": "12,83,100,118,76,62,90,68,66,99,64,60,67,61,57,113,59,60,56,72,122,80,79,117,80,80,88,66,65,77,79,83,71,73,72,69,90,70,89,94,73,82,92,48,68,85,84,61,63,62,108,91,96,92,79", "endOffsets": "415,499,600,719,796,859,950,1019,1086,1186,1251,1312,1380,1442,1500,1614,1674,1735,1792,1865,1988,2069,2149,2267,2348,2429,2518,2585,2651,2729,2809,2893,2965,3039,3112,3182,3273,3344,3434,3529,3603,3686,3779,3828,3897,3983,4068,4130,4194,4257,4366,4458,4555,4648,4728"}, "to": {"startLines": "27,67,75,76,77,118,172,173,176,215,216,217,218,219,220,221,222,223,224,225,226,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,274", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1364,4896,5692,5793,5912,10289,14280,14371,14627,18969,19069,19134,19195,19263,19325,19383,19497,19557,19618,19675,19748,20348,20429,20509,20627,20708,20789,20878,20945,21011,21089,21169,21253,21325,21399,21472,21542,21633,21704,21794,21889,21963,22046,22139,22188,22257,22343,22428,22490,22554,22617,22726,22818,22915,23706", "endLines": "34,67,75,76,77,118,172,173,176,215,216,217,218,219,220,221,222,223,224,225,226,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,274", "endColumns": "12,83,100,118,76,62,90,68,66,99,64,60,67,61,57,113,59,60,56,72,122,80,79,117,80,80,88,66,65,77,79,83,71,73,72,69,90,70,89,94,73,82,92,48,68,85,84,61,63,62,108,91,96,92,79", "endOffsets": "1724,4975,5788,5907,5984,10347,14366,14435,14689,19064,19129,19190,19258,19320,19378,19492,19552,19613,19670,19743,19866,20424,20504,20622,20703,20784,20873,20940,21006,21084,21164,21248,21320,21394,21467,21537,21628,21699,21789,21884,21958,22041,22134,22183,22252,22338,22423,22485,22549,22612,22721,22813,22910,23003,23781"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,277,372,470,555,636,742,826,907,978,1045,1126,1209,1279,1355,1429", "endColumns": "88,82,94,97,84,80,105,83,80,70,66,80,82,69,75,73,120", "endOffsets": "189,272,367,465,550,631,737,821,902,973,1040,1121,1204,1274,1350,1424,1545"}, "to": {"startLines": "78,79,113,115,117,174,175,268,269,271,272,276,277,280,285,286,287", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5989,6078,9856,10030,10204,14440,14521,23229,23313,23480,23551,23919,24000,24245,24763,24839,24913", "endColumns": "88,82,94,97,84,80,105,83,80,70,66,80,82,69,75,73,120", "endOffsets": "6073,6156,9946,10123,10284,14516,14622,23308,23389,23546,23613,23995,24078,24310,24834,24908,25029"}}]}]}