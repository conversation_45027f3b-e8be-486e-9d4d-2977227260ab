{"logs": [{"outputFile": "com.deshi.cinepix.test.app-mergeDebugAndroidTestResources-32:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\804c4e5761ff122de1231662730d5f72\\transformed\\ui-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,292,390,490,577,656,762,855,950,1015,1079,1163,1251,1336,1414,1483", "endColumns": "99,86,97,99,86,78,105,92,94,64,63,83,87,84,77,68,120", "endOffsets": "200,287,385,485,572,651,757,850,945,1010,1074,1158,1246,1331,1409,1478,1599"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "852,952,1039,1137,1237,1324,1403,1509,1602,1697,1762,1826,1910,1998,2184,2262,2331", "endColumns": "99,86,97,99,86,78,105,92,94,64,63,83,87,84,77,68,120", "endOffsets": "947,1034,1132,1232,1319,1398,1504,1597,1692,1757,1821,1905,1993,2078,2257,2326,2447"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\31a00a017e90aa446708533c4ea3d047\\transformed\\core-1.12.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "2,3,4,5,6,7,8,23", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,404,506,615,722,2083", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "198,300,399,501,610,717,847,2179"}}]}]}