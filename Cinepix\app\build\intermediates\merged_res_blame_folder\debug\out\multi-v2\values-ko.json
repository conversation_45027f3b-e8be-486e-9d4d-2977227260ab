{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-91:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,454,612,686,759,830,911,989,1048,1109,1186,1262,1326,1387,1446,1511,1599,1687,1776,1840,1909,1974,2032,2109,2185,2246,2311,2364,2421,2467,2526,2582,2644,2701,2761,2817,2873,2937,3000,3064,3114,3170,3240,3310", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,73,72,70,80,77,58,60,76,75,63,60,58,64,87,87,88,63,68,64,57,76,75,60,64,52,56,45,58,55,61,56,59,55,55,63,62,63,49,55,69,69,52", "endOffsets": "282,449,607,681,754,825,906,984,1043,1104,1181,1257,1321,1382,1441,1506,1594,1682,1771,1835,1904,1969,2027,2104,2180,2241,2306,2359,2416,2462,2521,2577,2639,2696,2756,2812,2868,2932,2995,3059,3109,3165,3235,3305,3358"}, "to": {"startLines": "2,11,15,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,504,8723,8797,8870,8941,9022,9100,9159,9220,9297,9373,9437,9498,9557,9622,9710,9798,9887,9951,10020,10085,10143,10220,10296,10357,11011,11064,11121,11167,11226,11282,11344,11401,11461,11517,11573,11637,11700,11764,11814,11870,11940,12010", "endLines": "10,14,18,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "endColumns": "17,12,12,73,72,70,80,77,58,60,76,75,63,60,58,64,87,87,88,63,68,64,57,76,75,60,64,52,56,45,58,55,61,56,59,55,55,63,62,63,49,55,69,69,52", "endOffsets": "332,499,657,8792,8865,8936,9017,9095,9154,9215,9292,9368,9432,9493,9552,9617,9705,9793,9882,9946,10015,10080,10138,10215,10291,10352,10417,11059,11116,11162,11221,11277,11339,11396,11456,11512,11568,11632,11695,11759,11809,11865,11935,12005,12058"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,172,232,288,360,419,501,581", "endColumns": "58,57,59,55,71,58,81,79,62", "endOffsets": "109,167,227,283,355,414,496,576,639"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10422,10481,10539,10599,10655,10727,10786,10868,10948", "endColumns": "58,57,59,55,71,58,81,79,62", "endOffsets": "10476,10534,10594,10650,10722,10781,10863,10943,11006"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "811,908,1002,1103,1185,1283,1389,1469,1544,1635,1728,1823,1917,2017,2110,2205,2299,2390,2481,2561,2659,2753,2848,2948,3045,3145,3297,21153", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "903,997,1098,1180,1278,1384,1464,1539,1630,1723,1818,1912,2012,2105,2200,2294,2385,2476,2556,2654,2748,2843,2943,3040,3140,3292,3386,21227"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,155", "endColumns": "99,101", "endOffsets": "150,252"}, "to": {"startLines": "254,255", "startColumns": "4,4", "startOffsets": "20077,20177", "endColumns": "99,101", "endOffsets": "20172,20274"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,326,458,627,709", "endColumns": "65,80,73,131,168,81,75", "endOffsets": "166,247,321,453,622,704,780"}, "to": {"startLines": "69,158,258,263,272,290,291", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5088,12063,20437,20793,21570,23233,23315", "endColumns": "65,80,73,131,168,81,75", "endOffsets": "5149,12139,20506,20920,21734,23310,23386"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,315,407,497,611,713,807,896,1010,1123,1221,1336,1457,1567,1676,1774,1864,1972,2059,2155,2252,2349,2439,2547,2650,2754,2857,2956,3059,3159,3264,3362,3465,3552,3632,3722,3853,3994", "endColumns": "107,101,91,89,113,101,93,88,113,112,97,114,120,109,108,97,89,107,86,95,96,96,89,107,102,103,102,98,102,99,104,97,102,86,79,89,130,140,81", "endOffsets": "208,310,402,492,606,708,802,891,1005,1118,1216,1331,1452,1562,1671,1769,1859,1967,2054,2150,2247,2344,2434,2542,2645,2749,2852,2951,3054,3154,3259,3357,3460,3547,3627,3717,3848,3989,4071"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12568,12676,12778,12870,12960,13074,13176,13270,13359,13473,13586,13684,13799,13920,14030,14139,14237,14327,14435,14522,14618,14715,14812,14902,15010,15113,15217,15320,15419,15522,15622,15727,15825,15928,16015,16095,16185,16316,20639", "endColumns": "107,101,91,89,113,101,93,88,113,112,97,114,120,109,108,97,89,107,86,95,96,96,89,107,102,103,102,98,102,99,104,97,102,86,79,89,130,140,81", "endOffsets": "12671,12773,12865,12955,13069,13171,13265,13354,13468,13581,13679,13794,13915,14025,14134,14232,14322,14430,14517,14613,14710,14807,14897,15005,15108,15212,15315,15414,15517,15617,15722,15820,15923,16010,16090,16180,16311,16452,20716"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,184,260,348,438,518,593,672,751,830,894,958,1031,1107,1175,1249,1313", "endColumns": "78,75,87,89,79,74,78,78,78,63,63,72,75,67,73,63,113", "endOffsets": "179,255,343,433,513,588,667,746,825,889,953,1026,1102,1170,1244,1308,1422"}, "to": {"startLines": "66,67,101,103,105,162,163,256,257,259,260,264,265,268,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4862,4941,8263,8421,8581,12357,12432,20279,20358,20511,20575,20925,20998,21232,21739,21813,21877", "endColumns": "78,75,87,89,79,74,78,78,78,63,63,72,75,67,73,63,113", "endOffsets": "4936,5012,8346,8506,8656,12427,12506,20353,20432,20570,20634,20993,21069,21295,21808,21872,21986"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,113,183,250,314,386,462,540", "endColumns": "57,69,66,63,71,75,77,81", "endOffsets": "108,178,245,309,381,457,535,617"}, "to": {"startLines": "50,102,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3391,8351,17262,17329,17393,17465,17541,17619", "endColumns": "57,69,66,63,71,75,77,81", "endOffsets": "3444,8416,17324,17388,17460,17536,17614,17696"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,204,273,356,462,537,599,680,742,799,886,944,1002,1061,1118,1172,1267,1323,1380,1434,1500,1604,1679,1756,1847,1912,1977,2056,2123,2189,2253,2323,2400,2468,2539,2606,2676,2756,2833,2913,2995,3067,3132,3204,3252,3316,3391,3468,3530,3594,3657,3741,3820,3900,3980", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,68,82,105,74,61,80,61,56,86,57,57,58,56,53,94,55,56,53,65,103,74,76,90,64,64,78,66,65,63,69,76,67,70,66,69,79,76,79,81,71,64,71,47,63,74,76,61,63,62,83,78,79,79,71", "endOffsets": "199,268,351,457,532,594,675,737,794,881,939,997,1056,1113,1167,1262,1318,1375,1429,1495,1599,1674,1751,1842,1907,1972,2051,2118,2184,2248,2318,2395,2463,2534,2601,2671,2751,2828,2908,2990,3062,3127,3199,3247,3311,3386,3463,3525,3589,3652,3736,3815,3895,3975,4047"}, "to": {"startLines": "19,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "662,3852,4598,4681,4787,8661,12214,12295,12511,16457,16544,16602,16660,16719,16776,16830,16925,16981,17038,17092,17158,17701,17776,17853,17944,18009,18074,18153,18220,18286,18350,18420,18497,18565,18636,18703,18773,18853,18930,19010,19092,19164,19229,19301,19349,19413,19488,19565,19627,19691,19754,19838,19917,19997,20721", "endLines": "22,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "endColumns": "12,68,82,105,74,61,80,61,56,86,57,57,58,56,53,94,55,56,53,65,103,74,76,90,64,64,78,66,65,63,69,76,67,70,66,69,79,76,79,81,71,64,71,47,63,74,76,61,63,62,83,78,79,79,71", "endOffsets": "806,3916,4676,4782,4857,8718,12290,12352,12563,16539,16597,16655,16714,16771,16825,16920,16976,17033,17087,17153,17257,17771,17848,17939,18004,18069,18148,18215,18281,18345,18415,18492,18560,18631,18698,18768,18848,18925,19005,19087,19159,19224,19296,19344,19408,19483,19560,19622,19686,19749,19833,19912,19992,20072,20788"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "56,57,58,59,60,61,62,270", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3921,4013,4113,4207,4304,4400,4498,21378", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "4008,4108,4202,4299,4395,4493,4593,21474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,156,256,358,458,529,614,717,832,938,1056,1134,1227,1308,1394,1499,1609,1707,1816,1923,2031,2160,2265,2365,2469,2575,2657,2748,2853,2958,3045,3139,3232,3341,3456,3551,3638,3708,3778,3857,3935,4026,4102,4180,4273,4364,4455,4548,4627,4719,4809,4902,5015,5091,5181", "endColumns": "100,99,101,99,70,84,102,114,105,117,77,92,80,85,104,109,97,108,106,107,128,104,99,103,105,81,90,104,104,86,93,92,108,114,94,86,69,69,78,77,90,75,77,92,90,90,92,78,91,89,92,112,75,89,86", "endOffsets": "151,251,353,453,524,609,712,827,933,1051,1129,1222,1303,1389,1494,1604,1702,1811,1918,2026,2155,2260,2360,2464,2570,2652,2743,2848,2953,3040,3134,3227,3336,3451,3546,3633,3703,3773,3852,3930,4021,4097,4175,4268,4359,4450,4543,4622,4714,4804,4897,5010,5086,5176,5263"}, "to": {"startLines": "51,52,53,54,68,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,104,159,266,269,271,276,277,278,279,280,281,282,283,284,285,286,287,288,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3449,3550,3650,3752,5017,5154,5239,5342,5457,5563,5681,5759,5852,5933,6019,6124,6234,6332,6441,6548,6656,6785,6890,6990,7094,7200,7282,7373,7478,7583,7670,7764,7857,7966,8081,8176,8511,12144,21074,21300,21479,21991,22067,22145,22238,22329,22420,22513,22592,22684,22774,22867,22980,23056,23146", "endColumns": "100,99,101,99,70,84,102,114,105,117,77,92,80,85,104,109,97,108,106,107,128,104,99,103,105,81,90,104,104,86,93,92,108,114,94,86,69,69,78,77,90,75,77,92,90,90,92,78,91,89,92,112,75,89,86", "endOffsets": "3545,3645,3747,3847,5083,5234,5337,5452,5558,5676,5754,5847,5928,6014,6119,6229,6327,6436,6543,6651,6780,6885,6985,7089,7195,7277,7368,7473,7578,7665,7759,7852,7961,8076,8171,8258,8576,12209,21148,21373,21565,22062,22140,22233,22324,22415,22508,22587,22679,22769,22862,22975,23051,23141,23228"}}]}]}