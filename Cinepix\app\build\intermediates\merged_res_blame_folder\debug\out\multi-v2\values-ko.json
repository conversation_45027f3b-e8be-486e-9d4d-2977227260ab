{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-99:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\31a00a017e90aa446708533c4ea3d047\\transformed\\core-1.12.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "56,57,58,59,60,61,62,288", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3921,4013,4113,4207,4304,4400,4498,23325", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "4008,4108,4202,4299,4395,4493,4593,23421"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc96508f556f069c09e91b3a9267645a\\transformed\\play-services-base-18.0.1\\res\\values-ko\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,428,540,638,749,862,965,1056,1197,1296,1428,1542,1656,1771,1826,1880", "endColumns": "99,134,111,97,110,112,102,90,140,98,131,113,113,114,54,53,70", "endOffsets": "292,427,539,637,748,861,964,1055,1196,1295,1427,1541,1655,1770,1825,1879,1950"}, "to": {"startLines": "69,70,71,72,73,74,75,76,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5088,5192,5331,5447,5549,5664,5781,5888,6104,6249,6352,6488,6606,6724,6843,6902,6960", "endColumns": "103,138,115,101,114,116,106,94,144,102,135,117,117,118,58,57,74", "endOffsets": "5187,5326,5442,5544,5659,5776,5883,5978,6244,6347,6483,6601,6719,6838,6897,6955,7030"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cb0d38356f8a450289b6a883ab8edf14\\transformed\\navigation-ui-2.7.6\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,155", "endColumns": "99,101", "endOffsets": "150,252"}, "to": {"startLines": "272,273", "startColumns": "4,4", "startOffsets": "22024,22124", "endColumns": "99,101", "endOffsets": "22119,22221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2907b06edc976dc86930b7aa57b04408\\transformed\\media3-session-1.2.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,113,183,250,314,386,462,540", "endColumns": "57,69,66,63,71,75,77,81", "endOffsets": "108,178,245,309,381,457,535,617"}, "to": {"startLines": "50,120,233,234,235,236,237,238", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3391,10298,19209,19276,19340,19412,19488,19566", "endColumns": "57,69,66,63,71,75,77,81", "endOffsets": "3444,10363,19271,19335,19407,19483,19561,19643"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f70384b9a1e1b9ce2411b7c78f9fa615\\transformed\\preference-1.2.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,326,458,627,709", "endColumns": "65,80,73,131,168,81,75", "endOffsets": "166,247,321,453,622,704,780"}, "to": {"startLines": "87,176,276,281,290,308,309", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7035,14010,22384,22740,23517,25180,25262", "endColumns": "65,80,73,131,168,81,75", "endOffsets": "7096,14086,22453,22867,23681,25257,25333"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e3384bca41f271b3200ce0446ec0f087\\transformed\\media3-exoplayer-1.2.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,172,232,288,360,419,501,581", "endColumns": "58,57,59,55,71,58,81,79,62", "endOffsets": "109,167,227,283,355,414,496,576,639"}, "to": {"startLines": "149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "12369,12428,12486,12546,12602,12674,12733,12815,12895", "endColumns": "58,57,59,55,71,58,81,79,62", "endOffsets": "12423,12481,12541,12597,12669,12728,12810,12890,12953"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\62c17cba0a75b790331013fbfad47644\\transformed\\play-services-basement-18.1.0\\res\\values-ko\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "116", "endOffsets": "311"}, "to": {"startLines": "77", "startColumns": "4", "startOffsets": "5983", "endColumns": "120", "endOffsets": "6099"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b15b518726989b0578dc4af44fa5f9f8\\transformed\\media3-ui-1.2.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,454,612,686,759,830,911,989,1048,1109,1186,1262,1326,1387,1446,1511,1599,1687,1776,1840,1909,1974,2032,2109,2185,2246,2311,2364,2421,2467,2526,2582,2644,2701,2761,2817,2873,2937,3000,3064,3114,3170,3240,3310", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,73,72,70,80,77,58,60,76,75,63,60,58,64,87,87,88,63,68,64,57,76,75,60,64,52,56,45,58,55,61,56,59,55,55,63,62,63,49,55,69,69,52", "endOffsets": "282,449,607,681,754,825,906,984,1043,1104,1181,1257,1321,1382,1441,1506,1594,1682,1771,1835,1904,1969,2027,2104,2180,2241,2306,2359,2416,2462,2521,2577,2639,2696,2756,2812,2868,2932,2995,3059,3109,3165,3235,3305,3358"}, "to": {"startLines": "2,11,15,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,504,10670,10744,10817,10888,10969,11047,11106,11167,11244,11320,11384,11445,11504,11569,11657,11745,11834,11898,11967,12032,12090,12167,12243,12304,12958,13011,13068,13114,13173,13229,13291,13348,13408,13464,13520,13584,13647,13711,13761,13817,13887,13957", "endLines": "10,14,18,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "endColumns": "17,12,12,73,72,70,80,77,58,60,76,75,63,60,58,64,87,87,88,63,68,64,57,76,75,60,64,52,56,45,58,55,61,56,59,55,55,63,62,63,49,55,69,69,52", "endOffsets": "332,499,657,10739,10812,10883,10964,11042,11101,11162,11239,11315,11379,11440,11499,11564,11652,11740,11829,11893,11962,12027,12085,12162,12238,12299,12364,13006,13063,13109,13168,13224,13286,13343,13403,13459,13515,13579,13642,13706,13756,13812,13882,13952,14005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\804c4e5761ff122de1231662730d5f72\\transformed\\ui-release\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,184,260,348,438,518,593,672,751,830,894,958,1031,1107,1175,1249,1313", "endColumns": "78,75,87,89,79,74,78,78,78,63,63,72,75,67,73,63,113", "endOffsets": "179,255,343,433,513,588,667,746,825,889,953,1026,1102,1170,1244,1308,1422"}, "to": {"startLines": "66,67,119,121,123,180,181,274,275,277,278,282,283,286,291,292,293", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4862,4941,10210,10368,10528,14304,14379,22226,22305,22458,22522,22872,22945,23179,23686,23760,23824", "endColumns": "78,75,87,89,79,74,78,78,78,63,63,72,75,67,73,63,113", "endOffsets": "4936,5012,10293,10453,10603,14374,14453,22300,22379,22517,22581,22940,23016,23242,23755,23819,23933"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\de0470acc971f743e832aff97e9527de\\transformed\\appcompat-1.6.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,285", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "811,908,1002,1103,1185,1283,1389,1469,1544,1635,1728,1823,1917,2017,2110,2205,2299,2390,2481,2561,2659,2753,2848,2948,3045,3145,3297,23100", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "903,997,1098,1180,1278,1384,1464,1539,1630,1723,1818,1912,2012,2105,2200,2294,2385,2476,2556,2654,2748,2843,2943,3040,3140,3292,3386,23174"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\677a264eecd9b3484ff2620a344ffab3\\transformed\\material3-1.1.2\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,156,256,358,458,529,614,717,832,938,1056,1134,1227,1308,1394,1499,1609,1707,1816,1923,2031,2160,2265,2365,2469,2575,2657,2748,2853,2958,3045,3139,3232,3341,3456,3551,3638,3708,3778,3857,3935,4026,4102,4180,4273,4364,4455,4548,4627,4719,4809,4902,5015,5091,5181", "endColumns": "100,99,101,99,70,84,102,114,105,117,77,92,80,85,104,109,97,108,106,107,128,104,99,103,105,81,90,104,104,86,93,92,108,114,94,86,69,69,78,77,90,75,77,92,90,90,92,78,91,89,92,112,75,89,86", "endOffsets": "151,251,353,453,524,609,712,827,933,1051,1129,1222,1303,1389,1494,1604,1702,1811,1918,2026,2155,2260,2360,2464,2570,2652,2743,2848,2953,3040,3134,3227,3336,3451,3546,3633,3703,3773,3852,3930,4021,4097,4175,4268,4359,4450,4543,4622,4714,4804,4897,5010,5086,5176,5263"}, "to": {"startLines": "51,52,53,54,68,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,122,177,284,287,289,294,295,296,297,298,299,300,301,302,303,304,305,306,307", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3449,3550,3650,3752,5017,7101,7186,7289,7404,7510,7628,7706,7799,7880,7966,8071,8181,8279,8388,8495,8603,8732,8837,8937,9041,9147,9229,9320,9425,9530,9617,9711,9804,9913,10028,10123,10458,14091,23021,23247,23426,23938,24014,24092,24185,24276,24367,24460,24539,24631,24721,24814,24927,25003,25093", "endColumns": "100,99,101,99,70,84,102,114,105,117,77,92,80,85,104,109,97,108,106,107,128,104,99,103,105,81,90,104,104,86,93,92,108,114,94,86,69,69,78,77,90,75,77,92,90,90,92,78,91,89,92,112,75,89,86", "endOffsets": "3545,3645,3747,3847,5083,7181,7284,7399,7505,7623,7701,7794,7875,7961,8066,8176,8274,8383,8490,8598,8727,8832,8932,9036,9142,9224,9315,9420,9525,9612,9706,9799,9908,10023,10118,10205,10523,14156,23095,23320,23512,24009,24087,24180,24271,24362,24455,24534,24626,24716,24809,24922,24998,25088,25175"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\35536fe73c76a56f94e622d08fe45089\\transformed\\leanback-1.0.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,315,407,497,611,713,807,896,1010,1123,1221,1336,1457,1567,1676,1774,1864,1972,2059,2155,2252,2349,2439,2547,2650,2754,2857,2956,3059,3159,3264,3362,3465,3552,3632,3722,3853,3994", "endColumns": "107,101,91,89,113,101,93,88,113,112,97,114,120,109,108,97,89,107,86,95,96,96,89,107,102,103,102,98,102,99,104,97,102,86,79,89,130,140,81", "endOffsets": "208,310,402,492,606,708,802,891,1005,1118,1216,1331,1452,1562,1671,1769,1859,1967,2054,2150,2247,2344,2434,2542,2645,2749,2852,2951,3054,3154,3259,3357,3460,3547,3627,3717,3848,3989,4071"}, "to": {"startLines": "183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,279", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14515,14623,14725,14817,14907,15021,15123,15217,15306,15420,15533,15631,15746,15867,15977,16086,16184,16274,16382,16469,16565,16662,16759,16849,16957,17060,17164,17267,17366,17469,17569,17674,17772,17875,17962,18042,18132,18263,22586", "endColumns": "107,101,91,89,113,101,93,88,113,112,97,114,120,109,108,97,89,107,86,95,96,96,89,107,102,103,102,98,102,99,104,97,102,86,79,89,130,140,81", "endOffsets": "14618,14720,14812,14902,15016,15118,15212,15301,15415,15528,15626,15741,15862,15972,16081,16179,16269,16377,16464,16560,16657,16754,16844,16952,17055,17159,17262,17361,17464,17564,17669,17767,17870,17957,18037,18127,18258,18399,22663"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\90e8727c8917f86c7d308f0c9abe80a3\\transformed\\material-1.4.0-beta01\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,204,273,356,462,537,599,680,742,799,886,944,1002,1061,1118,1172,1267,1323,1380,1434,1500,1604,1679,1756,1847,1912,1977,2056,2123,2189,2253,2323,2400,2468,2539,2606,2676,2756,2833,2913,2995,3067,3132,3204,3252,3316,3391,3468,3530,3594,3657,3741,3820,3900,3980", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,68,82,105,74,61,80,61,56,86,57,57,58,56,53,94,55,56,53,65,103,74,76,90,64,64,78,66,65,63,69,76,67,70,66,69,79,76,79,81,71,64,71,47,63,74,76,61,63,62,83,78,79,79,71", "endOffsets": "199,268,351,457,532,594,675,737,794,881,939,997,1056,1113,1167,1262,1318,1375,1429,1495,1599,1674,1751,1842,1907,1972,2051,2118,2184,2248,2318,2395,2463,2534,2601,2671,2751,2828,2908,2990,3062,3127,3199,3247,3311,3386,3463,3525,3589,3652,3736,3815,3895,3975,4047"}, "to": {"startLines": "19,55,63,64,65,124,178,179,182,221,222,223,224,225,226,227,228,229,230,231,232,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,280", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "662,3852,4598,4681,4787,10608,14161,14242,14458,18404,18491,18549,18607,18666,18723,18777,18872,18928,18985,19039,19105,19648,19723,19800,19891,19956,20021,20100,20167,20233,20297,20367,20444,20512,20583,20650,20720,20800,20877,20957,21039,21111,21176,21248,21296,21360,21435,21512,21574,21638,21701,21785,21864,21944,22668", "endLines": "22,55,63,64,65,124,178,179,182,221,222,223,224,225,226,227,228,229,230,231,232,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,280", "endColumns": "12,68,82,105,74,61,80,61,56,86,57,57,58,56,53,94,55,56,53,65,103,74,76,90,64,64,78,66,65,63,69,76,67,70,66,69,79,76,79,81,71,64,71,47,63,74,76,61,63,62,83,78,79,79,71", "endOffsets": "806,3916,4676,4782,4857,10665,14237,14299,14510,18486,18544,18602,18661,18718,18772,18867,18923,18980,19034,19100,19204,19718,19795,19886,19951,20016,20095,20162,20228,20292,20362,20439,20507,20578,20645,20715,20795,20872,20952,21034,21106,21171,21243,21291,21355,21430,21507,21569,21633,21696,21780,21859,21939,22019,22735"}}]}]}