{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-99:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\90e8727c8917f86c7d308f0c9abe80a3\\transformed\\material-1.4.0-beta01\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,336,420,517,654,746,812,911,988,1051,1169,1234,1291,1361,1422,1476,1592,1649,1711,1765,1839,1967,2055,2141,2248,2332,2417,2508,2575,2641,2713,2791,2887,2967,3043,3120,3197,3286,3359,3449,3544,3618,3699,3792,3847,3913,3999,4084,4146,4210,4273,4371,4471,4566,4668", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,83,96,136,91,65,98,76,62,117,64,56,69,60,53,115,56,61,53,73,127,87,85,106,83,84,90,66,65,71,77,95,79,75,76,76,88,72,89,94,73,80,92,54,65,85,84,61,63,62,97,99,94,101,79", "endOffsets": "331,415,512,649,741,807,906,983,1046,1164,1229,1286,1356,1417,1471,1587,1644,1706,1760,1834,1962,2050,2136,2243,2327,2412,2503,2570,2636,2708,2786,2882,2962,3038,3115,3192,3281,3354,3444,3539,3613,3694,3787,3842,3908,3994,4079,4141,4205,4268,4366,4466,4561,4663,4743"}, "to": {"startLines": "23,61,69,70,71,130,184,185,188,227,228,229,230,231,232,233,234,235,236,237,238,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,286", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "999,4528,5339,5436,5573,12220,16326,16425,16674,21087,21205,21270,21327,21397,21458,21512,21628,21685,21747,21801,21875,22505,22593,22679,22786,22870,22955,23046,23113,23179,23251,23329,23425,23505,23581,23658,23735,23824,23897,23987,24082,24156,24237,24330,24385,24451,24537,24622,24684,24748,24811,24909,25009,25104,25903", "endLines": "28,61,69,70,71,130,184,185,188,227,228,229,230,231,232,233,234,235,236,237,238,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,286", "endColumns": "12,83,96,136,91,65,98,76,62,117,64,56,69,60,53,115,56,61,53,73,127,87,85,106,83,84,90,66,65,71,77,95,79,75,76,76,88,72,89,94,73,80,92,54,65,85,84,61,63,62,97,99,94,101,79", "endOffsets": "1275,4607,5431,5568,5660,12281,16420,16497,16732,21200,21265,21322,21392,21453,21507,21623,21680,21742,21796,21870,21998,22588,22674,22781,22865,22950,23041,23108,23174,23246,23324,23420,23500,23576,23653,23730,23819,23892,23982,24077,24151,24232,24325,24380,24446,24532,24617,24679,24743,24806,24904,25004,25099,25201,25978"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\35536fe73c76a56f94e622d08fe45089\\transformed\\leanback-1.0.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,502,641,752,849,940,1064,1186,1296,1422,1567,1694,1819,1925,2016,2149,2247,2351,2455,2570,2673,2793,2907,3026,3143,3264,3390,3506,3634,3745,3868,3955,4038,4141,4283,4455", "endColumns": "106,100,94,93,138,110,96,90,123,121,109,125,144,126,124,105,90,132,97,103,103,114,102,119,113,118,116,120,125,115,127,110,122,86,82,102,141,171,81", "endOffsets": "207,308,403,497,636,747,844,935,1059,1181,1291,1417,1562,1689,1814,1920,2011,2144,2242,2346,2450,2565,2668,2788,2902,3021,3138,3259,3385,3501,3629,3740,3863,3950,4033,4136,4278,4450,4532"}, "to": {"startLines": "189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,285", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16737,16844,16945,17040,17134,17273,17384,17481,17572,17696,17818,17928,18054,18199,18326,18451,18557,18648,18781,18879,18983,19087,19202,19305,19425,19539,19658,19775,19896,20022,20138,20266,20377,20500,20587,20670,20773,20915,25821", "endColumns": "106,100,94,93,138,110,96,90,123,121,109,125,144,126,124,105,90,132,97,103,103,114,102,119,113,118,116,120,125,115,127,110,122,86,82,102,141,171,81", "endOffsets": "16839,16940,17035,17129,17268,17379,17476,17567,17691,17813,17923,18049,18194,18321,18446,18552,18643,18776,18874,18978,19082,19197,19300,19420,19534,19653,19770,19891,20017,20133,20261,20372,20495,20582,20665,20768,20910,21082,25898"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e3384bca41f271b3200ce0446ec0f087\\transformed\\media3-exoplayer-1.2.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,188,253,319,397,471,559,645", "endColumns": "71,60,64,65,77,73,87,85,76", "endOffsets": "122,183,248,314,392,466,554,640,717"}, "to": {"startLines": "155,156,157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "14324,14396,14457,14522,14588,14666,14740,14828,14914", "endColumns": "71,60,64,65,77,73,87,85,76", "endOffsets": "14391,14452,14517,14583,14661,14735,14823,14909,14986"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc96508f556f069c09e91b3a9267645a\\transformed\\play-services-base-18.0.1\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,458,580,686,824,949,1060,1160,1337,1440,1599,1721,1884,2038,2103,2159", "endColumns": "102,161,121,105,137,124,110,99,176,102,158,121,162,153,64,55,81", "endOffsets": "295,457,579,685,823,948,1059,1159,1336,1439,1598,1720,1883,2037,2102,2158,2240"}, "to": {"startLines": "75,76,77,78,79,80,81,82,84,85,86,87,88,89,90,91,92", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5918,6025,6191,6317,6427,6569,6698,6813,7074,7255,7362,7525,7651,7818,7976,8045,8105", "endColumns": "106,165,125,109,141,128,114,103,180,106,162,125,166,157,68,59,85", "endOffsets": "6020,6186,6312,6422,6564,6693,6808,6912,7250,7357,7520,7646,7813,7971,8040,8100,8186"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b15b518726989b0578dc4af44fa5f9f8\\transformed\\media3-ui-1.2.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,280,619,949,1032,1115,1198,1288,1388,1459,1532,1631,1732,1805,1877,1942,2020,2132,2243,2360,2437,2532,2604,2677,2765,2853,2922,2987,3040,3102,3150,3211,3278,3346,3412,3494,3552,3609,3675,3740,3806,3858,3919,4004,4089", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,82,82,82,89,99,70,72,98,100,72,71,64,77,111,110,116,76,94,71,72,87,87,68,64,52,61,47,60,66,67,65,81,57,56,65,64,65,51,60,84,84,62", "endOffsets": "275,614,944,1027,1110,1193,1283,1383,1454,1527,1626,1727,1800,1872,1937,2015,2127,2238,2355,2432,2527,2599,2672,2760,2848,2917,2982,3035,3097,3145,3206,3273,3341,3407,3489,3547,3604,3670,3735,3801,3853,3914,3999,4084,4147"}, "to": {"startLines": "2,11,17,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,330,669,12286,12369,12452,12535,12625,12725,12796,12869,12968,13069,13142,13214,13279,13357,13469,13580,13697,13774,13869,13941,14014,14102,14190,14259,14991,15044,15106,15154,15215,15282,15350,15416,15498,15556,15613,15679,15744,15810,15862,15923,16008,16093", "endLines": "10,16,22,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181", "endColumns": "17,12,12,82,82,82,89,99,70,72,98,100,72,71,64,77,111,110,116,76,94,71,72,87,87,68,64,52,61,47,60,66,67,65,81,57,56,65,64,65,51,60,84,84,62", "endOffsets": "325,664,994,12364,12447,12530,12620,12720,12791,12864,12963,13064,13137,13209,13274,13352,13464,13575,13692,13769,13864,13936,14009,14097,14185,14254,14319,15039,15101,15149,15210,15277,15345,15411,15493,15551,15608,15674,15739,15805,15857,15918,16003,16088,16151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2907b06edc976dc86930b7aa57b04408\\transformed\\media3-session-1.2.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,132,208,284,359,442,529,617", "endColumns": "76,75,75,74,82,86,87,92", "endOffsets": "127,203,279,354,437,524,612,705"}, "to": {"startLines": "56,126,239,240,241,242,243,244", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3997,11869,22003,22079,22154,22237,22324,22412", "endColumns": "76,75,75,74,82,86,87,92", "endOffsets": "4069,11940,22074,22149,22232,22319,22407,22500"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f70384b9a1e1b9ce2411b7c78f9fa615\\transformed\\preference-1.2.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,271,348,493,662,744", "endColumns": "73,91,76,144,168,81,77", "endOffsets": "174,266,343,488,657,739,817"}, "to": {"startLines": "93,182,282,287,296,314,315", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8191,16156,25603,25983,26831,28593,28675", "endColumns": "73,91,76,144,168,81,77", "endOffsets": "8260,16243,25675,26123,26995,28670,28748"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\804c4e5761ff122de1231662730d5f72\\transformed\\ui-release\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,379,481,573,655,745,833,915,986,1056,1140,1227,1299,1383,1453", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "193,276,374,476,568,650,740,828,910,981,1051,1135,1222,1294,1378,1448,1571"}, "to": {"startLines": "72,73,125,127,129,186,187,280,281,283,284,288,289,292,297,298,299", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5665,5758,11771,11945,12128,16502,16584,25433,25521,25680,25751,26128,26212,26471,27000,27084,27154", "endColumns": "92,82,97,101,91,81,89,87,81,70,69,83,86,71,83,69,122", "endOffsets": "5753,5836,11864,12042,12215,16579,16669,25516,25598,25746,25816,26207,26294,26538,27079,27149,27272"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cb0d38356f8a450289b6a883ab8edf14\\transformed\\navigation-ui-2.7.6\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,118", "endOffsets": "158,277"}, "to": {"startLines": "278,279", "startColumns": "4,4", "startOffsets": "25206,25314", "endColumns": "107,118", "endOffsets": "25309,25428"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\677a264eecd9b3484ff2620a344ffab3\\transformed\\material3-1.1.2\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,395,509,586,679,790,927,1040,1176,1256,1350,1439,1533,1644,1763,1869,1994,2118,2240,2416,2536,2655,2779,2896,2983,3079,3196,3325,3419,3529,3633,3760,3894,3997,4092,4173,4251,4341,4424,4528,4604,4684,4778,4875,4965,5056,5140,5242,5336,5430,5573,5649,5751", "endColumns": "113,111,113,113,76,92,110,136,112,135,79,93,88,93,110,118,105,124,123,121,175,119,118,123,116,86,95,116,128,93,109,103,126,133,102,94,80,77,89,82,103,75,79,93,96,89,90,83,101,93,93,142,75,101,92", "endOffsets": "164,276,390,504,581,674,785,922,1035,1171,1251,1345,1434,1528,1639,1758,1864,1989,2113,2235,2411,2531,2650,2774,2891,2978,3074,3191,3320,3414,3524,3628,3755,3889,3992,4087,4168,4246,4336,4419,4523,4599,4679,4773,4870,4960,5051,5135,5237,5331,5425,5568,5644,5746,5839"}, "to": {"startLines": "57,58,59,60,74,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,128,183,290,293,295,300,301,302,303,304,305,306,307,308,309,310,311,312,313", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4074,4188,4300,4414,5841,8265,8358,8469,8606,8719,8855,8935,9029,9118,9212,9323,9442,9548,9673,9797,9919,10095,10215,10334,10458,10575,10662,10758,10875,11004,11098,11208,11312,11439,11573,11676,12047,16248,26299,26543,26727,27277,27353,27433,27527,27624,27714,27805,27889,27991,28085,28179,28322,28398,28500", "endColumns": "113,111,113,113,76,92,110,136,112,135,79,93,88,93,110,118,105,124,123,121,175,119,118,123,116,86,95,116,128,93,109,103,126,133,102,94,80,77,89,82,103,75,79,93,96,89,90,83,101,93,93,142,75,101,92", "endOffsets": "4183,4295,4409,4523,5913,8353,8464,8601,8714,8850,8930,9024,9113,9207,9318,9437,9543,9668,9792,9914,10090,10210,10329,10453,10570,10657,10753,10870,10999,11093,11203,11307,11434,11568,11671,11766,12123,16321,26384,26621,26826,27348,27428,27522,27619,27709,27800,27884,27986,28080,28174,28317,28393,28495,28588"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\62c17cba0a75b790331013fbfad47644\\transformed\\play-services-basement-18.1.0\\res\\values-ru\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "152", "endOffsets": "347"}, "to": {"startLines": "83", "startColumns": "4", "startOffsets": "6917", "endColumns": "156", "endOffsets": "7069"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\de0470acc971f743e832aff97e9527de\\transformed\\appcompat-1.6.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,291", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1280,1395,1497,1596,1682,1787,1908,1987,2063,2155,2249,2344,2437,2532,2626,2722,2817,2909,3001,3090,3196,3303,3401,3510,3617,3731,3897,26389", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "1390,1492,1591,1677,1782,1903,1982,2058,2150,2244,2339,2432,2527,2621,2717,2812,2904,2996,3085,3191,3298,3396,3505,3612,3726,3892,3992,26466"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\31a00a017e90aa446708533c4ea3d047\\transformed\\core-1.12.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "62,63,64,65,66,67,68,294", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4612,4710,4812,4913,5014,5119,5222,26626", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "4705,4807,4908,5009,5114,5217,5334,26722"}}]}]}