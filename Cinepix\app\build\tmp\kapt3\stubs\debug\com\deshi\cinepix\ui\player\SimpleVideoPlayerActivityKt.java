package com.deshi.cinepix.ui.player;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000 \n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a:\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00032\u0012\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00010\u00062\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00010\tH\u0007\u00a8\u0006\n"}, d2 = {"SimpleVideoPlayerScreen", "", "videoUrl", "", "title", "onPlayerCreated", "Lkotlin/Function1;", "Landroidx/media3/exoplayer/ExoPlayer;", "onBackPressed", "Lkotlin/Function0;", "app_debug"})
public final class SimpleVideoPlayerActivityKt {
    
    @androidx.compose.runtime.Composable
    public static final void SimpleVideoPlayerScreen(@org.jetbrains.annotations.NotNull
    java.lang.String videoUrl, @org.jetbrains.annotations.NotNull
    java.lang.String title, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super androidx.media3.exoplayer.ExoPlayer, kotlin.Unit> onPlayerCreated, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onBackPressed) {
    }
}