{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-99:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cb0d38356f8a450289b6a883ab8edf14\\transformed\\navigation-ui-2.7.6\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,126", "endOffsets": "158,285"}, "to": {"startLines": "272,273", "startColumns": "4,4", "startOffsets": "25432,25540", "endColumns": "107,126", "endOffsets": "25535,25662"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\31a00a017e90aa446708533c4ea3d047\\transformed\\core-1.12.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "56,57,58,59,60,61,62,288", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4327,4423,4526,4625,4723,4830,4945,26924", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "4418,4521,4620,4718,4825,4940,5068,27020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b15b518726989b0578dc4af44fa5f9f8\\transformed\\media3-ui-1.2.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,505,700,789,878,959,1064,1168,1247,1313,1409,1506,1577,1642,1704,1776,1923,2066,2215,2284,2368,2441,2521,2623,2725,2792,2860,2913,2976,3024,3085,3152,3217,3278,3347,3410,3473,3539,3602,3669,3723,3787,3865,3943", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,88,80,104,103,78,65,95,96,70,64,61,71,146,142,148,68,83,72,79,101,101,66,67,52,62,47,60,66,64,60,68,62,62,65,62,66,53,63,77,77,55", "endOffsets": "280,500,695,784,873,954,1059,1163,1242,1308,1404,1501,1572,1637,1699,1771,1918,2061,2210,2279,2363,2436,2516,2618,2720,2787,2855,2908,2971,3019,3080,3147,3212,3273,3342,3405,3468,3534,3597,3664,3718,3782,3860,3938,3994"}, "to": {"startLines": "2,11,15,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,555,12214,12303,12392,12473,12578,12682,12761,12827,12923,13020,13091,13156,13218,13290,13437,13580,13729,13798,13882,13955,14035,14137,14239,14306,15082,15135,15198,15246,15307,15374,15439,15500,15569,15632,15695,15761,15824,15891,15945,16009,16087,16165", "endLines": "10,14,18,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "endColumns": "17,12,12,88,88,80,104,103,78,65,95,96,70,64,61,71,146,142,148,68,83,72,79,101,101,66,67,52,62,47,60,66,64,60,68,62,62,65,62,66,53,63,77,77,55", "endOffsets": "330,550,745,12298,12387,12468,12573,12677,12756,12822,12918,13015,13086,13151,13213,13285,13432,13575,13724,13793,13877,13950,14030,14132,14234,14301,14369,15130,15193,15241,15302,15369,15434,15495,15564,15627,15690,15756,15819,15886,15940,16004,16082,16160,16216"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2907b06edc976dc86930b7aa57b04408\\transformed\\media3-session-1.2.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,123,203,279,354,433,515,607", "endColumns": "67,79,75,74,78,81,91,96", "endOffsets": "118,198,274,349,428,510,602,699"}, "to": {"startLines": "50,120,233,234,235,236,237,238", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3688,11804,22204,22280,22355,22434,22516,22608", "endColumns": "67,79,75,74,78,81,91,96", "endOffsets": "3751,11879,22275,22350,22429,22511,22603,22700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f70384b9a1e1b9ce2411b7c78f9fa615\\transformed\\preference-1.2.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,264,347,496,665,746", "endColumns": "69,88,82,148,168,80,76", "endOffsets": "170,259,342,491,660,741,818"}, "to": {"startLines": "87,176,276,281,290,308,309", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7885,16221,25853,26269,27134,28955,29036", "endColumns": "69,88,82,148,168,80,76", "endOffsets": "7950,16305,25931,26413,27298,29031,29108"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\62c17cba0a75b790331013fbfad47644\\transformed\\play-services-basement-18.1.0\\res\\values-ta\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "156", "endOffsets": "351"}, "to": {"startLines": "77", "startColumns": "4", "startOffsets": "6633", "endColumns": "160", "endOffsets": "6789"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\90e8727c8917f86c7d308f0c9abe80a3\\transformed\\material-1.4.0-beta01\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,311,420,538,622,686,794,862,923,1031,1117,1175,1259,1326,1380,1503,1565,1628,1682,1770,1898,1984,2066,2168,2248,2329,2418,2485,2551,2636,2724,2816,2885,2962,3042,3110,3209,3292,3384,3478,3552,3638,3732,3782,3848,3933,4020,4083,4148,4211,4319,4422,4520,4625", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,83,108,117,83,63,107,67,60,107,85,57,83,66,53,122,61,62,53,87,127,85,81,101,79,80,88,66,65,84,87,91,68,76,79,67,98,82,91,93,73,85,93,49,65,84,86,62,64,62,107,102,97,104,85", "endOffsets": "222,306,415,533,617,681,789,857,918,1026,1112,1170,1254,1321,1375,1498,1560,1623,1677,1765,1893,1979,2061,2163,2243,2324,2413,2480,2546,2631,2719,2811,2880,2957,3037,3105,3204,3287,3379,3473,3547,3633,3727,3777,3843,3928,4015,4078,4143,4206,4314,4417,4515,4620,4706"}, "to": {"startLines": "19,55,63,64,65,124,178,179,182,221,222,223,224,225,226,227,228,229,230,231,232,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,280", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "750,4243,5073,5182,5300,12150,16393,16501,16761,21229,21337,21423,21481,21565,21632,21686,21809,21871,21934,21988,22076,22705,22791,22873,22975,23055,23136,23225,23292,23358,23443,23531,23623,23692,23769,23849,23917,24016,24099,24191,24285,24359,24445,24539,24589,24655,24740,24827,24890,24955,25018,25126,25229,25327,26183", "endLines": "22,55,63,64,65,124,178,179,182,221,222,223,224,225,226,227,228,229,230,231,232,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,280", "endColumns": "12,83,108,117,83,63,107,67,60,107,85,57,83,66,53,122,61,62,53,87,127,85,81,101,79,80,88,66,65,84,87,91,68,76,79,67,98,82,91,93,73,85,93,49,65,84,86,62,64,62,107,102,97,104,85", "endOffsets": "917,4322,5177,5295,5379,12209,16496,16564,16817,21332,21418,21476,21560,21627,21681,21804,21866,21929,21983,22071,22199,22786,22868,22970,23050,23131,23220,23287,23353,23438,23526,23618,23687,23764,23844,23912,24011,24094,24186,24280,24354,24440,24534,24584,24650,24735,24822,24885,24950,25013,25121,25224,25322,25427,26264"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\de0470acc971f743e832aff97e9527de\\transformed\\appcompat-1.6.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,285", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "922,1035,1137,1252,1341,1452,1573,1652,1728,1826,1926,2021,2115,2222,2322,2424,2518,2616,2714,2795,2903,3006,3105,3221,3324,3429,3586,26670", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "1030,1132,1247,1336,1447,1568,1647,1723,1821,1921,2016,2110,2217,2317,2419,2513,2611,2709,2790,2898,3001,3100,3216,3319,3424,3581,3683,26747"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\804c4e5761ff122de1231662730d5f72\\transformed\\ui-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,380,481,572,655,764,855,950,1030,1109,1191,1277,1367,1447,1516", "endColumns": "96,83,93,100,90,82,108,90,94,79,78,81,85,89,79,68,119", "endOffsets": "197,281,375,476,567,650,759,850,945,1025,1104,1186,1272,1362,1442,1511,1631"}, "to": {"startLines": "66,67,119,121,123,180,181,274,275,277,278,282,283,286,291,292,293", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5384,5481,11710,11884,12059,16569,16652,25667,25758,25936,26016,26418,26500,26752,27303,27383,27452", "endColumns": "96,83,93,100,90,82,108,90,94,79,78,81,85,89,79,68,119", "endOffsets": "5476,5560,11799,11980,12145,16647,16756,25753,25848,26011,26090,26495,26581,26837,27378,27447,27567"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\35536fe73c76a56f94e622d08fe45089\\transformed\\leanback-1.0.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,409,501,637,751,873,966,1096,1224,1332,1459,1628,1747,1864,1972,2069,2211,2302,2420,2537,2657,2752,2874,2998,3111,3220,3326,3437,3563,3695,3823,3957,4044,4127,4224,4361,4512", "endColumns": "106,100,95,91,135,113,121,92,129,127,107,126,168,118,116,107,96,141,90,117,116,119,94,121,123,112,108,105,110,125,131,127,133,86,82,96,136,150,87", "endOffsets": "207,308,404,496,632,746,868,961,1091,1219,1327,1454,1623,1742,1859,1967,2064,2206,2297,2415,2532,2652,2747,2869,2993,3106,3215,3321,3432,3558,3690,3818,3952,4039,4122,4219,4356,4507,4595"}, "to": {"startLines": "183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,279", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16822,16929,17030,17126,17218,17354,17468,17590,17683,17813,17941,18049,18176,18345,18464,18581,18689,18786,18928,19019,19137,19254,19374,19469,19591,19715,19828,19937,20043,20154,20280,20412,20540,20674,20761,20844,20941,21078,26095", "endColumns": "106,100,95,91,135,113,121,92,129,127,107,126,168,118,116,107,96,141,90,117,116,119,94,121,123,112,108,105,110,125,131,127,133,86,82,96,136,150,87", "endOffsets": "16924,17025,17121,17213,17349,17463,17585,17678,17808,17936,18044,18171,18340,18459,18576,18684,18781,18923,19014,19132,19249,19369,19464,19586,19710,19823,19932,20038,20149,20275,20407,20535,20669,20756,20839,20936,21073,21224,26178"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\677a264eecd9b3484ff2620a344ffab3\\transformed\\material3-1.1.2\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,303,420,542,626,718,828,975,1102,1239,1319,1419,1521,1625,1753,1872,1977,2119,2259,2389,2591,2716,2833,2955,3098,3201,3295,3434,3566,3668,3777,3880,4021,4169,4274,4381,4455,4538,4622,4704,4813,4889,4973,5073,5189,5280,5381,5466,5584,5684,5786,5915,5991,6098", "endColumns": "123,123,116,121,83,91,109,146,126,136,79,99,101,103,127,118,104,141,139,129,201,124,116,121,142,102,93,138,131,101,108,102,140,147,104,106,73,82,83,81,108,75,83,99,115,90,100,84,117,99,101,128,75,106,97", "endOffsets": "174,298,415,537,621,713,823,970,1097,1234,1314,1414,1516,1620,1748,1867,1972,2114,2254,2384,2586,2711,2828,2950,3093,3196,3290,3429,3561,3663,3772,3875,4016,4164,4269,4376,4450,4533,4617,4699,4808,4884,4968,5068,5184,5275,5376,5461,5579,5679,5781,5910,5986,6093,6191"}, "to": {"startLines": "51,52,53,54,68,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,122,177,284,287,289,294,295,296,297,298,299,300,301,302,303,304,305,306,307", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3756,3880,4004,4121,5565,7955,8047,8157,8304,8431,8568,8648,8748,8850,8954,9082,9201,9306,9448,9588,9718,9920,10045,10162,10284,10427,10530,10624,10763,10895,10997,11106,11209,11350,11498,11603,11985,16310,26586,26842,27025,27572,27648,27732,27832,27948,28039,28140,28225,28343,28443,28545,28674,28750,28857", "endColumns": "123,123,116,121,83,91,109,146,126,136,79,99,101,103,127,118,104,141,139,129,201,124,116,121,142,102,93,138,131,101,108,102,140,147,104,106,73,82,83,81,108,75,83,99,115,90,100,84,117,99,101,128,75,106,97", "endOffsets": "3875,3999,4116,4238,5644,8042,8152,8299,8426,8563,8643,8743,8845,8949,9077,9196,9301,9443,9583,9713,9915,10040,10157,10279,10422,10525,10619,10758,10890,10992,11101,11204,11345,11493,11598,11705,12054,16388,26665,26919,27129,27643,27727,27827,27943,28034,28135,28220,28338,28438,28540,28669,28745,28852,28950"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e3384bca41f271b3200ce0446ec0f087\\transformed\\media3-exoplayer-1.2.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,197,266,336,418,499,596,681", "endColumns": "68,72,68,69,81,80,96,84,81", "endOffsets": "119,192,261,331,413,494,591,676,758"}, "to": {"startLines": "149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "14374,14443,14516,14585,14655,14737,14818,14915,15000", "endColumns": "68,72,68,69,81,80,96,84,81", "endOffsets": "14438,14511,14580,14650,14732,14813,14910,14995,15077"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc96508f556f069c09e91b3a9267645a\\transformed\\play-services-base-18.0.1\\res\\values-ta\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,442,565,667,815,939,1048,1145,1321,1424,1573,1704,1854,2006,2064,2123", "endColumns": "100,147,122,101,147,123,108,96,175,102,148,130,149,151,57,58,76", "endOffsets": "293,441,564,666,814,938,1047,1144,1320,1423,1572,1703,1853,2005,2063,2122,2199"}, "to": {"startLines": "69,70,71,72,73,74,75,76,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5649,5754,5906,6033,6139,6291,6419,6532,6794,6974,7081,7234,7369,7523,7679,7741,7804", "endColumns": "104,151,126,105,151,127,112,100,179,106,152,134,153,155,61,62,80", "endOffsets": "5749,5901,6028,6134,6286,6414,6527,6628,6969,7076,7229,7364,7518,7674,7736,7799,7880"}}]}]}