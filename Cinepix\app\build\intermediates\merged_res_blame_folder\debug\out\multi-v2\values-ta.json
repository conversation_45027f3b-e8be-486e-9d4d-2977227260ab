{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-91:/values-ta/values-ta.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,311,420,538,622,686,794,862,923,1031,1117,1175,1259,1326,1380,1503,1565,1628,1682,1770,1898,1984,2066,2168,2248,2329,2418,2485,2551,2636,2724,2816,2885,2962,3042,3110,3209,3292,3384,3478,3552,3638,3732,3782,3848,3933,4020,4083,4148,4211,4319,4422,4520,4625", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,83,108,117,83,63,107,67,60,107,85,57,83,66,53,122,61,62,53,87,127,85,81,101,79,80,88,66,65,84,87,91,68,76,79,67,98,82,91,93,73,85,93,49,65,84,86,62,64,62,107,102,97,104,85", "endOffsets": "222,306,415,533,617,681,789,857,918,1026,1112,1170,1254,1321,1375,1498,1560,1623,1677,1765,1893,1979,2061,2163,2243,2324,2413,2480,2546,2631,2719,2811,2880,2957,3037,3105,3204,3287,3379,3473,3547,3633,3727,3777,3843,3928,4015,4078,4143,4206,4314,4417,4515,4620,4706"}, "to": {"startLines": "19,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "750,4243,5073,5182,5300,9914,14157,14265,14525,18993,19101,19187,19245,19329,19396,19450,19573,19635,19698,19752,19840,20469,20555,20637,20739,20819,20900,20989,21056,21122,21207,21295,21387,21456,21533,21613,21681,21780,21863,21955,22049,22123,22209,22303,22353,22419,22504,22591,22654,22719,22782,22890,22993,23091,23947", "endLines": "22,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "endColumns": "12,83,108,117,83,63,107,67,60,107,85,57,83,66,53,122,61,62,53,87,127,85,81,101,79,80,88,66,65,84,87,91,68,76,79,67,98,82,91,93,73,85,93,49,65,84,86,62,64,62,107,102,97,104,85", "endOffsets": "917,4322,5177,5295,5379,9973,14260,14328,14581,19096,19182,19240,19324,19391,19445,19568,19630,19693,19747,19835,19963,20550,20632,20734,20814,20895,20984,21051,21117,21202,21290,21382,21451,21528,21608,21676,21775,21858,21950,22044,22118,22204,22298,22348,22414,22499,22586,22649,22714,22777,22885,22988,23086,23191,24028"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,264,347,496,665,746", "endColumns": "69,88,82,148,168,80,76", "endOffsets": "170,259,342,491,660,741,818"}, "to": {"startLines": "69,158,258,263,272,290,291", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5649,13985,23617,24033,24898,26719,26800", "endColumns": "69,88,82,148,168,80,76", "endOffsets": "5714,14069,23695,24177,25062,26795,26872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,380,481,572,655,764,855,950,1030,1109,1191,1277,1367,1447,1516", "endColumns": "96,83,93,100,90,82,108,90,94,79,78,81,85,89,79,68,119", "endOffsets": "197,281,375,476,567,650,759,850,945,1025,1104,1186,1272,1362,1442,1511,1631"}, "to": {"startLines": "66,67,101,103,105,162,163,256,257,259,260,264,265,268,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5384,5481,9474,9648,9823,14333,14416,23431,23522,23700,23780,24182,24264,24516,25067,25147,25216", "endColumns": "96,83,93,100,90,82,108,90,94,79,78,81,85,89,79,68,119", "endOffsets": "5476,5560,9563,9744,9909,14411,14520,23517,23612,23775,23854,24259,24345,24601,25142,25211,25331"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,409,501,637,751,873,966,1096,1224,1332,1459,1628,1747,1864,1972,2069,2211,2302,2420,2537,2657,2752,2874,2998,3111,3220,3326,3437,3563,3695,3823,3957,4044,4127,4224,4361,4512", "endColumns": "106,100,95,91,135,113,121,92,129,127,107,126,168,118,116,107,96,141,90,117,116,119,94,121,123,112,108,105,110,125,131,127,133,86,82,96,136,150,87", "endOffsets": "207,308,404,496,632,746,868,961,1091,1219,1327,1454,1623,1742,1859,1967,2064,2206,2297,2415,2532,2652,2747,2869,2993,3106,3215,3321,3432,3558,3690,3818,3952,4039,4122,4219,4356,4507,4595"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14586,14693,14794,14890,14982,15118,15232,15354,15447,15577,15705,15813,15940,16109,16228,16345,16453,16550,16692,16783,16901,17018,17138,17233,17355,17479,17592,17701,17807,17918,18044,18176,18304,18438,18525,18608,18705,18842,23859", "endColumns": "106,100,95,91,135,113,121,92,129,127,107,126,168,118,116,107,96,141,90,117,116,119,94,121,123,112,108,105,110,125,131,127,133,86,82,96,136,150,87", "endOffsets": "14688,14789,14885,14977,15113,15227,15349,15442,15572,15700,15808,15935,16104,16223,16340,16448,16545,16687,16778,16896,17013,17133,17228,17350,17474,17587,17696,17802,17913,18039,18171,18299,18433,18520,18603,18700,18837,18988,23942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "922,1035,1137,1252,1341,1452,1573,1652,1728,1826,1926,2021,2115,2222,2322,2424,2518,2616,2714,2795,2903,3006,3105,3221,3324,3429,3586,24434", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "1030,1132,1247,1336,1447,1568,1647,1723,1821,1921,2016,2110,2217,2317,2419,2513,2611,2709,2790,2898,3001,3100,3216,3319,3424,3581,3683,24511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,123,203,279,354,433,515,607", "endColumns": "67,79,75,74,78,81,91,96", "endOffsets": "118,198,274,349,428,510,602,699"}, "to": {"startLines": "50,102,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3688,9568,19968,20044,20119,20198,20280,20372", "endColumns": "67,79,75,74,78,81,91,96", "endOffsets": "3751,9643,20039,20114,20193,20275,20367,20464"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,303,420,542,626,718,828,975,1102,1239,1319,1419,1521,1625,1753,1872,1977,2119,2259,2389,2591,2716,2833,2955,3098,3201,3295,3434,3566,3668,3777,3880,4021,4169,4274,4381,4455,4538,4622,4704,4813,4889,4973,5073,5189,5280,5381,5466,5584,5684,5786,5915,5991,6098", "endColumns": "123,123,116,121,83,91,109,146,126,136,79,99,101,103,127,118,104,141,139,129,201,124,116,121,142,102,93,138,131,101,108,102,140,147,104,106,73,82,83,81,108,75,83,99,115,90,100,84,117,99,101,128,75,106,97", "endOffsets": "174,298,415,537,621,713,823,970,1097,1234,1314,1414,1516,1620,1748,1867,1972,2114,2254,2384,2586,2711,2828,2950,3093,3196,3290,3429,3561,3663,3772,3875,4016,4164,4269,4376,4450,4533,4617,4699,4808,4884,4968,5068,5184,5275,5376,5461,5579,5679,5781,5910,5986,6093,6191"}, "to": {"startLines": "51,52,53,54,68,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,104,159,266,269,271,276,277,278,279,280,281,282,283,284,285,286,287,288,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3756,3880,4004,4121,5565,5719,5811,5921,6068,6195,6332,6412,6512,6614,6718,6846,6965,7070,7212,7352,7482,7684,7809,7926,8048,8191,8294,8388,8527,8659,8761,8870,8973,9114,9262,9367,9749,14074,24350,24606,24789,25336,25412,25496,25596,25712,25803,25904,25989,26107,26207,26309,26438,26514,26621", "endColumns": "123,123,116,121,83,91,109,146,126,136,79,99,101,103,127,118,104,141,139,129,201,124,116,121,142,102,93,138,131,101,108,102,140,147,104,106,73,82,83,81,108,75,83,99,115,90,100,84,117,99,101,128,75,106,97", "endOffsets": "3875,3999,4116,4238,5644,5806,5916,6063,6190,6327,6407,6507,6609,6713,6841,6960,7065,7207,7347,7477,7679,7804,7921,8043,8186,8289,8383,8522,8654,8756,8865,8968,9109,9257,9362,9469,9818,14152,24429,24683,24893,25407,25491,25591,25707,25798,25899,25984,26102,26202,26304,26433,26509,26616,26714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,197,266,336,418,499,596,681", "endColumns": "68,72,68,69,81,80,96,84,81", "endOffsets": "119,192,261,331,413,494,591,676,758"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "12138,12207,12280,12349,12419,12501,12582,12679,12764", "endColumns": "68,72,68,69,81,80,96,84,81", "endOffsets": "12202,12275,12344,12414,12496,12577,12674,12759,12841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "56,57,58,59,60,61,62,270", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4327,4423,4526,4625,4723,4830,4945,24688", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "4418,4521,4620,4718,4825,4940,5068,24784"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,126", "endOffsets": "158,285"}, "to": {"startLines": "254,255", "startColumns": "4,4", "startOffsets": "23196,23304", "endColumns": "107,126", "endOffsets": "23299,23426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,505,700,789,878,959,1064,1168,1247,1313,1409,1506,1577,1642,1704,1776,1923,2066,2215,2284,2368,2441,2521,2623,2725,2792,2860,2913,2976,3024,3085,3152,3217,3278,3347,3410,3473,3539,3602,3669,3723,3787,3865,3943", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,88,80,104,103,78,65,95,96,70,64,61,71,146,142,148,68,83,72,79,101,101,66,67,52,62,47,60,66,64,60,68,62,62,65,62,66,53,63,77,77,55", "endOffsets": "280,500,695,784,873,954,1059,1163,1242,1308,1404,1501,1572,1637,1699,1771,1918,2061,2210,2279,2363,2436,2516,2618,2720,2787,2855,2908,2971,3019,3080,3147,3212,3273,3342,3405,3468,3534,3597,3664,3718,3782,3860,3938,3994"}, "to": {"startLines": "2,11,15,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,555,9978,10067,10156,10237,10342,10446,10525,10591,10687,10784,10855,10920,10982,11054,11201,11344,11493,11562,11646,11719,11799,11901,12003,12070,12846,12899,12962,13010,13071,13138,13203,13264,13333,13396,13459,13525,13588,13655,13709,13773,13851,13929", "endLines": "10,14,18,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "endColumns": "17,12,12,88,88,80,104,103,78,65,95,96,70,64,61,71,146,142,148,68,83,72,79,101,101,66,67,52,62,47,60,66,64,60,68,62,62,65,62,66,53,63,77,77,55", "endOffsets": "330,550,745,10062,10151,10232,10337,10441,10520,10586,10682,10779,10850,10915,10977,11049,11196,11339,11488,11557,11641,11714,11794,11896,11998,12065,12133,12894,12957,13005,13066,13133,13198,13259,13328,13391,13454,13520,13583,13650,13704,13768,13846,13924,13980"}}]}]}