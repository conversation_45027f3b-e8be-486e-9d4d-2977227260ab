{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeReleaseResources-87:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,263,342,490,659,739", "endColumns": "69,87,78,147,168,79,76", "endOffsets": "170,258,337,485,654,734,811"}, "to": {"startLines": "69,158,258,263,272,290,291", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5427,13216,22367,22745,23567,25299,25379", "endColumns": "69,87,78,147,168,79,76", "endOffsets": "5492,13299,22441,22888,23731,25374,25451"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-da\\values-da.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,305,405,554,632,696,782,855,915,1002,1064,1126,1194,1259,1315,1433,1491,1552,1608,1683,1809,1895,1975,2086,2164,2244,2330,2397,2463,2531,2605,2694,2766,2844,2914,2987,3071,3148,3236,3325,3399,3472,3557,3606,3672,3752,3835,3897,3961,4024,4132,4227,4328,4423", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,77,99,148,77,63,85,72,59,86,61,61,67,64,55,117,57,60,55,74,125,85,79,110,77,79,85,66,65,67,73,88,71,77,69,72,83,76,87,88,73,72,84,48,65,79,82,61,63,62,107,94,100,94,79", "endOffsets": "222,300,400,549,627,691,777,850,910,997,1059,1121,1189,1254,1310,1428,1486,1547,1603,1678,1804,1890,1970,2081,2159,2239,2325,2392,2458,2526,2600,2689,2761,2839,2909,2982,3066,3143,3231,3320,3394,3467,3552,3601,3667,3747,3830,3892,3956,4019,4127,4222,4323,4418,4498"}, "to": {"startLines": "19,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "714,4048,4853,4953,5102,9402,13379,13465,13704,17987,18074,18136,18198,18266,18331,18387,18505,18563,18624,18680,18755,19368,19454,19534,19645,19723,19803,19889,19956,20022,20090,20164,20253,20325,20403,20473,20546,20630,20707,20795,20884,20958,21031,21116,21165,21231,21311,21394,21456,21520,21583,21691,21786,21887,22665", "endLines": "22,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "endColumns": "12,77,99,148,77,63,85,72,59,86,61,61,67,64,55,117,57,60,55,74,125,85,79,110,77,79,85,66,65,67,73,88,71,77,69,72,83,76,87,88,73,72,84,48,65,79,82,61,63,62,107,94,100,94,79", "endOffsets": "881,4121,4948,5097,5175,9461,13460,13533,13759,18069,18131,18193,18261,18326,18382,18500,18558,18619,18675,18750,18876,19449,19529,19640,19718,19798,19884,19951,20017,20085,20159,20248,20320,20398,20468,20541,20625,20702,20790,20879,20953,21026,21111,21160,21226,21306,21389,21451,21515,21578,21686,21781,21882,21977,22740"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "56,57,58,59,60,61,62,270", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4126,4222,4324,4421,4519,4626,4735,23369", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "4217,4319,4416,4514,4621,4730,4848,23465"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "886,986,1080,1196,1281,1381,1494,1572,1648,1739,1832,1925,2019,2113,2206,2301,2399,2490,2581,2660,2768,2875,2971,3084,3187,3288,3441,23138", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "981,1075,1191,1276,1376,1489,1567,1643,1734,1827,1920,2014,2108,2201,2296,2394,2485,2576,2655,2763,2870,2966,3079,3182,3283,3436,3533,23213"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,277,372,471,553,630,719,808,890,955,1020,1101,1185,1255,1333,1400", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "192,272,367,466,548,625,714,803,885,950,1015,1096,1180,1250,1328,1395,1515"}, "to": {"startLines": "66,67,101,103,105,162,163,256,257,259,260,264,265,268,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5180,5272,8975,9145,9320,13538,13615,22196,22285,22446,22511,22893,22974,23218,23736,23814,23881", "endColumns": "91,79,94,98,81,76,88,88,81,64,64,80,83,69,77,66,119", "endOffsets": "5267,5347,9065,9239,9397,13610,13699,22280,22362,22506,22571,22969,23053,23283,23809,23876,23996"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,504,622,734,831,931,1057,1182,1284,1402,1555,1676,1796,1905,2003,2133,2224,2326,2430,2530,2629,2745,2866,2975,3083,3189,3301,3416,3536,3648,3765,3852,3933,4033,4171,4328", "endColumns": "106,100,96,93,117,111,96,99,125,124,101,117,152,120,119,108,97,129,90,101,103,99,98,115,120,108,107,105,111,114,119,111,116,86,80,99,137,156,88", "endOffsets": "207,308,405,499,617,729,826,926,1052,1177,1279,1397,1550,1671,1791,1900,1998,2128,2219,2321,2425,2525,2624,2740,2861,2970,3078,3184,3296,3411,3531,3643,3760,3847,3928,4028,4166,4323,4412"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13764,13871,13972,14069,14163,14281,14393,14490,14590,14716,14841,14943,15061,15214,15335,15455,15564,15662,15792,15883,15985,16089,16189,16288,16404,16525,16634,16742,16848,16960,17075,17195,17307,17424,17511,17592,17692,17830,22576", "endColumns": "106,100,96,93,117,111,96,99,125,124,101,117,152,120,119,108,97,129,90,101,103,99,98,115,120,108,107,105,111,114,119,111,116,86,80,99,137,156,88", "endOffsets": "13866,13967,14064,14158,14276,14388,14485,14585,14711,14836,14938,15056,15209,15330,15450,15559,15657,15787,15878,15980,16084,16184,16283,16399,16520,16629,16737,16843,16955,17070,17190,17302,17419,17506,17587,17687,17825,17982,22660"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,131,206,281,349,427,505,596", "endColumns": "75,74,74,67,77,77,90,96", "endOffsets": "126,201,276,344,422,500,591,688"}, "to": {"startLines": "50,102,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3538,9070,18881,18956,19024,19102,19180,19271", "endColumns": "75,74,74,67,77,77,90,96", "endOffsets": "3609,9140,18951,19019,19097,19175,19266,19363"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,189,253,322,399,473,573,664", "endColumns": "71,61,63,68,76,73,99,90,67", "endOffsets": "122,184,248,317,394,468,568,659,727"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11395,11467,11529,11593,11662,11739,11813,11913,12004", "endColumns": "71,61,63,68,76,73,99,90,67", "endOffsets": "11462,11524,11588,11657,11734,11808,11908,11999,12067"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,108", "endOffsets": "155,264"}, "to": {"startLines": "254,255", "startColumns": "4,4", "startOffsets": "21982,22087", "endColumns": "104,108", "endOffsets": "22082,22191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,474,664,750,837,912,997,1084,1155,1219,1317,1413,1485,1550,1616,1686,1793,1900,2005,2078,2158,2234,2303,2382,2462,2525,2593,2646,2704,2752,2813,2883,2955,3023,3097,3161,3220,3284,3354,3420,3472,3533,3609,3684", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,74,84,86,70,63,97,95,71,64,65,69,106,106,104,72,79,75,68,78,79,62,67,52,57,47,60,69,71,67,73,63,58,63,69,65,51,60,75,74,52", "endOffsets": "280,469,659,745,832,907,992,1079,1150,1214,1312,1408,1480,1545,1611,1681,1788,1895,2000,2073,2153,2229,2298,2377,2457,2520,2588,2641,2699,2747,2808,2878,2950,3018,3092,3156,3215,3279,3349,3415,3467,3528,3604,3679,3732"}, "to": {"startLines": "2,11,15,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,524,9466,9552,9639,9714,9799,9886,9957,10021,10119,10215,10287,10352,10418,10488,10595,10702,10807,10880,10960,11036,11105,11184,11264,11327,12072,12125,12183,12231,12292,12362,12434,12502,12576,12640,12699,12763,12833,12899,12951,13012,13088,13163", "endLines": "10,14,18,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "endColumns": "17,12,12,85,86,74,84,86,70,63,97,95,71,64,65,69,106,106,104,72,79,75,68,78,79,62,67,52,57,47,60,69,71,67,73,63,58,63,69,65,51,60,75,74,52", "endOffsets": "330,519,709,9547,9634,9709,9794,9881,9952,10016,10114,10210,10282,10347,10413,10483,10590,10697,10802,10875,10955,11031,11100,11179,11259,11322,11390,12120,12178,12226,12287,12357,12429,12497,12571,12635,12694,12758,12828,12894,12946,13007,13083,13158,13211"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,276,379,489,564,655,764,904,1022,1167,1247,1343,1428,1518,1628,1747,1848,1971,2089,2221,2390,2516,2629,2748,2864,2950,3044,3159,3288,3382,3496,3594,3717,3848,3949,4042,4118,4193,4273,4354,4451,4527,4607,4704,4799,4890,4985,5068,5169,5267,5367,5480,5556,5654", "endColumns": "111,108,102,109,74,90,108,139,117,144,79,95,84,89,109,118,100,122,117,131,168,125,112,118,115,85,93,114,128,93,113,97,122,130,100,92,75,74,79,80,96,75,79,96,94,90,94,82,100,97,99,112,75,97,94", "endOffsets": "162,271,374,484,559,650,759,899,1017,1162,1242,1338,1423,1513,1623,1742,1843,1966,2084,2216,2385,2511,2624,2743,2859,2945,3039,3154,3283,3377,3491,3589,3712,3843,3944,4037,4113,4188,4268,4349,4446,4522,4602,4699,4794,4885,4980,5063,5164,5262,5362,5475,5551,5649,5744"}, "to": {"startLines": "51,52,53,54,68,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,104,159,266,269,271,276,277,278,279,280,281,282,283,284,285,286,287,288,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3614,3726,3835,3938,5352,5497,5588,5697,5837,5955,6100,6180,6276,6361,6451,6561,6680,6781,6904,7022,7154,7323,7449,7562,7681,7797,7883,7977,8092,8221,8315,8429,8527,8650,8781,8882,9244,13304,23058,23288,23470,24001,24077,24157,24254,24349,24440,24535,24618,24719,24817,24917,25030,25106,25204", "endColumns": "111,108,102,109,74,90,108,139,117,144,79,95,84,89,109,118,100,122,117,131,168,125,112,118,115,85,93,114,128,93,113,97,122,130,100,92,75,74,79,80,96,75,79,96,94,90,94,82,100,97,99,112,75,97,94", "endOffsets": "3721,3830,3933,4043,5422,5583,5692,5832,5950,6095,6175,6271,6356,6446,6556,6675,6776,6899,7017,7149,7318,7444,7557,7676,7792,7878,7972,8087,8216,8310,8424,8522,8645,8776,8877,8970,9315,13374,23133,23364,23562,24072,24152,24249,24344,24435,24530,24613,24714,24812,24912,25025,25101,25199,25294"}}]}]}