{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeReleaseResources-87:/values-uk/values-uk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,131,213,287,359,442,529,628", "endColumns": "75,81,73,71,82,86,98,104", "endOffsets": "126,208,282,354,437,524,623,728"}, "to": {"startLines": "56,108,221,222,223,224,225,226", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4004,9646,19822,19896,19968,20051,20138,20237", "endColumns": "75,81,73,71,82,86,98,104", "endOffsets": "4075,9723,19891,19963,20046,20133,20232,20337"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,395,509,586,677,786,920,1032,1175,1255,1351,1440,1532,1644,1764,1865,2002,2135,2266,2451,2574,2694,2820,2938,3027,3124,3246,3372,3466,3567,3672,3812,3959,4063,4158,4229,4307,4389,4472,4567,4643,4725,4820,4920,5011,5107,5192,5295,5387,5485,5599,5675,5776", "endColumns": "113,111,113,113,76,90,108,133,111,142,79,95,88,91,111,119,100,136,132,130,184,122,119,125,117,88,96,121,125,93,100,104,139,146,103,94,70,77,81,82,94,75,81,94,99,90,95,84,102,91,97,113,75,100,101", "endOffsets": "164,276,390,504,581,672,781,915,1027,1170,1250,1346,1435,1527,1639,1759,1860,1997,2130,2261,2446,2569,2689,2815,2933,3022,3119,3241,3367,3461,3562,3667,3807,3954,4058,4153,4224,4302,4384,4467,4562,4638,4720,4815,4915,5006,5102,5187,5290,5382,5480,5594,5670,5771,5873"}, "to": {"startLines": "57,58,59,60,74,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,110,165,272,275,277,282,283,284,285,286,287,288,289,290,291,292,293,294,295", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4080,4194,4306,4420,5821,5972,6063,6172,6306,6418,6561,6641,6737,6826,6918,7030,7150,7251,7388,7521,7652,7837,7960,8080,8206,8324,8413,8510,8632,8758,8852,8953,9058,9198,9345,9449,9829,14093,24141,24377,24561,25098,25174,25256,25351,25451,25542,25638,25723,25826,25918,26016,26130,26206,26307", "endColumns": "113,111,113,113,76,90,108,133,111,142,79,95,88,91,111,119,100,136,132,130,184,122,119,125,117,88,96,121,125,93,100,104,139,146,103,94,70,77,81,82,94,75,81,94,99,90,95,84,102,91,97,113,75,100,101", "endOffsets": "4189,4301,4415,4529,5893,6058,6167,6301,6413,6556,6636,6732,6821,6913,7025,7145,7246,7383,7516,7647,7832,7955,8075,8201,8319,8408,8505,8627,8753,8847,8948,9053,9193,9340,9444,9539,9895,14166,24218,24455,24651,25169,25251,25346,25446,25537,25633,25718,25821,25913,26011,26125,26201,26302,26404"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,384,485,569,651,740,828,910,980,1051,1136,1224,1296,1376,1446", "endColumns": "92,83,101,100,83,81,88,87,81,69,70,84,87,71,79,69,122", "endOffsets": "193,277,379,480,564,646,735,823,905,975,1046,1131,1219,1291,1371,1441,1564"}, "to": {"startLines": "72,73,107,109,111,168,169,262,263,265,266,270,271,274,279,280,281", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5644,5737,9544,9728,9900,14328,14410,23261,23349,23512,23582,23968,24053,24305,24825,24905,24975", "endColumns": "92,83,101,100,83,81,88,87,81,69,70,84,87,71,79,69,122", "endOffsets": "5732,5816,9641,9824,9979,14405,14494,23344,23426,23577,23648,24048,24136,24372,24900,24970,25093"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,507,628,739,835,926,1049,1172,1282,1410,1571,1694,1817,1923,2020,2158,2253,2357,2463,2576,2679,2801,2920,3038,3156,3274,3397,3525,3657,3780,3907,3994,4077,4189,4325,4485", "endColumns": "106,100,99,93,120,110,95,90,122,122,109,127,160,122,122,105,96,137,94,103,105,112,102,121,118,117,117,117,122,127,131,122,126,86,82,111,135,159,90", "endOffsets": "207,308,408,502,623,734,830,921,1044,1167,1277,1405,1566,1689,1812,1918,2015,2153,2248,2352,2458,2571,2674,2796,2915,3033,3151,3269,3392,3520,3652,3775,3902,3989,4072,4184,4320,4480,4571"}, "to": {"startLines": "171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14562,14669,14770,14870,14964,15085,15196,15292,15383,15506,15629,15739,15867,16028,16151,16274,16380,16477,16615,16710,16814,16920,17033,17136,17258,17377,17495,17613,17731,17854,17982,18114,18237,18364,18451,18534,18646,18782,23653", "endColumns": "106,100,99,93,120,110,95,90,122,122,109,127,160,122,122,105,96,137,94,103,105,112,102,121,118,117,117,117,122,127,131,122,126,86,82,111,135,159,90", "endOffsets": "14664,14765,14865,14959,15080,15191,15287,15378,15501,15624,15734,15862,16023,16146,16269,16375,16472,16610,16705,16809,16915,17028,17131,17253,17372,17490,17608,17726,17849,17977,18109,18232,18359,18446,18529,18641,18777,18937,23739"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,267,348,490,659,744", "endColumns": "73,87,80,141,168,84,82", "endOffsets": "174,262,343,485,654,739,822"}, "to": {"startLines": "75,164,264,269,278,296,297", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5898,14005,23431,23826,24656,26409,26494", "endColumns": "73,87,80,141,168,84,82", "endOffsets": "5967,14088,23507,23963,24820,26489,26572"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,329,417,513,629,712,779,870,936,999,1087,1154,1212,1283,1342,1396,1510,1570,1633,1687,1760,1879,1965,2048,2157,2242,2329,2417,2484,2550,2622,2698,2788,2861,2938,3019,3093,3183,3262,3353,3449,3523,3604,3699,3753,3819,3906,3992,4054,4118,4181,4288,4380,4478,4570", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,87,95,115,82,66,90,65,62,87,66,57,70,58,53,113,59,62,53,72,118,85,82,108,84,86,87,66,65,71,75,89,72,76,80,73,89,78,90,95,73,80,94,53,65,86,85,61,63,62,106,91,97,91,81", "endOffsets": "324,412,508,624,707,774,865,931,994,1082,1149,1207,1278,1337,1391,1505,1565,1628,1682,1755,1874,1960,2043,2152,2237,2324,2412,2479,2545,2617,2693,2783,2856,2933,3014,3088,3178,3257,3348,3444,3518,3599,3694,3748,3814,3901,3987,4049,4113,4176,4283,4375,4473,4565,4647"}, "to": {"startLines": "23,61,69,70,71,112,166,167,170,209,210,211,212,213,214,215,216,217,218,219,220,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,268", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1006,4534,5349,5445,5561,9984,14171,14262,14499,18942,19030,19097,19155,19226,19285,19339,19453,19513,19576,19630,19703,20342,20428,20511,20620,20705,20792,20880,20947,21013,21085,21161,21251,21324,21401,21482,21556,21646,21725,21816,21912,21986,22067,22162,22216,22282,22369,22455,22517,22581,22644,22751,22843,22941,23744", "endLines": "28,61,69,70,71,112,166,167,170,209,210,211,212,213,214,215,216,217,218,219,220,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,268", "endColumns": "12,87,95,115,82,66,90,65,62,87,66,57,70,58,53,113,59,62,53,72,118,85,82,108,84,86,87,66,65,71,75,89,72,76,80,73,89,78,90,95,73,80,94,53,65,86,85,61,63,62,106,91,97,91,81", "endOffsets": "1275,4617,5440,5556,5639,10046,14257,14323,14557,19025,19092,19150,19221,19280,19334,19448,19508,19571,19625,19698,19817,20423,20506,20615,20700,20787,20875,20942,21008,21080,21156,21246,21319,21396,21477,21551,21641,21720,21811,21907,21981,22062,22157,22211,22277,22364,22450,22512,22576,22639,22746,22838,22936,23028,23821"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,316,424,510,615,733,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1930,2036,2142,2240,2347,2454,2559,2729,2829", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "209,311,419,505,610,728,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1925,2031,2137,2235,2342,2449,2554,2724,2824,2906"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,273", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1280,1389,1491,1599,1685,1790,1908,1989,2068,2159,2252,2347,2441,2541,2634,2729,2824,2915,3006,3105,3211,3317,3415,3522,3629,3734,3904,24223", "endColumns": "108,101,107,85,104,117,80,78,90,92,94,93,99,92,94,94,90,90,98,105,105,97,106,106,104,169,99,81", "endOffsets": "1384,1486,1594,1680,1785,1903,1984,2063,2154,2247,2342,2436,2536,2629,2724,2819,2910,3001,3100,3206,3312,3410,3517,3624,3729,3899,3999,24300"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,626,956,1040,1122,1205,1305,1404,1489,1552,1650,1749,1820,1889,1955,2023,2149,2274,2411,2488,2570,2645,2733,2828,2921,2989,3074,3127,3187,3235,3296,3363,3431,3495,3562,3627,3687,3753,3818,3884,3936,3997,4082,4167", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,83,81,82,99,98,84,62,97,98,70,68,65,67,125,124,136,76,81,74,87,94,92,67,84,52,59,47,60,66,67,63,66,64,59,65,64,65,51,60,84,84,54", "endOffsets": "282,621,951,1035,1117,1200,1300,1399,1484,1547,1645,1744,1815,1884,1950,2018,2144,2269,2406,2483,2565,2640,2728,2823,2916,2984,3069,3122,3182,3230,3291,3358,3426,3490,3557,3622,3682,3748,3813,3879,3931,3992,4077,4162,4217"}, "to": {"startLines": "2,11,17,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,676,10051,10135,10217,10300,10400,10499,10584,10647,10745,10844,10915,10984,11050,11118,11244,11369,11506,11583,11665,11740,11828,11923,12016,12084,12857,12910,12970,13018,13079,13146,13214,13278,13345,13410,13470,13536,13601,13667,13719,13780,13865,13950", "endLines": "10,16,22,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163", "endColumns": "17,12,12,83,81,82,99,98,84,62,97,98,70,68,65,67,125,124,136,76,81,74,87,94,92,67,84,52,59,47,60,66,67,63,66,64,59,65,64,65,51,60,84,84,54", "endOffsets": "332,671,1001,10130,10212,10295,10395,10494,10579,10642,10740,10839,10910,10979,11045,11113,11239,11364,11501,11578,11660,11735,11823,11918,12011,12079,12164,12905,12965,13013,13074,13141,13209,13273,13340,13405,13465,13531,13596,13662,13714,13775,13860,13945,14000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,262,333,413,486,579,668", "endColumns": "73,64,67,70,79,72,92,88,74", "endOffsets": "124,189,257,328,408,481,574,663,738"}, "to": {"startLines": "137,138,139,140,141,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "12169,12243,12308,12376,12447,12527,12600,12693,12782", "endColumns": "73,64,67,70,79,72,92,88,74", "endOffsets": "12238,12303,12371,12442,12522,12595,12688,12777,12852"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "62,63,64,65,66,67,68,276", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4622,4722,4824,4925,5026,5131,5236,24460", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "4717,4819,4920,5021,5126,5231,5344,24556"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,119", "endOffsets": "158,278"}, "to": {"startLines": "260,261", "startColumns": "4,4", "startOffsets": "23033,23141", "endColumns": "107,119", "endOffsets": "23136,23256"}}]}]}