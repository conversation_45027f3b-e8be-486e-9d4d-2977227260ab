{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeReleaseResources-87:/values-zu/values-zu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,284,395,508,585,679,791,924,1037,1188,1269,1365,1453,1545,1661,1778,1879,2019,2150,2283,2451,2579,2701,2823,2948,3037,3133,3257,3395,3490,3587,3692,3827,3964,4070,4167,4241,4320,4402,4484,4586,4662,4743,4847,4945,5041,5135,5219,5321,5423,5520,5638,5714,5817", "endColumns": "113,114,110,112,76,93,111,132,112,150,80,95,87,91,115,116,100,139,130,132,167,127,121,121,124,88,95,123,137,94,96,104,134,136,105,96,73,78,81,81,101,75,80,103,97,95,93,83,101,101,96,117,75,102,95", "endOffsets": "164,279,390,503,580,674,786,919,1032,1183,1264,1360,1448,1540,1656,1773,1874,2014,2145,2278,2446,2574,2696,2818,2943,3032,3128,3252,3390,3485,3582,3687,3822,3959,4065,4162,4236,4315,4397,4479,4581,4657,4738,4842,4940,5036,5130,5214,5316,5418,5515,5633,5709,5812,5908"}, "to": {"startLines": "51,52,53,54,68,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,104,159,266,269,271,276,277,278,279,280,281,282,283,284,285,286,287,288,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3719,3833,3948,4059,5475,5623,5717,5829,5962,6075,6226,6307,6403,6491,6583,6699,6816,6917,7057,7188,7321,7489,7617,7739,7861,7986,8075,8171,8295,8433,8528,8625,8730,8865,9002,9108,9492,13702,23873,24113,24296,24840,24916,24997,25101,25199,25295,25389,25473,25575,25677,25774,25892,25968,26071", "endColumns": "113,114,110,112,76,93,111,132,112,150,80,95,87,91,115,116,100,139,130,132,167,127,121,121,124,88,95,123,137,94,96,104,134,136,105,96,73,78,81,81,101,75,80,103,97,95,93,83,101,101,96,117,75,102,95", "endOffsets": "3828,3943,4054,4167,5547,5712,5824,5957,6070,6221,6302,6398,6486,6578,6694,6811,6912,7052,7183,7316,7484,7612,7734,7856,7981,8070,8166,8290,8428,8523,8620,8725,8860,8997,9103,9200,9561,13776,23950,24190,24393,24911,24992,25096,25194,25290,25384,25468,25570,25672,25769,25887,25963,26066,26162"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,282,387,492,582,664,753,846,929,997,1065,1153,1241,1317,1396,1466", "endColumns": "94,81,104,104,89,81,88,92,82,67,67,87,87,75,78,69,123", "endOffsets": "195,277,382,487,577,659,748,841,924,992,1060,1148,1236,1312,1391,1461,1585"}, "to": {"startLines": "66,67,101,103,105,162,163,256,257,259,260,264,265,268,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5298,5393,9205,9387,9566,13959,14041,22995,23088,23248,23316,23697,23785,24037,24567,24646,24716", "endColumns": "94,81,104,104,89,81,88,92,82,67,67,87,87,75,78,69,123", "endOffsets": "5388,5470,9305,9487,9651,14036,14125,23083,23166,23311,23379,23780,23868,24108,24641,24711,24835"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,502,646,762,866,957,1099,1245,1362,1501,1651,1779,1911,2019,2121,2257,2347,2450,2558,2663,2764,2882,2997,3109,3225,3332,3447,3571,3695,3818,3931,4018,4101,4206,4342,4500", "endColumns": "106,100,96,91,143,115,103,90,141,145,116,138,149,127,131,107,101,135,89,102,107,104,100,117,114,111,115,106,114,123,123,122,112,86,82,104,135,157,92", "endOffsets": "207,308,405,497,641,757,861,952,1094,1240,1357,1496,1646,1774,1906,2014,2116,2252,2342,2445,2553,2658,2759,2877,2992,3104,3220,3327,3442,3566,3690,3813,3926,4013,4096,4201,4337,4495,4588"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14193,14300,14401,14498,14590,14734,14850,14954,15045,15187,15333,15450,15589,15739,15867,15999,16107,16209,16345,16435,16538,16646,16751,16852,16970,17085,17197,17313,17420,17535,17659,17783,17906,18019,18106,18189,18294,18430,23384", "endColumns": "106,100,96,91,143,115,103,90,141,145,116,138,149,127,131,107,101,135,89,102,107,104,100,117,114,111,115,106,114,123,123,122,112,86,82,104,135,157,92", "endOffsets": "14295,14396,14493,14585,14729,14845,14949,15040,15182,15328,15445,15584,15734,15862,15994,16102,16204,16340,16430,16533,16641,16746,16847,16965,17080,17192,17308,17415,17530,17654,17778,17901,18014,18101,18184,18289,18425,18583,23472"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,257,356,459,565,672,785", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "148,252,351,454,560,667,780,881"}, "to": {"startLines": "56,57,58,59,60,61,62,270", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4249,4347,4451,4550,4653,4759,4866,24195", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "4342,4446,4545,4648,4754,4861,4974,24291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,141,218,288,355,438,525,615", "endColumns": "85,76,69,66,82,86,89,98", "endOffsets": "136,213,283,350,433,520,610,709"}, "to": {"startLines": "50,102,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3633,9310,19517,19587,19654,19737,19824,19914", "endColumns": "85,76,69,66,82,86,89,98", "endOffsets": "3714,9382,19582,19649,19732,19819,19909,20008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,270,347,485,654,735", "endColumns": "70,93,76,137,168,80,77", "endOffsets": "171,265,342,480,649,730,808"}, "to": {"startLines": "69,158,258,263,272,290,291", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5552,13608,23171,23559,24398,26167,26248", "endColumns": "70,93,76,137,168,80,77", "endOffsets": "5618,13697,23243,23692,24562,26243,26321"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,522,726,823,914,998,1092,1187,1259,1330,1429,1529,1596,1660,1726,1806,1924,2048,2166,2241,2333,2407,2480,2574,2662,2725,2794,2847,2905,2957,3018,3078,3140,3205,3273,3343,3402,3470,3537,3605,3659,3727,3814,3901", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,96,90,83,93,94,71,70,98,99,66,63,65,79,117,123,117,74,91,73,72,93,87,62,68,52,57,51,60,59,61,64,67,69,58,67,66,67,53,67,86,86,54", "endOffsets": "288,517,721,818,909,993,1087,1182,1254,1325,1424,1524,1591,1655,1721,1801,1919,2043,2161,2236,2328,2402,2475,2569,2657,2720,2789,2842,2900,2952,3013,3073,3135,3200,3268,3338,3397,3465,3532,3600,3654,3722,3809,3896,3951"}, "to": {"startLines": "2,11,15,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,343,572,9723,9820,9911,9995,10089,10184,10256,10327,10426,10526,10593,10657,10723,10803,10921,11045,11163,11238,11330,11404,11477,11571,11659,11722,12446,12499,12557,12609,12670,12730,12792,12857,12925,12995,13054,13122,13189,13257,13311,13379,13466,13553", "endLines": "10,14,18,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "endColumns": "17,12,12,96,90,83,93,94,71,70,98,99,66,63,65,79,117,123,117,74,91,73,72,93,87,62,68,52,57,51,60,59,61,64,67,69,58,67,66,67,53,67,86,86,54", "endOffsets": "338,567,771,9815,9906,9990,10084,10179,10251,10322,10421,10521,10588,10652,10718,10798,10916,11040,11158,11233,11325,11399,11472,11566,11654,11717,11786,12494,12552,12604,12665,12725,12787,12852,12920,12990,13049,13117,13184,13252,13306,13374,13461,13548,13603"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,183,245,314,391,471,560,641", "endColumns": "68,58,61,68,76,79,88,80,68", "endOffsets": "119,178,240,309,386,466,555,636,705"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11791,11860,11919,11981,12050,12127,12207,12296,12377", "endColumns": "68,58,61,68,76,79,88,80,68", "endOffsets": "11855,11914,11976,12045,12122,12202,12291,12372,12441"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,120", "endOffsets": "163,284"}, "to": {"startLines": "254,255", "startColumns": "4,4", "startOffsets": "22761,22874", "endColumns": "112,120", "endOffsets": "22869,22990"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,432,520,623,738,817,894,985,1078,1173,1267,1367,1460,1555,1649,1740,1833,1914,2018,2121,2219,2326,2433,2538,2695,2791", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "208,315,427,515,618,733,812,889,980,1073,1168,1262,1362,1455,1550,1644,1735,1828,1909,2013,2116,2214,2321,2428,2533,2690,2786,2868"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "947,1055,1162,1274,1362,1465,1580,1659,1736,1827,1920,2015,2109,2209,2302,2397,2491,2582,2675,2756,2860,2963,3061,3168,3275,3380,3537,23955", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "1050,1157,1269,1357,1460,1575,1654,1731,1822,1915,2010,2104,2204,2297,2392,2486,2577,2670,2751,2855,2958,3056,3163,3270,3375,3532,3628,24032"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,226,303,415,541,622,689,792,867,930,1022,1087,1154,1226,1298,1352,1473,1532,1596,1650,1727,1859,1944,2025,2144,2231,2314,2406,2473,2539,2611,2688,2779,2859,2938,3013,3092,3182,3255,3349,3446,3520,3593,3692,3747,3815,3903,3992,4054,4118,4181,4290,4395,4498,4607", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,111,125,80,66,102,74,62,91,64,66,71,71,53,120,58,63,53,76,131,84,80,118,86,82,91,66,65,71,76,90,79,78,74,78,89,72,93,96,73,72,98,54,67,87,88,61,63,62,108,104,102,108,81", "endOffsets": "221,298,410,536,617,684,787,862,925,1017,1082,1149,1221,1293,1347,1468,1527,1591,1645,1722,1854,1939,2020,2139,2226,2309,2401,2468,2534,2606,2683,2774,2854,2933,3008,3087,3177,3250,3344,3441,3515,3588,3687,3742,3810,3898,3987,4049,4113,4176,4285,4390,4493,4602,4684"}, "to": {"startLines": "19,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "776,4172,4979,5091,5217,9656,13781,13884,14130,18588,18680,18745,18812,18884,18956,19010,19131,19190,19254,19308,19385,20013,20098,20179,20298,20385,20468,20560,20627,20693,20765,20842,20933,21013,21092,21167,21246,21336,21409,21503,21600,21674,21747,21846,21901,21969,22057,22146,22208,22272,22335,22444,22549,22652,23477", "endLines": "22,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "endColumns": "12,76,111,125,80,66,102,74,62,91,64,66,71,71,53,120,58,63,53,76,131,84,80,118,86,82,91,66,65,71,76,90,79,78,74,78,89,72,93,96,73,72,98,54,67,87,88,61,63,62,108,104,102,108,81", "endOffsets": "942,4244,5086,5212,5293,9718,13879,13954,14188,18675,18740,18807,18879,18951,19005,19126,19185,19249,19303,19380,19512,20093,20174,20293,20380,20463,20555,20622,20688,20760,20837,20928,21008,21087,21162,21241,21331,21404,21498,21595,21669,21742,21841,21896,21964,22052,22141,22203,22267,22330,22439,22544,22647,22756,23554"}}]}]}