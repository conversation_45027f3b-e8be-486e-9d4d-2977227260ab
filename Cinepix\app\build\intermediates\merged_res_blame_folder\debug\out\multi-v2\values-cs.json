{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-91:/values-cs/values-cs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,276,354,501,670,754", "endColumns": "72,97,77,146,168,83,80", "endOffsets": "173,271,349,496,665,749,830"}, "to": {"startLines": "75,164,264,269,278,296,297", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5825,13656,23029,23417,24247,25988,26072", "endColumns": "72,97,77,146,168,83,80", "endOffsets": "5893,13749,23102,23559,24411,26067,26148"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,400,492,618,699,764,863,939,1000,1089,1156,1210,1278,1338,1392,1509,1569,1631,1685,1757,1879,1963,2055,2162,2240,2322,2410,2477,2543,2615,2692,2776,2848,2925,2999,3070,3158,3229,3322,3417,3491,3565,3661,3713,3780,3866,3954,4016,4080,4143,4253,4349,4448,4546", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,76,91,125,80,64,98,75,60,88,66,53,67,59,53,116,59,61,53,71,121,83,91,106,77,81,87,66,65,71,76,83,71,76,73,70,87,70,92,94,73,73,95,51,66,85,87,61,63,62,109,95,98,97,78", "endOffsets": "318,395,487,613,694,759,858,934,995,1084,1151,1205,1273,1333,1387,1504,1564,1626,1680,1752,1874,1958,2050,2157,2235,2317,2405,2472,2538,2610,2687,2771,2843,2920,2994,3065,3153,3224,3317,3412,3486,3560,3656,3708,3775,3861,3949,4011,4075,4138,4248,4344,4443,4541,4620"}, "to": {"startLines": "23,61,69,70,71,112,166,167,170,209,210,211,212,213,214,215,216,217,218,219,220,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,268", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "974,4466,5274,5366,5492,9793,13831,13930,14176,18572,18661,18728,18782,18850,18910,18964,19081,19141,19203,19257,19329,19955,20039,20131,20238,20316,20398,20486,20553,20619,20691,20768,20852,20924,21001,21075,21146,21234,21305,21398,21493,21567,21641,21737,21789,21856,21942,22030,22092,22156,22219,22329,22425,22524,23338", "endLines": "28,61,69,70,71,112,166,167,170,209,210,211,212,213,214,215,216,217,218,219,220,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,268", "endColumns": "12,76,91,125,80,64,98,75,60,88,66,53,67,59,53,116,59,61,53,71,121,83,91,106,77,81,87,66,65,71,76,83,71,76,73,70,87,70,92,94,73,73,95,51,66,85,87,61,63,62,109,95,98,97,78", "endOffsets": "1237,4538,5361,5487,5568,9853,13925,14001,14232,18656,18723,18777,18845,18905,18959,19076,19136,19198,19252,19324,19446,20034,20126,20233,20311,20393,20481,20548,20614,20686,20763,20847,20919,20996,21070,21141,21229,21300,21393,21488,21562,21636,21732,21784,21851,21937,22025,22087,22151,22214,22324,22420,22519,22617,23412"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,273", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1242,1349,1451,1561,1647,1752,1869,1947,2023,2114,2207,2302,2396,2490,2583,2678,2775,2866,2957,3041,3145,3257,3356,3462,3573,3675,3838,23815", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "1344,1446,1556,1642,1747,1864,1942,2018,2109,2202,2297,2391,2485,2578,2673,2770,2861,2952,3036,3140,3252,3351,3457,3568,3670,3833,3931,23893"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,129,203,276,345,425,509,604", "endColumns": "73,73,72,68,79,83,94,102", "endOffsets": "124,198,271,340,420,504,599,702"}, "to": {"startLines": "56,108,221,222,223,224,225,226", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3936,9445,19451,19524,19593,19673,19757,19852", "endColumns": "73,73,72,68,79,83,94,102", "endOffsets": "4005,9514,19519,19588,19668,19752,19847,19950"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,282,397,511,587,678,787,917,1029,1160,1241,1337,1425,1518,1630,1748,1849,1978,2104,2238,2397,2521,2634,2755,2873,2962,3055,3168,3279,3373,3472,3576,3703,3840,3946,4040,4120,4197,4280,4362,4456,4532,4614,4711,4810,4903,5000,5084,5186,5281,5379,5494,5570,5670", "endColumns": "113,112,114,113,75,90,108,129,111,130,80,95,87,92,111,117,100,128,125,133,158,123,112,120,117,88,92,112,110,93,98,103,126,136,105,93,79,76,82,81,93,75,81,96,98,92,96,83,101,94,97,114,75,99,90", "endOffsets": "164,277,392,506,582,673,782,912,1024,1155,1236,1332,1420,1513,1625,1743,1844,1973,2099,2233,2392,2516,2629,2750,2868,2957,3050,3163,3274,3368,3467,3571,3698,3835,3941,4035,4115,4192,4275,4357,4451,4527,4609,4706,4805,4898,4995,5079,5181,5276,5374,5489,5565,5665,5756"}, "to": {"startLines": "57,58,59,60,74,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,110,165,272,275,277,282,283,284,285,286,287,288,289,290,291,292,293,294,295", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4010,4124,4237,4352,5749,5898,5989,6098,6228,6340,6471,6552,6648,6736,6829,6941,7059,7160,7289,7415,7549,7708,7832,7945,8066,8184,8273,8366,8479,8590,8684,8783,8887,9014,9151,9257,9621,13754,23732,23970,24153,24683,24759,24841,24938,25037,25130,25227,25311,25413,25508,25606,25721,25797,25897", "endColumns": "113,112,114,113,75,90,108,129,111,130,80,95,87,92,111,117,100,128,125,133,158,123,112,120,117,88,92,112,110,93,98,103,126,136,105,93,79,76,82,81,93,75,81,96,98,92,96,83,101,94,97,114,75,99,90", "endOffsets": "4119,4232,4347,4461,5820,5984,6093,6223,6335,6466,6547,6643,6731,6824,6936,7054,7155,7284,7410,7544,7703,7827,7940,8061,8179,8268,8361,8474,8585,8679,8778,8882,9009,9146,9252,9346,9696,13826,23810,24047,24242,24754,24836,24933,25032,25125,25222,25306,25408,25503,25601,25716,25792,25892,25983"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,375,477,569,647,739,830,911,980,1049,1131,1217,1289,1368,1436", "endColumns": "92,82,93,101,91,77,91,90,80,68,68,81,85,71,78,67,119", "endOffsets": "193,276,370,472,564,642,734,825,906,975,1044,1126,1212,1284,1363,1431,1551"}, "to": {"startLines": "72,73,107,109,111,168,169,262,263,265,266,270,271,274,279,280,281", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5573,5666,9351,9519,9701,14006,14084,22857,22948,23107,23176,23564,23646,23898,24416,24495,24563", "endColumns": "92,82,93,101,91,77,91,90,80,68,68,81,85,71,78,67,119", "endOffsets": "5661,5744,9440,9616,9788,14079,14171,22943,23024,23171,23240,23641,23727,23965,24490,24558,24678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,509,639,753,850,940,1061,1181,1288,1411,1573,1697,1820,1923,2019,2149,2241,2344,2446,2559,2659,2776,2897,3019,3140,3248,3364,3488,3614,3738,3864,3951,4035,4139,4274,4440", "endColumns": "106,100,99,95,129,113,96,89,120,119,106,122,161,123,122,102,95,129,91,102,101,112,99,116,120,121,120,107,115,123,125,123,125,86,83,103,134,165,92", "endOffsets": "207,308,408,504,634,748,845,935,1056,1176,1283,1406,1568,1692,1815,1918,2014,2144,2236,2339,2441,2554,2654,2771,2892,3014,3135,3243,3359,3483,3609,3733,3859,3946,4030,4134,4269,4435,4528"}, "to": {"startLines": "171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14237,14344,14445,14545,14641,14771,14885,14982,15072,15193,15313,15420,15543,15705,15829,15952,16055,16151,16281,16373,16476,16578,16691,16791,16908,17029,17151,17272,17380,17496,17620,17746,17870,17996,18083,18167,18271,18406,23245", "endColumns": "106,100,99,95,129,113,96,89,120,119,106,122,161,123,122,102,95,129,91,102,101,112,99,116,120,121,120,107,115,123,125,123,125,86,83,103,134,165,92", "endOffsets": "14339,14440,14540,14636,14766,14880,14977,15067,15188,15308,15415,15538,15700,15824,15947,16050,16146,16276,16368,16471,16573,16686,16786,16903,17024,17146,17267,17375,17491,17615,17741,17865,17991,18078,18162,18266,18401,18567,23333"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,610,924,1005,1085,1163,1265,1363,1441,1505,1594,1686,1756,1822,1887,1959,2072,2187,2310,2384,2464,2536,2617,2711,2806,2873,2938,2991,3049,3097,3158,3224,3291,3354,3421,3486,3545,3610,3674,3740,3792,3855,3932,4009", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,80,79,77,101,97,77,63,88,91,69,65,64,71,112,114,122,73,79,71,80,93,94,66,64,52,57,47,60,65,66,62,66,64,58,64,63,65,51,62,76,76,53", "endOffsets": "282,605,919,1000,1080,1158,1260,1358,1436,1500,1589,1681,1751,1817,1882,1954,2067,2182,2305,2379,2459,2531,2612,2706,2801,2868,2933,2986,3044,3092,3153,3219,3286,3349,3416,3481,3540,3605,3669,3735,3787,3850,3927,4004,4058"}, "to": {"startLines": "2,11,17,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,660,9858,9939,10019,10097,10199,10297,10375,10439,10528,10620,10690,10756,10821,10893,11006,11121,11244,11318,11398,11470,11551,11645,11740,11807,12531,12584,12642,12690,12751,12817,12884,12947,13014,13079,13138,13203,13267,13333,13385,13448,13525,13602", "endLines": "10,16,22,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163", "endColumns": "17,12,12,80,79,77,101,97,77,63,88,91,69,65,64,71,112,114,122,73,79,71,80,93,94,66,64,52,57,47,60,65,66,62,66,64,58,64,63,65,51,62,76,76,53", "endOffsets": "332,655,969,9934,10014,10092,10194,10292,10370,10434,10523,10615,10685,10751,10816,10888,11001,11116,11239,11313,11393,11465,11546,11640,11735,11802,11867,12579,12637,12685,12746,12812,12879,12942,13009,13074,13133,13198,13262,13328,13380,13443,13520,13597,13651"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,193,256,325,402,472,554,634", "endColumns": "75,61,62,68,76,69,81,79,79", "endOffsets": "126,188,251,320,397,467,549,629,709"}, "to": {"startLines": "137,138,139,140,141,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11872,11948,12010,12073,12142,12219,12289,12371,12451", "endColumns": "75,61,62,68,76,69,81,79,79", "endOffsets": "11943,12005,12068,12137,12214,12284,12366,12446,12526"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "62,63,64,65,66,67,68,276", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4543,4641,4743,4844,4943,5048,5155,24052", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "4636,4738,4839,4938,5043,5150,5269,24148"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,126", "endOffsets": "158,285"}, "to": {"startLines": "260,261", "startColumns": "4,4", "startOffsets": "22622,22730", "endColumns": "107,126", "endOffsets": "22725,22852"}}]}]}