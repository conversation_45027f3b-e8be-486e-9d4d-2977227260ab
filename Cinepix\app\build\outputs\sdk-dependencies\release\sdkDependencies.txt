# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "viewbinding"
    version: "8.2.1"
  }
  digests {
    sha256: "?R7\243\360\204\034 \274\031\023_\302\306\324\214*\200&\333\266g\307R\00430\316\236mFt"
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.8.1"
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.8.1"
  }
  digests {
    sha256: "\232\2532m\224\222\200\t\221\205C`\254$\217I<\347\367\303\0305\0310\233x\254\351\342@\366\366"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.9.0"
  }
  digests {
    sha256: "5\256\377\276-\265\252D`r\316\345\017\316\344\213\177\251\342\374Q\3127\300\314}}\v\303\235\225."
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.9.0"
  }
  digests {
    sha256: "(2t K\327\300 x\236\304o\217\216r\257BD\327\365P\2639*W\345\312\000j\327\252,"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
}
library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "databinding-common"
    version: "8.2.1"
  }
  digests {
    sha256: "f\312\270&9\332\300\366\302C4d\300\223\260t\326\b\304\273\210~\303\212\233\213\304\254\230\022g2"
  }
}
library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "databinding-runtime"
    version: "8.2.1"
  }
  digests {
    sha256: "\330e\311~\2110\246n\215\353R\302\326\032\320t\336E(\252\372\201\026\a\001\223I>?I(\365"
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.2.0"
  }
  digests {
    sha256: "\026\327~\214D?\245_\351\246\aM\000D]R\f\245\311\371\023\316\375\277H(5bU!NB"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.7.0"
  }
  digests {
    sha256: "\201\306\373\035\273j<\327\334\202}{\b\341\310\024.\323\244\000\243B$A\020\204\200\342/\2117\304"
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.7.0"
  }
  digests {
    sha256: "S=\355\216\340dZ\030\366e=\245?\341\354Z\025C\016\1776\2477\324^A\0051\363\0173\030"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.0"
  }
  digests {
    sha256: "\245\237\242O\337\037\373YK\256\315\277\017\321\000\020\371w\316\241\0026\324\207\3764d\227zsw\372"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.0"
  }
  digests {
    sha256: "\267\227\232z\254\224\005_\r\237\037\323\264|\345\377\341\313`2\250B\272\237\276q\206\360\205(\221x"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.7.0"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.7.0"
  }
  digests {
    sha256: "\232\377\242La`\334\214\255\252\311B-OqNP\tS}\002C\257\304\274t\265\267\317\r\344\255"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.7.0"
  }
  digests {
    sha256: "\257\216\021\270~\003\rn1\a\3559\255\317\001\372\020\326%\236\261\\C\313\246\347\264\343\377\256\337\'"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\251\177L!\221\353\3352\371\232\302)Y{\321\251$F\260\034\321\240\210\3214\224\314\005\312\277\r\357"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\222\375\017\002\273YO\a\264\017\202p\3533**\341\345\030\313\246\376\307\320b\363\366\004<\231\315\257"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.7.0"
  }
  digests {
    sha256: "o\263=\224s\244\223=\251\331\212\v\022\266\006\022~\200h\033\331\271\003\t\314\322\302#\bc\2719"
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\357p3\261]\262uiw\034<\n\353o\2229+VT!a\225\376t\023n\243\341\b\221*\324"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.7.0"
  }
  digests {
    sha256: "\343\033\320\351+\320\263\0336\004H\312Z[\200\374\037cP{\264\356\216\233\323\000CtI\321x\233"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.7.0"
  }
  digests {
    sha256: "N\035\222\342\211\222\f\327\265\0166q\271\031\033\324\023@sI\315\246\366\002\260(Td\364\034\034\202"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-compose"
    version: "2.7.0"
  }
  digests {
    sha256: "v@;\261Y\237n*\334\233\2007.\373&t\270\313\355\342\260\361\346\350\\J\224\341\f{\224\260"
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime"
    version: "1.5.4"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-android"
    version: "1.5.4"
  }
  digests {
    sha256: "\247\3379\274\3012\177\245\247\352]\312)v\341\033\207\376\352L\250\235\031h\313\273i\024\371\203\315L"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-livedata"
    version: "1.5.4"
  }
  digests {
    sha256: "\344\226\217\016\366]&\251HW\233\204\3659\250\315k\263Rv\340\276@exeJ?z\372_\321"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui"
    version: "1.5.4"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-android"
    version: "1.5.4"
  }
  digests {
    sha256: "\023\363\232\217\v\372\366!\253W\367z\320\"\324\202\265\362\261H\003\245b<\304\375\312.\371\005\312&"
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.8.2"
  }
  digests {
    sha256: "\\x(=\031V\261K\"\317j\327\fq*s\252\bA\026\267SU&\027l;\205\346\251 Z"
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.2"
  }
  digests {
    sha256: "\354\301\031&%0\246\342\371\363s ~G-Gc\006\276\323\324\262\026\376a\261\352B\343\276\366\210"
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.12.0"
  }
  digests {
    sha256: "B\377\247\312G\327\272\217\341\330t\305~\371\307\021\033\304\032+\f\f!Q\2129\340}\"-\355\213"
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.12.0"
  }
  digests {
    sha256: "\225\263\355\257x\"{|\310\330\002\321\037\207\223\251\256\322\222\345+\217\277\214\b\326\023L\313\027\026\323"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.7.0"
  }
  digests {
    sha256: "W\305x\206.!\2366\fiW\377\v\312o\026\227\a\261\345X-O\245\223\342\204\367\366>3\366"
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\264\222\333\374\205\222dq6q\022\366\320\345\322\311\231\036\205\\T!\25379\223\226\314\001#\342\257"
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.1"
  }
  digests {
    sha256: "\320\344\002\3541\362@(\241\334~\266\240\243\371\331c\\\024Y9,\32749cC\267=g9H"
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-compose"
    version: "1.8.2"
  }
  digests {
    sha256: "Zg\351\204\361N\322\257\305\205\252:#\355\377\034\027\221\310\f\252+\366\212\017y\234\033\021\243\2208"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable"
    version: "1.5.4"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable-android"
    version: "1.5.4"
  }
  digests {
    sha256: "^\203L\022\327\322&\241\005~np\263A\314\'AJ\372.\235\000\af\v\235\377\354kvs\355"
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.0.0"
  }
  digests {
    sha256: "\311F\217V\340P\006\352\025\032BlT\225|\320y\233\213\203\245y\322\204m\322 a\363>^\315"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry"
    version: "1.5.4"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry-android"
    version: "1.5.4"
  }
  digests {
    sha256: ".\315\005\320\30032S\004\203J6\035>_,\0019[\374]\242@&A\317\316\344\230\032\001\251"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util"
    version: "1.5.4"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util-android"
    version: "1.5.4"
  }
  digests {
    sha256: "\002\315Y\253\274\317\225\376\362\202[\244\300\207\022U8\fo[\243\352\352\205\261\211-\342KNkP"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics"
    version: "1.5.4"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics-android"
    version: "1.5.4"
  }
  digests {
    sha256: "\217x\021\323O\027\216-\346c\231\350\260\375t}\263P*\233\204JN-\224jq\036ue\\\274"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit"
    version: "1.5.4"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit-android"
    version: "1.5.4"
  }
  digests {
    sha256: "\254u:\261)\267\v\253y\317\352i\036\326\274\216\302\017\336\'\215\267&\233\224\365\002\'\247\0220."
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text"
    version: "1.5.4"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text-android"
    version: "1.5.4"
  }
  digests {
    sha256: "\0263\335T\024!i\245\006e\200\371\245\200\322f\fy\017\301\266\214.S<6\251\300\370\261\n\273"
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.4.0"
  }
  digests {
    sha256: "C?\353\323CJEfqv\307jd\363\362\005\312c5\246\265D\305\265\325\177%\243\2127RB"
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.4.0"
  }
  digests {
    sha256: "\355]>\327r\245\373\360\325p\367RoX\\\326\032\030\016`\3717%\204\303(\246\216,\3773u"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview"
    version: "1.5.4"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview-android"
    version: "1.5.4"
  }
  digests {
    sha256: "\022c(\204\2007-\277\265Mc#N\234\253\372\177\224\261:[\241\b\320lJ\241\227m\273\252\336"
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation"
    version: "1.5.4"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-android"
    version: "1.5.4"
  }
  digests {
    sha256: "t\a[\177]\340\202\367|\342{\200\270x|Y\254>\310m\nf\277WT7r\021\362\211@r"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation"
    version: "1.5.4"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-android"
    version: "1.5.4"
  }
  digests {
    sha256: "B\2058w^\245U)\271S\373\267\246\000\f\317\335\310\227\"\306\234z0C\032c\017\242\376t\362"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core"
    version: "1.5.4"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core-android"
    version: "1.5.4"
  }
  digests {
    sha256: "\327\350_\264\3736\333\025\353\025\023O\376\3106?\332\205\027\356\224J\361D\030\025\023\020)\371`\310"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout"
    version: "1.5.4"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout-android"
    version: "1.5.4"
  }
  digests {
    sha256: "c\326\263\203 4\346\361\207!9p\364-\202\320\316\023ml`at\2174\360\\\362\272\035Cd"
  }
}
library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "databinding-adapters"
    version: "8.2.1"
  }
  digests {
    sha256: "\355\332A\350\310X\346y\317\363\256\201[\216\027i\233\255\030\277\0350\340<<y\325\000\230\336Q\262"
  }
}
library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "databinding-ktx"
    version: "8.2.1"
  }
  digests {
    sha256: "\272\367\245\r\205\274,\337U1^|\325\'\223\277Yi\214\244\277\253\005\352\004!\377\365\231\374f\003"
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.6.2"
  }
  digests {
    sha256: "F\f\2206\266M\247\316z\006j\f\261\204\340\024Q\317\304I)\252\033b\376\006\211Y\177#C\370"
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.1.0"
  }
  digests {
    sha256: "+\374TG\\\004q1\2213a\365m\017\177\001\234n[\356S\356\260\353}\224\247\304\231\240R\'"
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.6.2"
  }
  digests {
    sha256: "\337{\247?7}\275|\315(\261\022\257\320\215(\266\016\302\306\274\221\354e\206\t\310ED*\316\b"
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.6.1"
  }
  digests {
    sha256: "~\245W;\223\253\253\323\27521$Q\306\352H\246b\260:\024\r\332\201\256\276uwj \244\""
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.6.1"
  }
  digests {
    sha256: "\333\221]\277I5xc\336\026i\377\237\335\216\220\b\326_\343W\257l\316\232\3460C\255_f\027"
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.1.4"
  }
  digests {
    sha256: "\r\367\024\300\265\036Tq\016\277tn\264i\3233\027k\273<\262\237\200w]\303\312N\263\026%\022"
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.0.4"
  }
  digests {
    sha256: ">G\177M\3421\345\213%\365\251\222\363\276E\351}3,4\243\232\236>}Kx\256\n\302%o"
  }
}
library {
  maven_library {
    groupId: "androidx.multidex"
    artifactId: "multidex"
    version: "2.0.1"
  }
  digests {
    sha256: "B\3352\377\237\227\370Wq\270* \000:\215p\366\212\267\264\2722\211d1,\340s&\223\333\t"
  }
}
library {
  maven_library {
    groupId: "androidx.compose"
    artifactId: "compose-bom"
    version: "2023.10.01"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-extended"
    version: "1.5.4"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-extended-android"
    version: "1.5.4"
  }
  digests {
    sha256: "\266\231]\b~y\273\030\264K\350\017\036\214q\026\326\005o\252\243\332#\323\326%\355qQ\304\337\301"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core"
    version: "1.5.4"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core-android"
    version: "1.5.4"
  }
  digests {
    sha256: "\374+\307]\315S\225\231\247s\343\250m\273:\230\231\375\305\213e\351\031u\037J-w\276\273\321\207"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple"
    version: "1.5.4"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple-android"
    version: "1.5.4"
  }
  digests {
    sha256: "\212l\213K\374\214\256\235\307\347\226S\245\336:H,\271\271m)\017k\275\306\323\000+\221\355H\313"
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3"
    version: "1.1.2"
  }
  digests {
    sha256: "\375\237\217\351\035fa\257\312\360\351\311\316\363\vZ\031kM\357[(\243p\361?,%\234&\344\202"
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-compose"
    version: "2.7.6"
  }
  digests {
    sha256: "\344\331\325\253S\207\022l\227\004\326\326\352\347\257\342\037^\307;>\376\235\240\326\205_4-\a\300\336"
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime-ktx"
    version: "2.7.6"
  }
  digests {
    sha256: "\231o\242es\367\205\025\250\322\365.\272\037\262G\031\a\bd\2471[\347\022b\032WDU\347V"
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common-ktx"
    version: "2.7.6"
  }
  digests {
    sha256: "\311\325\267\331+ \030\252\351\205\027\266\003\244E\034\347\225\221B\\\020\227\376\024+\301,\352\vS\274"
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common"
    version: "2.7.6"
  }
  digests {
    sha256: "3\315U\3019Qr$\2376\255\231\037s\337x\213B\016\374\302\250/\022\310\244\027\365\317\364\b,"
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-fragment"
    version: "2.7.6"
  }
  digests {
    sha256: "\372\'\3242\002z\3565Y\304\335{\020\365\226\"!\b\205\352\216\217\302\205\367I\033\356\342\262\024\262"
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime"
    version: "2.7.6"
  }
  digests {
    sha256: "\333\255-K\312\257\260\r\r\346\274o\315\n\030\207\025j{\257\343<\242\313\345\203A\355\300\324:\373"
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-fragment-ktx"
    version: "2.7.6"
  }
  digests {
    sha256: "a\304\234\357\253\235\006J\352\030\231\354\242\300<\002hl\242,j\367\337D\355\177\005\255\306\271!\357"
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-ui-ktx"
    version: "2.7.6"
  }
  digests {
    sha256: "\032\352\232\203\022u\017\215G3\332\213\243z\2221:\351\262I%D\270#\354nV\034IJ\rg"
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-ui"
    version: "2.7.6"
  }
  digests {
    sha256: "\230&n\026\312\214\343,\321\301,\225\031\207\177h\236\270\307(\\\241\334\211^\321\361e\227\334\177\355"
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.4.1"
  }
  digests {
    sha256: "6\322\215\236\303:\214d18B\274\351\234\225sm\245\262zk:Q69\005\r\350/\aW&"
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.4.0-beta01"
  }
  digests {
    sha256: "Cc2\346\210\317\021\202R[-8HW\271\201g\261\337\304\023G\270\334#\220U\227\337\233\027\021"
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.3.0"
  }
  digests {
    sha256: "\326Y(\240\017cX\232I\342\031%A.\017H\205/\211%K\a\260<\003\rV\017\221\357\374\210"
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.2.0"
  }
  digests {
    sha256: "_S3\233\342\244\371\n\232\276\243W\035\325\236p\250\244\236\177\025\335\202\227J8\230\264e.\207\024"
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window"
    version: "1.0.0"
  }
  digests {
    sha256: "2\022\230[\344\022ss\312M\016\247\370\270\032%\n\342\020^\222Oy@\020]\006z\017\232\3010"
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "retrofit"
    version: "2.9.0"
  }
  digests {
    sha256: "\346\352\031)\304hR\365\276\306j\2635}\243\203Gl\357N\215\035\356\375\277\031[y\314Me\201"
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.11.0"
  }
  digests {
    sha256: "\356\217k\326\315\022W\001=t\2030\364\312\024v8\251\373\313R\3738\215Z\311<\3654\bt]"
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.4.0"
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.4.0"
  }
  digests {
    sha256: "\0019\354zPm\273\325L\255b)\033\001\234\270PSK\340\227\310\306l\020\000\325\373\350\355\357>"
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "converter-gson"
    version: "2.9.0"
  }
  digests {
    sha256: "2\252 k\232)\311\337^\332\223\240\222\317\263\260\271\023>#,\006+\252\210/\003\031\360\347\237\016"
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.10.1"
  }
  digests {
    sha256: "BA\301Jw\'\303O\356\246P~\310\0011\212=J\220\360p\344RV\201\a\237\271N\344\305\223"
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "logging-interceptor"
    version: "4.11.0"
  }
  digests {
    sha256: "\271\237-H\217\316\232\305\256\277ux:xH\370?\247\247\252\301C\202\fN\275I\333\204\231\333\214"
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.6.1"
  }
  digests {
    sha256: "ibO\327\255\326\316[\374\301#b\315BsA\322\221\016\'~\325\246\374\304a2\244\211\221\024\320"
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.6.1"
  }
  digests {
    sha256: "\2312m>\354\244\246Fq\274\210\002\361\344_Nk4\216\214\177\253\2420\303\205M1h\t\377\317"
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-ktx"
    version: "2.6.1"
  }
  digests {
    sha256: "\332L\f\272~\374\257\242\237\276\253\035\264\031\204#\217%\301\2436\022\301\326\rc\271\225\226\215p\312"
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.4.0"
  }
  digests {
    sha256: "\273\177\241\023\021/~HWIn\".0Q\327:\221\n\335t\277@v\036\033\332\345[\002\026\257"
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.4.0"
  }
  digests {
    sha256: "\312nP3\"\262\346\003t\302\263l\225\305\v\026p\235\223\210\3766\350\017\262=\341\373\367\246\353\225"
  }
}
library {
  maven_library {
    groupId: "io.coil-kt"
    artifactId: "coil-compose"
    version: "2.4.0"
  }
  digests {
    sha256: "\n\v\3273\241a|W+f\346\302\200\rI\355\rtK$\270\306\320$P\241\311N\b\302\227o"
  }
}
library {
  maven_library {
    groupId: "io.coil-kt"
    artifactId: "coil-compose-base"
    version: "2.4.0"
  }
  digests {
    sha256: "\366\021\236\003\352\327\215\200K\255B\030.\035\017\337\217\235,d}\257\n\2723\350\234d\374\312,\342"
  }
}
library {
  maven_library {
    groupId: "com.google.accompanist"
    artifactId: "accompanist-drawablepainter"
    version: "0.30.1"
  }
  digests {
    sha256: ",2\322t=\275)\b\r\by\273\214\303Y\360\372\250C\370!C\274{\037\345.\327\214\312q\320"
  }
}
library {
  maven_library {
    groupId: "io.coil-kt"
    artifactId: "coil-base"
    version: "2.4.0"
  }
  digests {
    sha256: "\367\245\214\017\246_\365R\213\240;\263\251\232\020\0040\027\233\306\341\266.;\211ye\022\344\227cA"
  }
}
library {
  maven_library {
    groupId: "androidx.exifinterface"
    artifactId: "exifinterface"
    version: "1.3.6"
  }
  digests {
    sha256: "\030\004\020^\236\005\375\330\367`A;\255]\344\230\303\201\2522\237O\235\224\310Q\274\211\032\306T\306"
  }
}
library {
  maven_library {
    groupId: "io.coil-kt"
    artifactId: "coil"
    version: "2.4.0"
  }
  digests {
    sha256: "!\tO))\341\374\241\b\317=\006\237t2\214\307\376\366P\206\3072i\317\351\017\360 \313\025\363"
  }
}
library {
  maven_library {
    groupId: "io.coil-kt"
    artifactId: "coil-gif"
    version: "2.4.0"
  }
  digests {
    sha256: "rX{\244\262&\274\221\244\351\016\331H\024\345\314ogW\3172\035\265\3748\264\350\243L\314\236\026"
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer"
    version: "1.2.1"
  }
  digests {
    sha256: "\205 L/\274\264\352h\375\021\271\304]\241ki\030\230\024z!\343\351\331W\226\255[E\304\0370"
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-common"
    version: "1.2.1"
  }
  digests {
    sha256: "=\3353\350\301\024\266\022v\255\304\352\260\342\310xT\t?\306x\350\273\2140\276;\251o\323\267\027"
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "31.1-android"
  }
  digests {
    sha256: "2\254.\327\t\331m\'\213].>\\\352\027\217\244\223\2319\305%\373du2\360\0230\215\263\t"
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-container"
    version: "1.2.1"
  }
  digests {
    sha256: "\346}\213P\375\261t\252##\217y\263X:H\\\254\251e\205$\265\006\265\352\326\351\304}\033d"
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-database"
    version: "1.2.1"
  }
  digests {
    sha256: "%M`\b\031\352\003\354L!\350<:\277\357\322,\035\030d\aN\\\252\247S\030\217lE\021\225"
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-datasource"
    version: "1.2.1"
  }
  digests {
    sha256: "m\240\366\210\\\205:\037\371\342\207O\216\317hM{\215\030\324]\214\030\274\033|I_\005\245\264\325"
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-decoder"
    version: "1.2.1"
  }
  digests {
    sha256: "\216D\212\203\257\364|~\255\264\333\333\374\344\200\206\255\315\370P\276\3103E\n\346\222\006\370>\'m"
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer-dash"
    version: "1.2.1"
  }
  digests {
    sha256: "\223\337\251\375\302|\274\344\335\321p\372F\027#Q`\223\021\204\343s\034u\310m0\206\277\223\025\365"
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-exoplayer-hls"
    version: "1.2.1"
  }
  digests {
    sha256: "\310\003Ks1\301O\021\225\f\241\255\n\273\370\234\017\031Lu\306\244\33563\315\274Q=-\265\276"
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-extractor"
    version: "1.2.1"
  }
  digests {
    sha256: "f\225[\177[\315\t\323I\231r;w\314\307\'\253\315\271o\332\221M\302\346c\302\202q\217\356\355"
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-session"
    version: "1.2.1"
  }
  digests {
    sha256: "\254\366\257w\024\373j\005Yf\204[\346~<nC(K\242\357w\343BI\313\247\b\v\343\a\027"
  }
}
library {
  maven_library {
    groupId: "androidx.media"
    artifactId: "media"
    version: "1.6.0"
  }
  digests {
    sha256: "\376b\210G\330\371\t\312\217#\326\205\006\037\232v\204\252\f\233\311\301\262\001\304\377n\267\2637(("
  }
}
library {
  maven_library {
    groupId: "androidx.media3"
    artifactId: "media3-ui"
    version: "1.2.1"
  }
  digests {
    sha256: "\322\f\f\373)\271\344\022\341\224\037\302m\205c\030\321\257\221(H\343\336\271r\205iP\366a\312\337"
  }
}
library {
  maven_library {
    groupId: "androidx.webkit"
    artifactId: "webkit"
    version: "1.12.1"
  }
  digests {
    sha256: "\030\240\245\032\321)\340\310V?\003\035F\310\247\346\021i\365j\350NV*\ruhiA&!\021"
  }
}
library {
  maven_library {
    groupId: "androidx.leanback"
    artifactId: "leanback"
    version: "1.0.0"
  }
  digests {
    sha256: "\b,H\377|8e\216\255\227\212\325#\bc\020\354\356\212Q(\344U\367Q\250@/\223\216\373\267"
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-ui"
    version: "1.0.0"
  }
  digests {
    sha256: "\r\022`\306\347\346\2437\370u\337q\265\026\223\036p?qn\220\210\230\027\315: \372Z\303\331G"
  }
}
library {
  maven_library {
    groupId: "androidx.swiperefreshlayout"
    artifactId: "swiperefreshlayout"
    version: "1.0.0"
  }
  digests {
    sha256: "\227a\263\250\t\311\260\223\375\006\243\304\273\306Eum\354\016\225\265\311\332A\233\311\362\243\363\002n\215"
  }
}
library {
  maven_library {
    groupId: "androidx.asynclayoutinflater"
    artifactId: "asynclayoutinflater"
    version: "1.0.0"
  }
  digests {
    sha256: "\367\352\266\fW\255\335\224\273\006\'X2\376v\000a\033\352\252\341\241\354Y|#\031V\372\371l\213"
  }
}
library {
  maven_library {
    groupId: "androidx.leanback"
    artifactId: "leanback-preference"
    version: "1.0.0"
  }
  digests {
    sha256: "\377\213\250\275\300\r\370KU\212\305NZ\236\276\265\'3\204\311\331F\037\220e\264\327\375\335\277H@"
  }
}
library {
  maven_library {
    groupId: "androidx.preference"
    artifactId: "preference"
    version: "1.2.1"
  }
  digests {
    sha256: "@\312\212\337\333~\377\266\037\254\263\233\331\312./:@\321\006t;\f\326\334\236!\350\276\335O\205"
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-preference-v14"
    version: "1.0.0"
  }
  digests {
    sha256: "\326\321\031\023\345k\217-\024\375V\v\321\255m\177\325bJ\025\335N\300s\262\331\030\202\005\370b\200"
  }
}
library {
  maven_library {
    groupId: "androidx.preference"
    artifactId: "preference-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\207*^>\a\300\270\322\034\267wi\253/F\213 \232\266\354\b#\030t@\032\354\376\243\004\250\034"
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime-ktx"
    version: "2.9.0"
  }
  digests {
    sha256: "</\232\nQ\202o\033\247Gu\035\211\235\230W\336\371\227l\t\370\352\220\006k6J\274\262J\031"
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime"
    version: "2.9.0"
  }
  digests {
    sha256: "\213\205\363\212\250&\331\002\350\250\215*\371\334s\245\364?f\030r\004\205\361nt&\222\305\241\217o"
  }
}
library {
  maven_library {
    groupId: "com.google.accompanist"
    artifactId: "accompanist-permissions"
    version: "0.30.1"
  }
  digests {
    sha256: "\017\306\237\311\327\004\031O\347H\237j%\325\b\'v\304\211\324\000<\277\265R\r\177\t\253\251\002\313"
  }
}
library {
  maven_library {
    groupId: "io.github.aakira"
    artifactId: "napier"
    version: "1.4.1"
  }
}
library {
  maven_library {
    groupId: "io.github.aakira"
    artifactId: "napier-android"
    version: "1.4.1"
  }
  digests {
    sha256: "&\234\307\264:.K]7\337)8\322u-)F\276\251\271\217\373\200\3540\257\025\003\200E\037L"
  }
}
library {
  maven_library {
    groupId: "com.google.accompanist"
    artifactId: "accompanist-systemuicontroller"
    version: "0.30.1"
  }
  digests {
    sha256: "\202\246\342\\\367\272\032\241\347\350PPV\261e\032xS`\347\212\353\313\301\361\301s\035\333\243\234&"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-parcelize-runtime"
    version: "1.9.0"
  }
  digests {
    sha256: "\372i\005\t\3102\031\271\235\313\341R\"\2438\tZo|\205\230w\324\301\247(\001yX6\223\300"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-android-extensions-runtime"
    version: "1.9.0"
  }
  digests {
    sha256: "\346d\233\201\332\325\230\210\350\260\235X\0268\375\026j\366<Y\327\a\027\307\034W5\034xf~y"
  }
}
library_dependencies {
  library_dep_index: 1
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
  library_dep_index: 5
}
library_dependencies {
  library_index: 7
  library_dep_index: 8
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 9
}
library_dependencies {
  library_index: 8
  library_dep_index: 1
}
library_dependencies {
  library_index: 9
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 49
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 48
  library_dep_index: 45
  library_dep_index: 21
}
library_dependencies {
  library_index: 10
  library_dep_index: 1
}
library_dependencies {
  library_index: 11
  library_dep_index: 1
  library_dep_index: 10
}
library_dependencies {
  library_index: 12
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 9
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 48
  library_dep_index: 45
}
library_dependencies {
  library_index: 13
  library_dep_index: 14
  library_dep_index: 16
  library_dep_index: 17
}
library_dependencies {
  library_index: 14
  library_dep_index: 15
}
library_dependencies {
  library_index: 15
  library_dep_index: 5
  library_dep_index: 16
  library_dep_index: 4
  library_dep_index: 17
}
library_dependencies {
  library_index: 16
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 15
}
library_dependencies {
  library_index: 17
  library_dep_index: 3
  library_dep_index: 18
}
library_dependencies {
  library_index: 18
  library_dep_index: 3
}
library_dependencies {
  library_index: 19
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 12
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 9
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 48
  library_dep_index: 45
}
library_dependencies {
  library_index: 20
  library_dep_index: 10
  library_dep_index: 11
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 3
  library_dep_index: 14
  library_dep_index: 12
  library_dep_index: 19
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 9
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 48
  library_dep_index: 45
}
library_dependencies {
  library_index: 21
  library_dep_index: 10
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 9
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 48
  library_dep_index: 45
}
library_dependencies {
  library_index: 22
  library_dep_index: 21
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 9
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 48
  library_dep_index: 45
}
library_dependencies {
  library_index: 23
  library_dep_index: 20
  library_dep_index: 22
  library_dep_index: 3
  library_dep_index: 14
  library_dep_index: 20
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 9
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 48
  library_dep_index: 19
  library_dep_index: 45
  library_dep_index: 12
  library_dep_index: 21
}
library_dependencies {
  library_index: 24
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 25
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 9
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 48
  library_dep_index: 45
}
library_dependencies {
  library_index: 25
  library_dep_index: 1
  library_dep_index: 26
}
library_dependencies {
  library_index: 26
  library_dep_index: 1
}
library_dependencies {
  library_index: 27
  library_dep_index: 1
  library_dep_index: 9
  library_dep_index: 3
  library_dep_index: 13
  library_dep_index: 20
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 9
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 48
  library_dep_index: 22
  library_dep_index: 19
  library_dep_index: 45
  library_dep_index: 12
  library_dep_index: 21
}
library_dependencies {
  library_index: 28
  library_dep_index: 9
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 9
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 48
  library_dep_index: 45
}
library_dependencies {
  library_index: 29
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 9
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 30
  library_dep_index: 48
  library_dep_index: 45
}
library_dependencies {
  library_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 35
  library_dep_index: 19
  library_dep_index: 48
  library_dep_index: 45
  library_dep_index: 3
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 9
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 48
  library_dep_index: 45
  library_dep_index: 12
  library_dep_index: 21
}
library_dependencies {
  library_index: 31
  library_dep_index: 3
}
library_dependencies {
  library_index: 32
  library_dep_index: 33
}
library_dependencies {
  library_index: 33
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 34
  library_dep_index: 51
}
library_dependencies {
  library_index: 34
  library_dep_index: 32
  library_dep_index: 35
  library_dep_index: 20
  library_dep_index: 9
  library_dep_index: 3
  library_dep_index: 32
  library_dep_index: 51
}
library_dependencies {
  library_index: 35
  library_dep_index: 36
}
library_dependencies {
  library_index: 36
  library_dep_index: 37
  library_dep_index: 1
  library_dep_index: 53
  library_dep_index: 8
  library_dep_index: 32
  library_dep_index: 51
  library_dep_index: 54
  library_dep_index: 58
  library_dep_index: 62
  library_dep_index: 60
  library_dep_index: 56
  library_dep_index: 39
  library_dep_index: 68
  library_dep_index: 64
  library_dep_index: 9
  library_dep_index: 29
  library_dep_index: 49
  library_dep_index: 47
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 54
  library_dep_index: 58
  library_dep_index: 62
  library_dep_index: 66
  library_dep_index: 60
  library_dep_index: 56
  library_dep_index: 69
}
library_dependencies {
  library_index: 37
  library_dep_index: 38
  library_dep_index: 44
  library_dep_index: 27
  library_dep_index: 48
  library_dep_index: 47
  library_dep_index: 3
  library_dep_index: 38
  library_dep_index: 50
}
library_dependencies {
  library_index: 38
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 39
  library_dep_index: 9
  library_dep_index: 29
  library_dep_index: 45
  library_dep_index: 49
  library_dep_index: 46
  library_dep_index: 26
  library_dep_index: 3
  library_dep_index: 50
  library_dep_index: 37
}
library_dependencies {
  library_index: 39
  library_dep_index: 1
  library_dep_index: 31
  library_dep_index: 8
  library_dep_index: 40
  library_dep_index: 42
  library_dep_index: 9
  library_dep_index: 43
  library_dep_index: 3
  library_dep_index: 44
}
library_dependencies {
  library_index: 40
  library_dep_index: 1
  library_dep_index: 41
}
library_dependencies {
  library_index: 42
  library_dep_index: 1
}
library_dependencies {
  library_index: 43
  library_dep_index: 1
  library_dep_index: 8
}
library_dependencies {
  library_index: 44
  library_dep_index: 1
  library_dep_index: 39
  library_dep_index: 3
  library_dep_index: 39
}
library_dependencies {
  library_index: 45
  library_dep_index: 1
  library_dep_index: 44
  library_dep_index: 21
  library_dep_index: 29
  library_dep_index: 46
  library_dep_index: 3
  library_dep_index: 13
  library_dep_index: 12
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 9
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 48
}
library_dependencies {
  library_index: 46
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 12
  library_dep_index: 3
  library_dep_index: 47
}
library_dependencies {
  library_index: 47
  library_dep_index: 46
  library_dep_index: 3
  library_dep_index: 46
}
library_dependencies {
  library_index: 48
  library_dep_index: 29
  library_dep_index: 3
  library_dep_index: 13
  library_dep_index: 20
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 9
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 19
  library_dep_index: 45
  library_dep_index: 12
  library_dep_index: 21
}
library_dependencies {
  library_index: 49
  library_dep_index: 1
  library_dep_index: 40
  library_dep_index: 25
  library_dep_index: 41
}
library_dependencies {
  library_index: 50
  library_dep_index: 37
  library_dep_index: 32
  library_dep_index: 51
  library_dep_index: 35
  library_dep_index: 29
  library_dep_index: 3
  library_dep_index: 37
  library_dep_index: 38
}
library_dependencies {
  library_index: 51
  library_dep_index: 52
}
library_dependencies {
  library_index: 52
  library_dep_index: 1
  library_dep_index: 32
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 32
  library_dep_index: 34
}
library_dependencies {
  library_index: 53
  library_dep_index: 39
}
library_dependencies {
  library_index: 54
  library_dep_index: 55
}
library_dependencies {
  library_index: 55
  library_dep_index: 1
  library_dep_index: 32
  library_dep_index: 56
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 35
  library_dep_index: 58
  library_dep_index: 62
  library_dep_index: 66
  library_dep_index: 60
  library_dep_index: 56
}
library_dependencies {
  library_index: 56
  library_dep_index: 57
}
library_dependencies {
  library_index: 57
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 35
  library_dep_index: 54
  library_dep_index: 58
  library_dep_index: 62
  library_dep_index: 66
  library_dep_index: 60
}
library_dependencies {
  library_index: 58
  library_dep_index: 59
}
library_dependencies {
  library_index: 59
  library_dep_index: 1
  library_dep_index: 32
  library_dep_index: 60
  library_dep_index: 56
  library_dep_index: 4
  library_dep_index: 35
  library_dep_index: 54
  library_dep_index: 62
  library_dep_index: 66
  library_dep_index: 60
  library_dep_index: 56
}
library_dependencies {
  library_index: 60
  library_dep_index: 61
}
library_dependencies {
  library_index: 61
  library_dep_index: 1
  library_dep_index: 32
  library_dep_index: 54
  library_dep_index: 56
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 35
  library_dep_index: 54
  library_dep_index: 58
  library_dep_index: 62
  library_dep_index: 66
  library_dep_index: 56
}
library_dependencies {
  library_index: 62
  library_dep_index: 63
}
library_dependencies {
  library_index: 63
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 32
  library_dep_index: 51
  library_dep_index: 58
  library_dep_index: 60
  library_dep_index: 56
  library_dep_index: 39
  library_dep_index: 64
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 14
  library_dep_index: 35
  library_dep_index: 54
  library_dep_index: 58
  library_dep_index: 66
  library_dep_index: 60
  library_dep_index: 56
}
library_dependencies {
  library_index: 64
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 39
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 65
}
library_dependencies {
  library_index: 65
  library_dep_index: 8
  library_dep_index: 39
  library_dep_index: 64
  library_dep_index: 64
}
library_dependencies {
  library_index: 66
  library_dep_index: 67
}
library_dependencies {
  library_index: 67
  library_dep_index: 1
  library_dep_index: 32
  library_dep_index: 4
  library_dep_index: 35
  library_dep_index: 54
  library_dep_index: 58
  library_dep_index: 62
  library_dep_index: 60
  library_dep_index: 56
}
library_dependencies {
  library_index: 68
  library_dep_index: 44
  library_dep_index: 3
}
library_dependencies {
  library_index: 69
  library_dep_index: 70
}
library_dependencies {
  library_index: 70
  library_dep_index: 1
  library_dep_index: 71
  library_dep_index: 75
  library_dep_index: 32
  library_dep_index: 35
  library_dep_index: 62
  library_dep_index: 56
  library_dep_index: 39
  library_dep_index: 64
  library_dep_index: 4
  library_dep_index: 75
}
library_dependencies {
  library_index: 71
  library_dep_index: 72
}
library_dependencies {
  library_index: 72
  library_dep_index: 1
  library_dep_index: 73
  library_dep_index: 75
  library_dep_index: 32
  library_dep_index: 35
  library_dep_index: 54
  library_dep_index: 56
  library_dep_index: 4
  library_dep_index: 73
}
library_dependencies {
  library_index: 73
  library_dep_index: 74
}
library_dependencies {
  library_index: 74
  library_dep_index: 1
  library_dep_index: 32
  library_dep_index: 35
  library_dep_index: 60
  library_dep_index: 56
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 14
  library_dep_index: 71
}
library_dependencies {
  library_index: 75
  library_dep_index: 76
}
library_dependencies {
  library_index: 76
  library_dep_index: 1
  library_dep_index: 73
  library_dep_index: 32
  library_dep_index: 35
  library_dep_index: 56
  library_dep_index: 39
  library_dep_index: 4
  library_dep_index: 69
}
library_dependencies {
  library_index: 77
  library_dep_index: 7
  library_dep_index: 6
}
library_dependencies {
  library_index: 78
  library_dep_index: 7
  library_dep_index: 17
  library_dep_index: 13
  library_dep_index: 27
  library_dep_index: 20
  library_dep_index: 24
  library_dep_index: 28
  library_dep_index: 29
}
library_dependencies {
  library_index: 79
  library_dep_index: 37
  library_dep_index: 80
  library_dep_index: 44
  library_dep_index: 81
  library_dep_index: 22
  library_dep_index: 48
  library_dep_index: 47
  library_dep_index: 3
  library_dep_index: 81
}
library_dependencies {
  library_index: 80
  library_dep_index: 3
  library_dep_index: 8
}
library_dependencies {
  library_index: 81
  library_dep_index: 38
  library_dep_index: 1
  library_dep_index: 31
  library_dep_index: 8
  library_dep_index: 44
  library_dep_index: 21
  library_dep_index: 9
  library_dep_index: 29
  library_dep_index: 45
  library_dep_index: 82
  library_dep_index: 49
  library_dep_index: 46
  library_dep_index: 83
  library_dep_index: 3
  library_dep_index: 79
}
library_dependencies {
  library_index: 82
  library_dep_index: 1
  library_dep_index: 39
  library_dep_index: 20
  library_dep_index: 29
}
library_dependencies {
  library_index: 83
  library_dep_index: 1
  library_dep_index: 39
  library_dep_index: 84
}
library_dependencies {
  library_index: 84
  library_dep_index: 1
  library_dep_index: 39
  library_dep_index: 8
}
library_dependencies {
  library_index: 85
  library_dep_index: 38
  library_dep_index: 1
  library_dep_index: 86
  library_dep_index: 8
  library_dep_index: 39
  library_dep_index: 44
  library_dep_index: 89
  library_dep_index: 90
  library_dep_index: 64
  library_dep_index: 65
  library_dep_index: 81
  library_dep_index: 9
  library_dep_index: 29
  library_dep_index: 91
  library_dep_index: 46
  library_dep_index: 3
  library_dep_index: 86
}
library_dependencies {
  library_index: 86
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 39
  library_dep_index: 87
  library_dep_index: 88
  library_dep_index: 85
}
library_dependencies {
  library_index: 87
  library_dep_index: 1
  library_dep_index: 39
  library_dep_index: 8
}
library_dependencies {
  library_index: 88
  library_dep_index: 87
  library_dep_index: 42
  library_dep_index: 8
}
library_dependencies {
  library_index: 89
  library_dep_index: 1
}
library_dependencies {
  library_index: 90
  library_dep_index: 1
  library_dep_index: 39
  library_dep_index: 84
}
library_dependencies {
  library_index: 91
  library_dep_index: 1
}
library_dependencies {
  library_index: 92
  library_dep_index: 85
  library_dep_index: 39
  library_dep_index: 93
}
library_dependencies {
  library_index: 95
  library_dep_index: 96
  library_dep_index: 102
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 58
  library_dep_index: 66
  library_dep_index: 32
  library_dep_index: 51
  library_dep_index: 71
  library_dep_index: 75
  library_dep_index: 69
  library_dep_index: 97
  library_dep_index: 73
  library_dep_index: 98
  library_dep_index: 100
  library_dep_index: 62
  library_dep_index: 56
  library_dep_index: 36
  library_dep_index: 59
  library_dep_index: 67
  library_dep_index: 33
  library_dep_index: 52
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 70
  library_dep_index: 74
  library_dep_index: 99
  library_dep_index: 101
  library_dep_index: 63
  library_dep_index: 57
  library_dep_index: 54
  library_dep_index: 60
  library_dep_index: 55
  library_dep_index: 61
}
library_dependencies {
  library_index: 96
  library_dep_index: 97
}
library_dependencies {
  library_index: 97
  library_dep_index: 98
  library_dep_index: 32
  library_dep_index: 4
  library_dep_index: 98
  library_dep_index: 100
}
library_dependencies {
  library_index: 98
  library_dep_index: 99
}
library_dependencies {
  library_index: 99
  library_dep_index: 35
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 96
  library_dep_index: 100
}
library_dependencies {
  library_index: 100
  library_dep_index: 101
}
library_dependencies {
  library_index: 101
  library_dep_index: 71
  library_dep_index: 69
  library_dep_index: 32
  library_dep_index: 56
  library_dep_index: 4
  library_dep_index: 98
  library_dep_index: 96
}
library_dependencies {
  library_index: 102
  library_dep_index: 50
  library_dep_index: 73
  library_dep_index: 69
  library_dep_index: 75
  library_dep_index: 98
  library_dep_index: 100
  library_dep_index: 32
  library_dep_index: 35
  library_dep_index: 58
  library_dep_index: 62
  library_dep_index: 56
  library_dep_index: 19
  library_dep_index: 9
  library_dep_index: 29
  library_dep_index: 47
  library_dep_index: 4
}
library_dependencies {
  library_index: 103
  library_dep_index: 50
  library_dep_index: 71
  library_dep_index: 75
  library_dep_index: 32
  library_dep_index: 51
  library_dep_index: 35
  library_dep_index: 30
  library_dep_index: 104
  library_dep_index: 3
  library_dep_index: 109
  library_dep_index: 104
  library_dep_index: 110
  library_dep_index: 107
  library_dep_index: 111
  library_dep_index: 105
  library_dep_index: 108
  library_dep_index: 106
}
library_dependencies {
  library_index: 104
  library_dep_index: 105
  library_dep_index: 108
  library_dep_index: 105
  library_dep_index: 103
  library_dep_index: 107
  library_dep_index: 109
  library_dep_index: 108
  library_dep_index: 111
  library_dep_index: 110
  library_dep_index: 106
}
library_dependencies {
  library_index: 105
  library_dep_index: 106
  library_dep_index: 106
  library_dep_index: 103
  library_dep_index: 107
  library_dep_index: 109
  library_dep_index: 108
  library_dep_index: 104
  library_dep_index: 111
  library_dep_index: 110
}
library_dependencies {
  library_index: 106
  library_dep_index: 1
  library_dep_index: 80
  library_dep_index: 44
  library_dep_index: 12
  library_dep_index: 27
  library_dep_index: 48
  library_dep_index: 45
  library_dep_index: 49
  library_dep_index: 47
  library_dep_index: 3
  library_dep_index: 105
  library_dep_index: 103
  library_dep_index: 107
  library_dep_index: 109
  library_dep_index: 108
  library_dep_index: 104
  library_dep_index: 111
  library_dep_index: 110
}
library_dependencies {
  library_index: 107
  library_dep_index: 79
  library_dep_index: 108
  library_dep_index: 123
  library_dep_index: 3
  library_dep_index: 105
  library_dep_index: 103
  library_dep_index: 109
  library_dep_index: 108
  library_dep_index: 104
  library_dep_index: 111
  library_dep_index: 110
  library_dep_index: 106
}
library_dependencies {
  library_index: 108
  library_dep_index: 37
  library_dep_index: 31
  library_dep_index: 8
  library_dep_index: 27
  library_dep_index: 48
  library_dep_index: 106
  library_dep_index: 3
  library_dep_index: 106
  library_dep_index: 105
  library_dep_index: 103
  library_dep_index: 107
  library_dep_index: 109
  library_dep_index: 104
  library_dep_index: 111
  library_dep_index: 110
}
library_dependencies {
  library_index: 109
  library_dep_index: 107
  library_dep_index: 104
  library_dep_index: 103
  library_dep_index: 107
  library_dep_index: 104
  library_dep_index: 110
  library_dep_index: 111
  library_dep_index: 105
  library_dep_index: 108
  library_dep_index: 106
}
library_dependencies {
  library_index: 110
  library_dep_index: 104
  library_dep_index: 111
  library_dep_index: 103
  library_dep_index: 107
  library_dep_index: 109
  library_dep_index: 104
  library_dep_index: 111
  library_dep_index: 105
  library_dep_index: 108
  library_dep_index: 106
}
library_dependencies {
  library_index: 111
  library_dep_index: 31
  library_dep_index: 84
  library_dep_index: 90
  library_dep_index: 108
  library_dep_index: 112
  library_dep_index: 113
  library_dep_index: 105
  library_dep_index: 103
  library_dep_index: 107
  library_dep_index: 109
  library_dep_index: 108
  library_dep_index: 104
  library_dep_index: 110
  library_dep_index: 106
}
library_dependencies {
  library_index: 112
  library_dep_index: 1
  library_dep_index: 39
  library_dep_index: 8
}
library_dependencies {
  library_index: 113
  library_dep_index: 1
  library_dep_index: 85
  library_dep_index: 114
  library_dep_index: 115
  library_dep_index: 92
  library_dep_index: 39
  library_dep_index: 116
  library_dep_index: 31
  library_dep_index: 81
  library_dep_index: 9
  library_dep_index: 121
  library_dep_index: 112
  library_dep_index: 87
  library_dep_index: 122
}
library_dependencies {
  library_index: 114
  library_dep_index: 1
}
library_dependencies {
  library_index: 115
  library_dep_index: 1
  library_dep_index: 39
  library_dep_index: 84
  library_dep_index: 8
}
library_dependencies {
  library_index: 116
  library_dep_index: 39
  library_dep_index: 8
  library_dep_index: 117
}
library_dependencies {
  library_index: 117
  library_dep_index: 1
  library_dep_index: 39
  library_dep_index: 118
  library_dep_index: 82
  library_dep_index: 119
  library_dep_index: 120
}
library_dependencies {
  library_index: 118
  library_dep_index: 1
}
library_dependencies {
  library_index: 119
  library_dep_index: 1
}
library_dependencies {
  library_index: 120
  library_dep_index: 1
}
library_dependencies {
  library_index: 121
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 39
  library_dep_index: 84
  library_dep_index: 68
}
library_dependencies {
  library_index: 122
  library_dep_index: 1
  library_dep_index: 81
  library_dep_index: 121
  library_dep_index: 39
  library_dep_index: 8
}
library_dependencies {
  library_index: 123
  library_dep_index: 1
  library_dep_index: 84
  library_dep_index: 39
  library_dep_index: 124
  library_dep_index: 112
}
library_dependencies {
  library_index: 124
  library_dep_index: 3
  library_dep_index: 13
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 39
}
library_dependencies {
  library_index: 125
  library_dep_index: 126
}
library_dependencies {
  library_index: 126
  library_dep_index: 127
  library_dep_index: 3
  library_dep_index: 17
}
library_dependencies {
  library_index: 127
  library_dep_index: 128
}
library_dependencies {
  library_index: 128
  library_dep_index: 17
  library_dep_index: 4
}
library_dependencies {
  library_index: 129
  library_dep_index: 125
  library_dep_index: 130
}
library_dependencies {
  library_index: 131
  library_dep_index: 126
  library_dep_index: 17
}
library_dependencies {
  library_index: 132
  library_dep_index: 31
  library_dep_index: 11
  library_dep_index: 133
  library_dep_index: 135
  library_dep_index: 136
  library_dep_index: 133
  library_dep_index: 134
}
library_dependencies {
  library_index: 133
  library_dep_index: 1
  library_dep_index: 17
  library_dep_index: 134
  library_dep_index: 132
}
library_dependencies {
  library_index: 134
  library_dep_index: 133
  library_dep_index: 132
  library_dep_index: 3
  library_dep_index: 13
  library_dep_index: 133
  library_dep_index: 132
}
library_dependencies {
  library_index: 135
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 136
}
library_dependencies {
  library_index: 136
  library_dep_index: 1
  library_dep_index: 135
  library_dep_index: 3
  library_dep_index: 135
}
library_dependencies {
  library_index: 137
  library_dep_index: 138
  library_dep_index: 142
  library_dep_index: 17
}
library_dependencies {
  library_index: 138
  library_dep_index: 44
  library_dep_index: 139
  library_dep_index: 140
  library_dep_index: 69
  library_dep_index: 17
}
library_dependencies {
  library_index: 139
  library_dep_index: 35
  library_dep_index: 13
  library_dep_index: 17
}
library_dependencies {
  library_index: 140
  library_dep_index: 1
  library_dep_index: 86
  library_dep_index: 8
  library_dep_index: 44
  library_dep_index: 141
  library_dep_index: 49
  library_dep_index: 9
  library_dep_index: 13
  library_dep_index: 3
  library_dep_index: 126
  library_dep_index: 127
}
library_dependencies {
  library_index: 141
  library_dep_index: 1
}
library_dependencies {
  library_index: 142
  library_dep_index: 140
  library_dep_index: 17
}
library_dependencies {
  library_index: 143
  library_dep_index: 44
  library_dep_index: 88
  library_dep_index: 140
  library_dep_index: 17
}
library_dependencies {
  library_index: 144
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 39
  library_dep_index: 141
  library_dep_index: 145
  library_dep_index: 148
  library_dep_index: 150
  library_dep_index: 151
  library_dep_index: 154
  library_dep_index: 149
}
library_dependencies {
  library_index: 145
  library_dep_index: 1
  library_dep_index: 146
  library_dep_index: 31
  library_dep_index: 148
  library_dep_index: 149
  library_dep_index: 150
  library_dep_index: 151
  library_dep_index: 144
  library_dep_index: 152
  library_dep_index: 153
  library_dep_index: 154
  library_dep_index: 155
  library_dep_index: 157
}
library_dependencies {
  library_index: 146
  library_dep_index: 147
  library_dep_index: 41
}
library_dependencies {
  library_index: 148
  library_dep_index: 145
  library_dep_index: 1
}
library_dependencies {
  library_index: 149
  library_dep_index: 145
  library_dep_index: 1
}
library_dependencies {
  library_index: 150
  library_dep_index: 145
  library_dep_index: 149
  library_dep_index: 1
  library_dep_index: 141
}
library_dependencies {
  library_index: 151
  library_dep_index: 145
  library_dep_index: 1
}
library_dependencies {
  library_index: 152
  library_dep_index: 144
  library_dep_index: 1
}
library_dependencies {
  library_index: 153
  library_dep_index: 1
  library_dep_index: 144
}
library_dependencies {
  library_index: 154
  library_dep_index: 1
  library_dep_index: 145
  library_dep_index: 148
  library_dep_index: 151
}
library_dependencies {
  library_index: 155
  library_dep_index: 150
  library_dep_index: 8
  library_dep_index: 156
  library_dep_index: 39
  library_dep_index: 145
}
library_dependencies {
  library_index: 156
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 39
}
library_dependencies {
  library_index: 157
  library_dep_index: 145
  library_dep_index: 156
  library_dep_index: 1
  library_dep_index: 121
}
library_dependencies {
  library_index: 158
  library_dep_index: 1
  library_dep_index: 31
  library_dep_index: 39
}
library_dependencies {
  library_index: 159
  library_dep_index: 39
  library_dep_index: 160
  library_dep_index: 156
  library_dep_index: 81
  library_dep_index: 121
}
library_dependencies {
  library_index: 160
  library_dep_index: 1
  library_dep_index: 39
  library_dep_index: 117
  library_dep_index: 84
  library_dep_index: 83
  library_dep_index: 115
  library_dep_index: 90
  library_dep_index: 123
  library_dep_index: 42
  library_dep_index: 161
  library_dep_index: 162
  library_dep_index: 89
}
library_dependencies {
  library_index: 161
  library_dep_index: 1
  library_dep_index: 39
  library_dep_index: 42
}
library_dependencies {
  library_index: 162
  library_dep_index: 1
  library_dep_index: 39
}
library_dependencies {
  library_index: 163
  library_dep_index: 8
  library_dep_index: 85
  library_dep_index: 121
  library_dep_index: 164
  library_dep_index: 165
  library_dep_index: 159
}
library_dependencies {
  library_index: 164
  library_dep_index: 1
  library_dep_index: 85
  library_dep_index: 39
  library_dep_index: 37
  library_dep_index: 79
  library_dep_index: 121
  library_dep_index: 123
  library_dep_index: 8
}
library_dependencies {
  library_index: 165
  library_dep_index: 164
}
library_dependencies {
  library_index: 166
  library_dep_index: 164
  library_dep_index: 44
  library_dep_index: 79
  library_dep_index: 3
}
library_dependencies {
  library_index: 167
  library_dep_index: 168
  library_dep_index: 168
}
library_dependencies {
  library_index: 168
  library_dep_index: 31
  library_dep_index: 39
  library_dep_index: 20
  library_dep_index: 28
  library_dep_index: 134
  library_dep_index: 136
  library_dep_index: 25
  library_dep_index: 41
  library_dep_index: 3
  library_dep_index: 13
  library_dep_index: 167
}
library_dependencies {
  library_index: 169
  library_dep_index: 50
  library_dep_index: 69
  library_dep_index: 13
  library_dep_index: 170
  library_dep_index: 17
}
library_dependencies {
  library_index: 170
  library_dep_index: 171
}
library_dependencies {
  library_index: 171
  library_dep_index: 3
  library_dep_index: 4
}
library_dependencies {
  library_index: 172
  library_dep_index: 44
  library_dep_index: 35
  library_dep_index: 13
  library_dep_index: 17
}
library_dependencies {
  library_index: 173
  library_dep_index: 3
  library_dep_index: 174
}
library_dependencies {
  library_index: 174
  library_dep_index: 3
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 6
  dependency_index: 7
  dependency_index: 77
  dependency_index: 78
  dependency_index: 17
  dependency_index: 44
  dependency_index: 27
  dependency_index: 50
  dependency_index: 79
  dependency_index: 85
  dependency_index: 92
  dependency_index: 94
  dependency_index: 95
  dependency_index: 35
  dependency_index: 58
  dependency_index: 66
  dependency_index: 102
  dependency_index: 96
  dependency_index: 34
  dependency_index: 103
  dependency_index: 109
  dependency_index: 110
  dependency_index: 48
  dependency_index: 23
  dependency_index: 30
  dependency_index: 125
  dependency_index: 129
  dependency_index: 126
  dependency_index: 131
  dependency_index: 132
  dependency_index: 134
  dependency_index: 127
  dependency_index: 137
  dependency_index: 143
  dependency_index: 144
  dependency_index: 157
  dependency_index: 153
  dependency_index: 152
  dependency_index: 155
  dependency_index: 158
  dependency_index: 159
  dependency_index: 163
  dependency_index: 166
  dependency_index: 167
  dependency_index: 13
  dependency_index: 130
  dependency_index: 169
  dependency_index: 172
  dependency_index: 173
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
