-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:112:9-120:20
	android:grantUriPermissions
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:116:13-47
	android:authorities
		INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:114:13-64
	android:exported
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:115:13-37
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:113:13-62
manifest
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:2:1-124:12
INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:2:1-124:12
INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:2:1-124:12
INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:2:1-124:12
MERGED from [androidx.databinding:databinding-adapters:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c7958a14adcdf0c9ddbfe3540b9068a2\transformed\databinding-adapters-8.2.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a74320c034711100a825235b4c7ae89d\transformed\databinding-ktx-8.2.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\f268b6e8783198178d39a1a8da6bd023\transformed\databinding-runtime-8.2.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\5fc93f76f025c4a6d925878a404e243a\transformed\viewbinding-8.2.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-gif:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f40f949006f3ef1dc760ecd5dab06377\transformed\coil-gif-2.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ff2b0dd41f30e9280eaf00cb36dc8203\transformed\coil-compose-2.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f8e35eec7d6d3e7136f78d160def6de\transformed\coil-compose-base-2.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\395330835a7e4a8a6d4c01a11c86fdc5\transformed\coil-2.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\dad5c85623f94e8a3459991a954da24f\transformed\coil-base-2.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\00be8937d92b8fc0bfad53ea8614a9f9\transformed\preference-ktx-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\c6ae56ba32a0d7e13ae926720a6e34dd\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\594cd96a32fb5e59389044a993ebc9c1\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\4be92dc06e8151e719fd51a3d6a250bd\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\2a338965b2f1373a80a8da32afadb3a2\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\299531eb008ad1843b382bd32c5409a0\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\64c4d85989bee6dc6c06c8c507119445\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\9cf8b3343fe92a51e9f0e238932d7a54\transformed\navigation-compose-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\fca8c0614f19c73cf28366584747aab2\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\b8d3f188d948b1d91488b96ae88ce5c0\transformed\navigation-ui-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.4.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-3\4d037915cf63427aa04d936e78cd9314\transformed\material-1.4.0-beta01\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\bb6582ba3d231a89d2913299acd06265\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.leanback:leanback-preference:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6302310fbc6bef9afdaacc8890ea1a91\transformed\leanback-preference-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\aca3e785219cac0a286b6b4a39c8c3c0\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-preference-v14:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d62b772cff36830c211fdb64950b7035\transformed\legacy-preference-v14-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\cd0d18521f4c5c20c6fe1e241ab341cc\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\790baa963f0557a5338a3a6815b465fa\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.leanback:leanback:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c78be4bd27d45b180b53d555e044be1\transformed\leanback-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e169d27fc0ebdc154978050a3cf3b5c\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\6949afc074ba66e8db76ccb4e9117d7e\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\8e9d613f5acf4de3feab649a9993efd4\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ac1f4354741721caf5bce0b1d23444ae\transformed\material3-1.1.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.accompanist:accompanist-permissions:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\615fdfd718bcedf0ab678ec6e4f21f19\transformed\accompanist-permissions-0.30.1\AndroidManifest.xml:17:1-25:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\79d3e895393b1f3943fe95f54be23ec8\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\934f65d7b790dba796e52d0a7345847f\transformed\accompanist-systemuicontroller-0.30.1\AndroidManifest.xml:17:1-25:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\16921239c9222e0406f8ce23f7bfa767\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\8df5a633acf500a429bce96a987decbf\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\18fb8cf413417cc875cbaadb30aab297\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c3d3e457075d1dd703bc6b43493462c8\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\523dd792a30fd3ab8625974b3b7c3bd8\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e8e27d13d5416bc8d201942c524bf7ce\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c166d39a4c3a761e7b48270be41075c3\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\8e5a57c665294c810097b15a6e7b5aca\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2021d59f4ff667974f321d998683c27d\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7279343ef73249131f545cfe72681acf\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\bc0356f288ba35638b47fe9fcc09e5c4\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c90e487b429ed8e0cc64b1567573b529\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\8a8d3dc9a6034041ede581fd2dd723d1\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\e315115439c9df93968e046f4b84f2b4\transformed\accompanist-drawablepainter-0.30.1\AndroidManifest.xml:17:1-25:12
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e9729538bde244a4064dd4e07fe2100b\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\d0b577a43bc5c0843a0723304a7c5a5f\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\a9a807b1f5569b7e2233d5e7ce764acd\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\39600768786906f631f607f41c8a7de1\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2edfb5405896e45ddfc0c3d9d4e7e28f\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-livedata:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\86d61d2c60d3a72e32befee14b5f3623\transformed\runtime-livedata-1.5.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b6ef924816131ee39cbb5c7994b396d3\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6e399a980bdd55a64e60ac897ab0488\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ad85d1b6ae55e49781190048d44cfb11\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\59266c72f527f1f82e5040bee2724a1c\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\81d7258b8ed4074ff501accfe432bcb6\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f771aa8b0638e4c6ebcda7f02bda7ba\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d87920914c99ccca818b91da6e15aec3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3ba584b714b84ee46925d76df6cc1b4\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d96496f807db043f771f448679459bf\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\c51740ddd1d28714d3e11ac67e37b798\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f60570bd5900787d4fa4ed33ff7a5a1\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\c97775a752b193f660203af3d58b02ad\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\995d1cadead2c5b2445ce5fadf00951a\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a50ec885653e6cd09785f808ddd3075\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\99979ab94a57ce3c940ec82741460ab9\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\68ca9ab12b0db528ad78673337d34387\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\096a1a95c5c7acc3f9b9418951a07cf5\transformed\media3-exoplayer-hls-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\6bd19a4b325ea3e60079d3f4e2e85df3\transformed\media3-exoplayer-dash-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3c10f13ce021e8e2981c4376f2d01b28\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-session:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\f143c6e419d61b5e7da1da45bd6aa2a1\transformed\media3-session-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\5f89dedf1b6a900e1708df14f2f1897e\transformed\media3-extractor-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\9e65bcf9f8e807cb9d91accf79b6b480\transformed\media3-container-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\729bda222688bb546ff8e9570ba53ed6\transformed\media3-datasource-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7fc5d311dbd9f7aed0a00fa643afdb62\transformed\media3-decoder-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b1fc2e911ac11b79f57b0f6bbe1b7a17\transformed\media3-database-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3afef8feff99a52c113ec1c1c658da88\transformed\media3-common-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-ui:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d967763d34b2e16001ff17d7de719a33\transformed\media3-ui-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c5debe83cd199d05fc87f0ba167cc42\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\85b17514f110d3f8e4eae40a09b5c478\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\fac9583018dcb243f4497b58dd2463b3\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\transforms-3\cc636dc20bc2d7ffedef855b40be2fe5\transformed\webkit-1.12.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\29f22966b2ce3d810e00200149526ea9\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c7d7a58e2e894c347a7c0e10dc25f385\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5cd43762056880b85788d81f8cd495\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e8859937e8906b6cd334b421ba4ae6d\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e445fbcf31b0d28902d8e65706794d08\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a7077519896beb26b02564ed007755f5\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b7dde4bb104e7e5b0e547cdb8fd726d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\55486016060b5811f241cfa7aaa060f9\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\53e28e13167ed6f0bb752ee1bfd53650\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b19994a56beb5533d8263e4774d410a2\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1e89af102512db797e7d286726a3e22\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\76de3e8e41f0a04f335f2feab771f677\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a5f556f21c3f90189a360269bb2a9d7c\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d41822a5f59f468a06cdc0a94218e4b\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\55cfaf28de752464cd8dc133a3dfe752\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\aac6f0913e85722ce77f1294ce5fc4d0\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\382a60b5ad1fd59346ee5bf563e8ebc9\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\29ad22528f4df7a20b7ea327a6a067ab\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\307ff8eeba61facfa7814a81ec37475a\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\36a7cfcced38c566a3ea219045132271\transformed\multidex-2.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0244b39d585037021cb97a6bc2947957\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b21369e09ad287f956b710a5f6f31c15\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\354946b7b3c2b4197595909f42d70692\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a11449a06a7cb8dc3dc0fa591842a846\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\cdc4bb13384a3ca7ded94992f8094b0c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\6e11c4b2e0c0664c06b394c2c80a3efa\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\11ab85b57ff575b45f39e42627723cf2\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c4ed98dd76855dd8bb4203a2067bff66\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d2a1a65ef83f686fdd6d47545f44ee7\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed0dbcb45c158e171ca87840e450ff05\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b54810bf2b33e4bbbaf05c0378fc25e\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d79715e0c24a65a014ee2b40ddff209\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d82450e5084488591c85171bc38ae86\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\89120d8548ca320fd69ebe514f81d75b\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.aakira:napier-android:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\4bb908507dfa5f7986a148d0aa39d1c6\transformed\napier-release\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3c10f13ce021e8e2981c4376f2d01b28\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3c10f13ce021e8e2981c4376f2d01b28\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3afef8feff99a52c113ec1c1c658da88\transformed\media3-common-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3afef8feff99a52c113ec1c1c658da88\transformed\media3-common-1.2.1\AndroidManifest.xml:22:5-79
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:8:5-76
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:8:22-73
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:9:5-10:38
	android:maxSdkVersion
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:10:9-35
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:9:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:11:5-12:38
	android:maxSdkVersion
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:12:9-35
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:11:22-77
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:13:5-75
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:13:22-72
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:14:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:14:22-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:15:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:15:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:16:5-92
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:16:22-89
uses-feature#android.software.leanback
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:19:5-21:36
	android:required
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:21:9-33
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:20:9-49
uses-feature#android.hardware.touchscreen
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:22:5-24:36
	android:required
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:24:9-33
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:23:9-52
uses-feature#android.hardware.wifi
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:27:5-29:36
	android:required
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:29:9-33
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:28:9-45
uses-feature#android.hardware.ethernet
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:30:5-32:36
	android:required
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:32:9-33
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:31:9-49
application
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:34:5-122:19
INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:34:5-122:19
MERGED from [com.google.android.material:material:1.4.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-3\4d037915cf63427aa04d936e78cd9314\transformed\material-1.4.0-beta01\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.4.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-3\4d037915cf63427aa04d936e78cd9314\transformed\material-1.4.0-beta01\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\bb6582ba3d231a89d2913299acd06265\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\bb6582ba3d231a89d2913299acd06265\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\995d1cadead2c5b2445ce5fadf00951a\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\995d1cadead2c5b2445ce5fadf00951a\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a50ec885653e6cd09785f808ddd3075\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a50ec885653e6cd09785f808ddd3075\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\29ad22528f4df7a20b7ea327a6a067ab\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\29ad22528f4df7a20b7ea327a6a067ab\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\cdc4bb13384a3ca7ded94992f8094b0c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\cdc4bb13384a3ca7ded94992f8094b0c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c4ed98dd76855dd8bb4203a2067bff66\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c4ed98dd76855dd8bb4203a2067bff66\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml
	android:roundIcon
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:41:9-54
	android:largeHeap
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:46:9-33
	android:icon
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:39:9-43
	android:banner
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:47:9-45
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:42:9-35
	android:label
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:40:9-41
	android:hardwareAccelerated
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:45:9-43
	android:fullBackupContent
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:38:9-54
	tools:targetApi
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:48:9-29
	android:allowBackup
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:36:9-35
	android:theme
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:43:9-45
	android:dataExtractionRules
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:37:9-65
	android:usesCleartextTraffic
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:44:9-44
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:35:9-43
activity#com.deshi.cinepix.MainActivity
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:49:9-75:20
	android:screenOrientation
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:55:13-52
	android:label
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:52:13-45
	android:launchMode
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:54:13-43
	android:exported
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:51:13-36
	android:supportsPictureInPicture
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:57:13-52
	android:configChanges
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:56:13-106
	android:theme
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:53:13-49
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:50:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:58:13-61:29
action#android.intent.action.MAIN
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:59:17-69
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:59:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:60:17-77
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:60:27-74
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:63:13-66:29
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:65:17-86
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:65:27-83
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:cinepix.top+data:host:cinepix.top+data:scheme:http+data:scheme:https
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:68:13-74:29
	android:autoVerify
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:68:28-53
action#android.intent.action.VIEW
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:69:17-69
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:69:25-66
category#android.intent.category.DEFAULT
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:70:17-76
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:70:27-73
category#android.intent.category.BROWSABLE
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:71:17-78
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:71:27-75
data
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:72:17-74
	android:host
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:72:45-71
	android:scheme
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:72:23-44
activity#com.deshi.cinepix.ui.player.SimpleVideoPlayerActivity
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:78:9-84:46
	android:screenOrientation
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:82:13-50
	android:launchMode
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:84:13-43
	android:exported
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:80:13-37
	android:configChanges
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:83:13-106
	android:theme
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:81:13-60
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:79:13-64
activity#com.deshi.cinepix.ui.webview.SimpleWebViewActivity
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:87:9-91:109
	android:exported
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:89:13-37
	android:configChanges
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:91:13-106
	android:theme
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:90:13-49
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:88:13-61
activity#com.deshi.cinepix.ui.download.DownloadActivity
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:94:9-97:52
	android:exported
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:96:13-37
	android:theme
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:97:13-49
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:95:13-57
activity#com.deshi.cinepix.ui.tv.TvMainActivity
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:100:9-109:20
	android:screenOrientation
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:104:13-50
	android:exported
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:102:13-36
	android:theme
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:103:13-58
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:101:13-49
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:117:13-119:54
	android:resource
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:119:17-51
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:118:17-67
uses-sdk
INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml
INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:databinding-adapters:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c7958a14adcdf0c9ddbfe3540b9068a2\transformed\databinding-adapters-8.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c7958a14adcdf0c9ddbfe3540b9068a2\transformed\databinding-adapters-8.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a74320c034711100a825235b4c7ae89d\transformed\databinding-ktx-8.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\a74320c034711100a825235b4c7ae89d\transformed\databinding-ktx-8.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\f268b6e8783198178d39a1a8da6bd023\transformed\databinding-runtime-8.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\f268b6e8783198178d39a1a8da6bd023\transformed\databinding-runtime-8.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\5fc93f76f025c4a6d925878a404e243a\transformed\viewbinding-8.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\5fc93f76f025c4a6d925878a404e243a\transformed\viewbinding-8.2.1\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-gif:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f40f949006f3ef1dc760ecd5dab06377\transformed\coil-gif-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-gif:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f40f949006f3ef1dc760ecd5dab06377\transformed\coil-gif-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ff2b0dd41f30e9280eaf00cb36dc8203\transformed\coil-compose-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ff2b0dd41f30e9280eaf00cb36dc8203\transformed\coil-compose-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f8e35eec7d6d3e7136f78d160def6de\transformed\coil-compose-base-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f8e35eec7d6d3e7136f78d160def6de\transformed\coil-compose-base-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\395330835a7e4a8a6d4c01a11c86fdc5\transformed\coil-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\395330835a7e4a8a6d4c01a11c86fdc5\transformed\coil-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\dad5c85623f94e8a3459991a954da24f\transformed\coil-base-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\dad5c85623f94e8a3459991a954da24f\transformed\coil-base-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\00be8937d92b8fc0bfad53ea8614a9f9\transformed\preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\00be8937d92b8fc0bfad53ea8614a9f9\transformed\preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\c6ae56ba32a0d7e13ae926720a6e34dd\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\c6ae56ba32a0d7e13ae926720a6e34dd\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\594cd96a32fb5e59389044a993ebc9c1\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\594cd96a32fb5e59389044a993ebc9c1\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\4be92dc06e8151e719fd51a3d6a250bd\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\4be92dc06e8151e719fd51a3d6a250bd\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\2a338965b2f1373a80a8da32afadb3a2\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\2a338965b2f1373a80a8da32afadb3a2\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\299531eb008ad1843b382bd32c5409a0\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\299531eb008ad1843b382bd32c5409a0\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\64c4d85989bee6dc6c06c8c507119445\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\64c4d85989bee6dc6c06c8c507119445\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\9cf8b3343fe92a51e9f0e238932d7a54\transformed\navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\9cf8b3343fe92a51e9f0e238932d7a54\transformed\navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\fca8c0614f19c73cf28366584747aab2\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\fca8c0614f19c73cf28366584747aab2\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\b8d3f188d948b1d91488b96ae88ce5c0\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\b8d3f188d948b1d91488b96ae88ce5c0\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.4.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-3\4d037915cf63427aa04d936e78cd9314\transformed\material-1.4.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.4.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-3\4d037915cf63427aa04d936e78cd9314\transformed\material-1.4.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\bb6582ba3d231a89d2913299acd06265\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\bb6582ba3d231a89d2913299acd06265\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.leanback:leanback-preference:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6302310fbc6bef9afdaacc8890ea1a91\transformed\leanback-preference-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.leanback:leanback-preference:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6302310fbc6bef9afdaacc8890ea1a91\transformed\leanback-preference-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\aca3e785219cac0a286b6b4a39c8c3c0\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\aca3e785219cac0a286b6b4a39c8c3c0\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-preference-v14:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d62b772cff36830c211fdb64950b7035\transformed\legacy-preference-v14-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-preference-v14:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d62b772cff36830c211fdb64950b7035\transformed\legacy-preference-v14-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\cd0d18521f4c5c20c6fe1e241ab341cc\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\cd0d18521f4c5c20c6fe1e241ab341cc\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\790baa963f0557a5338a3a6815b465fa\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\790baa963f0557a5338a3a6815b465fa\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.leanback:leanback:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c78be4bd27d45b180b53d555e044be1\transformed\leanback-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.leanback:leanback:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c78be4bd27d45b180b53d555e044be1\transformed\leanback-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e169d27fc0ebdc154978050a3cf3b5c\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e169d27fc0ebdc154978050a3cf3b5c\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\6949afc074ba66e8db76ccb4e9117d7e\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\6949afc074ba66e8db76ccb4e9117d7e\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\8e9d613f5acf4de3feab649a9993efd4\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\8e9d613f5acf4de3feab649a9993efd4\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ac1f4354741721caf5bce0b1d23444ae\transformed\material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\ac1f4354741721caf5bce0b1d23444ae\transformed\material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\615fdfd718bcedf0ab678ec6e4f21f19\transformed\accompanist-permissions-0.30.1\AndroidManifest.xml:21:5-23:42
MERGED from [com.google.accompanist:accompanist-permissions:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\615fdfd718bcedf0ab678ec6e4f21f19\transformed\accompanist-permissions-0.30.1\AndroidManifest.xml:21:5-23:42
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\79d3e895393b1f3943fe95f54be23ec8\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\79d3e895393b1f3943fe95f54be23ec8\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\934f65d7b790dba796e52d0a7345847f\transformed\accompanist-systemuicontroller-0.30.1\AndroidManifest.xml:21:5-23:42
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\934f65d7b790dba796e52d0a7345847f\transformed\accompanist-systemuicontroller-0.30.1\AndroidManifest.xml:21:5-23:42
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\16921239c9222e0406f8ce23f7bfa767\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\16921239c9222e0406f8ce23f7bfa767\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\8df5a633acf500a429bce96a987decbf\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\8df5a633acf500a429bce96a987decbf\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\18fb8cf413417cc875cbaadb30aab297\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\18fb8cf413417cc875cbaadb30aab297\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c3d3e457075d1dd703bc6b43493462c8\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c3d3e457075d1dd703bc6b43493462c8\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\523dd792a30fd3ab8625974b3b7c3bd8\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\523dd792a30fd3ab8625974b3b7c3bd8\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e8e27d13d5416bc8d201942c524bf7ce\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e8e27d13d5416bc8d201942c524bf7ce\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c166d39a4c3a761e7b48270be41075c3\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c166d39a4c3a761e7b48270be41075c3\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\8e5a57c665294c810097b15a6e7b5aca\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\8e5a57c665294c810097b15a6e7b5aca\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2021d59f4ff667974f321d998683c27d\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2021d59f4ff667974f321d998683c27d\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7279343ef73249131f545cfe72681acf\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7279343ef73249131f545cfe72681acf\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\bc0356f288ba35638b47fe9fcc09e5c4\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\bc0356f288ba35638b47fe9fcc09e5c4\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c90e487b429ed8e0cc64b1567573b529\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c90e487b429ed8e0cc64b1567573b529\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\8a8d3dc9a6034041ede581fd2dd723d1\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\8a8d3dc9a6034041ede581fd2dd723d1\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\e315115439c9df93968e046f4b84f2b4\transformed\accompanist-drawablepainter-0.30.1\AndroidManifest.xml:21:5-23:42
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\e315115439c9df93968e046f4b84f2b4\transformed\accompanist-drawablepainter-0.30.1\AndroidManifest.xml:21:5-23:42
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e9729538bde244a4064dd4e07fe2100b\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e9729538bde244a4064dd4e07fe2100b\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\d0b577a43bc5c0843a0723304a7c5a5f\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\d0b577a43bc5c0843a0723304a7c5a5f\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\a9a807b1f5569b7e2233d5e7ce764acd\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\a9a807b1f5569b7e2233d5e7ce764acd\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\39600768786906f631f607f41c8a7de1\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\39600768786906f631f607f41c8a7de1\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2edfb5405896e45ddfc0c3d9d4e7e28f\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2edfb5405896e45ddfc0c3d9d4e7e28f\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\86d61d2c60d3a72e32befee14b5f3623\transformed\runtime-livedata-1.5.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\86d61d2c60d3a72e32befee14b5f3623\transformed\runtime-livedata-1.5.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b6ef924816131ee39cbb5c7994b396d3\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b6ef924816131ee39cbb5c7994b396d3\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6e399a980bdd55a64e60ac897ab0488\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b6e399a980bdd55a64e60ac897ab0488\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ad85d1b6ae55e49781190048d44cfb11\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\ad85d1b6ae55e49781190048d44cfb11\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\59266c72f527f1f82e5040bee2724a1c\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\59266c72f527f1f82e5040bee2724a1c\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\81d7258b8ed4074ff501accfe432bcb6\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\81d7258b8ed4074ff501accfe432bcb6\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f771aa8b0638e4c6ebcda7f02bda7ba\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f771aa8b0638e4c6ebcda7f02bda7ba\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d87920914c99ccca818b91da6e15aec3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d87920914c99ccca818b91da6e15aec3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3ba584b714b84ee46925d76df6cc1b4\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3ba584b714b84ee46925d76df6cc1b4\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d96496f807db043f771f448679459bf\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d96496f807db043f771f448679459bf\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\c51740ddd1d28714d3e11ac67e37b798\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\c51740ddd1d28714d3e11ac67e37b798\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f60570bd5900787d4fa4ed33ff7a5a1\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5f60570bd5900787d4fa4ed33ff7a5a1\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\c97775a752b193f660203af3d58b02ad\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\c97775a752b193f660203af3d58b02ad\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\995d1cadead2c5b2445ce5fadf00951a\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\995d1cadead2c5b2445ce5fadf00951a\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a50ec885653e6cd09785f808ddd3075\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a50ec885653e6cd09785f808ddd3075\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\99979ab94a57ce3c940ec82741460ab9\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\99979ab94a57ce3c940ec82741460ab9\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\68ca9ab12b0db528ad78673337d34387\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\68ca9ab12b0db528ad78673337d34387\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\096a1a95c5c7acc3f9b9418951a07cf5\transformed\media3-exoplayer-hls-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\096a1a95c5c7acc3f9b9418951a07cf5\transformed\media3-exoplayer-hls-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\6bd19a4b325ea3e60079d3f4e2e85df3\transformed\media3-exoplayer-dash-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\6bd19a4b325ea3e60079d3f4e2e85df3\transformed\media3-exoplayer-dash-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3c10f13ce021e8e2981c4376f2d01b28\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3c10f13ce021e8e2981c4376f2d01b28\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\f143c6e419d61b5e7da1da45bd6aa2a1\transformed\media3-session-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\f143c6e419d61b5e7da1da45bd6aa2a1\transformed\media3-session-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\5f89dedf1b6a900e1708df14f2f1897e\transformed\media3-extractor-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\5f89dedf1b6a900e1708df14f2f1897e\transformed\media3-extractor-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\9e65bcf9f8e807cb9d91accf79b6b480\transformed\media3-container-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\9e65bcf9f8e807cb9d91accf79b6b480\transformed\media3-container-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\729bda222688bb546ff8e9570ba53ed6\transformed\media3-datasource-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\729bda222688bb546ff8e9570ba53ed6\transformed\media3-datasource-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7fc5d311dbd9f7aed0a00fa643afdb62\transformed\media3-decoder-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7fc5d311dbd9f7aed0a00fa643afdb62\transformed\media3-decoder-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b1fc2e911ac11b79f57b0f6bbe1b7a17\transformed\media3-database-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b1fc2e911ac11b79f57b0f6bbe1b7a17\transformed\media3-database-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3afef8feff99a52c113ec1c1c658da88\transformed\media3-common-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\3afef8feff99a52c113ec1c1c658da88\transformed\media3-common-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d967763d34b2e16001ff17d7de719a33\transformed\media3-ui-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d967763d34b2e16001ff17d7de719a33\transformed\media3-ui-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c5debe83cd199d05fc87f0ba167cc42\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7c5debe83cd199d05fc87f0ba167cc42\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\85b17514f110d3f8e4eae40a09b5c478\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\85b17514f110d3f8e4eae40a09b5c478\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\fac9583018dcb243f4497b58dd2463b3\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\fac9583018dcb243f4497b58dd2463b3\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\transforms-3\cc636dc20bc2d7ffedef855b40be2fe5\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\transforms-3\cc636dc20bc2d7ffedef855b40be2fe5\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\29f22966b2ce3d810e00200149526ea9\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\29f22966b2ce3d810e00200149526ea9\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c7d7a58e2e894c347a7c0e10dc25f385\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c7d7a58e2e894c347a7c0e10dc25f385\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5cd43762056880b85788d81f8cd495\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b5cd43762056880b85788d81f8cd495\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e8859937e8906b6cd334b421ba4ae6d\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\0e8859937e8906b6cd334b421ba4ae6d\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e445fbcf31b0d28902d8e65706794d08\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e445fbcf31b0d28902d8e65706794d08\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a7077519896beb26b02564ed007755f5\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\a7077519896beb26b02564ed007755f5\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b7dde4bb104e7e5b0e547cdb8fd726d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b7dde4bb104e7e5b0e547cdb8fd726d\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\55486016060b5811f241cfa7aaa060f9\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\55486016060b5811f241cfa7aaa060f9\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\53e28e13167ed6f0bb752ee1bfd53650\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\53e28e13167ed6f0bb752ee1bfd53650\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b19994a56beb5533d8263e4774d410a2\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b19994a56beb5533d8263e4774d410a2\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1e89af102512db797e7d286726a3e22\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1e89af102512db797e7d286726a3e22\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\76de3e8e41f0a04f335f2feab771f677\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\76de3e8e41f0a04f335f2feab771f677\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a5f556f21c3f90189a360269bb2a9d7c\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a5f556f21c3f90189a360269bb2a9d7c\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d41822a5f59f468a06cdc0a94218e4b\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3d41822a5f59f468a06cdc0a94218e4b\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\55cfaf28de752464cd8dc133a3dfe752\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\55cfaf28de752464cd8dc133a3dfe752\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\aac6f0913e85722ce77f1294ce5fc4d0\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\aac6f0913e85722ce77f1294ce5fc4d0\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\382a60b5ad1fd59346ee5bf563e8ebc9\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\382a60b5ad1fd59346ee5bf563e8ebc9\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\29ad22528f4df7a20b7ea327a6a067ab\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\29ad22528f4df7a20b7ea327a6a067ab\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\307ff8eeba61facfa7814a81ec37475a\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\307ff8eeba61facfa7814a81ec37475a\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\36a7cfcced38c566a3ea219045132271\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\36a7cfcced38c566a3ea219045132271\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0244b39d585037021cb97a6bc2947957\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0244b39d585037021cb97a6bc2947957\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b21369e09ad287f956b710a5f6f31c15\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\b21369e09ad287f956b710a5f6f31c15\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\354946b7b3c2b4197595909f42d70692\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\354946b7b3c2b4197595909f42d70692\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a11449a06a7cb8dc3dc0fa591842a846\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a11449a06a7cb8dc3dc0fa591842a846\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\cdc4bb13384a3ca7ded94992f8094b0c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\cdc4bb13384a3ca7ded94992f8094b0c\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\6e11c4b2e0c0664c06b394c2c80a3efa\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\6e11c4b2e0c0664c06b394c2c80a3efa\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\11ab85b57ff575b45f39e42627723cf2\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\11ab85b57ff575b45f39e42627723cf2\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c4ed98dd76855dd8bb4203a2067bff66\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c4ed98dd76855dd8bb4203a2067bff66\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d2a1a65ef83f686fdd6d47545f44ee7\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8d2a1a65ef83f686fdd6d47545f44ee7\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed0dbcb45c158e171ca87840e450ff05\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed0dbcb45c158e171ca87840e450ff05\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b54810bf2b33e4bbbaf05c0378fc25e\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b54810bf2b33e4bbbaf05c0378fc25e\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d79715e0c24a65a014ee2b40ddff209\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d79715e0c24a65a014ee2b40ddff209\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d82450e5084488591c85171bc38ae86\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2d82450e5084488591c85171bc38ae86\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\89120d8548ca320fd69ebe514f81d75b\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\89120d8548ca320fd69ebe514f81d75b\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [io.github.aakira:napier-android:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\4bb908507dfa5f7986a148d0aa39d1c6\transformed\napier-release\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\4bb908507dfa5f7986a148d0aa39d1c6\transformed\napier-release\AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml
	tools:ignore
		ADDED from [com.google.accompanist:accompanist-permissions:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\615fdfd718bcedf0ab678ec6e4f21f19\transformed\accompanist-permissions-0.30.1\AndroidManifest.xml:23:9-39
	android:minSdkVersion
		INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\995d1cadead2c5b2445ce5fadf00951a\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\995d1cadead2c5b2445ce5fadf00951a\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a50ec885653e6cd09785f808ddd3075\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a50ec885653e6cd09785f808ddd3075\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c4ed98dd76855dd8bb4203a2067bff66\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\c4ed98dd76855dd8bb4203a2067bff66\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\995d1cadead2c5b2445ce5fadf00951a\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\995d1cadead2c5b2445ce5fadf00951a\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\995d1cadead2c5b2445ce5fadf00951a\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a50ec885653e6cd09785f808ddd3075\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a50ec885653e6cd09785f808ddd3075\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a50ec885653e6cd09785f808ddd3075\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.deshi.cinepix.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.deshi.cinepix.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\29ad22528f4df7a20b7ea327a6a067ab\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\29ad22528f4df7a20b7ea327a6a067ab\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\29ad22528f4df7a20b7ea327a6a067ab\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\29ad22528f4df7a20b7ea327a6a067ab\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\29ad22528f4df7a20b7ea327a6a067ab\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
