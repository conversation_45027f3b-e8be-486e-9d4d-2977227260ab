1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.deshi.cinepix"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:8:5-76
14-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission
15-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:9:5-10:38
16        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
16-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:9:22-78
17        android:maxSdkVersion="28" />
17-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:10:9-35
18    <uses-permission
18-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:11:5-12:38
19        android:name="android.permission.READ_EXTERNAL_STORAGE"
19-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:11:22-77
20        android:maxSdkVersion="32" />
20-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:12:9-35
21    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
21-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:13:5-75
21-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:13:22-72
22    <uses-permission android:name="android.permission.WAKE_LOCK" />
22-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:14:5-68
22-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:14:22-65
23    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
23-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:15:5-77
23-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:15:22-74
24    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
24-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:16:5-92
24-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:16:22-89
25
26    <!-- TV Features -->
27    <uses-feature
27-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:19:5-21:36
28        android:name="android.software.leanback"
28-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:20:9-49
29        android:required="false" />
29-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:21:9-33
30    <uses-feature
30-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:22:5-24:36
31        android:name="android.hardware.touchscreen"
31-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:23:9-52
32        android:required="false" />
32-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:24:9-33
33
34    <!-- Hardware features -->
35    <uses-feature
35-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:27:5-29:36
36        android:name="android.hardware.wifi"
36-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:28:9-45
37        android:required="false" />
37-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:29:9-33
38    <uses-feature
38-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:30:5-32:36
39        android:name="android.hardware.ethernet"
39-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:31:9-49
40        android:required="false" />
40-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:32:9-33
41
42    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
42-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
42-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
43
44    <permission
44-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
45        android:name="com.deshi.cinepix.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
45-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
46        android:protectionLevel="signature" />
46-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
47
48    <uses-permission android:name="com.deshi.cinepix.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
48-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
48-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
49
50    <application
50-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:34:5-122:19
51        android:name="com.deshi.cinepix.CinepixApplication"
51-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:35:9-43
52        android:allowBackup="true"
52-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:36:9-35
53        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
53-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
54        android:banner="@drawable/tv_banner"
54-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:47:9-45
55        android:dataExtractionRules="@xml/data_extraction_rules"
55-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:37:9-65
56        android:extractNativeLibs="false"
57        android:fullBackupContent="@xml/backup_rules"
57-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:38:9-54
58        android:hardwareAccelerated="true"
58-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:45:9-43
59        android:icon="@mipmap/ic_launcher"
59-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:39:9-43
60        android:label="@string/app_name"
60-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:40:9-41
61        android:largeHeap="true"
61-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:46:9-33
62        android:roundIcon="@mipmap/ic_launcher_round"
62-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:41:9-54
63        android:supportsRtl="true"
63-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:42:9-35
64        android:theme="@style/Theme.Cinepix"
64-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:43:9-45
65        android:usesCleartextTraffic="true" >
65-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:44:9-44
66        <activity
66-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:49:9-75:20
67            android:name="com.deshi.cinepix.MainActivity"
67-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:50:13-41
68            android:configChanges="orientation|screenSize|keyboardHidden|smallestScreenSize|screenLayout"
68-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:56:13-106
69            android:exported="true"
69-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:51:13-36
70            android:label="@string/app_name"
70-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:52:13-45
71            android:launchMode="singleTop"
71-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:54:13-43
72            android:screenOrientation="unspecified"
72-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:55:13-52
73            android:supportsPictureInPicture="true"
73-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:57:13-52
74            android:theme="@style/Theme.Cinepix" >
74-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:53:13-49
75            <intent-filter>
75-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:58:13-61:29
76                <action android:name="android.intent.action.MAIN" />
76-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:59:17-69
76-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:59:25-66
77
78                <category android:name="android.intent.category.LAUNCHER" />
78-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:60:17-77
78-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:60:27-74
79            </intent-filter>
80            <!-- TV Intent Filter -->
81            <intent-filter>
81-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:63:13-66:29
82                <action android:name="android.intent.action.MAIN" />
82-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:59:17-69
82-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:59:25-66
83
84                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
84-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:65:17-86
84-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:65:27-83
85            </intent-filter>
86            <!-- Handle video URLs -->
87            <intent-filter android:autoVerify="true" >
87-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:68:13-74:29
87-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:68:28-53
88                <action android:name="android.intent.action.VIEW" />
88-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:69:17-69
88-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:69:25-66
89
90                <category android:name="android.intent.category.DEFAULT" />
90-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:70:17-76
90-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:70:27-73
91                <category android:name="android.intent.category.BROWSABLE" />
91-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:71:17-78
91-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:71:27-75
92
93                <data
93-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:72:17-74
94                    android:host="cinepix.top"
94-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:72:45-71
95                    android:scheme="http" />
95-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:72:23-44
96                <data
96-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:72:17-74
97                    android:host="cinepix.top"
97-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:72:45-71
98                    android:scheme="https" />
98-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:72:23-44
99            </intent-filter>
100        </activity>
101
102        <!-- Video Player Activity -->
103        <activity
103-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:78:9-84:46
104            android:name="com.deshi.cinepix.ui.player.SimpleVideoPlayerActivity"
104-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:79:13-64
105            android:configChanges="orientation|screenSize|keyboardHidden|smallestScreenSize|screenLayout"
105-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:83:13-106
106            android:exported="false"
106-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:80:13-37
107            android:launchMode="singleTop"
107-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:84:13-43
108            android:screenOrientation="landscape"
108-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:82:13-50
109            android:theme="@style/Theme.Cinepix.Fullscreen" />
109-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:81:13-60
110
111        <!-- WebView Activity -->
112        <activity
112-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:87:9-91:109
113            android:name="com.deshi.cinepix.ui.webview.SimpleWebViewActivity"
113-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:88:13-61
114            android:configChanges="orientation|screenSize|keyboardHidden|smallestScreenSize|screenLayout"
114-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:91:13-106
115            android:exported="false"
115-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:89:13-37
116            android:theme="@style/Theme.Cinepix" />
116-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:90:13-49
117
118        <!-- Download Activity -->
119        <activity
119-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:94:9-97:52
120            android:name="com.deshi.cinepix.ui.download.DownloadActivity"
120-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:95:13-57
121            android:exported="false"
121-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:96:13-37
122            android:theme="@style/Theme.Cinepix" />
122-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:97:13-49
123
124        <!-- TV Main Activity -->
125        <activity
125-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:100:9-109:20
126            android:name="com.deshi.cinepix.ui.tv.TvMainActivity"
126-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:101:13-49
127            android:exported="true"
127-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:102:13-36
128            android:screenOrientation="landscape"
128-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:104:13-50
129            android:theme="@style/Theme.Cinepix.Leanback" >
129-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:103:13-58
130            <intent-filter>
130-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:63:13-66:29
131                <action android:name="android.intent.action.MAIN" />
131-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:59:17-69
131-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:59:25-66
132
133                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
133-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:65:17-86
133-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:65:27-83
134            </intent-filter>
135        </activity>
136
137        <!-- File Provider -->
138        <provider
139            android:name="androidx.core.content.FileProvider"
139-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:113:13-62
140            android:authorities="com.deshi.cinepix.fileprovider"
140-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:114:13-64
141            android:exported="false"
141-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:115:13-37
142            android:grantUriPermissions="true" >
142-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:116:13-47
143            <meta-data
143-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:117:13-119:54
144                android:name="android.support.FILE_PROVIDER_PATHS"
144-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:118:17-67
145                android:resource="@xml/file_paths" />
145-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:119:17-51
146        </provider>
147        <provider
147-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
148            android:name="androidx.startup.InitializationProvider"
148-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
149            android:authorities="com.deshi.cinepix.androidx-startup"
149-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
150            android:exported="false" >
150-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
151            <meta-data
151-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
152                android:name="androidx.work.WorkManagerInitializer"
152-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
153                android:value="androidx.startup" />
153-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
154            <meta-data
154-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\995d1cadead2c5b2445ce5fadf00951a\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
155                android:name="androidx.emoji2.text.EmojiCompatInitializer"
155-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\995d1cadead2c5b2445ce5fadf00951a\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
156                android:value="androidx.startup" />
156-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\995d1cadead2c5b2445ce5fadf00951a\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
157            <meta-data
157-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a50ec885653e6cd09785f808ddd3075\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
158                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
158-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a50ec885653e6cd09785f808ddd3075\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
159                android:value="androidx.startup" />
159-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a50ec885653e6cd09785f808ddd3075\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
160            <meta-data
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
161                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
161-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
162                android:value="androidx.startup" />
162-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
163        </provider>
164
165        <service
165-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
166            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
166-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
167            android:directBootAware="false"
167-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
168            android:enabled="@bool/enable_system_alarm_service_default"
168-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
169            android:exported="false" />
169-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
170        <service
170-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
171            android:name="androidx.work.impl.background.systemjob.SystemJobService"
171-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
172            android:directBootAware="false"
172-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
173            android:enabled="@bool/enable_system_job_service_default"
173-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
174            android:exported="true"
174-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
175            android:permission="android.permission.BIND_JOB_SERVICE" />
175-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
176        <service
176-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
177            android:name="androidx.work.impl.foreground.SystemForegroundService"
177-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
178            android:directBootAware="false"
178-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
179            android:enabled="@bool/enable_system_foreground_service_default"
179-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
180            android:exported="false" />
180-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
181
182        <receiver
182-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
183            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
183-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
184            android:directBootAware="false"
184-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
185            android:enabled="true"
185-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
186            android:exported="false" />
186-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
187        <receiver
187-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
188            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
188-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
189            android:directBootAware="false"
189-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
190            android:enabled="false"
190-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
191            android:exported="false" >
191-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
192            <intent-filter>
192-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
193                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
193-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
193-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
194                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
194-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
194-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
195            </intent-filter>
196        </receiver>
197        <receiver
197-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
198            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
198-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
199            android:directBootAware="false"
199-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
200            android:enabled="false"
200-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
201            android:exported="false" >
201-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
202            <intent-filter>
202-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
203                <action android:name="android.intent.action.BATTERY_OKAY" />
203-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
203-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
204                <action android:name="android.intent.action.BATTERY_LOW" />
204-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
204-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
205            </intent-filter>
206        </receiver>
207        <receiver
207-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
208            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
208-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
209            android:directBootAware="false"
209-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
210            android:enabled="false"
210-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
211            android:exported="false" >
211-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
212            <intent-filter>
212-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
213                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
213-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
213-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
214                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
214-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
214-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
215            </intent-filter>
216        </receiver>
217        <receiver
217-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
218            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
218-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
219            android:directBootAware="false"
219-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
220            android:enabled="false"
220-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
221            android:exported="false" >
221-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
222            <intent-filter>
222-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
223                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
223-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
223-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
224            </intent-filter>
225        </receiver>
226        <receiver
226-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
227            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
227-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
228            android:directBootAware="false"
228-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
229            android:enabled="false"
229-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
230            android:exported="false" >
230-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
231            <intent-filter>
231-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
232                <action android:name="android.intent.action.BOOT_COMPLETED" />
232-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
232-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
233                <action android:name="android.intent.action.TIME_SET" />
233-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
233-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
234                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
234-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
234-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
235            </intent-filter>
236        </receiver>
237        <receiver
237-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
238            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
238-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
239            android:directBootAware="false"
239-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
240            android:enabled="@bool/enable_system_alarm_service_default"
240-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
241            android:exported="false" >
241-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
242            <intent-filter>
242-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
243                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
243-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
243-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
244            </intent-filter>
245        </receiver>
246        <receiver
246-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
247            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
247-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
248            android:directBootAware="false"
248-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
249            android:enabled="true"
249-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
250            android:exported="true"
250-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
251            android:permission="android.permission.DUMP" >
251-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
252            <intent-filter>
252-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
253                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
253-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
253-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
254            </intent-filter>
255        </receiver>
256
257        <uses-library
257-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
258            android:name="androidx.window.extensions"
258-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
259            android:required="false" />
259-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
260        <uses-library
260-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
261            android:name="androidx.window.sidecar"
261-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
262            android:required="false" />
262-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
263
264        <service
264-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\29ad22528f4df7a20b7ea327a6a067ab\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
265            android:name="androidx.room.MultiInstanceInvalidationService"
265-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\29ad22528f4df7a20b7ea327a6a067ab\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
266            android:directBootAware="true"
266-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\29ad22528f4df7a20b7ea327a6a067ab\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
267            android:exported="false" />
267-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\29ad22528f4df7a20b7ea327a6a067ab\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
268
269        <receiver
269-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
270            android:name="androidx.profileinstaller.ProfileInstallReceiver"
270-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
271            android:directBootAware="false"
271-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
272            android:enabled="true"
272-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
273            android:exported="true"
273-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
274            android:permission="android.permission.DUMP" >
274-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
275            <intent-filter>
275-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
276                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
276-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
276-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
277            </intent-filter>
278            <intent-filter>
278-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
279                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
279-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
279-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
280            </intent-filter>
281            <intent-filter>
281-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
282                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
282-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
282-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
283            </intent-filter>
284            <intent-filter>
284-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
285                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
285-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
285-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
286            </intent-filter>
287        </receiver>
288    </application>
289
290</manifest>
