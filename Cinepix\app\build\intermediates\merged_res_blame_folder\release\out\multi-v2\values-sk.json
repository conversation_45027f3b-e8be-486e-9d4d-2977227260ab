{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeReleaseResources-87:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,624,938,1019,1099,1181,1284,1383,1462,1527,1618,1712,1782,1848,1913,1990,2112,2229,2350,2424,2506,2579,2661,2761,2860,2927,2992,3045,3103,3151,3212,3284,3358,3421,3494,3559,3619,3684,3748,3814,3866,3930,4008,4086", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,121,116,120,73,81,72,81,99,98,66,64,52,57,47,60,71,73,62,72,64,59,64,63,65,51,63,77,77,53", "endOffsets": "288,619,933,1014,1094,1176,1279,1378,1457,1522,1613,1707,1777,1843,1908,1985,2107,2224,2345,2419,2501,2574,2656,2756,2855,2922,2987,3040,3098,3146,3207,3279,3353,3416,3489,3554,3614,3679,3743,3809,3861,3925,4003,4081,4135"}, "to": {"startLines": "2,11,17,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,343,674,9960,10041,10121,10203,10306,10405,10484,10549,10640,10734,10804,10870,10935,11012,11134,11251,11372,11446,11528,11601,11683,11783,11882,11949,12685,12738,12796,12844,12905,12977,13051,13114,13187,13252,13312,13377,13441,13507,13559,13623,13701,13779", "endLines": "10,16,22,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163", "endColumns": "17,12,12,80,79,81,102,98,78,64,90,93,69,65,64,76,121,116,120,73,81,72,81,99,98,66,64,52,57,47,60,71,73,62,72,64,59,64,63,65,51,63,77,77,53", "endOffsets": "338,669,983,10036,10116,10198,10301,10400,10479,10544,10635,10729,10799,10865,10930,11007,11129,11246,11367,11441,11523,11596,11678,11778,11877,11944,12009,12733,12791,12839,12900,12972,13046,13109,13182,13247,13307,13372,13436,13502,13554,13618,13696,13774,13828"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,132,194,258,329,406,480,564,646", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "127,189,253,324,401,475,559,641,721"}, "to": {"startLines": "137,138,139,140,141,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "12014,12091,12153,12217,12288,12365,12439,12523,12605", "endColumns": "76,61,63,70,76,73,83,81,79", "endOffsets": "12086,12148,12212,12283,12360,12434,12518,12600,12680"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,126", "endOffsets": "156,283"}, "to": {"startLines": "260,261", "startColumns": "4,4", "startOffsets": "22826,22932", "endColumns": "105,126", "endOffsets": "22927,23054"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,128,202,275,344,431,522,615", "endColumns": "72,73,72,68,86,90,92,105", "endOffsets": "123,197,270,339,426,517,610,716"}, "to": {"startLines": "56,108,221,222,223,224,225,226", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3978,9546,19654,19727,19796,19883,19974,20067", "endColumns": "72,73,72,68,86,90,92,105", "endOffsets": "4046,9615,19722,19791,19878,19969,20062,20168"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,266,344,491,660,744", "endColumns": "72,87,77,146,168,83,80", "endOffsets": "173,261,339,486,655,739,820"}, "to": {"startLines": "75,164,264,269,278,296,297", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5861,13833,23230,23620,24462,26173,26257", "endColumns": "72,87,77,146,168,83,80", "endOffsets": "5929,13916,23303,23762,24626,26252,26333"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,284,379,482,574,653,747,837,918,987,1056,1139,1226,1298,1376,1444", "endColumns": "94,83,94,102,91,78,93,89,80,68,68,82,86,71,77,67,113", "endOffsets": "195,279,374,477,569,648,742,832,913,982,1051,1134,1221,1293,1371,1439,1553"}, "to": {"startLines": "72,73,107,109,111,168,169,262,263,265,266,270,271,274,279,280,281", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5606,5701,9451,9620,9803,14173,14252,23059,23149,23308,23377,23767,23850,24109,24631,24709,24777", "endColumns": "94,83,94,102,91,78,93,89,80,68,68,82,86,71,77,67,113", "endOffsets": "5696,5780,9541,9718,9890,14247,14341,23144,23225,23372,23441,23845,23932,24176,24704,24772,24886"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,509,627,740,837,930,1057,1183,1292,1417,1578,1711,1843,1948,2044,2174,2266,2372,2474,2587,2687,2804,2924,3046,3167,3281,3407,3517,3635,3741,3855,3942,4026,4142,4285,4460", "endColumns": "106,100,99,95,117,112,96,92,126,125,108,124,160,132,131,104,95,129,91,105,101,112,99,116,119,121,120,113,125,109,117,105,113,86,83,115,142,174,94", "endOffsets": "207,308,408,504,622,735,832,925,1052,1178,1287,1412,1573,1706,1838,1943,2039,2169,2261,2367,2469,2582,2682,2799,2919,3041,3162,3276,3402,3512,3630,3736,3850,3937,4021,4137,4280,4455,4550"}, "to": {"startLines": "171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14411,14518,14619,14719,14815,14933,15046,15143,15236,15363,15489,15598,15723,15884,16017,16149,16254,16350,16480,16572,16678,16780,16893,16993,17110,17230,17352,17473,17587,17713,17823,17941,18047,18161,18248,18332,18448,18591,23446", "endColumns": "106,100,99,95,117,112,96,92,126,125,108,124,160,132,131,104,95,129,91,105,101,112,99,116,119,121,120,113,125,109,117,105,113,86,83,115,142,174,94", "endOffsets": "14513,14614,14714,14810,14928,15041,15138,15231,15358,15484,15593,15718,15879,16012,16144,16249,16345,16475,16567,16673,16775,16888,16988,17105,17225,17347,17468,17582,17708,17818,17936,18042,18156,18243,18327,18443,18586,18761,23536"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,273", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1266,1373,1474,1585,1671,1779,1897,1976,2053,2144,2237,2335,2429,2529,2622,2717,2815,2906,2997,3081,3186,3294,3393,3499,3611,3714,3880,24026", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "1368,1469,1580,1666,1774,1892,1971,2048,2139,2232,2330,2424,2524,2617,2712,2810,2901,2992,3076,3181,3289,3388,3494,3606,3709,3875,3973,24104"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "62,63,64,65,66,67,68,276", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4568,4664,4766,4867,4965,5075,5183,24264", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "4659,4761,4862,4960,5070,5178,5300,24360"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,277,383,494,570,661,770,901,1013,1161,1242,1339,1427,1520,1632,1751,1853,1982,2111,2241,2401,2524,2644,2771,2888,2977,3070,3187,3305,3401,3500,3605,3741,3886,3991,4087,4167,4244,4333,4416,4513,4589,4671,4764,4863,4952,5045,5129,5230,5323,5417,5533,5609,5706", "endColumns": "110,110,105,110,75,90,108,130,111,147,80,96,87,92,111,118,101,128,128,129,159,122,119,126,116,88,92,116,117,95,98,104,135,144,104,95,79,76,88,82,96,75,81,92,98,88,92,83,100,92,93,115,75,96,88", "endOffsets": "161,272,378,489,565,656,765,896,1008,1156,1237,1334,1422,1515,1627,1746,1848,1977,2106,2236,2396,2519,2639,2766,2883,2972,3065,3182,3300,3396,3495,3600,3736,3881,3986,4082,4162,4239,4328,4411,4508,4584,4666,4759,4858,4947,5040,5124,5225,5318,5412,5528,5604,5701,5790"}, "to": {"startLines": "57,58,59,60,74,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,110,165,272,275,277,282,283,284,285,286,287,288,289,290,291,292,293,294,295", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4051,4162,4273,4379,5785,5934,6025,6134,6265,6377,6525,6606,6703,6791,6884,6996,7115,7217,7346,7475,7605,7765,7888,8008,8135,8252,8341,8434,8551,8669,8765,8864,8969,9105,9250,9355,9723,13921,23937,24181,24365,24891,24967,25049,25142,25241,25330,25423,25507,25608,25701,25795,25911,25987,26084", "endColumns": "110,110,105,110,75,90,108,130,111,147,80,96,87,92,111,118,101,128,128,129,159,122,119,126,116,88,92,116,117,95,98,104,135,144,104,95,79,76,88,82,96,75,81,92,98,88,92,83,100,92,93,115,75,96,88", "endOffsets": "4157,4268,4374,4485,5856,6020,6129,6260,6372,6520,6601,6698,6786,6879,6991,7110,7212,7341,7470,7600,7760,7883,8003,8130,8247,8336,8429,8546,8664,8760,8859,8964,9100,9245,9350,9446,9798,13993,24021,24259,24457,24962,25044,25137,25236,25325,25418,25502,25603,25696,25790,25906,25982,26079,26168"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,333,411,503,631,712,777,876,952,1017,1107,1173,1227,1296,1356,1410,1527,1587,1649,1703,1775,1905,1992,2084,2193,2262,2340,2428,2495,2561,2633,2710,2793,2865,2942,3015,3086,3174,3246,3338,3434,3508,3582,3678,3730,3797,3884,3971,4033,4097,4160,4266,4362,4460,4558", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,77,91,127,80,64,98,75,64,89,65,53,68,59,53,116,59,61,53,71,129,86,91,108,68,77,87,66,65,71,76,82,71,76,72,70,87,71,91,95,73,73,95,51,66,86,86,61,63,62,105,95,97,97,78", "endOffsets": "328,406,498,626,707,772,871,947,1012,1102,1168,1222,1291,1351,1405,1522,1582,1644,1698,1770,1900,1987,2079,2188,2257,2335,2423,2490,2556,2628,2705,2788,2860,2937,3010,3081,3169,3241,3333,3429,3503,3577,3673,3725,3792,3879,3966,4028,4092,4155,4261,4357,4455,4553,4632"}, "to": {"startLines": "23,61,69,70,71,112,166,167,170,209,210,211,212,213,214,215,216,217,218,219,220,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,268", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "988,4490,5305,5397,5525,9895,13998,14097,14346,18766,18856,18922,18976,19045,19105,19159,19276,19336,19398,19452,19524,20173,20260,20352,20461,20530,20608,20696,20763,20829,20901,20978,21061,21133,21210,21283,21354,21442,21514,21606,21702,21776,21850,21946,21998,22065,22152,22239,22301,22365,22428,22534,22630,22728,23541", "endLines": "28,61,69,70,71,112,166,167,170,209,210,211,212,213,214,215,216,217,218,219,220,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,268", "endColumns": "12,77,91,127,80,64,98,75,64,89,65,53,68,59,53,116,59,61,53,71,129,86,91,108,68,77,87,66,65,71,76,82,71,76,72,70,87,71,91,95,73,73,95,51,66,86,86,61,63,62,105,95,97,97,78", "endOffsets": "1261,4563,5392,5520,5601,9955,14092,14168,14406,18851,18917,18971,19040,19100,19154,19271,19331,19393,19447,19519,19649,20255,20347,20456,20525,20603,20691,20758,20824,20896,20973,21056,21128,21205,21278,21349,21437,21509,21601,21697,21771,21845,21941,21993,22060,22147,22234,22296,22360,22423,22529,22625,22723,22821,23615"}}]}]}