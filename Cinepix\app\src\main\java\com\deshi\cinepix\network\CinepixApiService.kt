package com.deshi.cinepix.network

import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import java.io.IOException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.TimeUnit

class CinepixApiService {
    
    private val client = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .followRedirects(true)
        .followSslRedirects(true)
        .build()
    
    companion object {
        private const val BASE_URL = "https://cinepix.top"
        private const val LOGIN_URL = "$BASE_URL/login/"
        private const val REGISTER_URL = "$BASE_URL/register/"
        private const val USER_AGENT = "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36 CinepixApp/1.0"
    }
    
    data class LoginResponse(
        val success: Boolean,
        val message: String,
        val sessionCookie: String? = null,
        val userInfo: Map<String, String>? = null
    )
    
    data class RegisterResponse(
        val success: Boolean,
        val message: String
    )
    
    suspend fun login(email: String, password: String): LoginResponse = withContext(Dispatchers.IO) {
        try {
            // First, get the login page to extract CSRF token
            val loginPageRequest = Request.Builder()
                .url(LOGIN_URL)
                .addHeader("User-Agent", USER_AGENT)
                .build()
            
            val loginPageResponse = client.newCall(loginPageRequest).execute()
            val loginPageHtml = loginPageResponse.body?.string() ?: ""
            val cookies = extractCookies(loginPageResponse)
            
            // Extract CSRF token from the page
            val csrfToken = extractCsrfToken(loginPageHtml)
            
            loginPageResponse.close()
            
            // Prepare login form data
            val formBody = FormBody.Builder()
                .add("email", email)
                .add("password", password)
            
            if (csrfToken.isNotEmpty()) {
                formBody.add("csrf_token", csrfToken)
                formBody.add("_token", csrfToken)
            }
            
            val loginRequest = Request.Builder()
                .url(LOGIN_URL)
                .addHeader("User-Agent", USER_AGENT)
                .addHeader("Referer", LOGIN_URL)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("Cookie", cookies)
                .post(formBody.build())
                .build()
            
            val loginResponse = client.newCall(loginRequest).execute()
            val responseBody = loginResponse.body?.string() ?: ""
            val responseCookies = extractCookies(loginResponse)
            
            loginResponse.close()
            
            // Check if login was successful
            when {
                loginResponse.isSuccessful && (
                    responseBody.contains("dashboard", ignoreCase = true) ||
                    responseBody.contains("welcome", ignoreCase = true) ||
                    loginResponse.isRedirect
                ) -> {
                    LoginResponse(
                        success = true,
                        message = "Login successful",
                        sessionCookie = responseCookies,
                        userInfo = extractUserInfo(responseBody)
                    )
                }
                responseBody.contains("invalid", ignoreCase = true) ||
                responseBody.contains("incorrect", ignoreCase = true) ||
                responseBody.contains("wrong", ignoreCase = true) -> {
                    LoginResponse(false, "Invalid email or password")
                }
                responseBody.contains("not found", ignoreCase = true) -> {
                    LoginResponse(false, "User not found")
                }
                else -> {
                    LoginResponse(false, "Login failed. Please try again.")
                }
            }
            
        } catch (e: IOException) {
            LoginResponse(false, "Network error: ${e.message}")
        } catch (e: Exception) {
            LoginResponse(false, "Login failed: ${e.message}")
        }
    }
    
    suspend fun register(username: String, email: String, password: String, fullName: String? = null): RegisterResponse = withContext(Dispatchers.IO) {
        try {
            // Get register page for CSRF token
            val registerPageRequest = Request.Builder()
                .url(REGISTER_URL)
                .addHeader("User-Agent", USER_AGENT)
                .build()
            
            val registerPageResponse = client.newCall(registerPageRequest).execute()
            val registerPageHtml = registerPageResponse.body?.string() ?: ""
            val cookies = extractCookies(registerPageResponse)
            val csrfToken = extractCsrfToken(registerPageHtml)
            
            registerPageResponse.close()
            
            // Prepare registration form data
            val formBody = FormBody.Builder()
                .add("username", username)
                .add("email", email)
                .add("password", password)
                .add("password_confirmation", password)
            
            fullName?.let { formBody.add("name", it) }
            
            if (csrfToken.isNotEmpty()) {
                formBody.add("csrf_token", csrfToken)
                formBody.add("_token", csrfToken)
            }
            
            val registerRequest = Request.Builder()
                .url(REGISTER_URL)
                .addHeader("User-Agent", USER_AGENT)
                .addHeader("Referer", REGISTER_URL)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("Cookie", cookies)
                .post(formBody.build())
                .build()
            
            val registerResponse = client.newCall(registerRequest).execute()
            val responseBody = registerResponse.body?.string() ?: ""
            
            registerResponse.close()
            
            when {
                registerResponse.isSuccessful && (
                    responseBody.contains("success", ignoreCase = true) ||
                    responseBody.contains("registered", ignoreCase = true) ||
                    registerResponse.isRedirect
                ) -> {
                    RegisterResponse(true, "Registration successful")
                }
                responseBody.contains("already exists", ignoreCase = true) ||
                responseBody.contains("already taken", ignoreCase = true) -> {
                    RegisterResponse(false, "Email or username already exists")
                }
                responseBody.contains("invalid", ignoreCase = true) -> {
                    RegisterResponse(false, "Invalid registration data")
                }
                else -> {
                    RegisterResponse(false, "Registration failed. Please try again.")
                }
            }
            
        } catch (e: IOException) {
            RegisterResponse(false, "Network error: ${e.message}")
        } catch (e: Exception) {
            RegisterResponse(false, "Registration failed: ${e.message}")
        }
    }
    
    private fun extractCookies(response: Response): String {
        return response.headers("Set-Cookie").joinToString("; ") { cookie ->
            cookie.split(";")[0]
        }
    }
    
    private fun extractCsrfToken(html: String): String {
        // Try different CSRF token patterns
        val patterns = listOf(
            Regex("""name="csrf_token"\s+value="([^"]+)""""),
            Regex("""name="_token"\s+value="([^"]+)""""),
            Regex("""content="([^"]+)"\s+name="csrf-token""""),
            Regex("""<meta name="csrf-token" content="([^"]+)"""")
        )
        
        for (pattern in patterns) {
            val match = pattern.find(html)
            if (match != null) {
                return match.groupValues[1]
            }
        }
        
        return ""
    }
    
    private fun extractUserInfo(html: String): Map<String, String> {
        val userInfo = mutableMapOf<String, String>()
        
        // Try to extract user information from the response
        val usernamePattern = Regex("""username['"]\s*:\s*['"]([^'"]+)['"]""", RegexOption.IGNORE_CASE)
        val emailPattern = Regex("""email['"]\s*:\s*['"]([^'"]+)['"]""", RegexOption.IGNORE_CASE)
        val namePattern = Regex("""name['"]\s*:\s*['"]([^'"]+)['"]""", RegexOption.IGNORE_CASE)
        
        usernamePattern.find(html)?.let { userInfo["username"] = it.groupValues[1] }
        emailPattern.find(html)?.let { userInfo["email"] = it.groupValues[1] }
        namePattern.find(html)?.let { userInfo["name"] = it.groupValues[1] }
        
        return userInfo
    }
}
