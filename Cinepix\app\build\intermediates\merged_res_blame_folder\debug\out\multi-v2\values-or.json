{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-99:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\de0470acc971f743e832aff97e9527de\\transformed\\appcompat-1.6.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,830,911,1002,1095,1198,1293,1393,1486,1581,1677,1768,1858,1947,2057,2161,2267,2378,2482,2600,2763,2869", "endColumns": "118,109,106,85,103,119,78,80,90,92,102,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "219,329,436,522,626,746,825,906,997,1090,1193,1288,1388,1481,1576,1672,1763,1853,1942,2052,2156,2262,2373,2477,2595,2758,2864,2954"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "277,396,506,613,699,803,923,1002,1083,1174,1267,1370,1465,1565,1658,1753,1849,1940,2030,2119,2229,2333,2439,2550,2654,2772,2935,20854", "endColumns": "118,109,106,85,103,119,78,80,90,92,102,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "391,501,608,694,798,918,997,1078,1169,1262,1365,1460,1560,1653,1748,1844,1935,2025,2114,2224,2328,2434,2545,2649,2767,2930,3036,20939"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\677a264eecd9b3484ff2620a344ffab3\\transformed\\material3-1.1.2\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,286,395,510,596,692,806,945,1067,1214,1295,1396,1488,1579,1691,1817,1923,2062,2196,2324,2513,2636,2761,2893,3018,3111,3203,3316,3434,3535,3636,3735,3872,4018,4121,4225,4296,4380,4470,4557,4658,4734,4815,4912,5014,5103,5200,5283,5387,5482,5580,5700,5776,5875", "endColumns": "117,112,108,114,85,95,113,138,121,146,80,100,91,90,111,125,105,138,133,127,188,122,124,131,124,92,91,112,117,100,100,98,136,145,102,103,70,83,89,86,100,75,80,96,101,88,96,82,103,94,97,119,75,98,89", "endOffsets": "168,281,390,505,591,687,801,940,1062,1209,1290,1391,1483,1574,1686,1812,1918,2057,2191,2319,2508,2631,2756,2888,3013,3106,3198,3311,3429,3530,3631,3730,3867,4013,4116,4220,4291,4375,4465,4552,4653,4729,4810,4907,5009,5098,5195,5278,5382,5477,5575,5695,5771,5870,5960"}, "to": {"startLines": "33,34,35,36,50,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,103,107,208,211,213,218,219,220,221,222,223,224,225,226,227,228,229,230,231", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3041,3159,3272,3381,4770,7210,7306,7420,7559,7681,7828,7909,8010,8102,8193,8305,8431,8537,8676,8810,8938,9127,9250,9375,9507,9632,9725,9817,9930,10048,10149,10250,10349,10486,10632,10735,11031,11342,20764,21014,21202,21741,21817,21898,21995,22097,22186,22283,22366,22470,22565,22663,22783,22859,22958", "endColumns": "117,112,108,114,85,95,113,138,121,146,80,100,91,90,111,125,105,138,133,127,188,122,124,131,124,92,91,112,117,100,100,98,136,145,102,103,70,83,89,86,100,75,80,96,101,88,96,82,103,94,97,119,75,98,89", "endOffsets": "3154,3267,3376,3491,4851,7301,7415,7554,7676,7823,7904,8005,8097,8188,8300,8426,8532,8671,8805,8933,9122,9245,9370,9502,9627,9720,9812,9925,10043,10144,10245,10344,10481,10627,10730,10834,11097,11421,20849,21096,21298,21812,21893,21990,22092,22181,22278,22361,22465,22560,22658,22778,22854,22953,23043"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc96508f556f069c09e91b3a9267645a\\transformed\\play-services-base-18.0.1\\res\\values-or\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,457,585,698,849,980,1090,1196,1359,1468,1625,1754,1900,2053,2114,2182", "endColumns": "106,156,127,112,150,130,109,105,162,108,156,128,145,152,60,67,82", "endOffsets": "299,456,584,697,848,979,1089,1195,1358,1467,1624,1753,1899,2052,2113,2181,2264"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4856,4967,5128,5260,5377,5532,5667,5781,6031,6198,6311,6472,6605,6755,6912,6977,7049", "endColumns": "110,160,131,116,154,134,113,109,166,112,160,132,149,156,64,71,86", "endOffsets": "4962,5123,5255,5372,5527,5662,5776,5886,6193,6306,6467,6600,6750,6907,6972,7044,7131"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cb0d38356f8a450289b6a883ab8edf14\\transformed\\navigation-ui-2.7.6\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,172", "endColumns": "116,120", "endOffsets": "167,288"}, "to": {"startLines": "196,197", "startColumns": "4,4", "startOffsets": "19661,19778", "endColumns": "116,120", "endOffsets": "19773,19894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\804c4e5761ff122de1231662730d5f72\\transformed\\ui-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,381,481,567,644,742,830,917,987,1057,1135,1217,1287,1370,1437", "endColumns": "96,86,91,99,85,76,97,87,86,69,69,77,81,69,82,66,118", "endOffsets": "197,284,376,476,562,639,737,825,912,982,1052,1130,1212,1282,1365,1432,1551"}, "to": {"startLines": "48,49,101,102,104,110,111,198,199,201,202,206,207,210,215,216,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4586,4683,10839,10931,11102,11580,11657,19899,19987,20154,20224,20604,20682,20944,21472,21555,21622", "endColumns": "96,86,91,99,85,76,97,87,86,69,69,77,81,69,82,66,118", "endOffsets": "4678,4765,10926,11026,11183,11652,11750,19982,20069,20219,20289,20677,20759,21009,21550,21617,21736"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\90e8727c8917f86c7d308f0c9abe80a3\\transformed\\material-1.4.0-beta01\\res\\values-or\\values-or.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,304,398,503,582,647,736,801,860,946,1010,1073,1146,1210,1264,1376,1434,1496,1550,1622,1744,1831,1917,2027,2104,2185,2276,2343,2409,2479,2556,2643,2714,2791,2860,2929,3020,3092,3181,3270,3344,3416,3502,3552,3618,3698,3782,3844,3908,3971,4071,4168,4260,4359", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,93,104,78,64,88,64,58,85,63,62,72,63,53,111,57,61,53,71,121,86,85,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,83", "endOffsets": "222,299,393,498,577,642,731,796,855,941,1005,1068,1141,1205,1259,1371,1429,1491,1545,1617,1739,1826,1912,2022,2099,2180,2271,2338,2404,2474,2551,2638,2709,2786,2855,2924,3015,3087,3176,3265,3339,3411,3497,3547,3613,3693,3777,3839,3903,3966,4066,4163,4255,4354,4438"}, "to": {"startLines": "2,37,45,46,47,105,108,109,112,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,204", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3496,4308,4402,4507,11188,11426,11515,11755,16162,16248,16312,16375,16448,16512,16566,16678,16736,16798,16852,16924,17046,17133,17219,17329,17406,17487,17578,17645,17711,17781,17858,17945,18016,18093,18162,18231,18322,18394,18483,18572,18646,18718,18804,18854,18920,19000,19084,19146,19210,19273,19373,19470,19562,20381", "endLines": "5,37,45,46,47,105,108,109,112,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,204", "endColumns": "12,76,93,104,78,64,88,64,58,85,63,62,72,63,53,111,57,61,53,71,121,86,85,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,83", "endOffsets": "272,3568,4397,4502,4581,11248,11510,11575,11809,16243,16307,16370,16443,16507,16561,16673,16731,16793,16847,16919,17041,17128,17214,17324,17401,17482,17573,17640,17706,17776,17853,17940,18011,18088,18157,18226,18317,18389,18478,18567,18641,18713,18799,18849,18915,18995,19079,19141,19205,19268,19368,19465,19557,19656,20460"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f70384b9a1e1b9ce2411b7c78f9fa615\\transformed\\preference-1.2.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,268,348,487,656,737", "endColumns": "73,88,79,138,168,80,81", "endOffsets": "174,263,343,482,651,732,814"}, "to": {"startLines": "69,106,200,205,214,232,233", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7136,11253,20074,20465,21303,23048,23129", "endColumns": "73,88,79,138,168,80,81", "endOffsets": "7205,11337,20149,20599,21467,23124,23206"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\31a00a017e90aa446708533c4ea3d047\\transformed\\core-1.12.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,212", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3573,3676,3778,3881,3986,4087,4189,21101", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "3671,3773,3876,3981,4082,4184,4303,21197"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\35536fe73c76a56f94e622d08fe45089\\transformed\\leanback-1.0.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,414,515,639,750,851,948,1090,1231,1339,1467,1608,1737,1865,1970,2067,2201,2293,2404,2519,2625,2720,2845,2956,3073,3189,3298,3414,3532,3652,3765,3880,3967,4058,4162,4298,4453", "endColumns": "106,100,100,100,123,110,100,96,141,140,107,127,140,128,127,104,96,133,91,110,114,105,94,124,110,116,115,108,115,117,119,112,114,86,90,103,135,154,86", "endOffsets": "207,308,409,510,634,745,846,943,1085,1226,1334,1462,1603,1732,1860,1965,2062,2196,2288,2399,2514,2620,2715,2840,2951,3068,3184,3293,3409,3527,3647,3760,3875,3962,4053,4157,4293,4448,4535"}, "to": {"startLines": "113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11814,11921,12022,12123,12224,12348,12459,12560,12657,12799,12940,13048,13176,13317,13446,13574,13679,13776,13910,14002,14113,14228,14334,14429,14554,14665,14782,14898,15007,15123,15241,15361,15474,15589,15676,15767,15871,16007,20294", "endColumns": "106,100,100,100,123,110,100,96,141,140,107,127,140,128,127,104,96,133,91,110,114,105,94,124,110,116,115,108,115,117,119,112,114,86,90,103,135,154,86", "endOffsets": "11916,12017,12118,12219,12343,12454,12555,12652,12794,12935,13043,13171,13312,13441,13569,13674,13771,13905,13997,14108,14223,14329,14424,14549,14660,14777,14893,15002,15118,15236,15356,15469,15584,15671,15762,15866,16002,16157,20376"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\62c17cba0a75b790331013fbfad47644\\transformed\\play-services-basement-18.1.0\\res\\values-or\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5891", "endColumns": "139", "endOffsets": "6026"}}]}]}