{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-99:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\804c4e5761ff122de1231662730d5f72\\transformed\\ui-release\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,292,388,491,581,667,755,848,932,1002,1072,1157,1244,1317,1395,1463", "endColumns": "97,88,95,102,89,85,87,92,83,69,69,84,86,72,77,67,121", "endOffsets": "198,287,383,486,576,662,750,843,927,997,1067,1152,1239,1312,1390,1458,1580"}, "to": {"startLines": "69,70,122,124,126,183,184,277,278,280,281,285,286,289,294,295,296", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5678,5776,11889,12061,12243,16585,16671,25591,25684,25848,25918,26306,26391,26647,27181,27259,27327", "endColumns": "97,88,95,102,89,85,87,92,83,69,69,84,86,72,77,67,121", "endOffsets": "5771,5860,11980,12159,12328,16666,16754,25679,25763,25913,25983,26386,26473,26715,27254,27322,27444"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\62c17cba0a75b790331013fbfad47644\\transformed\\play-services-basement-18.1.0\\res\\values-lv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "161", "endOffsets": "356"}, "to": {"startLines": "80", "startColumns": "4", "startOffsets": "6947", "endColumns": "165", "endOffsets": "7108"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\de0470acc971f743e832aff97e9527de\\transformed\\appcompat-1.6.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,2989", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,3067"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,288", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1105,1225,1335,1444,1530,1634,1756,1838,1918,2028,2136,2242,2351,2462,2565,2677,2784,2889,2989,3074,3183,3294,3393,3504,3611,3716,3890,26564", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "1220,1330,1439,1525,1629,1751,1833,1913,2023,2131,2237,2346,2457,2560,2672,2779,2884,2984,3069,3178,3289,3388,3499,3606,3711,3885,3984,26642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\677a264eecd9b3484ff2620a344ffab3\\transformed\\material3-1.1.2\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,178,301,412,533,610,706,820,954,1068,1211,1293,1387,1477,1574,1689,1809,1908,2042,2169,2303,2482,2610,2725,2849,2966,3057,3152,3269,3400,3498,3608,3710,3840,3981,4086,4184,4263,4339,4425,4509,4616,4692,4775,4866,4966,5053,5149,5234,5337,5435,5532,5681,5757,5858", "endColumns": "122,122,110,120,76,95,113,133,113,142,81,93,89,96,114,119,98,133,126,133,178,127,114,123,116,90,94,116,130,97,109,101,129,140,104,97,78,75,85,83,106,75,82,90,99,86,95,84,102,97,96,148,75,100,94", "endOffsets": "173,296,407,528,605,701,815,949,1063,1206,1288,1382,1472,1569,1684,1804,1903,2037,2164,2298,2477,2605,2720,2844,2961,3052,3147,3264,3395,3493,3603,3705,3835,3976,4081,4179,4258,4334,4420,4504,4611,4687,4770,4861,4961,5048,5144,5229,5332,5430,5527,5676,5752,5853,5948"}, "to": {"startLines": "54,55,56,57,71,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,125,180,287,290,292,297,298,299,300,301,302,303,304,305,306,307,308,309,310", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4070,4193,4316,4427,5865,8315,8411,8525,8659,8773,8916,8998,9092,9182,9279,9394,9514,9613,9747,9874,10008,10187,10315,10430,10554,10671,10762,10857,10974,11105,11203,11313,11415,11545,11686,11791,12164,16342,26478,26720,26905,27449,27525,27608,27699,27799,27886,27982,28067,28170,28268,28365,28514,28590,28691", "endColumns": "122,122,110,120,76,95,113,133,113,142,81,93,89,96,114,119,98,133,126,133,178,127,114,123,116,90,94,116,130,97,109,101,129,140,104,97,78,75,85,83,106,75,82,90,99,86,95,84,102,97,96,148,75,100,94", "endOffsets": "4188,4311,4422,4543,5937,8406,8520,8654,8768,8911,8993,9087,9177,9274,9389,9509,9608,9742,9869,10003,10182,10310,10425,10549,10666,10757,10852,10969,11100,11198,11308,11410,11540,11681,11786,11884,12238,16413,26559,26799,27007,27520,27603,27694,27794,27881,27977,28062,28165,28263,28360,28509,28585,28686,28781"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b15b518726989b0578dc4af44fa5f9f8\\transformed\\media3-ui-1.2.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,567,831,915,998,1081,1176,1271,1344,1411,1505,1599,1665,1732,1795,1871,1977,2088,2195,2269,2351,2425,2498,2598,2697,2763,2829,2882,2940,2988,3049,3107,3183,3247,3312,3377,3434,3500,3566,3632,3684,3748,3826,3904", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,83,82,82,94,94,72,66,93,93,65,66,62,75,105,110,106,73,81,73,72,99,98,65,65,52,57,47,60,57,75,63,64,64,56,65,65,65,51,63,77,77,54", "endOffsets": "281,562,826,910,993,1076,1171,1266,1339,1406,1500,1594,1660,1727,1790,1866,1972,2083,2190,2264,2346,2420,2493,2593,2692,2758,2824,2877,2935,2983,3044,3102,3178,3242,3307,3372,3429,3495,3561,3627,3679,3743,3821,3899,3954"}, "to": {"startLines": "2,11,16,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,617,12398,12482,12565,12648,12743,12838,12911,12978,13072,13166,13232,13299,13362,13438,13544,13655,13762,13836,13918,13992,14065,14165,14264,14330,15114,15167,15225,15273,15334,15392,15468,15532,15597,15662,15719,15785,15851,15917,15969,16033,16111,16189", "endLines": "10,15,20,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178", "endColumns": "17,12,12,83,82,82,94,94,72,66,93,93,65,66,62,75,105,110,106,73,81,73,72,99,98,65,65,52,57,47,60,57,75,63,64,64,56,65,65,65,51,63,77,77,54", "endOffsets": "331,612,876,12477,12560,12643,12738,12833,12906,12973,13067,13161,13227,13294,13357,13433,13539,13650,13757,13831,13913,13987,14060,14160,14259,14325,14391,15162,15220,15268,15329,15387,15463,15527,15592,15657,15714,15780,15846,15912,15964,16028,16106,16184,16239"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2907b06edc976dc86930b7aa57b04408\\transformed\\media3-session-1.2.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,136,212,282,352,433,520,614", "endColumns": "80,75,69,69,80,86,93,102", "endOffsets": "131,207,277,347,428,515,609,712"}, "to": {"startLines": "53,123,236,237,238,239,240,241", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3989,11985,22130,22200,22270,22351,22438,22532", "endColumns": "80,75,69,69,80,86,93,102", "endOffsets": "4065,12056,22195,22265,22346,22433,22527,22630"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f70384b9a1e1b9ce2411b7c78f9fa615\\transformed\\preference-1.2.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,272,352,498,667,752", "endColumns": "68,97,79,145,168,84,81", "endOffsets": "169,267,347,493,662,747,829"}, "to": {"startLines": "90,179,279,284,293,311,312", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8246,16244,25768,26160,27012,28786,28871", "endColumns": "68,97,79,145,168,84,81", "endOffsets": "8310,16337,25843,26301,27176,28866,28948"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e3384bca41f271b3200ce0446ec0f087\\transformed\\media3-exoplayer-1.2.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,196,268,338,418,495,596,694", "endColumns": "74,65,71,69,79,76,100,97,78", "endOffsets": "125,191,263,333,413,490,591,689,768"}, "to": {"startLines": "152,153,154,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "14396,14471,14537,14609,14679,14759,14836,14937,15035", "endColumns": "74,65,71,69,79,76,100,97,78", "endOffsets": "14466,14532,14604,14674,14754,14831,14932,15030,15109"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc96508f556f069c09e91b3a9267645a\\transformed\\play-services-base-18.0.1\\res\\values-lv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,453,582,686,824,951,1064,1166,1337,1442,1607,1738,1903,2054,2114,2178", "endColumns": "102,156,128,103,137,126,112,101,170,104,164,130,164,150,59,63,84", "endOffsets": "295,452,581,685,823,950,1063,1165,1336,1441,1606,1737,1902,2053,2113,2177,2262"}, "to": {"startLines": "72,73,74,75,76,77,78,79,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5942,6049,6210,6343,6451,6593,6724,6841,7113,7288,7397,7566,7701,7870,8025,8089,8157", "endColumns": "106,160,132,107,141,130,116,105,174,108,168,134,168,154,63,67,88", "endOffsets": "6044,6205,6338,6446,6588,6719,6836,6942,7283,7392,7561,7696,7865,8020,8084,8152,8241"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\90e8727c8917f86c7d308f0c9abe80a3\\transformed\\material-1.4.0-beta01\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,279,360,461,595,678,743,837,910,971,1096,1164,1225,1297,1357,1411,1531,1591,1653,1707,1784,1914,2001,2083,2194,2274,2359,2450,2517,2583,2657,2738,2822,2895,2972,3049,3123,3216,3291,3381,3472,3544,3622,3713,3767,3835,3919,4006,4068,4132,4195,4305,4418,4521,4633", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,80,100,133,82,64,93,72,60,124,67,60,71,59,53,119,59,61,53,76,129,86,81,110,79,84,90,66,65,73,80,83,72,76,76,73,92,74,89,90,71,77,90,53,67,83,86,61,63,62,109,112,102,111,76", "endOffsets": "274,355,456,590,673,738,832,905,966,1091,1159,1220,1292,1352,1406,1526,1586,1648,1702,1779,1909,1996,2078,2189,2269,2354,2445,2512,2578,2652,2733,2817,2890,2967,3044,3118,3211,3286,3376,3467,3539,3617,3708,3762,3830,3914,4001,4063,4127,4190,4300,4413,4516,4628,4705"}, "to": {"startLines": "21,58,66,67,68,127,181,182,185,224,225,226,227,228,229,230,231,232,233,234,235,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,283", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "881,4548,5360,5461,5595,12333,16418,16512,16759,21187,21312,21380,21441,21513,21573,21627,21747,21807,21869,21923,22000,22635,22722,22804,22915,22995,23080,23171,23238,23304,23378,23459,23543,23616,23693,23770,23844,23937,24012,24102,24193,24265,24343,24434,24488,24556,24640,24727,24789,24853,24916,25026,25139,25242,26083", "endLines": "25,58,66,67,68,127,181,182,185,224,225,226,227,228,229,230,231,232,233,234,235,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,283", "endColumns": "12,80,100,133,82,64,93,72,60,124,67,60,71,59,53,119,59,61,53,76,129,86,81,110,79,84,90,66,65,73,80,83,72,76,76,73,92,74,89,90,71,77,90,53,67,83,86,61,63,62,109,112,102,111,76", "endOffsets": "1100,4624,5456,5590,5673,12393,16507,16580,16815,21307,21375,21436,21508,21568,21622,21742,21802,21864,21918,21995,22125,22717,22799,22910,22990,23075,23166,23233,23299,23373,23454,23538,23611,23688,23765,23839,23932,24007,24097,24188,24260,24338,24429,24483,24551,24635,24722,24784,24848,24911,25021,25134,25237,25349,26155"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\35536fe73c76a56f94e622d08fe45089\\transformed\\leanback-1.0.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,506,627,743,841,936,1068,1199,1309,1435,1610,1758,1905,2012,2104,2235,2328,2432,2542,2647,2739,2856,2974,3104,3233,3337,3450,3564,3680,3790,3902,3989,4073,4176,4315,4472", "endColumns": "106,100,97,94,120,115,97,94,131,130,109,125,174,147,146,106,91,130,92,103,109,104,91,116,117,129,128,103,112,113,115,109,111,86,83,102,138,156,94", "endOffsets": "207,308,406,501,622,738,836,931,1063,1194,1304,1430,1605,1753,1900,2007,2099,2230,2323,2427,2537,2642,2734,2851,2969,3099,3228,3332,3445,3559,3675,3785,3897,3984,4068,4171,4310,4467,4562"}, "to": {"startLines": "186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,282", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16820,16927,17028,17126,17221,17342,17458,17556,17651,17783,17914,18024,18150,18325,18473,18620,18727,18819,18950,19043,19147,19257,19362,19454,19571,19689,19819,19948,20052,20165,20279,20395,20505,20617,20704,20788,20891,21030,25988", "endColumns": "106,100,97,94,120,115,97,94,131,130,109,125,174,147,146,106,91,130,92,103,109,104,91,116,117,129,128,103,112,113,115,109,111,86,83,102,138,156,94", "endOffsets": "16922,17023,17121,17216,17337,17453,17551,17646,17778,17909,18019,18145,18320,18468,18615,18722,18814,18945,19038,19142,19252,19357,19449,19566,19684,19814,19943,20047,20160,20274,20390,20500,20612,20699,20783,20886,21025,21182,26078"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\31a00a017e90aa446708533c4ea3d047\\transformed\\core-1.12.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "59,60,61,62,63,64,65,291", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4629,4727,4829,4929,5030,5137,5245,26804", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "4722,4824,4924,5025,5132,5240,5355,26900"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cb0d38356f8a450289b6a883ab8edf14\\transformed\\navigation-ui-2.7.6\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,122", "endOffsets": "164,287"}, "to": {"startLines": "275,276", "startColumns": "4,4", "startOffsets": "25354,25468", "endColumns": "113,122", "endOffsets": "25463,25586"}}]}]}