{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeReleaseResources-87:/values-en-rIN/values-en-rIN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,559,641,730,819,903,968,1032,1110,1192,1265,1342,1408", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,76,65,120", "endOffsets": "192,274,368,467,554,636,725,814,898,963,1027,1105,1187,1260,1337,1403,1524"}, "to": {"startLines": "58,59,93,95,97,151,152,199,200,202,203,206,207,210,215,216,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4601,4693,8343,8511,8684,12598,12680,17599,17688,17846,17911,18199,18277,18525,19049,19126,19192", "endColumns": "91,81,93,98,86,81,88,88,83,64,63,77,81,72,76,65,120", "endOffsets": "4688,4770,8432,8605,8766,12675,12764,17683,17767,17906,17970,18272,18354,18593,19121,19187,19308"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,173,260,334,468,637,717", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "168,255,329,463,632,712,788"}, "to": {"startLines": "61,149,201,205,214,232,233", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4853,12435,17772,18065,18880,20615,20695", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "4916,12517,17841,18194,19044,20690,20766"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "51,52,53,54,55,56,57,212", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3882,3978,4080,4179,4278,4382,4485,18681", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3973,4075,4174,4273,4377,4480,4596,18777"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,479,656,738,820,898,985,1070,1137,1200,1292,1384,1449,1512,1574,1645,1755,1866,1976,2043,2123,2194,2261,2346,2431,2494,2558,2611,2669,2717,2778,2843,2905,2970,3041,3099,3157,3223,3287,3353,3405,3467,3543,3619", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,63,52,57,47,60,64,61,64,70,57,57,65,63,65,51,61,75,75,53", "endOffsets": "280,474,651,733,815,893,980,1065,1132,1195,1287,1379,1444,1507,1569,1640,1750,1861,1971,2038,2118,2189,2256,2341,2426,2489,2553,2606,2664,2712,2773,2838,2900,2965,3036,3094,3152,3218,3282,3348,3400,3462,3538,3614,3668"}, "to": {"startLines": "2,11,15,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,529,8771,8853,8935,9013,9100,9185,9252,9315,9407,9499,9564,9627,9689,9760,9870,9981,10091,10158,10238,10309,10376,10461,10546,10609,11320,11373,11431,11479,11540,11605,11667,11732,11803,11861,11919,11985,12049,12115,12167,12229,12305,12381", "endLines": "10,14,18,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148", "endColumns": "17,12,12,81,81,77,86,84,66,62,91,91,64,62,61,70,109,110,109,66,79,70,66,84,84,62,63,52,57,47,60,64,61,64,70,57,57,65,63,65,51,61,75,75,53", "endOffsets": "330,524,701,8848,8930,9008,9095,9180,9247,9310,9402,9494,9559,9622,9684,9755,9865,9976,10086,10153,10233,10304,10371,10456,10541,10604,10668,11368,11426,11474,11535,11600,11662,11727,11798,11856,11914,11980,12044,12110,12162,12224,12300,12376,12430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,388,498,576,667,776,908,1020,1152,1232,1327,1414,1507,1622,1743,1843,1966,2085,2209,2367,2484,2596,2716,2838,2926,3020,3133,3253,3346,3444,3542,3667,3802,3904,3998,4072,4148,4231,4314,4412,4488,4568,4665,4762,4858,4953,5037,5139,5236,5335,5451,5527,5623", "endColumns": "113,111,106,109,77,90,108,131,111,131,79,94,86,92,114,120,99,122,118,123,157,116,111,119,121,87,93,112,119,92,97,97,124,134,101,93,73,75,82,82,97,75,79,96,96,95,94,83,101,96,98,115,75,95,90", "endOffsets": "164,276,383,493,571,662,771,903,1015,1147,1227,1322,1409,1502,1617,1738,1838,1961,2080,2204,2362,2479,2591,2711,2833,2921,3015,3128,3248,3341,3439,3537,3662,3797,3899,3993,4067,4143,4226,4309,4407,4483,4563,4660,4757,4853,4948,5032,5134,5231,5330,5446,5522,5618,5709"}, "to": {"startLines": "47,48,49,50,60,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,96,150,208,211,213,218,219,220,221,222,223,224,225,226,227,228,229,230,231", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3439,3553,3665,3772,4775,4921,5012,5121,5253,5365,5497,5577,5672,5759,5852,5967,6088,6188,6311,6430,6554,6712,6829,6941,7061,7183,7271,7365,7478,7598,7691,7789,7887,8012,8147,8249,8610,12522,18359,18598,18782,19313,19389,19469,19566,19663,19759,19854,19938,20040,20137,20236,20352,20428,20524", "endColumns": "113,111,106,109,77,90,108,131,111,131,79,94,86,92,114,120,99,122,118,123,157,116,111,119,121,87,93,112,119,92,97,97,124,134,101,93,73,75,82,82,97,75,79,96,96,95,94,83,101,96,98,115,75,95,90", "endOffsets": "3548,3660,3767,3877,4848,5007,5116,5248,5360,5492,5572,5667,5754,5847,5962,6083,6183,6306,6425,6549,6707,6824,6936,7056,7178,7266,7360,7473,7593,7686,7784,7882,8007,8142,8244,8338,8679,12593,18437,18676,18875,19384,19464,19561,19658,19754,19849,19933,20035,20132,20231,20347,20423,20519,20610"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "706,810,910,1018,1102,1202,1317,1395,1470,1561,1654,1749,1843,1943,2036,2131,2225,2316,2407,2489,2592,2695,2794,2899,3003,3107,3263,18442", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "805,905,1013,1097,1197,1312,1390,1465,1556,1649,1744,1838,1938,2031,2126,2220,2311,2402,2484,2587,2690,2789,2894,2998,3102,3258,3358,18520"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,131,205,273,339,412,494,584", "endColumns": "75,73,67,65,72,81,89,96", "endOffsets": "126,200,268,334,407,489,579,676"}, "to": {"startLines": "46,94,191,192,193,194,195,196", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3363,8437,16900,16968,17034,17107,17189,17279", "endColumns": "75,73,67,65,72,81,89,96", "endOffsets": "3434,8506,16963,17029,17102,17184,17274,17371"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,633", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "120,182,247,311,388,453,543,628,697"}, "to": {"startLines": "122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10673,10743,10805,10870,10934,11011,11076,11166,11251", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "10738,10800,10865,10929,11006,11071,11161,11246,11315"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,505,624,736,832,928,1059,1188,1293,1414,1542,1663,1782,1887,1978,2106,2195,2296,2399,2500,2593,2703,2809,2920,3029,3128,3235,3345,3461,3567,3679,3766,3850,3950,4085,4236", "endColumns": "106,100,97,93,118,111,95,95,130,128,104,120,127,120,118,104,90,127,88,100,102,100,92,109,105,110,108,98,106,109,115,105,111,86,83,99,134,150,89", "endOffsets": "207,308,406,500,619,731,827,923,1054,1183,1288,1409,1537,1658,1777,1882,1973,2101,2190,2291,2394,2495,2588,2698,2804,2915,3024,3123,3230,3340,3456,3562,3674,3761,3845,3945,4080,4231,4321"}, "to": {"startLines": "153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,204", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12769,12876,12977,13075,13169,13288,13400,13496,13592,13723,13852,13957,14078,14206,14327,14446,14551,14642,14770,14859,14960,15063,15164,15257,15367,15473,15584,15693,15792,15899,16009,16125,16231,16343,16430,16514,16614,16749,17975", "endColumns": "106,100,97,93,118,111,95,95,130,128,104,120,127,120,118,104,90,127,88,100,102,100,92,109,105,110,108,98,106,109,115,105,111,86,83,99,134,150,89", "endOffsets": "12871,12972,13070,13164,13283,13395,13491,13587,13718,13847,13952,14073,14201,14322,14441,14546,14637,14765,14854,14955,15058,15159,15252,15362,15468,15579,15688,15787,15894,16004,16120,16226,16338,16425,16509,16609,16744,16895,18060"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,116", "endOffsets": "156,273"}, "to": {"startLines": "197,198", "startColumns": "4,4", "startOffsets": "17376,17482", "endColumns": "105,116", "endOffsets": "17477,17594"}}]}]}