{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeReleaseResources-87:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,138,215,286,353,435,521,617", "endColumns": "82,76,70,66,81,85,95,101", "endOffsets": "133,210,281,348,430,516,612,714"}, "to": {"startLines": "53,105,218,219,220,221,222,223", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3816,9442,19361,19432,19499,19581,19667,19763", "endColumns": "82,76,70,66,81,85,95,101", "endOffsets": "3894,9514,19427,19494,19576,19662,19758,19860"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,270", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1088,1195,1296,1402,1488,1592,1714,1798,1879,1970,2063,2158,2252,2352,2445,2540,2645,2736,2827,2913,3018,3124,3227,3333,3442,3549,3719,23756", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "1190,1291,1397,1483,1587,1709,1793,1874,1965,2058,2153,2247,2347,2440,2535,2640,2731,2822,2908,3013,3119,3222,3328,3437,3544,3714,3811,23838"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,989,1059,1139,1224,1297,1376,1446", "endColumns": "96,86,96,100,85,76,90,91,84,70,69,79,84,72,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,984,1054,1134,1219,1292,1371,1441,1559"}, "to": {"startLines": "69,70,104,106,108,165,166,259,260,262,263,267,268,271,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5464,5561,9345,9519,9693,13921,13998,22785,22877,23042,23113,23506,23586,23843,24373,24452,24522", "endColumns": "96,86,96,100,85,76,90,91,84,70,69,79,84,72,78,69,117", "endOffsets": "5556,5643,9437,9615,9774,13993,14084,22872,22957,23108,23178,23581,23666,23911,24447,24517,24635"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,504,629,744,840,934,1057,1177,1286,1411,1567,1692,1814,1916,2010,2134,2224,2325,2436,2539,2641,2760,2877,3000,3120,3229,3344,3465,3588,3704,3822,3909,3997,4114,4253,4419", "endColumns": "106,100,96,93,124,114,95,93,122,119,108,124,155,124,121,101,93,123,89,100,110,102,101,118,116,122,119,108,114,120,122,115,117,86,87,116,138,165,90", "endOffsets": "207,308,405,499,624,739,835,929,1052,1172,1281,1406,1562,1687,1809,1911,2005,2129,2219,2320,2431,2534,2636,2755,2872,2995,3115,3224,3339,3460,3583,3699,3817,3904,3992,4109,4248,4414,4505"}, "to": {"startLines": "168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14152,14259,14360,14457,14551,14676,14791,14887,14981,15104,15224,15333,15458,15614,15739,15861,15963,16057,16181,16271,16372,16483,16586,16688,16807,16924,17047,17167,17276,17391,17512,17635,17751,17869,17956,18044,18161,18300,23183", "endColumns": "106,100,96,93,124,114,95,93,122,119,108,124,155,124,121,101,93,123,89,100,110,102,101,118,116,122,119,108,114,120,122,115,117,86,87,116,138,165,90", "endOffsets": "14254,14355,14452,14546,14671,14786,14882,14976,15099,15219,15328,15453,15609,15734,15856,15958,16052,16176,16266,16367,16478,16581,16683,16802,16919,17042,17162,17271,17386,17507,17630,17746,17864,17951,18039,18156,18295,18461,23269"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "59,60,61,62,63,64,65,273", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4432,4530,4632,4729,4833,4937,5042,24000", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "4525,4627,4724,4828,4932,5037,5153,24096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,343,495,664,750", "endColumns": "70,86,79,151,168,85,81", "endOffsets": "171,258,338,490,659,745,827"}, "to": {"startLines": "72,161,261,266,275,293,294", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5728,13594,22962,23354,24204,25946,26032", "endColumns": "70,86,79,151,168,85,81", "endOffsets": "5794,13676,23037,23501,24368,26027,26109"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,190,255,326,404,476,563,646", "endColumns": "73,60,64,70,77,71,86,82,72", "endOffsets": "124,185,250,321,399,471,558,641,714"}, "to": {"startLines": "134,135,136,137,138,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11808,11882,11943,12008,12079,12157,12229,12316,12399", "endColumns": "73,60,64,70,77,71,86,82,72", "endOffsets": "11877,11938,12003,12074,12152,12224,12311,12394,12467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,162", "endColumns": "106,122", "endOffsets": "157,280"}, "to": {"startLines": "257,258", "startColumns": "4,4", "startOffsets": "22555,22662", "endColumns": "106,122", "endOffsets": "22657,22780"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,556,817,898,980,1062,1151,1242,1312,1378,1471,1565,1633,1697,1760,1832,1939,2049,2158,2234,2321,2394,2465,2556,2648,2715,2780,2833,2891,2939,3000,3066,3130,3193,3258,3322,3383,3449,3514,3580,3632,3694,3770,3846", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,80,81,81,88,90,69,65,92,93,67,63,62,71,106,109,108,75,86,72,70,90,91,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "282,551,812,893,975,1057,1146,1237,1307,1373,1466,1560,1628,1692,1755,1827,1934,2044,2153,2229,2316,2389,2460,2551,2643,2710,2775,2828,2886,2934,2995,3061,3125,3188,3253,3317,3378,3444,3509,3575,3627,3689,3765,3841,3897"}, "to": {"startLines": "2,11,16,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,606,9845,9926,10008,10090,10179,10270,10340,10406,10499,10593,10661,10725,10788,10860,10967,11077,11186,11262,11349,11422,11493,11584,11676,11743,12472,12525,12583,12631,12692,12758,12822,12885,12950,13014,13075,13141,13206,13272,13324,13386,13462,13538", "endLines": "10,15,20,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160", "endColumns": "17,12,12,80,81,81,88,90,69,65,92,93,67,63,62,71,106,109,108,75,86,72,70,90,91,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "332,601,862,9921,10003,10085,10174,10265,10335,10401,10494,10588,10656,10720,10783,10855,10962,11072,11181,11257,11344,11417,11488,11579,11671,11738,11803,12520,12578,12626,12687,12753,12817,12880,12945,13009,13070,13136,13201,13267,13319,13381,13457,13533,13589"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,276,356,450,581,662,728,820,888,951,1054,1120,1176,1247,1307,1361,1473,1530,1591,1645,1721,1846,1932,2015,2123,2204,2287,2375,2442,2508,2582,2660,2749,2824,2900,2975,3046,3136,3209,3301,3397,3469,3545,3641,3694,3761,3848,3935,3997,4061,4124,4229,4333,4429,4536", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,79,93,130,80,65,91,67,62,102,65,55,70,59,53,111,56,60,53,75,124,85,82,107,80,82,87,66,65,73,77,88,74,75,74,70,89,72,91,95,71,75,95,52,66,86,86,61,63,62,104,103,95,106,79", "endOffsets": "271,351,445,576,657,723,815,883,946,1049,1115,1171,1242,1302,1356,1468,1525,1586,1640,1716,1841,1927,2010,2118,2199,2282,2370,2437,2503,2577,2655,2744,2819,2895,2970,3041,3131,3204,3296,3392,3464,3540,3636,3689,3756,3843,3930,3992,4056,4119,4224,4328,4424,4531,4611"}, "to": {"startLines": "21,58,66,67,68,109,163,164,167,206,207,208,209,210,211,212,213,214,215,216,217,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,265", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "867,4352,5158,5252,5383,9779,13761,13853,14089,18466,18569,18635,18691,18762,18822,18876,18988,19045,19106,19160,19236,19865,19951,20034,20142,20223,20306,20394,20461,20527,20601,20679,20768,20843,20919,20994,21065,21155,21228,21320,21416,21488,21564,21660,21713,21780,21867,21954,22016,22080,22143,22248,22352,22448,23274", "endLines": "25,58,66,67,68,109,163,164,167,206,207,208,209,210,211,212,213,214,215,216,217,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,265", "endColumns": "12,79,93,130,80,65,91,67,62,102,65,55,70,59,53,111,56,60,53,75,124,85,82,107,80,82,87,66,65,73,77,88,74,75,74,70,89,72,91,95,71,75,95,52,66,86,86,61,63,62,104,103,95,106,79", "endOffsets": "1083,4427,5247,5378,5459,9840,13848,13916,14147,18564,18630,18686,18757,18817,18871,18983,19040,19101,19155,19231,19356,19946,20029,20137,20218,20301,20389,20456,20522,20596,20674,20763,20838,20914,20989,21060,21150,21223,21315,21411,21483,21559,21655,21708,21775,21862,21949,22011,22075,22138,22243,22347,22443,22550,23349"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,275,398,508,588,678,786,918,1033,1173,1254,1350,1441,1535,1647,1768,1869,2006,2142,2271,2447,2568,2684,2806,2925,3017,3111,3224,3350,3446,3544,3649,3786,3931,4036,4134,4207,4287,4372,4456,4559,4635,4714,4807,4906,4995,5089,5172,5276,5369,5466,5595,5671,5772", "endColumns": "109,109,122,109,79,89,107,131,114,139,80,95,90,93,111,120,100,136,135,128,175,120,115,121,118,91,93,112,125,95,97,104,136,144,104,97,72,79,84,83,102,75,78,92,98,88,93,82,103,92,96,128,75,100,92", "endOffsets": "160,270,393,503,583,673,781,913,1028,1168,1249,1345,1436,1530,1642,1763,1864,2001,2137,2266,2442,2563,2679,2801,2920,3012,3106,3219,3345,3441,3539,3644,3781,3926,4031,4129,4202,4282,4367,4451,4554,4630,4709,4802,4901,4990,5084,5167,5271,5364,5461,5590,5666,5767,5860"}, "to": {"startLines": "54,55,56,57,71,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,107,162,269,272,274,279,280,281,282,283,284,285,286,287,288,289,290,291,292", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3899,4009,4119,4242,5648,5799,5889,5997,6129,6244,6384,6465,6561,6652,6746,6858,6979,7080,7217,7353,7482,7658,7779,7895,8017,8136,8228,8322,8435,8561,8657,8755,8860,8997,9142,9247,9620,13681,23671,23916,24101,24640,24716,24795,24888,24987,25076,25170,25253,25357,25450,25547,25676,25752,25853", "endColumns": "109,109,122,109,79,89,107,131,114,139,80,95,90,93,111,120,100,136,135,128,175,120,115,121,118,91,93,112,125,95,97,104,136,144,104,97,72,79,84,83,102,75,78,92,98,88,93,82,103,92,96,128,75,100,92", "endOffsets": "4004,4114,4237,4347,5723,5884,5992,6124,6239,6379,6460,6556,6647,6741,6853,6974,7075,7212,7348,7477,7653,7774,7890,8012,8131,8223,8317,8430,8556,8652,8750,8855,8992,9137,9242,9340,9688,13756,23751,23995,24199,24711,24790,24883,24982,25071,25165,25248,25352,25445,25542,25671,25747,25848,25941"}}]}]}