{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-91:/values-az/values-az.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "56,57,58,59,60,61,62,270", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4211,4312,4414,4517,4621,4722,4827,23661", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "4307,4409,4512,4616,4717,4822,4933,23757"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,263,342,474,643,727", "endColumns": "73,83,78,131,168,83,78", "endOffsets": "174,258,337,469,638,722,801"}, "to": {"startLines": "69,158,258,263,272,290,291", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5503,13382,22646,23036,23863,25598,25682", "endColumns": "73,83,78,131,168,83,78", "endOffsets": "5572,13461,22720,23163,24027,25677,25756"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,186,249,314,392,459,548,641", "endColumns": "69,60,62,64,77,66,88,92,70", "endOffsets": "120,181,244,309,387,454,543,636,707"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11620,11690,11751,11814,11879,11957,12024,12113,12206", "endColumns": "69,60,62,64,77,66,88,92,70", "endOffsets": "11685,11746,11809,11874,11952,12019,12108,12201,12272"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,426,514,621,735,817,895,986,1079,1173,1272,1372,1465,1560,1654,1745,1837,1922,2027,2133,2233,2342,2447,2549,2707,2813", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "210,311,421,509,616,730,812,890,981,1074,1168,1267,1367,1460,1555,1649,1740,1832,1917,2022,2128,2228,2337,2442,2544,2702,2808,2892"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "875,985,1086,1196,1284,1391,1505,1587,1665,1756,1849,1943,2042,2142,2235,2330,2424,2515,2607,2692,2797,2903,3003,3112,3217,3319,3477,23422", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "980,1081,1191,1279,1386,1500,1582,1660,1751,1844,1938,2037,2137,2230,2325,2419,2510,2602,2687,2792,2898,2998,3107,3212,3314,3472,3578,23501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,293,390,491,582,663,751,843,925,994,1061,1142,1231,1303,1384,1450", "endColumns": "99,87,96,100,90,80,87,91,81,68,66,80,88,71,80,65,116", "endOffsets": "200,288,385,486,577,658,746,838,920,989,1056,1137,1226,1298,1379,1445,1562"}, "to": {"startLines": "66,67,101,103,105,162,163,256,257,259,260,264,265,268,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5230,5330,9091,9264,9437,13711,13792,22472,22564,22725,22794,23168,23249,23506,24032,24113,24179", "endColumns": "99,87,96,100,90,80,87,91,81,68,66,80,88,71,80,65,116", "endOffsets": "5325,5413,9183,9360,9523,13787,13875,22559,22641,22789,22856,23244,23333,23573,24108,24174,24291"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,511,630,745,844,937,1071,1202,1314,1442,1587,1718,1846,1957,2053,2190,2287,2398,2504,2612,2709,2823,2934,3055,3173,3278,3386,3502,3611,3723,3827,3914,3999,4104,4241,4398", "endColumns": "106,100,99,97,118,114,98,92,133,130,111,127,144,130,127,110,95,136,96,110,105,107,96,113,110,120,117,104,107,115,108,111,103,86,84,104,136,156,94", "endOffsets": "207,308,408,506,625,740,839,932,1066,1197,1309,1437,1582,1713,1841,1952,2048,2185,2282,2393,2499,2607,2704,2818,2929,3050,3168,3273,3381,3497,3606,3718,3822,3909,3994,4099,4236,4393,4488"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13939,14046,14147,14247,14345,14464,14579,14678,14771,14905,15036,15148,15276,15421,15552,15680,15791,15887,16024,16121,16232,16338,16446,16543,16657,16768,16889,17007,17112,17220,17336,17445,17557,17661,17748,17833,17938,18075,22861", "endColumns": "106,100,99,97,118,114,98,92,133,130,111,127,144,130,127,110,95,136,96,110,105,107,96,113,110,120,117,104,107,115,108,111,103,86,84,104,136,156,94", "endOffsets": "14041,14142,14242,14340,14459,14574,14673,14766,14900,15031,15143,15271,15416,15547,15675,15786,15882,16019,16116,16227,16333,16441,16538,16652,16763,16884,17002,17107,17215,17331,17440,17552,17656,17743,17828,17933,18070,18227,22951"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,131,207,278,346,423,504,596", "endColumns": "75,75,70,67,76,80,91,95", "endOffsets": "126,202,273,341,418,499,591,687"}, "to": {"startLines": "50,102,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3583,9188,19126,19197,19265,19342,19423,19515", "endColumns": "75,75,70,67,76,80,91,95", "endOffsets": "3654,9259,19192,19260,19337,19418,19510,19606"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,162", "endColumns": "106,118", "endOffsets": "157,276"}, "to": {"startLines": "254,255", "startColumns": "4,4", "startOffsets": "22246,22353", "endColumns": "106,118", "endOffsets": "22348,22467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,476,660,748,837,914,1006,1094,1170,1234,1325,1416,1481,1546,1608,1676,1804,1936,2062,2133,2214,2284,2360,2456,2553,2622,2688,2741,2799,2847,2908,2972,3044,3103,3166,3229,3289,3355,3419,3485,3537,3595,3667,3739", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,87,88,76,91,87,75,63,90,90,64,64,61,67,127,131,125,70,80,69,75,95,96,68,65,52,57,47,60,63,71,58,62,62,59,65,63,65,51,57,71,71,53", "endOffsets": "280,471,655,743,832,909,1001,1089,1165,1229,1320,1411,1476,1541,1603,1671,1799,1931,2057,2128,2209,2279,2355,2451,2548,2617,2683,2736,2794,2842,2903,2967,3039,3098,3161,3224,3284,3350,3414,3480,3532,3590,3662,3734,3788"}, "to": {"startLines": "2,11,15,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,526,9592,9680,9769,9846,9938,10026,10102,10166,10257,10348,10413,10478,10540,10608,10736,10868,10994,11065,11146,11216,11292,11388,11485,11554,12277,12330,12388,12436,12497,12561,12633,12692,12755,12818,12878,12944,13008,13074,13126,13184,13256,13328", "endLines": "10,14,18,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "endColumns": "17,12,12,87,88,76,91,87,75,63,90,90,64,64,61,67,127,131,125,70,80,69,75,95,96,68,65,52,57,47,60,63,71,58,62,62,59,65,63,65,51,57,71,71,53", "endOffsets": "330,521,705,9675,9764,9841,9933,10021,10097,10161,10252,10343,10408,10473,10535,10603,10731,10863,10989,11060,11141,11211,11287,11383,11480,11549,11615,12325,12383,12431,12492,12556,12628,12687,12750,12813,12873,12939,13003,13069,13121,13179,13251,13323,13377"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-az\\values-az.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,300,396,512,592,656,750,818,877,972,1036,1095,1162,1225,1279,1394,1452,1514,1568,1639,1771,1855,1935,2039,2115,2191,2275,2342,2408,2478,2556,2639,2709,2785,2863,2934,3020,3103,3196,3289,3362,3434,3528,3582,3649,3733,3821,3885,3950,4014,4116,4213,4309,4406", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,79,95,115,79,63,93,67,58,94,63,58,66,62,53,114,57,61,53,70,131,83,79,103,75,75,83,66,65,69,77,82,69,75,77,70,85,82,92,92,72,71,93,53,66,83,87,63,64,63,101,96,95,96,79", "endOffsets": "215,295,391,507,587,651,745,813,872,967,1031,1090,1157,1220,1274,1389,1447,1509,1563,1634,1766,1850,1930,2034,2110,2186,2270,2337,2403,2473,2551,2634,2704,2780,2858,2929,3015,3098,3191,3284,3357,3429,3523,3577,3644,3728,3816,3880,3945,4009,4111,4208,4304,4401,4481"}, "to": {"startLines": "19,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "710,4131,4938,5034,5150,9528,13549,13643,13880,18232,18327,18391,18450,18517,18580,18634,18749,18807,18869,18923,18994,19611,19695,19775,19879,19955,20031,20115,20182,20248,20318,20396,20479,20549,20625,20703,20774,20860,20943,21036,21129,21202,21274,21368,21422,21489,21573,21661,21725,21790,21854,21956,22053,22149,22956", "endLines": "22,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "endColumns": "12,79,95,115,79,63,93,67,58,94,63,58,66,62,53,114,57,61,53,70,131,83,79,103,75,75,83,66,65,69,77,82,69,75,77,70,85,82,92,92,72,71,93,53,66,83,87,63,64,63,101,96,95,96,79", "endOffsets": "870,4206,5029,5145,5225,9587,13638,13706,13934,18322,18386,18445,18512,18575,18629,18744,18802,18864,18918,18989,19121,19690,19770,19874,19950,20026,20110,20177,20243,20313,20391,20474,20544,20620,20698,20769,20855,20938,21031,21124,21197,21269,21363,21417,21484,21568,21656,21720,21785,21849,21951,22048,22144,22241,23031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,179,297,406,527,612,710,826,958,1075,1217,1298,1397,1484,1578,1686,1804,1908,2043,2176,2302,2462,2583,2694,2809,2922,3010,3105,3218,3346,3448,3549,3651,3784,3923,4029,4126,4198,4281,4365,4448,4549,4625,4705,4801,4898,4991,5085,5169,5269,5365,5462,5583,5659,5757", "endColumns": "123,117,108,120,84,97,115,131,116,141,80,98,86,93,107,117,103,134,132,125,159,120,110,114,112,87,94,112,127,101,100,101,132,138,105,96,71,82,83,82,100,75,79,95,96,92,93,83,99,95,96,120,75,97,93", "endOffsets": "174,292,401,522,607,705,821,953,1070,1212,1293,1392,1479,1573,1681,1799,1903,2038,2171,2297,2457,2578,2689,2804,2917,3005,3100,3213,3341,3443,3544,3646,3779,3918,4024,4121,4193,4276,4360,4443,4544,4620,4700,4796,4893,4986,5080,5164,5264,5360,5457,5578,5654,5752,5846"}, "to": {"startLines": "51,52,53,54,68,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,104,159,266,269,271,276,277,278,279,280,281,282,283,284,285,286,287,288,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3659,3783,3901,4010,5418,5577,5675,5791,5923,6040,6182,6263,6362,6449,6543,6651,6769,6873,7008,7141,7267,7427,7548,7659,7774,7887,7975,8070,8183,8311,8413,8514,8616,8749,8888,8994,9365,13466,23338,23578,23762,24296,24372,24452,24548,24645,24738,24832,24916,25016,25112,25209,25330,25406,25504", "endColumns": "123,117,108,120,84,97,115,131,116,141,80,98,86,93,107,117,103,134,132,125,159,120,110,114,112,87,94,112,127,101,100,101,132,138,105,96,71,82,83,82,100,75,79,95,96,92,93,83,99,95,96,120,75,97,93", "endOffsets": "3778,3896,4005,4126,5498,5670,5786,5918,6035,6177,6258,6357,6444,6538,6646,6764,6868,7003,7136,7262,7422,7543,7654,7769,7882,7970,8065,8178,8306,8408,8509,8611,8744,8883,8989,9086,9432,13544,23417,23656,23858,24367,24447,24543,24640,24733,24827,24911,25011,25107,25204,25325,25401,25499,25593"}}]}]}