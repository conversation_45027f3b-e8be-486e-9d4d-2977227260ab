{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-91:/values-v21/values-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8283,8468,11238,11435,11634,11757,11880,11993,12176,12431,12632,12721,12832,13065,13166,13261,13384,13513,13630,13807,13906,14041,14184,14319,14438,14639,14758,14851,14962,15018,15125,15320,15431,15564,15659,15750,15841,15934,16051,16190,16261,16344,16967,17024,17082,17706", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8278,8463,11233,11430,11629,11752,11875,11988,12171,12426,12627,12716,12827,13060,13161,13256,13379,13508,13625,13802,13901,14036,14179,14314,14433,14634,14753,14846,14957,15013,15120,15315,15426,15559,15654,15745,15836,15929,16046,16185,16256,16339,16962,17019,17077,17701,18337"}, "to": {"startLines": "18,19,20,21,22,23,24,25,26,27,28,29,30,31,33,35,36,37,38,40,42,43,44,45,46,48,50,52,54,56,58,59,64,66,68,69,70,72,74,75,76,77,82,92,135,138,181,196,202,204,206,208,211,215,218,219,220,223,224,225,226,227,228,231,232,234,236,238,240,244,246,247,248,249,251,255,257,259,260,261,262,263,264,297,298,299,309,310,311,323", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1339,1430,1533,1636,1741,1848,1957,2066,2175,2284,2393,2500,2603,2722,2877,3032,3137,3258,3359,3506,3647,3750,3869,3976,4079,4234,4405,4554,4719,4876,5027,5146,5497,5646,5795,5907,6054,6207,6354,6429,6518,6605,7130,7976,10734,10919,13689,14822,15252,15375,15498,15611,15794,16049,16250,16339,16450,16683,16784,16879,17002,17131,17248,17425,17524,17659,17802,17937,18056,18257,18376,18469,18580,18636,18743,18938,19049,19182,19277,19368,19459,19552,19669,22188,22259,22342,22965,23022,23080,23704", "endLines": "18,19,20,21,22,23,24,25,26,27,28,29,30,32,34,35,36,37,39,41,42,43,44,45,47,49,51,53,55,57,58,63,65,67,68,69,71,73,74,75,76,77,82,134,137,180,183,198,203,205,207,210,214,217,218,219,222,223,224,225,226,227,230,231,233,235,237,239,243,245,246,247,248,250,254,256,258,259,260,261,262,263,265,297,298,308,309,310,322,334", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "1425,1528,1631,1736,1843,1952,2061,2170,2279,2388,2495,2598,2717,2872,3027,3132,3253,3354,3501,3642,3745,3864,3971,4074,4229,4400,4549,4714,4871,5022,5141,5492,5641,5790,5902,6049,6202,6349,6424,6513,6600,6701,7228,10729,10914,13684,13881,15016,15370,15493,15606,15789,16044,16245,16334,16445,16678,16779,16874,16997,17126,17243,17420,17519,17654,17797,17932,18051,18252,18371,18464,18575,18631,18738,18933,19044,19177,19272,19363,19454,19547,19664,19803,22254,22337,22960,23017,23075,23699,24335"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,114", "endLines": "2,7", "endColumns": "58,10", "endOffsets": "109,396"}, "to": {"startLines": "3,275", "startColumns": "4,4", "startOffsets": "173,20415", "endLines": "3,279", "endColumns": "58,10", "endOffsets": "227,20697"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,19,22,25,28,31,34,37,40,41,44,49,60,66,72,78,84,90,91,92,93,97,100,103,106,109,113,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,202,290,378,466,554,641,728,815,902,995,1102,1207,1326,1539,1798,2069,2287,2519,2755,3005,3236,3352,3522,3843,4872,5329,5671,6015,6365,6715,6853,6997,7153,7546,7764,7986,8212,8428,8669,8928", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,18,21,24,27,30,33,36,39,40,43,48,59,65,71,77,83,89,90,91,92,96,99,102,105,108,112,116,119", "endColumns": "67,78,87,87,87,87,86,86,86,86,92,106,104,118,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,137,143,155,10,10,10,10,10,10,10,10", "endOffsets": "118,197,285,373,461,549,636,723,810,897,990,1097,1202,1321,1534,1793,2064,2282,2514,2750,3000,3231,3347,3517,3838,4867,5324,5666,6010,6360,6710,6848,6992,7148,7541,7759,7981,8207,8423,8664,8923,9100"}, "to": {"startLines": "4,5,6,7,8,9,10,11,12,13,78,79,80,81,83,86,89,184,187,190,193,199,266,267,270,280,291,358,364,370,376,429,430,431,432,436,439,442,445,464,468,472", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "232,300,379,467,555,643,731,818,905,992,6706,6799,6906,7011,7233,7446,7705,13886,14104,14336,14572,15021,19808,19924,20094,20702,21731,25925,26217,26511,26811,30397,30535,30679,30835,31228,31446,31668,31894,33096,33337,33596", "endLines": "4,5,6,7,8,9,10,11,12,13,78,79,80,81,85,88,91,186,189,192,195,201,266,269,274,290,296,363,369,375,381,429,430,431,435,438,441,444,447,467,471,474", "endColumns": "67,78,87,87,87,87,86,86,86,86,92,106,104,118,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,137,143,155,10,10,10,10,10,10,10,10", "endOffsets": "295,374,462,550,638,726,813,900,987,1074,6794,6901,7006,7125,7441,7700,7971,14099,14331,14567,14817,15247,19919,20089,20410,21726,22183,26212,26506,26806,27106,30530,30674,30830,31223,31441,31663,31889,32105,33332,33591,33768"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,14,15,16,341,342,349,353,448,451", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,1079,1143,1210,24674,24790,25247,25541,32110,32282", "endLines": "2,14,15,16,341,342,349,353,450,455", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,1138,1205,1269,24785,24911,25368,25664,32277,32629"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,6", "startColumns": "4,4,4", "startOffsets": "55,120,276", "endLines": "2,5,8", "endColumns": "64,12,12", "endOffsets": "115,271,449"}, "to": {"startLines": "17,335,338", "startColumns": "4,4,4", "startOffsets": "1274,24340,24496", "endLines": "17,337,340", "endColumns": "64,12,12", "endOffsets": "1334,24491,24669"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\29f22966b2ce3d810e00200149526ea9\\transformed\\media-1.6.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,5,8,11", "startColumns": "4,4,4,4", "startOffsets": "55,223,386,554", "endLines": "4,7,10,13", "endColumns": "12,12,12,12", "endOffsets": "218,381,549,716"}, "to": {"startLines": "343,346,350,354", "startColumns": "4,4,4,4", "startOffsets": "24916,25084,25373,25669", "endLines": "345,348,352,356", "endColumns": "12,12,12,12", "endOffsets": "25079,25242,25536,25831"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,7,13,17,20,25,29,52,55,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,418,933,1169,1377,1801,2095,3791,3989,4180", "endLines": "2,6,12,16,19,24,28,51,54,58,59", "endColumns": "88,12,12,12,12,12,12,12,12,12,72", "endOffsets": "139,413,928,1164,1372,1796,2090,3786,3984,4175,4248"}, "to": {"startLines": "357,382,386,392,396,399,404,408,456,459,463", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25836,27111,27385,27900,28136,28344,28768,29062,32634,32832,33023", "endLines": "357,385,391,395,398,403,407,428,458,462,463", "endColumns": "88,12,12,12,12,12,12,12,12,12,72", "endOffsets": "25920,27380,27895,28131,28339,28763,29057,30392,32827,33018,33091"}}]}]}