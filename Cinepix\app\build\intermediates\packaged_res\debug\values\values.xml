<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="background">#0F0F0F</color>
    <color name="black">#FF000000</color>
    <color name="error">#F44336</color>
    <color name="ic_launcher_background">#070707</color>
    <color name="on_background">#E6E1E5</color>
    <color name="on_error">#FFFFFF</color>
    <color name="on_primary">#FFFFFF</color>
    <color name="on_primary_container">#001C38</color>
    <color name="on_secondary">#FFFFFF</color>
    <color name="on_secondary_container">#2C1600</color>
    <color name="on_surface">#E6E1E5</color>
    <color name="on_surface_variant">#CAC4D0</color>
    <color name="player_background">#000000</color>
    <color name="player_controls">#80000000</color>
    <color name="player_progress">#1976D2</color>
    <color name="primary">#1976D2</color>
    <color name="primary_container">#D3E3FD</color>
    <color name="secondary">#FF5722</color>
    <color name="secondary_container">#FFDBCC</color>
    <color name="surface">#0F0F0F</color>
    <color name="surface_variant">#1F1F1F</color>
    <color name="transparent">#00000000</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">Cinepix</string>
    <string name="cancel">Cancel</string>
    <string name="default_web_client_id" translatable="false">947051763394-mge9js50a7eia3euklvnchd9lnsgmn45.apps.googleusercontent.com</string>
    <string name="download_cancel">Cancel Download</string>
    <string name="download_completed">Download Completed</string>
    <string name="download_failed">Download Failed</string>
    <string name="download_pause">Pause Download</string>
    <string name="download_resume">Resume Download</string>
    <string name="download_start">Start Download</string>
    <string name="error">Error</string>
    <string name="gcm_defaultSenderId" translatable="false">947051763394</string>
    <string name="google_api_key" translatable="false">AIzaSyChB9LUCuGmXzsxG6gMNxnY_8elZmP4zXg</string>
    <string name="google_app_id" translatable="false">1:947051763394:android:d610ac95206f072a2b4d5b</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyChB9LUCuGmXzsxG6gMNxnY_8elZmP4zXg</string>
    <string name="google_storage_bucket" translatable="false">neildirve2022.firebasestorage.app</string>
    <string name="loading">Loading...</string>
    <string name="nav_downloads">Downloads</string>
    <string name="nav_favorites">Favorites</string>
    <string name="nav_home">Home</string>
    <string name="nav_live_tv">Live TV</string>
    <string name="nav_movies">Movies</string>
    <string name="nav_series">Series</string>
    <string name="nav_settings">Settings</string>
    <string name="no_internet">No Internet Connection</string>
    <string name="ok">OK</string>
    <string name="player_audio">Audio</string>
    <string name="player_exit_fullscreen">Exit Fullscreen</string>
    <string name="player_fullscreen">Fullscreen</string>
    <string name="player_next">Next</string>
    <string name="player_pause">Pause</string>
    <string name="player_play">Play</string>
    <string name="player_previous">Previous</string>
    <string name="player_quality">Quality</string>
    <string name="player_settings">Player Settings</string>
    <string name="player_speed">Playback Speed</string>
    <string name="player_subtitle">Subtitles</string>
    <string name="premium_required">Premium Membership Required</string>
    <string name="project_id" translatable="false">neildirve2022</string>
    <string name="retry">Retry</string>
    <string name="search">Search</string>
    <string name="tv_app_description">Watch movies and TV shows on your Android TV</string>
    <style name="Theme.Cinepix" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorOnPrimary">@color/on_primary</item>
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorOnSecondary">@color/on_secondary</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnSurface">@color/on_surface</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style>
    <style name="Theme.Cinepix.Fullscreen" parent="Theme.Cinepix">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowBackground">@android:color/black</item>
    </style>
    <style name="Theme.Cinepix.Leanback" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorAccent">@color/secondary</item>
        <item name="android:windowBackground">@color/surface</item>
    </style>
</resources>