<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res"><file name="ic_download" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\drawable\ic_download.xml" qualifiers="" type="drawable"/><file name="ic_download_24" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\drawable\ic_download_24.xml" qualifiers="" type="drawable"/><file name="ic_download_done" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\drawable\ic_download_done.xml" qualifiers="" type="drawable"/><file name="ic_error" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\drawable\ic_error.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_notification" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\drawable\ic_notification.xml" qualifiers="" type="drawable"/><file name="tv_banner" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\drawable\tv_banner.xml" qualifiers="" type="drawable"/><file name="activity_tv_main" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\layout\activity_tv_main.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\values\colors.xml" qualifiers=""><color name="primary">#1976D2</color><color name="on_primary">#FFFFFF</color><color name="primary_container">#D3E3FD</color><color name="on_primary_container">#001C38</color><color name="secondary">#FF5722</color><color name="on_secondary">#FFFFFF</color><color name="secondary_container">#FFDBCC</color><color name="on_secondary_container">#2C1600</color><color name="surface">#0F0F0F</color><color name="on_surface">#E6E1E5</color><color name="surface_variant">#1F1F1F</color><color name="on_surface_variant">#CAC4D0</color><color name="background">#0F0F0F</color><color name="on_background">#E6E1E5</color><color name="error">#F44336</color><color name="on_error">#FFFFFF</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="transparent">#00000000</color><color name="player_background">#000000</color><color name="player_controls">#80000000</color><color name="player_progress">#1976D2</color></file><file path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#070707</color></file><file path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Cinepix</string><string name="nav_home">Home</string><string name="nav_movies">Movies</string><string name="nav_series">Series</string><string name="nav_live_tv">Live TV</string><string name="nav_downloads">Downloads</string><string name="nav_favorites">Favorites</string><string name="nav_settings">Settings</string><string name="player_play">Play</string><string name="player_pause">Pause</string><string name="player_next">Next</string><string name="player_previous">Previous</string><string name="player_fullscreen">Fullscreen</string><string name="player_exit_fullscreen">Exit Fullscreen</string><string name="player_settings">Player Settings</string><string name="player_subtitle">Subtitles</string><string name="player_audio">Audio</string><string name="player_quality">Quality</string><string name="player_speed">Playback Speed</string><string name="download_start">Start Download</string><string name="download_pause">Pause Download</string><string name="download_resume">Resume Download</string><string name="download_cancel">Cancel Download</string><string name="download_completed">Download Completed</string><string name="download_failed">Download Failed</string><string name="loading">Loading...</string><string name="error">Error</string><string name="retry">Retry</string><string name="ok">OK</string><string name="cancel">Cancel</string><string name="search">Search</string><string name="no_internet">No Internet Connection</string><string name="premium_required">Premium Membership Required</string><string name="tv_app_description">Watch movies and TV shows on your Android TV</string></file><file path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Cinepix" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorOnPrimary">@color/on_primary</item>
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorOnSecondary">@color/on_secondary</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnSurface">@color/on_surface</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style><style name="Theme.Cinepix.Fullscreen" parent="Theme.Cinepix">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowBackground">@android:color/black</item>
    </style><style name="Theme.Cinepix.Leanback" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorAccent">@color/secondary</item>
        <item name="android:windowBackground">@color/surface</item>
    </style></file><file name="backup_rules" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\xampp\htdocs\deshiflix\Cinepix\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\xampp\htdocs\deshiflix\Cinepix\app\build\generated\res\resValues\debug"/><source path="D:\xampp\htdocs\deshiflix\Cinepix\app\build\generated\res\processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\xampp\htdocs\deshiflix\Cinepix\app\build\generated\res\resValues\debug"/><source path="D:\xampp\htdocs\deshiflix\Cinepix\app\build\generated\res\processDebugGoogleServices"><file path="D:\xampp\htdocs\deshiflix\Cinepix\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="default_web_client_id" translatable="false">947051763394-mge9js50a7eia3euklvnchd9lnsgmn45.apps.googleusercontent.com</string><string name="gcm_defaultSenderId" translatable="false">947051763394</string><string name="google_api_key" translatable="false">AIzaSyChB9LUCuGmXzsxG6gMNxnY_8elZmP4zXg</string><string name="google_app_id" translatable="false">1:947051763394:android:d610ac95206f072a2b4d5b</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyChB9LUCuGmXzsxG6gMNxnY_8elZmP4zXg</string><string name="google_storage_bucket" translatable="false">neildirve2022.firebasestorage.app</string><string name="project_id" translatable="false">neildirve2022</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>