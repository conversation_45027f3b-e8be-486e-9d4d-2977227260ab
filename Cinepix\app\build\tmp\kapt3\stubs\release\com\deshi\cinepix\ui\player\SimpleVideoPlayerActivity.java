package com.deshi.cinepix.ui.player;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b\u0002\u0018\u0000 \u000f2\u00020\u0001:\u0001\u000fB\u0005\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0005\u001a\u00020\u00062\b\u0010\u0007\u001a\u0004\u0018\u00010\bH\u0014J\b\u0010\t\u001a\u00020\u0006H\u0014J\b\u0010\n\u001a\u00020\u0006H\u0002J\u0018\u0010\u000b\u001a\u00020\u00062\u0006\u0010\f\u001a\u00020\u00042\u0006\u0010\r\u001a\u00020\u000eH\u0002R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0010"}, d2 = {"Lcom/deshi/cinepix/ui/player/SimpleVideoPlayerActivity;", "Landroidx/activity/ComponentActivity;", "()V", "exoPlayer", "Landroidx/media3/exoplayer/ExoPlayer;", "onCreate", "", "savedInstanceState", "Landroid/os/Bundle;", "onDestroy", "setupFullscreen", "setupPlayer", "player", "videoUrl", "", "Companion", "app_release"})
public final class SimpleVideoPlayerActivity extends androidx.activity.ComponentActivity {
    @org.jetbrains.annotations.Nullable
    private androidx.media3.exoplayer.ExoPlayer exoPlayer;
    @org.jetbrains.annotations.NotNull
    private static final java.lang.String EXTRA_VIDEO_URL = "extra_video_url";
    @org.jetbrains.annotations.NotNull
    private static final java.lang.String EXTRA_VIDEO_TITLE = "extra_video_title";
    @org.jetbrains.annotations.NotNull
    public static final com.deshi.cinepix.ui.player.SimpleVideoPlayerActivity.Companion Companion = null;
    
    public SimpleVideoPlayerActivity() {
        super();
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupFullscreen() {
    }
    
    private final void setupPlayer(androidx.media3.exoplayer.ExoPlayer player, java.lang.String videoUrl) {
    }
    
    @java.lang.Override
    protected void onDestroy() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001e\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u00042\u0006\u0010\u000b\u001a\u00020\u0004R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/deshi/cinepix/ui/player/SimpleVideoPlayerActivity$Companion;", "", "()V", "EXTRA_VIDEO_TITLE", "", "EXTRA_VIDEO_URL", "createIntent", "Landroid/content/Intent;", "context", "Landroid/content/Context;", "videoUrl", "title", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.content.Intent createIntent(@org.jetbrains.annotations.NotNull
        android.content.Context context, @org.jetbrains.annotations.NotNull
        java.lang.String videoUrl, @org.jetbrains.annotations.NotNull
        java.lang.String title) {
            return null;
        }
    }
}