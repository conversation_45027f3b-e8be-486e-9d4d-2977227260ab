R_DEF: Internal format may change without notice
local
color background
color black
color error
color on_background
color on_error
color on_primary
color on_primary_container
color on_secondary
color on_secondary_container
color on_surface
color on_surface_variant
color player_background
color player_controls
color player_progress
color primary
color primary_container
color secondary
color secondary_container
color surface
color surface_variant
color transparent
color white
drawable ic_download
drawable ic_download_done
drawable ic_error
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable tv_banner
id main_browse_fragment
layout activity_tv_main
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
string cancel
string download_cancel
string download_completed
string download_failed
string download_pause
string download_resume
string download_start
string error
string loading
string nav_downloads
string nav_favorites
string nav_home
string nav_live_tv
string nav_movies
string nav_series
string nav_settings
string no_internet
string ok
string player_audio
string player_exit_fullscreen
string player_fullscreen
string player_next
string player_pause
string player_play
string player_previous
string player_quality
string player_settings
string player_speed
string player_subtitle
string premium_required
string retry
string search
string tv_app_description
style Theme.Cinepix
style Theme.Cinepix.Fullscreen
style Theme.Cinepix.Leanback
xml backup_rules
xml data_extraction_rules
xml file_paths
