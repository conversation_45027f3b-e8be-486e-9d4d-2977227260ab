package com.deshi.cinepix.ui.webview;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\u0018\u0000 \u001d2\u00020\u0001:\u0003\u001d\u001e\u001fB\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0011\u001a\u00020\u00042\u0006\u0010\u0012\u001a\u00020\u0013H\u0002J\u0012\u0010\u0014\u001a\u00020\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u0017H\u0014J\u0010\u0010\u0018\u001a\u00020\u00152\u0006\u0010\u000f\u001a\u00020\u0010H\u0003J\u0016\u0010\u0019\u001a\u00020\u00152\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u001a\u001a\u00020\u0013J\u0016\u0010\u001b\u001a\u00020\u00152\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u001c\u001a\u00020\u0013R+\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u00048B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\b\n\u0010\u000b\u001a\u0004\b\u0006\u0010\u0007\"\u0004\b\b\u0010\tR+\u0010\f\u001a\u00020\u00042\u0006\u0010\u0003\u001a\u00020\u00048B@BX\u0082\u008e\u0002\u00a2\u0006\u0012\n\u0004\b\u000e\u0010\u000b\u001a\u0004\b\f\u0010\u0007\"\u0004\b\r\u0010\tR\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u0010X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006 "}, d2 = {"Lcom/deshi/cinepix/ui/webview/SimpleWebViewActivity;", "Landroidx/activity/ComponentActivity;", "()V", "<set-?>", "", "canGoBack", "getCanGoBack", "()Z", "setCanGoBack", "(Z)V", "canGoBack$delegate", "Landroidx/compose/runtime/MutableState;", "isLoading", "setLoading", "isLoading$delegate", "webView", "Landroid/webkit/WebView;", "isDirectVideoUrl", "url", "", "onCreate", "", "savedInstanceState", "Landroid/os/Bundle;", "setupWebView", "startDownload", "filename", "startVideoPlayer", "title", "Companion", "SimpleWebChromeClient", "SimpleWebViewClient", "app_release"})
public final class SimpleWebViewActivity extends androidx.activity.ComponentActivity {
    @org.jetbrains.annotations.Nullable
    private android.webkit.WebView webView;
    @org.jetbrains.annotations.NotNull
    private final androidx.compose.runtime.MutableState isLoading$delegate = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.compose.runtime.MutableState canGoBack$delegate = null;
    @org.jetbrains.annotations.NotNull
    private static final java.lang.String EXTRA_URL = "extra_url";
    @org.jetbrains.annotations.NotNull
    private static final java.lang.String CINEPIX_BASE_URL = "https://cinepix.top";
    @org.jetbrains.annotations.NotNull
    public static final com.deshi.cinepix.ui.webview.SimpleWebViewActivity.Companion Companion = null;
    
    public SimpleWebViewActivity() {
        super();
    }
    
    private final boolean isLoading() {
        return false;
    }
    
    private final void setLoading(boolean p0) {
    }
    
    private final boolean getCanGoBack() {
        return false;
    }
    
    private final void setCanGoBack(boolean p0) {
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    @android.annotation.SuppressLint(value = {"SetJavaScriptEnabled"})
    private final void setupWebView(android.webkit.WebView webView) {
    }
    
    private final boolean isDirectVideoUrl(java.lang.String url) {
        return false;
    }
    
    public final void startVideoPlayer(@org.jetbrains.annotations.NotNull
    java.lang.String url, @org.jetbrains.annotations.NotNull
    java.lang.String title) {
    }
    
    public final void startDownload(@org.jetbrains.annotations.NotNull
    java.lang.String url, @org.jetbrains.annotations.NotNull
    java.lang.String filename) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u0004R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/deshi/cinepix/ui/webview/SimpleWebViewActivity$Companion;", "", "()V", "CINEPIX_BASE_URL", "", "EXTRA_URL", "createIntent", "Landroid/content/Intent;", "context", "Landroid/content/Context;", "url", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final android.content.Intent createIntent(@org.jetbrains.annotations.NotNull
        android.content.Context context, @org.jetbrains.annotations.NotNull
        java.lang.String url) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\b\u0082\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u001a\u0010\u0003\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0016\u00a8\u0006\t"}, d2 = {"Lcom/deshi/cinepix/ui/webview/SimpleWebViewActivity$SimpleWebChromeClient;", "Landroid/webkit/WebChromeClient;", "(Lcom/deshi/cinepix/ui/webview/SimpleWebViewActivity;)V", "onProgressChanged", "", "view", "Landroid/webkit/WebView;", "newProgress", "", "app_release"})
    final class SimpleWebChromeClient extends android.webkit.WebChromeClient {
        
        public SimpleWebChromeClient() {
            super();
        }
        
        @java.lang.Override
        public void onProgressChanged(@org.jetbrains.annotations.Nullable
        android.webkit.WebView view, int newProgress) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0082\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u001c\u0010\u0003\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u00062\b\u0010\u0007\u001a\u0004\u0018\u00010\bH\u0016J&\u0010\t\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u00062\b\u0010\u0007\u001a\u0004\u0018\u00010\b2\b\u0010\n\u001a\u0004\u0018\u00010\u000bH\u0016J\u001c\u0010\f\u001a\u00020\r2\b\u0010\u0005\u001a\u0004\u0018\u00010\u00062\b\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u0016\u00a8\u0006\u0010"}, d2 = {"Lcom/deshi/cinepix/ui/webview/SimpleWebViewActivity$SimpleWebViewClient;", "Landroid/webkit/WebViewClient;", "(Lcom/deshi/cinepix/ui/webview/SimpleWebViewActivity;)V", "onPageFinished", "", "view", "Landroid/webkit/WebView;", "url", "", "onPageStarted", "favicon", "Landroid/graphics/Bitmap;", "shouldOverrideUrlLoading", "", "request", "Landroid/webkit/WebResourceRequest;", "app_release"})
    final class SimpleWebViewClient extends android.webkit.WebViewClient {
        
        public SimpleWebViewClient() {
            super();
        }
        
        @java.lang.Override
        public void onPageStarted(@org.jetbrains.annotations.Nullable
        android.webkit.WebView view, @org.jetbrains.annotations.Nullable
        java.lang.String url, @org.jetbrains.annotations.Nullable
        android.graphics.Bitmap favicon) {
        }
        
        @java.lang.Override
        public void onPageFinished(@org.jetbrains.annotations.Nullable
        android.webkit.WebView view, @org.jetbrains.annotations.Nullable
        java.lang.String url) {
        }
        
        @java.lang.Override
        public boolean shouldOverrideUrlLoading(@org.jetbrains.annotations.Nullable
        android.webkit.WebView view, @org.jetbrains.annotations.Nullable
        android.webkit.WebResourceRequest request) {
            return false;
        }
    }
}