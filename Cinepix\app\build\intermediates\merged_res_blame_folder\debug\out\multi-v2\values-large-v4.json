{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-99:/values-large-v4/values-large-v4.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\de0470acc971f743e832aff97e9527de\\transformed\\appcompat-1.6.1\\res\\values-large-v4\\values-large-v4.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,185,256,326,396,464,532,636", "endColumns": "58,70,70,69,69,67,67,103,115", "endOffsets": "109,180,251,321,391,459,527,631,747"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\90e8727c8917f86c7d308f0c9abe80a3\\transformed\\material-1.4.0-beta01\\res\\values-large-v4\\values-large-v4.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,177", "endColumns": "121,133", "endOffsets": "172,306"}, "to": {"startLines": "11,12", "startColumns": "4,4", "startOffsets": "752,874", "endColumns": "121,133", "endOffsets": "869,1003"}}]}]}