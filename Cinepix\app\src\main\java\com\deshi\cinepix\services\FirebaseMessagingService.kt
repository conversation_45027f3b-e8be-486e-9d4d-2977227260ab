package com.deshi.cinepix.services

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.media.RingtoneManager
import android.os.Build
import androidx.core.app.NotificationCompat
import com.deshi.cinepix.R
import com.deshi.cinepix.MainActivity
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.InputStream
import java.net.HttpURLConnection
import java.net.URL

class CinepixFirebaseMessagingService : FirebaseMessagingService() {

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)

        // Handle FCM messages here
        remoteMessage.notification?.let { notification ->
            val title = notification.title ?: "Cinepix"
            val body = notification.body ?: ""
            val imageUrl = notification.imageUrl?.toString()
            val clickAction = remoteMessage.data["click_action"]

            sendNotification(title, body, imageUrl, clickAction)
        }

        // Handle data payload
        if (remoteMessage.data.isNotEmpty()) {
            val title = remoteMessage.data["title"] ?: "Cinepix"
            val body = remoteMessage.data["body"] ?: ""
            val imageUrl = remoteMessage.data["image"]
            val clickAction = remoteMessage.data["click_action"]

            sendNotification(title, body, imageUrl, clickAction)
        }
    }

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        
        // Send token to server
        sendTokenToServer(token)
        
        // Subscribe to topics
        subscribeToTopics()
    }

    private fun sendTokenToServer(token: String) {
        // Store token locally
        val sharedPref = getSharedPreferences("fcm_token", Context.MODE_PRIVATE)
        sharedPref.edit().putString("token", token).apply()
        
        // TODO: Send token to your server if needed
        // You can implement API call to send token to your WordPress backend
    }

    private fun subscribeToTopics() {
        com.google.firebase.messaging.FirebaseMessaging.getInstance().apply {
            subscribeToTopic("all_users")
            subscribeToTopic("android_users")
            
            // Subscribe based on user preferences
            val sharedPref = getSharedPreferences("notification_prefs", Context.MODE_PRIVATE)
            if (sharedPref.getBoolean("movies", true)) {
                subscribeToTopic("movies")
            }
            if (sharedPref.getBoolean("tvshows", true)) {
                subscribeToTopic("tvshows")
            }
            if (sharedPref.getBoolean("episodes", true)) {
                subscribeToTopic("episodes")
            }
        }
    }

    private fun sendNotification(title: String, body: String, imageUrl: String?, clickAction: String?) {
        val intent = createNotificationIntent(clickAction)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_ONE_SHOT or PendingIntent.FLAG_IMMUTABLE
        )

        val channelId = "cinepix_notifications"
        val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
        
        val notificationBuilder = NotificationCompat.Builder(this, channelId)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(body)
            .setAutoCancel(true)
            .setSound(defaultSoundUri)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setDefaults(NotificationCompat.DEFAULT_ALL)

        // Load and set large image if provided
        if (!imageUrl.isNullOrEmpty()) {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    val bitmap = getBitmapFromUrl(imageUrl)
                    bitmap?.let {
                        notificationBuilder.setLargeIcon(it)
                        notificationBuilder.setStyle(
                            NotificationCompat.BigPictureStyle()
                                .bigPicture(it)
                                .bigLargeIcon(null as Bitmap?)
                        )
                        showNotification(notificationBuilder)
                    } ?: run {
                        showNotification(notificationBuilder)
                    }
                } catch (e: Exception) {
                    showNotification(notificationBuilder)
                }
            }
        } else {
            showNotification(notificationBuilder)
        }
    }

    private fun createNotificationIntent(clickAction: String?): Intent {
        val intent = Intent(this, MainActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)

        // Handle different click actions
        clickAction?.let { action ->
            when {
                action.startsWith("movie_detail_") -> {
                    val movieId = action.removePrefix("movie_detail_")
                    intent.putExtra("action", "open_movie")
                    intent.putExtra("movie_id", movieId)
                }
                action.startsWith("tvshow_detail_") -> {
                    val tvshowId = action.removePrefix("tvshow_detail_")
                    intent.putExtra("action", "open_tvshow")
                    intent.putExtra("tvshow_id", tvshowId)
                }
                action.startsWith("episode_detail_") -> {
                    val episodeId = action.removePrefix("episode_detail_")
                    intent.putExtra("action", "open_episode")
                    intent.putExtra("episode_id", episodeId)
                }
                else -> {
                    intent.putExtra("action", "open_home")
                }
            }
        }

        return intent
    }

    private fun showNotification(notificationBuilder: NotificationCompat.Builder) {
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        // Create notification channel for Android O and above
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                "cinepix_notifications",
                "Cinepix Notifications",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for new movies, TV shows and episodes"
                enableLights(true)
                enableVibration(true)
            }
            notificationManager.createNotificationChannel(channel)
        }

        val notificationId = System.currentTimeMillis().toInt()
        notificationManager.notify(notificationId, notificationBuilder.build())
    }

    private fun getBitmapFromUrl(imageUrl: String): Bitmap? {
        return try {
            val url = URL(imageUrl)
            val connection = url.openConnection() as HttpURLConnection
            connection.doInput = true
            connection.connect()
            val inputStream: InputStream = connection.inputStream
            BitmapFactory.decodeStream(inputStream)
        } catch (e: Exception) {
            null
        }
    }
}
