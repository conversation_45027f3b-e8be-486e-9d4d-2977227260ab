{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-91:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,118,199,267,334,412,493,578", "endColumns": "62,80,67,66,77,80,84,90", "endOffsets": "113,194,262,329,407,488,573,664"}, "to": {"startLines": "54,106,219,220,221,222,223,224", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3727,9111,18697,18765,18832,18910,18991,19076", "endColumns": "62,80,67,66,77,80,84,90", "endOffsets": "3785,9187,18760,18827,18905,18986,19071,19162"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,280,394,505,579,670,780,914,1025,1161,1242,1336,1423,1515,1628,1744,1843,1975,2106,2223,2372,2486,2592,2704,2819,2907,3000,3108,3228,3322,3417,3517,3646,3783,3886,3981,4058,4131,4213,4294,4393,4469,4549,4646,4741,4828,4919,5001,5099,5194,5286,5407,5483,5580", "endColumns": "112,111,113,110,73,90,109,133,110,135,80,93,86,91,112,115,98,131,130,116,148,113,105,111,114,87,92,107,119,93,94,99,128,136,102,94,76,72,81,80,98,75,79,96,94,86,90,81,97,94,91,120,75,96,91", "endOffsets": "163,275,389,500,574,665,775,909,1020,1156,1237,1331,1418,1510,1623,1739,1838,1970,2101,2218,2367,2481,2587,2699,2814,2902,2995,3103,3223,3317,3412,3512,3641,3778,3881,3976,4053,4126,4208,4289,4388,4464,4544,4641,4736,4823,4914,4996,5094,5189,5281,5402,5478,5575,5667"}, "to": {"startLines": "55,56,57,58,72,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,108,163,270,273,275,280,281,282,283,284,285,286,287,288,289,290,291,292,293", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3790,3903,4015,4129,5473,5616,5707,5817,5951,6062,6198,6279,6373,6460,6552,6665,6781,6880,7012,7143,7260,7409,7523,7629,7741,7856,7944,8037,8145,8265,8359,8454,8554,8683,8820,8923,9287,13197,22795,23028,23210,23742,23818,23898,23995,24090,24177,24268,24350,24448,24543,24635,24756,24832,24929", "endColumns": "112,111,113,110,73,90,109,133,110,135,80,93,86,91,112,115,98,131,130,116,148,113,105,111,114,87,92,107,119,93,94,99,128,136,102,94,76,72,81,80,98,75,79,96,94,86,90,81,97,94,91,120,75,96,91", "endOffsets": "3898,4010,4124,4235,5542,5702,5812,5946,6057,6193,6274,6368,6455,6547,6660,6776,6875,7007,7138,7255,7404,7518,7624,7736,7851,7939,8032,8140,8260,8354,8449,8549,8678,8815,8918,9013,9359,13265,22872,23104,23304,23813,23893,23990,24085,24172,24263,24345,24443,24538,24630,24751,24827,24924,25016"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,278,371,466,549,626,711,797,876,942,1008,1086,1168,1237,1311,1382", "endColumns": "88,83,92,94,82,76,84,85,78,65,65,77,81,68,73,70,118", "endOffsets": "189,273,366,461,544,621,706,792,871,937,1003,1081,1163,1232,1306,1377,1496"}, "to": {"startLines": "70,71,105,107,109,166,167,260,261,263,264,268,269,272,277,278,279", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5300,5389,9018,9192,9364,13428,13505,21974,22060,22215,22281,22635,22713,22959,23478,23552,23623", "endColumns": "88,83,92,94,82,76,84,85,78,65,65,77,81,68,73,70,118", "endOffsets": "5384,5468,9106,9282,9442,13500,13585,22055,22134,22276,22342,22708,22790,23023,23547,23618,23737"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,319,413,505,627,735,830,928,1047,1165,1268,1391,1532,1650,1767,1873,1964,2089,2179,2282,2390,2497,2594,2712,2815,2922,3028,3136,3250,3372,3496,3613,3732,3819,3902,4008,4145,4300", "endColumns": "109,103,93,91,121,107,94,97,118,117,102,122,140,117,116,105,90,124,89,102,107,106,96,117,102,106,105,107,113,121,123,116,118,86,82,105,136,154,87", "endOffsets": "210,314,408,500,622,730,825,923,1042,1160,1263,1386,1527,1645,1762,1868,1959,2084,2174,2277,2385,2492,2589,2707,2810,2917,3023,3131,3245,3367,3491,3608,3727,3814,3897,4003,4140,4295,4383"}, "to": {"startLines": "169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,265", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13653,13763,13867,13961,14053,14175,14283,14378,14476,14595,14713,14816,14939,15080,15198,15315,15421,15512,15637,15727,15830,15938,16045,16142,16260,16363,16470,16576,16684,16798,16920,17044,17161,17280,17367,17450,17556,17693,22347", "endColumns": "109,103,93,91,121,107,94,97,118,117,102,122,140,117,116,105,90,124,89,102,107,106,96,117,102,106,105,107,113,121,123,116,118,86,82,105,136,154,87", "endOffsets": "13758,13862,13956,14048,14170,14278,14373,14471,14590,14708,14811,14934,15075,15193,15310,15416,15507,15632,15722,15825,15933,16040,16137,16255,16358,16465,16571,16679,16793,16915,17039,17156,17275,17362,17445,17551,17688,17843,22430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,258,334,459,628,709", "endColumns": "68,83,75,124,168,80,78", "endOffsets": "169,253,329,454,623,704,783"}, "to": {"startLines": "73,162,262,267,276,294,295", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5547,13113,22139,22510,23309,25021,25102", "endColumns": "68,83,75,124,168,80,78", "endOffsets": "5611,13192,22210,22630,23473,25097,25176"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,319,396,489,602,682,747,835,905,968,1060,1120,1179,1242,1303,1362,1464,1521,1580,1638,1706,1817,1898,1980,2082,2153,2226,2314,2381,2447,2520,2596,2682,2752,2827,2909,2977,3062,3132,3222,3313,3387,3460,3549,3600,3667,3749,3834,3896,3960,4023,4117,4212,4302,4398", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,76,92,112,79,64,87,69,62,91,59,58,62,60,58,101,56,58,57,67,110,80,81,101,70,72,87,66,65,72,75,85,69,74,81,67,84,69,89,90,73,72,88,50,66,81,84,61,63,62,93,94,89,95,74", "endOffsets": "314,391,484,597,677,742,830,900,963,1055,1115,1174,1237,1298,1357,1459,1516,1575,1633,1701,1812,1893,1975,2077,2148,2221,2309,2376,2442,2515,2591,2677,2747,2822,2904,2972,3057,3127,3217,3308,3382,3455,3544,3595,3662,3744,3829,3891,3955,4018,4112,4207,4297,4393,4468"}, "to": {"startLines": "21,59,67,68,69,110,164,165,168,207,208,209,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,266", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "807,4240,5014,5107,5220,9447,13270,13358,13590,17848,17940,18000,18059,18122,18183,18242,18344,18401,18460,18518,18586,19167,19248,19330,19432,19503,19576,19664,19731,19797,19870,19946,20032,20102,20177,20259,20327,20412,20482,20572,20663,20737,20810,20899,20950,21017,21099,21184,21246,21310,21373,21467,21562,21652,22435", "endLines": "26,59,67,68,69,110,164,165,168,207,208,209,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,266", "endColumns": "12,76,92,112,79,64,87,69,62,91,59,58,62,60,58,101,56,58,57,67,110,80,81,101,70,72,87,66,65,72,75,85,69,74,81,67,84,69,89,90,73,72,88,50,66,81,84,61,63,62,93,94,89,95,74", "endOffsets": "1066,4312,5102,5215,5295,9507,13353,13423,13648,17935,17995,18054,18117,18178,18237,18339,18396,18455,18513,18581,18692,19243,19325,19427,19498,19571,19659,19726,19792,19865,19941,20027,20097,20172,20254,20322,20407,20477,20567,20658,20732,20805,20894,20945,21012,21094,21179,21241,21305,21368,21462,21557,21647,21743,22505"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,271", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1071,1176,1276,1384,1468,1570,1686,1765,1843,1934,2028,2122,2216,2316,2409,2504,2597,2688,2780,2861,2966,3069,3167,3272,3374,3476,3630,22877", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "1171,1271,1379,1463,1565,1681,1760,1838,1929,2023,2117,2211,2311,2404,2499,2592,2683,2775,2856,2961,3064,3162,3267,3369,3471,3625,3722,22954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,246,311,385,447,527,607", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "115,174,241,306,380,442,522,602,664"}, "to": {"startLines": "135,136,137,138,139,140,141,142,143", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11405,11470,11529,11596,11661,11735,11797,11877,11957", "endColumns": "64,58,66,64,73,61,79,79,61", "endOffsets": "11465,11524,11591,11656,11730,11792,11872,11952,12014"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,119", "endOffsets": "156,276"}, "to": {"startLines": "258,259", "startColumns": "4,4", "startOffsets": "21748,21854", "endColumns": "105,119", "endOffsets": "21849,21969"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,523,757,836,914,990,1075,1159,1221,1283,1372,1458,1523,1587,1650,1718,1838,1948,2066,2137,2214,2283,2344,2434,2523,2587,2650,2704,2775,2823,2884,2943,3010,3071,3134,3195,3252,3318,3382,3448,3500,3554,3622,3690", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,78,77,75,84,83,61,61,88,85,64,63,62,67,119,109,117,70,76,68,60,89,88,63,62,53,70,47,60,58,66,60,62,60,56,65,63,65,51,53,67,67,53", "endOffsets": "279,518,752,831,909,985,1070,1154,1216,1278,1367,1453,1518,1582,1645,1713,1833,1943,2061,2132,2209,2278,2339,2429,2518,2582,2645,2699,2770,2818,2879,2938,3005,3066,3129,3190,3247,3313,3377,3443,3495,3549,3617,3685,3739"}, "to": {"startLines": "2,11,16,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,334,573,9512,9591,9669,9745,9830,9914,9976,10038,10127,10213,10278,10342,10405,10473,10593,10703,10821,10892,10969,11038,11099,11189,11278,11342,12019,12073,12144,12192,12253,12312,12379,12440,12503,12564,12621,12687,12751,12817,12869,12923,12991,13059", "endLines": "10,15,20,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161", "endColumns": "17,12,12,78,77,75,84,83,61,61,88,85,64,63,62,67,119,109,117,70,76,68,60,89,88,63,62,53,70,47,60,58,66,60,62,60,56,65,63,65,51,53,67,67,53", "endOffsets": "329,568,802,9586,9664,9740,9825,9909,9971,10033,10122,10208,10273,10337,10400,10468,10588,10698,10816,10887,10964,11033,11094,11184,11273,11337,11400,12068,12139,12187,12248,12307,12374,12435,12498,12559,12616,12682,12746,12812,12864,12918,12986,13054,13108"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "60,61,62,63,64,65,66,274", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4317,4411,4513,4610,4707,4808,4908,23109", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "4406,4508,4605,4702,4803,4903,5009,23205"}}]}]}