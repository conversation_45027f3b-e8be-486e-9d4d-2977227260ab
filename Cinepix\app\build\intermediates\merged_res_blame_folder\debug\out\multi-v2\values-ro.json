{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-99:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cb0d38356f8a450289b6a883ab8edf14\\transformed\\navigation-ui-2.7.6\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,123", "endOffsets": "160,284"}, "to": {"startLines": "275,276", "startColumns": "4,4", "startOffsets": "25180,25290", "endColumns": "109,123", "endOffsets": "25285,25409"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\31a00a017e90aa446708533c4ea3d047\\transformed\\core-1.12.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "59,60,61,62,63,64,65,291", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4524,4622,4724,4824,4923,5025,5134,26618", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "4617,4719,4819,4918,5020,5129,5246,26714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2907b06edc976dc86930b7aa57b04408\\transformed\\media3-session-1.2.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,139,214,286,352,435,522,618", "endColumns": "83,74,71,65,82,86,95,100", "endOffsets": "134,209,281,347,430,517,613,714"}, "to": {"startLines": "53,123,236,237,238,239,240,241", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3869,11830,21934,22006,22072,22155,22242,22338", "endColumns": "83,74,71,65,82,86,95,100", "endOffsets": "3948,11900,22001,22067,22150,22237,22333,22434"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b15b518726989b0578dc4af44fa5f9f8\\transformed\\media3-ui-1.2.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,582,852,940,1030,1119,1216,1310,1385,1451,1548,1646,1715,1778,1841,1910,2024,2137,2251,2328,2408,2477,2553,2652,2753,2819,2882,2935,2993,3041,3102,3166,3236,3301,3370,3431,3489,3555,3619,3685,3737,3799,3875,3951", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,87,89,88,96,93,74,65,96,97,68,62,62,68,113,112,113,76,79,68,75,98,100,65,62,52,57,47,60,63,69,64,68,60,57,65,63,65,51,61,75,75,56", "endOffsets": "281,577,847,935,1025,1114,1211,1305,1380,1446,1543,1641,1710,1773,1836,1905,2019,2132,2246,2323,2403,2472,2548,2647,2748,2814,2877,2930,2988,3036,3097,3161,3231,3296,3365,3426,3484,3550,3614,3680,3732,3794,3870,3946,4003"}, "to": {"startLines": "2,11,16,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,632,12233,12321,12411,12500,12597,12691,12766,12832,12929,13027,13096,13159,13222,13291,13405,13518,13632,13709,13789,13858,13934,14033,14134,14200,14958,15011,15069,15117,15178,15242,15312,15377,15446,15507,15565,15631,15695,15761,15813,15875,15951,16027", "endLines": "10,15,20,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178", "endColumns": "17,12,12,87,89,88,96,93,74,65,96,97,68,62,62,68,113,112,113,76,79,68,75,98,100,65,62,52,57,47,60,63,69,64,68,60,57,65,63,65,51,61,75,75,56", "endOffsets": "331,627,897,12316,12406,12495,12592,12686,12761,12827,12924,13022,13091,13154,13217,13286,13400,13513,13627,13704,13784,13853,13929,14028,14129,14195,14258,15006,15064,15112,15173,15237,15307,15372,15441,15502,15560,15626,15690,15756,15808,15870,15946,16022,16079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f70384b9a1e1b9ce2411b7c78f9fa615\\transformed\\preference-1.2.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,262,337,476,645,732", "endColumns": "70,85,74,138,168,86,80", "endOffsets": "171,257,332,471,640,727,808"}, "to": {"startLines": "90,179,279,284,293,311,312", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8075,16084,25587,25978,26816,28592,28679", "endColumns": "70,85,74,138,168,86,80", "endOffsets": "8141,16165,25657,26112,26980,28674,28755"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e3384bca41f271b3200ce0446ec0f087\\transformed\\media3-exoplayer-1.2.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,136,198,263,335,413,493,583,676", "endColumns": "80,61,64,71,77,79,89,92,73", "endOffsets": "131,193,258,330,408,488,578,671,745"}, "to": {"startLines": "152,153,154,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "14263,14344,14406,14471,14543,14621,14701,14791,14884", "endColumns": "80,61,64,71,77,79,89,92,73", "endOffsets": "14339,14401,14466,14538,14616,14696,14786,14879,14953"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\677a264eecd9b3484ff2620a344ffab3\\transformed\\material3-1.1.2\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,304,417,539,616,709,820,953,1069,1208,1288,1383,1474,1568,1684,1807,1907,2040,2171,2308,2480,2613,2728,2848,2971,3063,3155,3278,3415,3511,3612,3719,3854,3996,4104,4203,4275,4349,4431,4515,4612,4690,4769,4864,4964,5055,5155,5238,5345,5441,5550,5671,5749,5860", "endColumns": "125,122,112,121,76,92,110,132,115,138,79,94,90,93,115,122,99,132,130,136,171,132,114,119,122,91,91,122,136,95,100,106,134,141,107,98,71,73,81,83,96,77,78,94,99,90,99,82,106,95,108,120,77,110,99", "endOffsets": "176,299,412,534,611,704,815,948,1064,1203,1283,1378,1469,1563,1679,1802,1902,2035,2166,2303,2475,2608,2723,2843,2966,3058,3150,3273,3410,3506,3607,3714,3849,3991,4099,4198,4270,4344,4426,4510,4607,4685,4764,4859,4959,5050,5150,5233,5340,5436,5545,5666,5744,5855,5955"}, "to": {"startLines": "54,55,56,57,71,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,125,180,287,290,292,297,298,299,300,301,302,303,304,305,306,307,308,309,310", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3953,4079,4202,4315,5738,8146,8239,8350,8483,8599,8738,8818,8913,9004,9098,9214,9337,9437,9570,9701,9838,10010,10143,10258,10378,10501,10593,10685,10808,10945,11041,11142,11249,11384,11526,11634,12007,16170,26295,26534,26719,27244,27322,27401,27496,27596,27687,27787,27870,27977,28073,28182,28303,28381,28492", "endColumns": "125,122,112,121,76,92,110,132,115,138,79,94,90,93,115,122,99,132,130,136,171,132,114,119,122,91,91,122,136,95,100,106,134,141,107,98,71,73,81,83,96,77,78,94,99,90,99,82,106,95,108,120,77,110,99", "endOffsets": "4074,4197,4310,4432,5810,8234,8345,8478,8594,8733,8813,8908,8999,9093,9209,9332,9432,9565,9696,9833,10005,10138,10253,10373,10496,10588,10680,10803,10940,11036,11137,11244,11379,11521,11629,11728,12074,16239,26372,26613,26811,27317,27396,27491,27591,27682,27782,27865,27972,28068,28177,28298,28376,28487,28587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\62c17cba0a75b790331013fbfad47644\\transformed\\play-services-basement-18.1.0\\res\\values-ro\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "80", "startColumns": "4", "startOffsets": "6835", "endColumns": "144", "endOffsets": "6975"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\de0470acc971f743e832aff97e9527de\\transformed\\appcompat-1.6.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,527,631,752,837,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1855,1938,2050,2158,2258,2372,2478,2584,2748,2851", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "221,325,438,522,626,747,832,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1850,1933,2045,2153,2253,2367,2473,2579,2743,2846,2930"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,288", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1123,1244,1348,1461,1545,1649,1770,1855,1935,2026,2119,2214,2308,2408,2501,2596,2690,2781,2873,2956,3068,3176,3276,3390,3496,3602,3766,26377", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "1239,1343,1456,1540,1644,1765,1850,1930,2021,2114,2209,2303,2403,2496,2591,2685,2776,2868,2951,3063,3171,3271,3385,3491,3597,3761,3864,26456"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\804c4e5761ff122de1231662730d5f72\\transformed\\ui-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,383,485,573,651,738,829,911,983,1052,1140,1230,1303,1380,1447", "endColumns": "96,83,96,101,87,77,86,90,81,71,68,87,89,72,76,66,114", "endOffsets": "197,281,378,480,568,646,733,824,906,978,1047,1135,1225,1298,1375,1442,1557"}, "to": {"startLines": "69,70,122,124,126,183,184,277,278,280,281,285,286,289,294,295,296", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5557,5654,11733,11905,12079,16413,16491,25414,25505,25662,25734,26117,26205,26461,26985,27062,27129", "endColumns": "96,83,96,101,87,77,86,90,81,71,68,87,89,72,76,66,114", "endOffsets": "5649,5733,11825,12002,12162,16486,16573,25500,25582,25729,25798,26200,26290,26529,27057,27124,27239"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\35536fe73c76a56f94e622d08fe45089\\transformed\\leanback-1.0.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,511,639,753,852,945,1075,1201,1316,1450,1615,1744,1869,1979,2076,2208,2299,2404,2507,2611,2713,2835,2949,3081,3209,3325,3446,3563,3686,3798,3916,4003,4088,4195,4331,4489", "endColumns": "106,100,99,97,127,113,98,92,129,125,114,133,164,128,124,109,96,131,90,104,102,103,101,121,113,131,127,115,120,116,122,111,117,86,84,106,135,157,95", "endOffsets": "207,308,408,506,634,748,847,940,1070,1196,1311,1445,1610,1739,1864,1974,2071,2203,2294,2399,2502,2606,2708,2830,2944,3076,3204,3320,3441,3558,3681,3793,3911,3998,4083,4190,4326,4484,4580"}, "to": {"startLines": "186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,282", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16638,16745,16846,16946,17044,17172,17286,17385,17478,17608,17734,17849,17983,18148,18277,18402,18512,18609,18741,18832,18937,19040,19144,19246,19368,19482,19614,19742,19858,19979,20096,20219,20331,20449,20536,20621,20728,20864,25803", "endColumns": "106,100,99,97,127,113,98,92,129,125,114,133,164,128,124,109,96,131,90,104,102,103,101,121,113,131,127,115,120,116,122,111,117,86,84,106,135,157,95", "endOffsets": "16740,16841,16941,17039,17167,17281,17380,17473,17603,17729,17844,17978,18143,18272,18397,18507,18604,18736,18827,18932,19035,19139,19241,19363,19477,19609,19737,19853,19974,20091,20214,20326,20444,20531,20616,20723,20859,21017,25894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc96508f556f069c09e91b3a9267645a\\transformed\\play-services-base-18.0.1\\res\\values-ro\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,452,577,682,829,957,1076,1181,1339,1445,1600,1728,1870,2032,2099,2162", "endColumns": "102,155,124,104,146,127,118,104,157,105,154,127,141,161,66,62,77", "endOffsets": "295,451,576,681,828,956,1075,1180,1338,1444,1599,1727,1869,2031,2098,2161,2239"}, "to": {"startLines": "72,73,74,75,76,77,78,79,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5815,5922,6082,6211,6320,6471,6603,6726,6980,7142,7252,7411,7543,7689,7855,7926,7993", "endColumns": "106,159,128,108,150,131,122,108,161,109,158,131,145,165,70,66,81", "endOffsets": "5917,6077,6206,6315,6466,6598,6721,6830,7137,7247,7406,7538,7684,7850,7921,7988,8070"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\90e8727c8917f86c7d308f0c9abe80a3\\transformed\\material-1.4.0-beta01\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,276,363,464,585,669,735,830,904,964,1048,1114,1172,1245,1308,1364,1483,1540,1601,1657,1731,1876,1962,2046,2149,2231,2314,2404,2471,2537,2610,2688,2776,2847,2924,2998,3070,3161,3235,3330,3428,3502,3582,3683,3736,3802,3891,3981,4043,4107,4170,4282,4395,4505,4617", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,86,100,120,83,65,94,73,59,83,65,57,72,62,55,118,56,60,55,73,144,85,83,102,81,82,89,66,65,72,77,87,70,76,73,71,90,73,94,97,73,79,100,52,65,88,89,61,63,62,111,112,109,111,78", "endOffsets": "271,358,459,580,664,730,825,899,959,1043,1109,1167,1240,1303,1359,1478,1535,1596,1652,1726,1871,1957,2041,2144,2226,2309,2399,2466,2532,2605,2683,2771,2842,2919,2993,3065,3156,3230,3325,3423,3497,3577,3678,3731,3797,3886,3976,4038,4102,4165,4277,4390,4500,4612,4691"}, "to": {"startLines": "21,58,66,67,68,127,181,182,185,224,225,226,227,228,229,230,231,232,233,234,235,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,283", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "902,4437,5251,5352,5473,12167,16244,16339,16578,21022,21106,21172,21230,21303,21366,21422,21541,21598,21659,21715,21789,22439,22525,22609,22712,22794,22877,22967,23034,23100,23173,23251,23339,23410,23487,23561,23633,23724,23798,23893,23991,24065,24145,24246,24299,24365,24454,24544,24606,24670,24733,24845,24958,25068,25899", "endLines": "25,58,66,67,68,127,181,182,185,224,225,226,227,228,229,230,231,232,233,234,235,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,283", "endColumns": "12,86,100,120,83,65,94,73,59,83,65,57,72,62,55,118,56,60,55,73,144,85,83,102,81,82,89,66,65,72,77,87,70,76,73,71,90,73,94,97,73,79,100,52,65,88,89,61,63,62,111,112,109,111,78", "endOffsets": "1118,4519,5347,5468,5552,12228,16334,16408,16633,21101,21167,21225,21298,21361,21417,21536,21593,21654,21710,21784,21929,22520,22604,22707,22789,22872,22962,23029,23095,23168,23246,23334,23405,23482,23556,23628,23719,23793,23888,23986,24060,24140,24241,24294,24360,24449,24539,24601,24665,24728,24840,24953,25063,25175,25973"}}]}]}