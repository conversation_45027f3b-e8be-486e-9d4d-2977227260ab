{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeReleaseResources-87:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,282,364,460,586,667,733,825,902,965,1073,1139,1195,1266,1326,1380,1499,1556,1618,1672,1747,1871,1959,2042,2157,2242,2328,2416,2483,2549,2623,2701,2788,2860,2937,3010,3080,3173,3245,3337,3433,3507,3583,3679,3732,3799,3886,3973,4035,4099,4162,4270,4372,4473,4578", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,81,95,125,80,65,91,76,62,107,65,55,70,59,53,118,56,61,53,74,123,87,82,114,84,85,87,66,65,73,77,86,71,76,72,69,92,71,91,95,73,75,95,52,66,86,86,61,63,62,107,101,100,104,79", "endOffsets": "277,359,455,581,662,728,820,897,960,1068,1134,1190,1261,1321,1375,1494,1551,1613,1667,1742,1866,1954,2037,2152,2237,2323,2411,2478,2544,2618,2696,2783,2855,2932,3005,3075,3168,3240,3332,3428,3502,3578,3674,3727,3794,3881,3968,4030,4094,4157,4265,4367,4468,4573,4653"}, "to": {"startLines": "21,58,66,67,68,109,163,164,167,206,207,208,209,210,211,212,213,214,215,216,217,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,265", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "885,4381,5188,5284,5410,9830,13952,14044,14290,18640,18748,18814,18870,18941,19001,19055,19174,19231,19293,19347,19422,20066,20154,20237,20352,20437,20523,20611,20678,20744,20818,20896,20983,21055,21132,21205,21275,21368,21440,21532,21628,21702,21778,21874,21927,21994,22081,22168,22230,22294,22357,22465,22567,22668,23494", "endLines": "25,58,66,67,68,109,163,164,167,206,207,208,209,210,211,212,213,214,215,216,217,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,265", "endColumns": "12,81,95,125,80,65,91,76,62,107,65,55,70,59,53,118,56,61,53,74,123,87,82,114,84,85,87,66,65,73,77,86,71,76,72,69,92,71,91,95,73,75,95,52,66,86,86,61,63,62,107,101,100,104,79", "endOffsets": "1107,4458,5279,5405,5486,9891,14039,14116,14348,18743,18809,18865,18936,18996,19050,19169,19226,19288,19342,19417,19541,20149,20232,20347,20432,20518,20606,20673,20739,20813,20891,20978,21050,21127,21200,21270,21363,21435,21527,21623,21697,21773,21869,21922,21989,22076,22163,22225,22289,22352,22460,22562,22663,22768,23569"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "59,60,61,62,63,64,65,273", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4463,4561,4663,4761,4865,4969,5071,24216", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "4556,4658,4756,4860,4964,5066,5183,24312"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,346,484,653,740", "endColumns": "70,86,82,137,168,86,82", "endOffsets": "171,258,341,479,648,735,818"}, "to": {"startLines": "72,161,261,266,275,293,294", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5755,13788,23178,23574,24422,26188,26275", "endColumns": "70,86,82,137,168,86,82", "endOffsets": "5821,13870,23256,23707,24586,26270,26353"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,206,294,388,487,573,650,742,834,919,991,1062,1143,1229,1302,1382,1452", "endColumns": "100,87,93,98,85,76,91,91,84,71,70,80,85,72,79,69,117", "endOffsets": "201,289,383,482,568,645,737,829,914,986,1057,1138,1224,1297,1377,1447,1565"}, "to": {"startLines": "69,70,104,106,108,165,166,259,260,262,263,267,268,271,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5491,5592,9384,9564,9744,14121,14198,23001,23093,23261,23333,23712,23793,24056,24591,24671,24741", "endColumns": "100,87,93,98,85,76,91,91,84,71,70,80,85,72,79,69,117", "endOffsets": "5587,5675,9473,9658,9825,14193,14285,23088,23173,23328,23399,23788,23874,24124,24666,24736,24854"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2243,2348,2462,2565,2734,2830", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2238,2343,2457,2560,2729,2825,2912"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,270", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1112,1233,1330,1437,1523,1627,1749,1834,1916,2007,2100,2195,2289,2389,2482,2577,2672,2763,2854,2942,3045,3149,3250,3355,3469,3572,3741,23969", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "1228,1325,1432,1518,1622,1744,1829,1911,2002,2095,2190,2284,2384,2477,2572,2667,2758,2849,2937,3040,3144,3245,3350,3464,3567,3736,3832,24051"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,504,626,741,838,930,1052,1172,1271,1386,1536,1661,1784,1888,1979,2108,2205,2306,2410,2513,2619,2731,2848,2975,3100,3207,3319,3434,3557,3670,3791,3878,3966,4085,4222,4392", "endColumns": "106,100,96,93,121,114,96,91,121,119,98,114,149,124,122,103,90,128,96,100,103,102,105,111,116,126,124,106,111,114,122,112,120,86,87,118,136,169,89", "endOffsets": "207,308,405,499,621,736,833,925,1047,1167,1266,1381,1531,1656,1779,1883,1974,2103,2200,2301,2405,2508,2614,2726,2843,2970,3095,3202,3314,3429,3552,3665,3786,3873,3961,4080,4217,4387,4477"}, "to": {"startLines": "168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14353,14460,14561,14658,14752,14874,14989,15086,15178,15300,15420,15519,15634,15784,15909,16032,16136,16227,16356,16453,16554,16658,16761,16867,16979,17096,17223,17348,17455,17567,17682,17805,17918,18039,18126,18214,18333,18470,23404", "endColumns": "106,100,96,93,121,114,96,91,121,119,98,114,149,124,122,103,90,128,96,100,103,102,105,111,116,126,124,106,111,114,122,112,120,86,87,118,136,169,89", "endOffsets": "14455,14556,14653,14747,14869,14984,15081,15173,15295,15415,15514,15629,15779,15904,16027,16131,16222,16351,16448,16549,16653,16756,16862,16974,17091,17218,17343,17450,17562,17677,17800,17913,18034,18121,18209,18328,18465,18635,23489"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,135,221,294,370,452,540,638", "endColumns": "79,85,72,75,81,87,97,102", "endOffsets": "130,216,289,365,447,535,633,736"}, "to": {"startLines": "53,105,218,219,220,221,222,223", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3837,9478,19546,19619,19695,19777,19865,19963", "endColumns": "79,85,72,75,81,87,97,102", "endOffsets": "3912,9559,19614,19690,19772,19860,19958,20061"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,571,835,921,1008,1096,1194,1301,1371,1438,1534,1626,1691,1764,1827,1895,2009,2125,2241,2321,2405,2476,2547,2648,2750,2822,2892,2945,3003,3051,3112,3184,3251,3315,3386,3450,3509,3574,3639,3710,3762,3829,3910,3991", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,85,86,87,97,106,69,66,95,91,64,72,62,67,113,115,115,79,83,70,70,100,101,71,69,52,57,47,60,71,66,63,70,63,58,64,64,70,51,66,80,80,55", "endOffsets": "288,566,830,916,1003,1091,1189,1296,1366,1433,1529,1621,1686,1759,1822,1890,2004,2120,2236,2316,2400,2471,2542,2643,2745,2817,2887,2940,2998,3046,3107,3179,3246,3310,3381,3445,3504,3569,3634,3705,3757,3824,3905,3986,4042"}, "to": {"startLines": "2,11,16,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,343,621,9896,9982,10069,10157,10255,10362,10432,10499,10595,10687,10752,10825,10888,10956,11070,11186,11302,11382,11466,11537,11608,11709,11811,11883,12633,12686,12744,12792,12853,12925,12992,13056,13127,13191,13250,13315,13380,13451,13503,13570,13651,13732", "endLines": "10,15,20,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160", "endColumns": "17,12,12,85,86,87,97,106,69,66,95,91,64,72,62,67,113,115,115,79,83,70,70,100,101,71,69,52,57,47,60,71,66,63,70,63,58,64,64,70,51,66,80,80,55", "endOffsets": "338,616,880,9977,10064,10152,10250,10357,10427,10494,10590,10682,10747,10820,10883,10951,11065,11181,11297,11377,11461,11532,11603,11704,11806,11878,11948,12681,12739,12787,12848,12920,12987,13051,13122,13186,13245,13310,13375,13446,13498,13565,13646,13727,13783"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,329,408,481,581,662", "endColumns": "74,60,64,72,78,72,99,80,72", "endOffsets": "125,186,251,324,403,476,576,657,730"}, "to": {"startLines": "134,135,136,137,138,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11953,12028,12089,12154,12227,12306,12379,12479,12560", "endColumns": "74,60,64,72,78,72,99,80,72", "endOffsets": "12023,12084,12149,12222,12301,12374,12474,12555,12628"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,158", "endColumns": "102,124", "endOffsets": "153,278"}, "to": {"startLines": "257,258", "startColumns": "4,4", "startOffsets": "22773,22876", "endColumns": "102,124", "endOffsets": "22871,22996"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,286,403,519,594,688,800,941,1057,1191,1272,1368,1459,1553,1666,1789,1890,2021,2151,2288,2454,2585,2705,2830,2950,3040,3134,3251,3375,3471,3569,3674,3808,3949,4054,4152,4233,4310,4400,4487,4592,4671,4750,4843,4943,5032,5125,5209,5313,5406,5503,5637,5723,5826", "endColumns": "114,115,116,115,74,93,111,140,115,133,80,95,90,93,112,122,100,130,129,136,165,130,119,124,119,89,93,116,123,95,97,104,133,140,104,97,80,76,89,86,104,78,78,92,99,88,92,83,103,92,96,133,85,102,94", "endOffsets": "165,281,398,514,589,683,795,936,1052,1186,1267,1363,1454,1548,1661,1784,1885,2016,2146,2283,2449,2580,2700,2825,2945,3035,3129,3246,3370,3466,3564,3669,3803,3944,4049,4147,4228,4305,4395,4482,4587,4666,4745,4838,4938,5027,5120,5204,5308,5401,5498,5632,5718,5821,5916"}, "to": {"startLines": "54,55,56,57,71,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,107,162,269,272,274,279,280,281,282,283,284,285,286,287,288,289,290,291,292", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3917,4032,4148,4265,5680,5826,5920,6032,6173,6289,6423,6504,6600,6691,6785,6898,7021,7122,7253,7383,7520,7686,7817,7937,8062,8182,8272,8366,8483,8607,8703,8801,8906,9040,9181,9286,9663,13875,23879,24129,24317,24859,24938,25017,25110,25210,25299,25392,25476,25580,25673,25770,25904,25990,26093", "endColumns": "114,115,116,115,74,93,111,140,115,133,80,95,90,93,112,122,100,130,129,136,165,130,119,124,119,89,93,116,123,95,97,104,133,140,104,97,80,76,89,86,104,78,78,92,99,88,92,83,103,92,96,133,85,102,94", "endOffsets": "4027,4143,4260,4376,5750,5915,6027,6168,6284,6418,6499,6595,6686,6780,6893,7016,7117,7248,7378,7515,7681,7812,7932,8057,8177,8267,8361,8478,8602,8698,8796,8901,9035,9176,9281,9379,9739,13947,23964,24211,24417,24933,25012,25105,25205,25294,25387,25471,25575,25668,25765,25899,25985,26088,26183"}}]}]}