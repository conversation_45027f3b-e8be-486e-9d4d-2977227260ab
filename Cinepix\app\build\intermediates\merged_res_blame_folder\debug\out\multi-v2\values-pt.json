{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-91:/values-pt/values-pt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,341,492,661,748", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "170,257,336,487,656,743,824"}, "to": {"startLines": "61,149,201,205,214,232,233", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4962,12876,18374,18687,19543,21269,21356", "endColumns": "69,86,78,150,168,86,80", "endOffsets": "5027,12958,18448,18833,19707,21351,21432"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,912,1003,1096,1191,1285,1385,1478,1573,1668,1759,1850,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "220,326,433,522,623,742,827,907,998,1091,1186,1280,1380,1473,1568,1663,1754,1845,1930,2037,2148,2250,2358,2466,2576,2738,2838,2924"}, "to": {"startLines": "19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "700,820,926,1033,1122,1223,1342,1427,1507,1598,1691,1786,1880,1980,2073,2168,2263,2354,2445,2530,2637,2748,2850,2958,3066,3176,3338,19098", "endColumns": "119,105,106,88,100,118,84,79,90,92,94,93,99,92,94,94,90,90,84,106,110,101,107,107,109,161,99,85", "endOffsets": "815,921,1028,1117,1218,1337,1422,1502,1593,1686,1781,1875,1975,2068,2163,2258,2349,2440,2525,2632,2743,2845,2953,3061,3171,3333,3433,19179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,131,207,276,343,420,497,589", "endColumns": "75,75,68,66,76,76,91,96", "endOffsets": "126,202,271,338,415,492,584,681"}, "to": {"startLines": "46,94,191,192,193,194,195,196", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3438,8689,17485,17554,17621,17698,17775,17867", "endColumns": "75,75,68,66,76,76,91,96", "endOffsets": "3509,8760,17549,17616,17693,17770,17862,17959"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,287,399,511,587,679,789,919,1033,1180,1260,1358,1449,1545,1656,1782,1885,2020,2154,2290,2452,2584,2700,2821,2945,3037,3130,3246,3358,3454,3561,3666,3802,3943,4049,4147,4229,4303,4388,4473,4570,4646,4726,4823,4925,5013,5108,5192,5300,5397,5496,5611,5687,5783", "endColumns": "114,116,111,111,75,91,109,129,113,146,79,97,90,95,110,125,102,134,133,135,161,131,115,120,123,91,92,115,111,95,106,104,135,140,105,97,81,73,84,84,96,75,79,96,101,87,94,83,107,96,98,114,75,95,87", "endOffsets": "165,282,394,506,582,674,784,914,1028,1175,1255,1353,1444,1540,1651,1777,1880,2015,2149,2285,2447,2579,2695,2816,2940,3032,3125,3241,3353,3449,3556,3661,3797,3938,4044,4142,4224,4298,4383,4468,4565,4641,4721,4818,4920,5008,5103,5187,5295,5392,5491,5606,5682,5778,5866"}, "to": {"startLines": "47,48,49,50,60,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,96,150,208,211,213,218,219,220,221,222,223,224,225,226,227,228,229,230,231", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3514,3629,3746,3858,4886,5032,5124,5234,5364,5478,5625,5705,5803,5894,5990,6101,6227,6330,6465,6599,6735,6897,7029,7145,7266,7390,7482,7575,7691,7803,7899,8006,8111,8247,8388,8494,8864,12963,19013,19260,19446,19968,20044,20124,20221,20323,20411,20506,20590,20698,20795,20894,21009,21085,21181", "endColumns": "114,116,111,111,75,91,109,129,113,146,79,97,90,95,110,125,102,134,133,135,161,131,115,120,123,91,92,115,111,95,106,104,135,140,105,97,81,73,84,84,96,75,79,96,101,87,94,83,107,96,98,114,75,95,87", "endOffsets": "3624,3741,3853,3965,4957,5119,5229,5359,5473,5620,5700,5798,5889,5985,6096,6222,6325,6460,6594,6730,6892,7024,7140,7261,7385,7477,7570,7686,7798,7894,8001,8106,8242,8383,8489,8587,8941,13032,19093,19340,19538,20039,20119,20216,20318,20406,20501,20585,20693,20790,20889,21004,21080,21176,21264"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,998,1067,1152,1242,1318,1394,1461", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,993,1062,1147,1237,1313,1389,1456,1569"}, "to": {"startLines": "58,59,93,95,97,151,152,199,200,202,203,206,207,210,215,216,217", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4705,4800,8592,8765,8946,13037,13120,18196,18287,18453,18525,18838,18923,19184,19712,19788,19855", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "4795,4881,8684,8859,9027,13115,13212,18282,18369,18520,18589,18918,19008,19255,19783,19850,19963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,508,634,748,847,948,1081,1210,1310,1426,1592,1717,1838,1941,2033,2165,2260,2363,2466,2572,2669,2783,2911,3032,3149,3252,3360,3471,3587,3692,3802,3889,3976,4080,4218,4373", "endColumns": "106,100,98,95,125,113,98,100,132,128,99,115,165,124,120,102,91,131,94,102,102,105,96,113,127,120,116,102,107,110,115,104,109,86,86,103,137,154,92", "endOffsets": "207,308,407,503,629,743,842,943,1076,1205,1305,1421,1587,1712,1833,1936,2028,2160,2255,2358,2461,2567,2664,2778,2906,3027,3144,3247,3355,3466,3582,3687,3797,3884,3971,4075,4213,4368,4461"}, "to": {"startLines": "153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,204", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13217,13324,13425,13524,13620,13746,13860,13959,14060,14193,14322,14422,14538,14704,14829,14950,15053,15145,15277,15372,15475,15578,15684,15781,15895,16023,16144,16261,16364,16472,16583,16699,16804,16914,17001,17088,17192,17330,18594", "endColumns": "106,100,98,95,125,113,98,100,132,128,99,115,165,124,120,102,91,131,94,102,102,105,96,113,127,120,116,102,107,110,115,104,109,86,86,103,137,154,92", "endOffsets": "13319,13420,13519,13615,13741,13855,13954,14055,14188,14317,14417,14533,14699,14824,14945,15048,15140,15272,15367,15470,15573,15679,15776,15890,16018,16139,16256,16359,16467,16578,16694,16799,16909,16996,17083,17187,17325,17480,18682"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,471,650,731,814,887,986,1082,1156,1222,1318,1413,1479,1548,1615,1686,1804,1921,2042,2109,2195,2271,2345,2443,2543,2607,2671,2724,2782,2830,2891,2956,3018,3084,3154,3218,3279,3345,3410,3476,3529,3593,3671,3749", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,117,116,120,66,85,75,73,97,99,63,63,52,57,47,60,64,61,65,69,63,60,65,64,65,52,63,77,77,58", "endOffsets": "280,466,645,726,809,882,981,1077,1151,1217,1313,1408,1474,1543,1610,1681,1799,1916,2037,2104,2190,2266,2340,2438,2538,2602,2666,2719,2777,2825,2886,2951,3013,3079,3149,3213,3274,3340,3405,3471,3524,3588,3666,3744,3803"}, "to": {"startLines": "2,11,15,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,521,9032,9113,9196,9269,9368,9464,9538,9604,9700,9795,9861,9930,9997,10068,10186,10303,10424,10491,10577,10653,10727,10825,10925,10989,11739,11792,11850,11898,11959,12024,12086,12152,12222,12286,12347,12413,12478,12544,12597,12661,12739,12817", "endLines": "10,14,18,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148", "endColumns": "17,12,12,80,82,72,98,95,73,65,95,94,65,68,66,70,117,116,120,66,85,75,73,97,99,63,63,52,57,47,60,64,61,65,69,63,60,65,64,65,52,63,77,77,58", "endOffsets": "330,516,695,9108,9191,9264,9363,9459,9533,9599,9695,9790,9856,9925,9992,10063,10181,10298,10419,10486,10572,10648,10722,10820,10920,10984,11048,11787,11845,11893,11954,12019,12081,12147,12217,12281,12342,12408,12473,12539,12592,12656,12734,12812,12871"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,195,267,333,410,477,578,671", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "120,190,262,328,405,472,573,666,736"}, "to": {"startLines": "122,123,124,125,126,127,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11053,11123,11193,11265,11331,11408,11475,11576,11669", "endColumns": "69,69,71,65,76,66,100,92,69", "endOffsets": "11118,11188,11260,11326,11403,11470,11571,11664,11734"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "51,52,53,54,55,56,57,212", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3970,4067,4169,4268,4368,4475,4585,19345", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "4062,4164,4263,4363,4470,4580,4700,19441"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}, "to": {"startLines": "197,198", "startColumns": "4,4", "startOffsets": "17964,18076", "endColumns": "111,119", "endOffsets": "18071,18191"}}]}]}