{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-99:/values-v22/values-v22.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\35536fe73c76a56f94e622d08fe45089\\transformed\\leanback-1.0.0\\res\\values-v22\\values-v22.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,107", "endColumns": "51,53", "endOffsets": "102,156"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\de0470acc971f743e832aff97e9527de\\transformed\\appcompat-1.6.1\\res\\values-v22\\values-v22.xml", "from": {"startLines": "2,3,4,9", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,487", "endLines": "2,3,8,13", "endColumns": "74,86,12,12", "endOffsets": "125,212,482,764"}, "to": {"startLines": "4,5,6,11", "startColumns": "4,4,4,4", "startOffsets": "161,236,323,593", "endLines": "4,5,10,15", "endColumns": "74,86,12,12", "endOffsets": "231,318,588,870"}}]}]}