{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeReleaseResources-87:/values-v22/values-v22.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-v22\\values-v22.xml", "from": {"startLines": "2,3,4,9", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,487", "endLines": "2,3,8,13", "endColumns": "74,86,12,12", "endOffsets": "125,212,482,764"}, "to": {"startLines": "4,5,6,11", "startColumns": "4,4,4,4", "startOffsets": "161,236,323,593", "endLines": "4,5,10,15", "endColumns": "74,86,12,12", "endOffsets": "231,318,588,870"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-v22\\values-v22.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,107", "endColumns": "51,53", "endOffsets": "102,156"}}]}]}