{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-99:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\90e8727c8917f86c7d308f0c9abe80a3\\transformed\\material-1.4.0-beta01\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,214,297,397,513,595,658,749,814,873,961,1023,1083,1150,1213,1267,1381,1438,1499,1553,1623,1742,1823,1908,2013,2090,2167,2253,2320,2386,2456,2534,2621,2691,2767,2838,2907,3003,3077,3175,3271,3345,3415,3517,3572,3639,3726,3819,3882,3946,4009,4109,4212,4306,4410", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,99,115,81,62,90,64,58,87,61,59,66,62,53,113,56,60,53,69,118,80,84,104,76,76,85,66,65,69,77,86,69,75,70,68,95,73,97,95,73,69,101,54,66,86,92,62,63,62,99,102,93,103,77", "endOffsets": "209,292,392,508,590,653,744,809,868,956,1018,1078,1145,1208,1262,1376,1433,1494,1548,1618,1737,1818,1903,2008,2085,2162,2248,2315,2381,2451,2529,2616,2686,2762,2833,2902,2998,3072,3170,3266,3340,3410,3512,3567,3634,3721,3814,3877,3941,4004,4104,4207,4301,4405,4483"}, "to": {"startLines": "19,55,63,64,65,124,178,179,182,221,222,223,224,225,226,227,228,229,230,231,232,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,280", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "721,4112,4927,5027,5143,11675,15616,15707,15943,20162,20250,20312,20372,20439,20502,20556,20670,20727,20788,20842,20912,21496,21577,21662,21767,21844,21921,22007,22074,22140,22210,22288,22375,22445,22521,22592,22661,22757,22831,22929,23025,23099,23169,23271,23326,23393,23480,23573,23636,23700,23763,23863,23966,24060,24873", "endLines": "22,55,63,64,65,124,178,179,182,221,222,223,224,225,226,227,228,229,230,231,232,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,280", "endColumns": "12,82,99,115,81,62,90,64,58,87,61,59,66,62,53,113,56,60,53,69,118,80,84,104,76,76,85,66,65,69,77,86,69,75,70,68,95,73,97,95,73,69,101,54,66,86,92,62,63,62,99,102,93,103,77", "endOffsets": "875,4190,5022,5138,5220,11733,15702,15767,15997,20245,20307,20367,20434,20497,20551,20665,20722,20783,20837,20907,21026,21572,21657,21762,21839,21916,22002,22069,22135,22205,22283,22370,22440,22516,22587,22656,22752,22826,22924,23020,23094,23164,23266,23321,23388,23475,23568,23631,23695,23758,23858,23961,24055,24159,24946"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\35536fe73c76a56f94e622d08fe45089\\transformed\\leanback-1.0.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,506,626,738,835,928,1049,1169,1271,1392,1533,1655,1777,1881,1977,2107,2196,2301,2413,2516,2614,2725,2840,2951,3062,3162,3266,3379,3492,3601,3710,3797,3879,3980,4113,4265", "endColumns": "106,100,98,93,119,111,96,92,120,119,101,120,140,121,121,103,95,129,88,104,111,102,97,110,114,110,110,99,103,112,112,108,108,86,81,100,132,151,86", "endOffsets": "207,308,407,501,621,733,830,923,1044,1164,1266,1387,1528,1650,1772,1876,1972,2102,2191,2296,2408,2511,2609,2720,2835,2946,3057,3157,3261,3374,3487,3596,3705,3792,3874,3975,4108,4260,4347"}, "to": {"startLines": "183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,279", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16002,16109,16210,16309,16403,16523,16635,16732,16825,16946,17066,17168,17289,17430,17552,17674,17778,17874,18004,18093,18198,18310,18413,18511,18622,18737,18848,18959,19059,19163,19276,19389,19498,19607,19694,19776,19877,20010,24786", "endColumns": "106,100,98,93,119,111,96,92,120,119,101,120,140,121,121,103,95,129,88,104,111,102,97,110,114,110,110,99,103,112,112,108,108,86,81,100,132,151,86", "endOffsets": "16104,16205,16304,16398,16518,16630,16727,16820,16941,17061,17163,17284,17425,17547,17669,17773,17869,17999,18088,18193,18305,18408,18506,18617,18732,18843,18954,19054,19158,19271,19384,19493,19602,19689,19771,19872,20005,20157,24868"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc96508f556f069c09e91b3a9267645a\\transformed\\play-services-base-18.0.1\\res\\values-mr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,460,579,687,828,945,1049,1142,1288,1392,1542,1662,1797,1946,2002,2064", "endColumns": "102,163,118,107,140,116,103,92,145,103,149,119,134,148,55,61,76", "endOffsets": "295,459,578,686,827,944,1048,1141,1287,1391,1541,1661,1796,1945,2001,2063,2140"}, "to": {"startLines": "69,70,71,72,73,74,75,76,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5482,5589,5757,5880,5992,6137,6258,6366,6606,6756,6864,7018,7142,7281,7434,7494,7560", "endColumns": "106,167,122,111,144,120,107,96,149,107,153,123,138,152,59,65,80", "endOffsets": "5584,5752,5875,5987,6132,6253,6361,6458,6751,6859,7013,7137,7276,7429,7489,7555,7636"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\de0470acc971f743e832aff97e9527de\\transformed\\appcompat-1.6.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,620,732,810,887,978,1071,1164,1261,1361,1454,1549,1643,1734,1825,1905,2012,2113,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "211,317,424,514,615,727,805,882,973,1066,1159,1256,1356,1449,1544,1638,1729,1820,1900,2007,2108,2205,2314,2416,2530,2687,2790,2870"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,285", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "880,991,1097,1204,1294,1395,1507,1585,1662,1753,1846,1939,2036,2136,2229,2324,2418,2509,2600,2680,2787,2888,2985,3094,3196,3310,3467,25334", "endColumns": "110,105,106,89,100,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,96,108,101,113,156,102,79", "endOffsets": "986,1092,1199,1289,1390,1502,1580,1657,1748,1841,1934,2031,2131,2224,2319,2413,2504,2595,2675,2782,2883,2980,3089,3191,3305,3462,3565,25409"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\677a264eecd9b3484ff2620a344ffab3\\transformed\\material3-1.1.2\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,295,402,523,604,700,812,944,1063,1205,1286,1388,1475,1569,1676,1802,1909,2042,2171,2295,2470,2591,2703,2819,2939,3027,3118,3234,3359,3455,3554,3659,3791,3929,4040,4133,4205,4287,4368,4454,4550,4626,4705,4800,4895,4988,5083,5166,5266,5362,5461,5575,5651,5747", "endColumns": "120,118,106,120,80,95,111,131,118,141,80,101,86,93,106,125,106,132,128,123,174,120,111,115,119,87,90,115,124,95,98,104,131,137,110,92,71,81,80,85,95,75,78,94,94,92,94,82,99,95,98,113,75,95,89", "endOffsets": "171,290,397,518,599,695,807,939,1058,1200,1281,1383,1470,1564,1671,1797,1904,2037,2166,2290,2465,2586,2698,2814,2934,3022,3113,3229,3354,3450,3549,3654,3786,3924,4035,4128,4200,4282,4363,4449,4545,4621,4700,4795,4890,4983,5078,5161,5261,5357,5456,5570,5646,5742,5832"}, "to": {"startLines": "51,52,53,54,68,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,122,177,284,287,289,294,295,296,297,298,299,300,301,302,303,304,305,306,307", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3644,3765,3884,3991,5401,7713,7809,7921,8053,8172,8314,8395,8497,8584,8678,8785,8911,9018,9151,9280,9404,9579,9700,9812,9928,10048,10136,10227,10343,10468,10564,10663,10768,10900,11038,11149,11516,15534,25253,25485,25672,26197,26273,26352,26447,26542,26635,26730,26813,26913,27009,27108,27222,27298,27394", "endColumns": "120,118,106,120,80,95,111,131,118,141,80,101,86,93,106,125,106,132,128,123,174,120,111,115,119,87,90,115,124,95,98,104,131,137,110,92,71,81,80,85,95,75,78,94,94,92,94,82,99,95,98,113,75,95,89", "endOffsets": "3760,3879,3986,4107,5477,7804,7916,8048,8167,8309,8390,8492,8579,8673,8780,8906,9013,9146,9275,9399,9574,9695,9807,9923,10043,10131,10222,10338,10463,10559,10658,10763,10895,11033,11144,11237,11583,15611,25329,25566,25763,26268,26347,26442,26537,26630,26725,26808,26908,27004,27103,27217,27293,27389,27479"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\62c17cba0a75b790331013fbfad47644\\transformed\\play-services-basement-18.1.0\\res\\values-mr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "77", "startColumns": "4", "startOffsets": "6463", "endColumns": "142", "endOffsets": "6601"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e3384bca41f271b3200ce0446ec0f087\\transformed\\media3-exoplayer-1.2.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,190,259,328,403,467,564,658", "endColumns": "69,64,68,68,74,63,96,93,72", "endOffsets": "120,185,254,323,398,462,559,653,726"}, "to": {"startLines": "149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "13662,13732,13797,13866,13935,14010,14074,14171,14265", "endColumns": "69,64,68,68,74,63,96,93,72", "endOffsets": "13727,13792,13861,13930,14005,14069,14166,14260,14333"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\804c4e5761ff122de1231662730d5f72\\transformed\\ui-release\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,281,378,476,563,649,734,823,906,975,1045,1125,1210,1281,1357,1423", "endColumns": "93,81,96,97,86,85,84,88,82,68,69,79,84,70,75,65,117", "endOffsets": "194,276,373,471,558,644,729,818,901,970,1040,1120,1205,1276,1352,1418,1536"}, "to": {"startLines": "66,67,119,121,123,180,181,274,275,277,278,282,283,286,291,292,293", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5225,5319,11242,11418,11588,15772,15858,24393,24482,24647,24716,25088,25168,25414,25937,26013,26079", "endColumns": "93,81,96,97,86,85,84,88,82,68,69,79,84,70,75,65,117", "endOffsets": "5314,5396,11334,11511,11670,15853,15938,24477,24560,24711,24781,25163,25248,25480,26008,26074,26192"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\31a00a017e90aa446708533c4ea3d047\\transformed\\core-1.12.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "56,57,58,59,60,61,62,288", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4195,4295,4399,4500,4603,4705,4810,25571", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "4290,4394,4495,4598,4700,4805,4922,25667"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cb0d38356f8a450289b6a883ab8edf14\\transformed\\navigation-ui-2.7.6\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,116", "endOffsets": "162,279"}, "to": {"startLines": "272,273", "startColumns": "4,4", "startOffsets": "24164,24276", "endColumns": "111,116", "endOffsets": "24271,24388"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2907b06edc976dc86930b7aa57b04408\\transformed\\media3-session-1.2.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,129,208,277,347,421,498,583", "endColumns": "73,78,68,69,73,76,84,89", "endOffsets": "124,203,272,342,416,493,578,668"}, "to": {"startLines": "50,120,233,234,235,236,237,238", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3570,11339,21031,21100,21170,21244,21321,21406", "endColumns": "73,78,68,69,73,76,84,89", "endOffsets": "3639,11413,21095,21165,21239,21316,21401,21491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f70384b9a1e1b9ce2411b7c78f9fa615\\transformed\\preference-1.2.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,261,343,480,649,729", "endColumns": "71,83,81,136,168,79,77", "endOffsets": "172,256,338,475,644,724,802"}, "to": {"startLines": "87,176,276,281,290,308,309", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7641,15450,24565,24951,25768,27484,27564", "endColumns": "71,83,81,136,168,79,77", "endOffsets": "7708,15529,24642,25083,25932,27559,27637"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b15b518726989b0578dc4af44fa5f9f8\\transformed\\media3-ui-1.2.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,487,671,753,833,916,1003,1097,1165,1229,1319,1410,1475,1543,1603,1671,1784,1903,2014,2086,2165,2236,2306,2388,2468,2532,2595,2648,2706,2754,2815,2876,2943,3005,3071,3130,3195,3260,3325,3393,3446,3506,3580,3654", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,81,79,82,86,93,67,63,89,90,64,67,59,67,112,118,110,71,78,70,69,81,79,63,62,52,57,47,60,60,66,61,65,58,64,64,64,67,52,59,73,73,52", "endOffsets": "281,482,666,748,828,911,998,1092,1160,1224,1314,1405,1470,1538,1598,1666,1779,1898,2009,2081,2160,2231,2301,2383,2463,2527,2590,2643,2701,2749,2810,2871,2938,3000,3066,3125,3190,3255,3320,3388,3441,3501,3575,3649,3702"}, "to": {"startLines": "2,11,15,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,336,537,11738,11820,11900,11983,12070,12164,12232,12296,12386,12477,12542,12610,12670,12738,12851,12970,13081,13153,13232,13303,13373,13455,13535,13599,14338,14391,14449,14497,14558,14619,14686,14748,14814,14873,14938,15003,15068,15136,15189,15249,15323,15397", "endLines": "10,14,18,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "endColumns": "17,12,12,81,79,82,86,93,67,63,89,90,64,67,59,67,112,118,110,71,78,70,69,81,79,63,62,52,57,47,60,60,66,61,65,58,64,64,64,67,52,59,73,73,52", "endOffsets": "331,532,716,11815,11895,11978,12065,12159,12227,12291,12381,12472,12537,12605,12665,12733,12846,12965,13076,13148,13227,13298,13368,13450,13530,13594,13657,14386,14444,14492,14553,14614,14681,14743,14809,14868,14933,14998,15063,15131,15184,15244,15318,15392,15445"}}]}]}