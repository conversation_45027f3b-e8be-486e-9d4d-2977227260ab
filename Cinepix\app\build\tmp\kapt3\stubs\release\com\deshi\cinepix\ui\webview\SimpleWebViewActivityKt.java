package com.deshi.cinepix.ui.webview;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000.\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\u001ax\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0012\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u00020\u00010\t2\u0012\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\t2\u0012\u0010\f\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00010\t2\f\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00010\u000e2\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00010\u000eH\u0007\u00a8\u0006\u0010"}, d2 = {"SimpleWebViewScreen", "", "url", "", "isLoading", "", "drawerState", "Landroidx/compose/material3/DrawerState;", "onWebViewCreated", "Lkotlin/Function1;", "Landroid/webkit/WebView;", "onLoadingChanged", "onCanGoBackChanged", "onNavigateToDownloads", "Lkotlin/Function0;", "onNavigateToSettings", "app_release"})
public final class SimpleWebViewActivityKt {
    
    @androidx.compose.runtime.Composable
    public static final void SimpleWebViewScreen(@org.jetbrains.annotations.NotNull
    java.lang.String url, boolean isLoading, @org.jetbrains.annotations.NotNull
    androidx.compose.material3.DrawerState drawerState, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super android.webkit.WebView, kotlin.Unit> onWebViewCreated, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onLoadingChanged, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onCanGoBackChanged, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToDownloads, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToSettings) {
    }
}