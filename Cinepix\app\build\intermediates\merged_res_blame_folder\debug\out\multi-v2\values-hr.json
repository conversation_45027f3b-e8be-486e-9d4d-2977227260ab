{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-91:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,295,585,864,945,1027,1107,1214,1321,1391,1458,1549,1641,1706,1777,1840,1912,2031,2155,2276,2344,2428,2499,2570,2674,2779,2846,2911,2964,3022,3070,3131,3205,3284,3360,3434,3498,3557,3628,3693,3764,3816,3879,3964,4049", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,118,123,120,67,83,70,70,103,104,66,64,52,57,47,60,73,78,75,73,63,58,70,64,70,51,62,84,84,55", "endOffsets": "290,580,859,940,1022,1102,1209,1316,1386,1453,1544,1636,1701,1772,1835,1907,2026,2150,2271,2339,2423,2494,2565,2669,2774,2841,2906,2959,3017,3065,3126,3200,3279,3355,3429,3493,3552,3623,3688,3759,3811,3874,3959,4044,4100"}, "to": {"startLines": "2,11,16,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,345,635,9910,9991,10073,10153,10260,10367,10437,10504,10595,10687,10752,10823,10886,10958,11077,11201,11322,11390,11474,11545,11616,11720,11825,11892,12623,12676,12734,12782,12843,12917,12996,13072,13146,13210,13269,13340,13405,13476,13528,13591,13676,13761", "endLines": "10,15,20,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,118,123,120,67,83,70,70,103,104,66,64,52,57,47,60,73,78,75,73,63,58,70,64,70,51,62,84,84,55", "endOffsets": "340,630,909,9986,10068,10148,10255,10362,10432,10499,10590,10682,10747,10818,10881,10953,11072,11196,11317,11385,11469,11540,11611,11715,11820,11887,11952,12671,12729,12777,12838,12912,12991,13067,13141,13205,13264,13335,13400,13471,13523,13586,13671,13756,13812"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,329,408,481,566,648", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "125,186,251,324,403,476,561,643,716"}, "to": {"startLines": "134,135,136,137,138,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11957,12032,12093,12158,12231,12310,12383,12468,12550", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "12027,12088,12153,12226,12305,12378,12463,12545,12618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,270", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1134,1239,1334,1441,1527,1631,1750,1835,1917,2008,2101,2196,2290,2390,2483,2578,2673,2764,2855,2941,3045,3157,3258,3363,3477,3579,3748,24004", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "1234,1329,1436,1522,1626,1745,1830,1912,2003,2096,2191,2285,2385,2478,2573,2668,2759,2850,2936,3040,3152,3253,3358,3472,3574,3743,3840,24084"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,158", "endColumns": "102,124", "endOffsets": "153,278"}, "to": {"startLines": "257,258", "startColumns": "4,4", "startOffsets": "22788,22891", "endColumns": "102,124", "endOffsets": "22886,23011"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,346,494,663,750", "endColumns": "70,86,82,147,168,86,82", "endOffsets": "171,258,341,489,658,745,828"}, "to": {"startLines": "72,161,261,266,275,293,294", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5779,13817,23196,23599,24450,26205,26292", "endColumns": "70,86,82,147,168,86,82", "endOffsets": "5845,13899,23274,23742,24614,26287,26370"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,275,360,456,586,670,738,834,902,965,1073,1139,1195,1266,1326,1380,1506,1563,1625,1679,1754,1888,1973,2054,2161,2245,2331,2422,2489,2555,2629,2707,2795,2867,2944,3024,3098,3191,3264,3356,3452,3526,3602,3698,3750,3817,3904,3991,4053,4117,4180,4286,4387,4484,4588", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,84,95,129,83,67,95,67,62,107,65,55,70,59,53,125,56,61,53,74,133,84,80,106,83,85,90,66,65,73,77,87,71,76,79,73,92,72,91,95,73,75,95,51,66,86,86,61,63,62,105,100,96,103,79", "endOffsets": "270,355,451,581,665,733,829,897,960,1068,1134,1190,1261,1321,1375,1501,1558,1620,1674,1749,1883,1968,2049,2156,2240,2326,2417,2484,2550,2624,2702,2790,2862,2939,3019,3093,3186,3259,3351,3447,3521,3597,3693,3745,3812,3899,3986,4048,4112,4175,4281,4382,4479,4583,4663"}, "to": {"startLines": "21,58,66,67,68,109,163,164,167,206,207,208,209,210,211,212,213,214,215,216,217,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,265", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "914,4391,5202,5298,5428,9842,13981,14077,14317,18663,18771,18837,18893,18964,19024,19078,19204,19261,19323,19377,19452,20088,20173,20254,20361,20445,20531,20622,20689,20755,20829,20907,20995,21067,21144,21224,21298,21391,21464,21556,21652,21726,21802,21898,21950,22017,22104,22191,22253,22317,22380,22486,22587,22684,23519", "endLines": "25,58,66,67,68,109,163,164,167,206,207,208,209,210,211,212,213,214,215,216,217,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,265", "endColumns": "12,84,95,129,83,67,95,67,62,107,65,55,70,59,53,125,56,61,53,74,133,84,80,106,83,85,90,66,65,73,77,87,71,76,79,73,92,72,91,95,73,75,95,51,66,86,86,61,63,62,105,100,96,103,79", "endOffsets": "1129,4471,5293,5423,5507,9905,14072,14140,14375,18766,18832,18888,18959,19019,19073,19199,19256,19318,19372,19447,19581,20168,20249,20356,20440,20526,20617,20684,20750,20824,20902,20990,21062,21139,21219,21293,21386,21459,21551,21647,21721,21797,21893,21945,22012,22099,22186,22248,22312,22375,22481,22582,22679,22783,23594"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "59,60,61,62,63,64,65,273", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4476,4574,4681,4778,4877,4981,5085,24244", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "4569,4676,4773,4872,4976,5080,5197,24340"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,288,404,521,596,686,794,931,1046,1187,1268,1364,1455,1549,1664,1786,1887,2019,2150,2280,2444,2566,2686,2811,2932,3024,3118,3244,3374,3467,3565,3670,3806,3949,4054,4149,4230,4307,4397,4479,4584,4668,4747,4840,4937,5026,5125,5209,5310,5403,5499,5633,5719,5815", "endColumns": "115,116,115,116,74,89,107,136,114,140,80,95,90,93,114,121,100,131,130,129,163,121,119,124,120,91,93,125,129,92,97,104,135,142,104,94,80,76,89,81,104,83,78,92,96,88,98,83,100,92,95,133,85,95,87", "endOffsets": "166,283,399,516,591,681,789,926,1041,1182,1263,1359,1450,1544,1659,1781,1882,2014,2145,2275,2439,2561,2681,2806,2927,3019,3113,3239,3369,3462,3560,3665,3801,3944,4049,4144,4225,4302,4392,4474,4579,4663,4742,4835,4932,5021,5120,5204,5305,5398,5494,5628,5714,5810,5898"}, "to": {"startLines": "54,55,56,57,71,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,107,162,269,272,274,279,280,281,282,283,284,285,286,287,288,289,290,291,292", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3925,4041,4158,4274,5704,5850,5940,6048,6185,6300,6441,6522,6618,6709,6803,6918,7040,7141,7273,7404,7534,7698,7820,7940,8065,8186,8278,8372,8498,8628,8721,8819,8924,9060,9203,9308,9671,13904,23914,24162,24345,24886,24970,25049,25142,25239,25328,25427,25511,25612,25705,25801,25935,26021,26117", "endColumns": "115,116,115,116,74,89,107,136,114,140,80,95,90,93,114,121,100,131,130,129,163,121,119,124,120,91,93,125,129,92,97,104,135,142,104,94,80,76,89,81,104,83,78,92,96,88,98,83,100,92,95,133,85,95,87", "endOffsets": "4036,4153,4269,4386,5774,5935,6043,6180,6295,6436,6517,6613,6704,6798,6913,7035,7136,7268,7399,7529,7693,7815,7935,8060,8181,8273,8367,8493,8623,8716,8814,8919,9055,9198,9303,9398,9747,13976,23999,24239,24445,24965,25044,25137,25234,25323,25422,25506,25607,25700,25796,25930,26016,26112,26200"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,135,210,281,355,434,523,615", "endColumns": "79,74,70,73,78,88,91,96", "endOffsets": "130,205,276,350,429,518,610,707"}, "to": {"startLines": "53,105,218,219,220,221,222,223", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3845,9497,19586,19657,19731,19810,19899,19991", "endColumns": "79,74,70,73,78,88,91,96", "endOffsets": "3920,9567,19652,19726,19805,19894,19986,20083"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,504,630,748,845,937,1060,1180,1287,1410,1553,1680,1804,1908,1999,2129,2221,2322,2429,2532,2626,2737,2850,2978,3103,3213,3328,3445,3563,3677,3792,3879,3967,4084,4220,4388", "endColumns": "106,100,96,93,125,117,96,91,122,119,106,122,142,126,123,103,90,129,91,100,106,102,93,110,112,127,124,109,114,116,117,113,114,86,87,116,135,167,96", "endOffsets": "207,308,405,499,625,743,840,932,1055,1175,1282,1405,1548,1675,1799,1903,1994,2124,2216,2317,2424,2527,2621,2732,2845,2973,3098,3208,3323,3440,3558,3672,3787,3874,3962,4079,4215,4383,4480"}, "to": {"startLines": "168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,264", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14380,14487,14588,14685,14779,14905,15023,15120,15212,15335,15455,15562,15685,15828,15955,16079,16183,16274,16404,16496,16597,16704,16807,16901,17012,17125,17253,17378,17488,17603,17720,17838,17952,18067,18154,18242,18359,18495,23422", "endColumns": "106,100,96,93,125,117,96,91,122,119,106,122,142,126,123,103,90,129,91,100,106,102,93,110,112,127,124,109,114,116,117,113,114,86,87,116,135,167,96", "endOffsets": "14482,14583,14680,14774,14900,15018,15115,15207,15330,15450,15557,15680,15823,15950,16074,16178,16269,16399,16491,16592,16699,16802,16896,17007,17120,17248,17373,17483,17598,17715,17833,17947,18062,18149,18237,18354,18490,18658,23514"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,297,391,490,580,659,752,847,932,1004,1075,1156,1242,1315,1394,1464", "endColumns": "104,86,93,98,89,78,92,94,84,71,70,80,85,72,78,69,117", "endOffsets": "205,292,386,485,575,654,747,842,927,999,1070,1151,1237,1310,1389,1459,1577"}, "to": {"startLines": "69,70,104,106,108,165,166,259,260,262,263,267,268,271,276,277,278", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5512,5617,9403,9572,9752,14145,14224,23016,23111,23279,23351,23747,23828,24089,24619,24698,24768", "endColumns": "104,86,93,98,89,78,92,94,84,71,70,80,85,72,78,69,117", "endOffsets": "5612,5699,9492,9666,9837,14219,14312,23106,23191,23346,23417,23823,23909,24157,24693,24763,24881"}}]}]}