{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-99:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\90e8727c8917f86c7d308f0c9abe80a3\\transformed\\material-1.4.0-beta01\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,305,406,547,631,695,789,859,920,1007,1071,1130,1204,1266,1320,1437,1495,1556,1610,1684,1806,1890,1986,2088,2166,2244,2333,2400,2466,2535,2612,2699,2771,2847,2929,3002,3087,3166,3256,3348,3422,3507,3597,3649,3714,3797,3882,3944,4008,4071,4188,4282,4382,4477", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,84,100,140,83,63,93,69,60,86,63,58,73,61,53,116,57,60,53,73,121,83,95,101,77,77,88,66,65,68,76,86,71,75,81,72,84,78,89,91,73,84,89,51,64,82,84,61,63,62,116,93,99,94,81", "endOffsets": "215,300,401,542,626,690,784,854,915,1002,1066,1125,1199,1261,1315,1432,1490,1551,1605,1679,1801,1885,1981,2083,2161,2239,2328,2395,2461,2530,2607,2694,2766,2842,2924,2997,3082,3161,3251,3343,3417,3502,3592,3644,3709,3792,3877,3939,4003,4066,4183,4277,4377,4472,4554"}, "to": {"startLines": "19,55,63,64,65,124,178,179,182,221,222,223,224,225,226,227,228,229,230,231,232,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,280", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "726,4174,4990,5091,5232,11861,16016,16110,16360,20719,20806,20870,20929,21003,21065,21119,21236,21294,21355,21409,21483,22095,22179,22275,22377,22455,22533,22622,22689,22755,22824,22901,22988,23060,23136,23218,23291,23376,23455,23545,23637,23711,23796,23886,23938,24003,24086,24171,24233,24297,24360,24477,24571,24671,25470", "endLines": "22,55,63,64,65,124,178,179,182,221,222,223,224,225,226,227,228,229,230,231,232,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,280", "endColumns": "12,84,100,140,83,63,93,69,60,86,63,58,73,61,53,116,57,60,53,73,121,83,95,101,77,77,88,66,65,68,76,86,71,75,81,72,84,78,89,91,73,84,89,51,64,82,84,61,63,62,116,93,99,94,81", "endOffsets": "886,4254,5086,5227,5311,11920,16105,16175,16416,20801,20865,20924,20998,21060,21114,21231,21289,21350,21404,21478,21600,22174,22270,22372,22450,22528,22617,22684,22750,22819,22896,22983,23055,23131,23213,23286,23371,23450,23540,23632,23706,23791,23881,23933,23998,24081,24166,24228,24292,24355,24472,24566,24666,24761,25547"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\35536fe73c76a56f94e622d08fe45089\\transformed\\leanback-1.0.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,410,503,635,747,846,937,1068,1199,1304,1425,1590,1713,1836,1944,2035,2164,2253,2359,2470,2577,2675,2790,2908,3024,3140,3255,3374,3494,3611,3725,3840,3927,4010,4114,4248,4403", "endColumns": "106,100,96,92,131,111,98,90,130,130,104,120,164,122,122,107,90,128,88,105,110,106,97,114,117,115,115,114,118,119,116,113,114,86,82,103,133,154,89", "endOffsets": "207,308,405,498,630,742,841,932,1063,1194,1299,1420,1585,1708,1831,1939,2030,2159,2248,2354,2465,2572,2670,2785,2903,3019,3135,3250,3369,3489,3606,3720,3835,3922,4005,4109,4243,4398,4488"}, "to": {"startLines": "183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,279", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16421,16528,16629,16726,16819,16951,17063,17162,17253,17384,17515,17620,17741,17906,18029,18152,18260,18351,18480,18569,18675,18786,18893,18991,19106,19224,19340,19456,19571,19690,19810,19927,20041,20156,20243,20326,20430,20564,25380", "endColumns": "106,100,96,92,131,111,98,90,130,130,104,120,164,122,122,107,90,128,88,105,110,106,97,114,117,115,115,114,118,119,116,113,114,86,82,103,133,154,89", "endOffsets": "16523,16624,16721,16814,16946,17058,17157,17248,17379,17510,17615,17736,17901,18024,18147,18255,18346,18475,18564,18670,18781,18888,18986,19101,19219,19335,19451,19566,19685,19805,19922,20036,20151,20238,20321,20425,20559,20714,25465"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b15b518726989b0578dc4af44fa5f9f8\\transformed\\media3-ui-1.2.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,484,676,768,858,936,1026,1123,1210,1276,1373,1471,1539,1605,1670,1740,1871,2000,2136,2208,2289,2363,2451,2545,2636,2703,2769,2822,2883,2931,2992,3065,3141,3201,3271,3329,3386,3452,3517,3583,3635,3694,3770,3846", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,91,89,77,89,96,86,65,96,97,67,65,64,69,130,128,135,71,80,73,87,93,90,66,65,52,60,47,60,72,75,59,69,57,56,65,64,65,51,58,75,75,54", "endOffsets": "280,479,671,763,853,931,1021,1118,1205,1271,1368,1466,1534,1600,1665,1735,1866,1995,2131,2203,2284,2358,2446,2540,2631,2698,2764,2817,2878,2926,2987,3060,3136,3196,3266,3324,3381,3447,3512,3578,3630,3689,3765,3841,3896"}, "to": {"startLines": "2,11,15,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,534,11925,12017,12107,12185,12275,12372,12459,12525,12622,12720,12788,12854,12919,12989,13120,13249,13385,13457,13538,13612,13700,13794,13885,13952,14706,14759,14820,14868,14929,15002,15078,15138,15208,15266,15323,15389,15454,15520,15572,15631,15707,15783", "endLines": "10,14,18,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "endColumns": "17,12,12,91,89,77,89,96,86,65,96,97,67,65,64,69,130,128,135,71,80,73,87,93,90,66,65,52,60,47,60,72,75,59,69,57,56,65,64,65,51,58,75,75,54", "endOffsets": "330,529,721,12012,12102,12180,12270,12367,12454,12520,12617,12715,12783,12849,12914,12984,13115,13244,13380,13452,13533,13607,13695,13789,13880,13947,14013,14754,14815,14863,14924,14997,15073,15133,15203,15261,15318,15384,15449,15515,15567,15626,15702,15778,15833"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\677a264eecd9b3484ff2620a344ffab3\\transformed\\material3-1.1.2\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,287,420,541,622,716,828,960,1097,1249,1329,1423,1511,1604,1716,1831,1930,2062,2192,2325,2496,2621,2734,2851,2969,3058,3152,3270,3403,3502,3623,3724,3853,3988,4093,4190,4262,4348,4430,4511,4619,4704,4784,4880,4977,5069,5162,5245,5350,5445,5540,5684,5770,5885", "endColumns": "118,112,132,120,80,93,111,131,136,151,79,93,87,92,111,114,98,131,129,132,170,124,112,116,117,88,93,117,132,98,120,100,128,134,104,96,71,85,81,80,107,84,79,95,96,91,92,82,104,94,94,143,85,114,103", "endOffsets": "169,282,415,536,617,711,823,955,1092,1244,1324,1418,1506,1599,1711,1826,1925,2057,2187,2320,2491,2616,2729,2846,2964,3053,3147,3265,3398,3497,3618,3719,3848,3983,4088,4185,4257,4343,4425,4506,4614,4699,4779,4875,4972,5064,5157,5240,5345,5440,5535,5679,5765,5880,5984"}, "to": {"startLines": "51,52,53,54,68,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,122,177,284,287,289,294,295,296,297,298,299,300,301,302,303,304,305,306,307", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3688,3807,3920,4053,5493,7852,7946,8058,8190,8327,8479,8559,8653,8741,8834,8946,9061,9160,9292,9422,9555,9726,9851,9964,10081,10199,10288,10382,10500,10633,10732,10853,10954,11083,11218,11323,11704,15930,25865,26102,26284,26826,26911,26991,27087,27184,27276,27369,27452,27557,27652,27747,27891,27977,28092", "endColumns": "118,112,132,120,80,93,111,131,136,151,79,93,87,92,111,114,98,131,129,132,170,124,112,116,117,88,93,117,132,98,120,100,128,134,104,96,71,85,81,80,107,84,79,95,96,91,92,82,104,94,94,143,85,114,103", "endOffsets": "3802,3915,4048,4169,5569,7941,8053,8185,8322,8474,8554,8648,8736,8829,8941,9056,9155,9287,9417,9550,9721,9846,9959,10076,10194,10283,10377,10495,10628,10727,10848,10949,11078,11213,11318,11415,11771,16011,25942,26178,26387,26906,26986,27082,27179,27271,27364,27447,27552,27647,27742,27886,27972,28087,28191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2907b06edc976dc86930b7aa57b04408\\transformed\\media3-session-1.2.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,140,214,285,354,435,520,610", "endColumns": "84,73,70,68,80,84,89,93", "endOffsets": "135,209,280,349,430,515,605,699"}, "to": {"startLines": "50,120,233,234,235,236,237,238", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3603,11530,21605,21676,21745,21826,21911,22001", "endColumns": "84,73,70,68,80,84,89,93", "endOffsets": "3683,11599,21671,21740,21821,21906,21996,22090"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f70384b9a1e1b9ce2411b7c78f9fa615\\transformed\\preference-1.2.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,268,347,488,657,738", "endColumns": "70,91,78,140,168,80,78", "endOffsets": "171,263,342,483,652,733,812"}, "to": {"startLines": "87,176,276,281,290,308,309", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7781,15838,25168,25552,26392,28196,28277", "endColumns": "70,91,78,140,168,80,78", "endOffsets": "7847,15925,25242,25688,26556,28272,28351"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e3384bca41f271b3200ce0446ec0f087\\transformed\\media3-exoplayer-1.2.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,191,262,333,420,491,578,662", "endColumns": "70,64,70,70,86,70,86,83,80", "endOffsets": "121,186,257,328,415,486,573,657,738"}, "to": {"startLines": "149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "14018,14089,14154,14225,14296,14383,14454,14541,14625", "endColumns": "70,64,70,70,86,70,86,83,80", "endOffsets": "14084,14149,14220,14291,14378,14449,14536,14620,14701"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\31a00a017e90aa446708533c4ea3d047\\transformed\\core-1.12.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,571,675,786", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "150,252,355,462,566,670,781,882"}, "to": {"startLines": "56,57,58,59,60,61,62,288", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4259,4359,4461,4564,4671,4775,4879,26183", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "4354,4456,4559,4666,4770,4874,4985,26279"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\62c17cba0a75b790331013fbfad47644\\transformed\\play-services-basement-18.1.0\\res\\values-ky\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "153", "endOffsets": "348"}, "to": {"startLines": "77", "startColumns": "4", "startOffsets": "6561", "endColumns": "157", "endOffsets": "6714"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\804c4e5761ff122de1231662730d5f72\\transformed\\ui-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,392,492,577,659,757,846,931,997,1064,1149,1236,1309,1388,1456", "endColumns": "92,83,109,99,84,81,97,88,84,65,66,84,86,72,78,67,117", "endOffsets": "193,277,387,487,572,654,752,841,926,992,1059,1144,1231,1304,1383,1451,1569"}, "to": {"startLines": "66,67,119,121,123,180,181,274,275,277,278,282,283,286,291,292,293", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5316,5409,11420,11604,11776,16180,16262,24994,25083,25247,25313,25693,25778,26029,26561,26640,26708", "endColumns": "92,83,109,99,84,81,97,88,84,65,66,84,86,72,78,67,117", "endOffsets": "5404,5488,11525,11699,11856,16257,16355,25078,25163,25308,25375,25773,25860,26097,26635,26703,26821"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cb0d38356f8a450289b6a883ab8edf14\\transformed\\navigation-ui-2.7.6\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,117", "endOffsets": "160,278"}, "to": {"startLines": "272,273", "startColumns": "4,4", "startOffsets": "24766,24876", "endColumns": "109,117", "endOffsets": "24871,24989"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\de0470acc971f743e832aff97e9527de\\transformed\\appcompat-1.6.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,325,437,522,627,744,823,901,992,1085,1180,1274,1374,1467,1562,1657,1748,1839,1920,2026,2131,2229,2336,2439,2554,2715,2817", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "211,320,432,517,622,739,818,896,987,1080,1175,1269,1369,1462,1557,1652,1743,1834,1915,2021,2126,2224,2331,2434,2549,2710,2812,2894"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,285", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "891,1002,1111,1223,1308,1413,1530,1609,1687,1778,1871,1966,2060,2160,2253,2348,2443,2534,2625,2706,2812,2917,3015,3122,3225,3340,3501,25947", "endColumns": "110,108,111,84,104,116,78,77,90,92,94,93,99,92,94,94,90,90,80,105,104,97,106,102,114,160,101,81", "endOffsets": "997,1106,1218,1303,1408,1525,1604,1682,1773,1866,1961,2055,2155,2248,2343,2438,2529,2620,2701,2807,2912,3010,3117,3220,3335,3496,3598,26024"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc96508f556f069c09e91b3a9267645a\\transformed\\play-services-base-18.0.1\\res\\values-ky\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,440,565,668,809,932,1043,1148,1313,1416,1562,1688,1821,1984,2044,2100", "endColumns": "101,144,124,102,140,122,110,104,164,102,145,125,132,162,59,55,73", "endOffsets": "294,439,564,667,808,931,1042,1147,1312,1415,1561,1687,1820,1983,2043,2099,2173"}, "to": {"startLines": "69,70,71,72,73,74,75,76,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5574,5680,5829,5958,6065,6210,6337,6452,6719,6888,6995,7145,7275,7412,7579,7643,7703", "endColumns": "105,148,128,106,144,126,114,108,168,106,149,129,136,166,63,59,77", "endOffsets": "5675,5824,5953,6060,6205,6332,6447,6556,6883,6990,7140,7270,7407,7574,7638,7698,7776"}}]}]}