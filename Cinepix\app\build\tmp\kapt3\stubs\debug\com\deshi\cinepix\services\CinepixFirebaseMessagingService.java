package com.deshi.cinepix.services;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0003\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u0006H\u0002J\u0012\u0010\u0007\u001a\u0004\u0018\u00010\b2\u0006\u0010\t\u001a\u00020\u0006H\u0002J\u0010\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rH\u0016J\u0010\u0010\u000e\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u0006H\u0016J,\u0010\u0010\u001a\u00020\u000b2\u0006\u0010\u0011\u001a\u00020\u00062\u0006\u0010\u0012\u001a\u00020\u00062\b\u0010\t\u001a\u0004\u0018\u00010\u00062\b\u0010\u0005\u001a\u0004\u0018\u00010\u0006H\u0002J\u0010\u0010\u0013\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u0006H\u0002J\u0010\u0010\u0014\u001a\u00020\u000b2\u0006\u0010\u0015\u001a\u00020\u0016H\u0002J\b\u0010\u0017\u001a\u00020\u000bH\u0002\u00a8\u0006\u0018"}, d2 = {"Lcom/deshi/cinepix/services/CinepixFirebaseMessagingService;", "Lcom/google/firebase/messaging/FirebaseMessagingService;", "()V", "createNotificationIntent", "Landroid/content/Intent;", "clickAction", "", "getBitmapFromUrl", "Landroid/graphics/Bitmap;", "imageUrl", "onMessageReceived", "", "remoteMessage", "Lcom/google/firebase/messaging/RemoteMessage;", "onNewToken", "token", "sendNotification", "title", "body", "sendTokenToServer", "showNotification", "notificationBuilder", "Landroidx/core/app/NotificationCompat$Builder;", "subscribeToTopics", "app_debug"})
public final class CinepixFirebaseMessagingService extends com.google.firebase.messaging.FirebaseMessagingService {
    
    public CinepixFirebaseMessagingService() {
        super();
    }
    
    @java.lang.Override
    public void onMessageReceived(@org.jetbrains.annotations.NotNull
    com.google.firebase.messaging.RemoteMessage remoteMessage) {
    }
    
    @java.lang.Override
    public void onNewToken(@org.jetbrains.annotations.NotNull
    java.lang.String token) {
    }
    
    private final void sendTokenToServer(java.lang.String token) {
    }
    
    private final void subscribeToTopics() {
    }
    
    private final void sendNotification(java.lang.String title, java.lang.String body, java.lang.String imageUrl, java.lang.String clickAction) {
    }
    
    private final android.content.Intent createNotificationIntent(java.lang.String clickAction) {
        return null;
    }
    
    private final void showNotification(androidx.core.app.NotificationCompat.Builder notificationBuilder) {
    }
    
    private final android.graphics.Bitmap getBitmapFromUrl(java.lang.String imageUrl) {
        return null;
    }
}