package com.deshi.cinepix.ui.webview;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0018\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\bH\u0007J\u0010\u0010\n\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\bH\u0007J\u0018\u0010\f\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\r\u001a\u00020\bH\u0007J\u0010\u0010\u000e\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\bH\u0007R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/deshi/cinepix/ui/webview/SimpleJavaScriptInterface;", "", "activity", "Lcom/deshi/cinepix/ui/webview/SimpleWebViewActivity;", "(Lcom/deshi/cinepix/ui/webview/SimpleWebViewActivity;)V", "handleDownload", "", "url", "", "filename", "logMessage", "message", "playVideo", "title", "showToast", "app_debug"})
public final class SimpleJavaScriptInterface {
    @org.jetbrains.annotations.NotNull
    private final com.deshi.cinepix.ui.webview.SimpleWebViewActivity activity = null;
    
    public SimpleJavaScriptInterface(@org.jetbrains.annotations.NotNull
    com.deshi.cinepix.ui.webview.SimpleWebViewActivity activity) {
        super();
    }
    
    @android.webkit.JavascriptInterface
    public final void playVideo(@org.jetbrains.annotations.NotNull
    java.lang.String url, @org.jetbrains.annotations.NotNull
    java.lang.String title) {
    }
    
    @android.webkit.JavascriptInterface
    public final void showToast(@org.jetbrains.annotations.NotNull
    java.lang.String message) {
    }
    
    @android.webkit.JavascriptInterface
    public final void logMessage(@org.jetbrains.annotations.NotNull
    java.lang.String message) {
    }
    
    @android.webkit.JavascriptInterface
    public final void handleDownload(@org.jetbrains.annotations.NotNull
    java.lang.String url, @org.jetbrains.annotations.NotNull
    java.lang.String filename) {
    }
}