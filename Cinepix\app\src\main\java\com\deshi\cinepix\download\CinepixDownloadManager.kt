package com.deshi.cinepix.download

import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import android.os.Environment
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import okhttp3.*
import okio.*
import java.io.File
import java.io.IOException
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import com.deshi.cinepix.data.*
import com.deshi.cinepix.R

class CinepixDownloadManager(
    private val context: Context,
    private val database: DownloadDatabase
) {
    private val downloadDao = database.downloadDao()
    private val activeDownloads = ConcurrentHashMap<String, Job>()
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
        .readTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
        .writeTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
        .callTimeout(300, java.util.concurrent.TimeUnit.SECONDS)
        .retryOnConnectionFailure(true)
        .followRedirects(true)
        .followSslRedirects(true)
        .build()

    private val downloadScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val notificationManager = NotificationManagerCompat.from(context)

    companion object {
        private const val NOTIFICATION_CHANNEL_ID = "cinepix_downloads"
        private const val NOTIFICATION_CHANNEL_NAME = "Downloads"
    }

    init {
        createNotificationChannel()
    }
    
    fun getAllDownloads(): Flow<List<DownloadItem>> = downloadDao.getAllDownloads()
    
    fun getActiveDownloads(): Flow<List<DownloadItem>> = 
        downloadDao.getDownloadsByStatus(DownloadStatus.DOWNLOADING)
    
    fun getCompletedDownloads(): Flow<List<DownloadItem>> =
        downloadDao.getDownloadsByStatus(DownloadStatus.COMPLETED)

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                NOTIFICATION_CHANNEL_ID,
                NOTIFICATION_CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Download progress notifications"
                setShowBadge(false)
            }

            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun showDownloadNotification(downloadItem: DownloadItem) {
        val notification = NotificationCompat.Builder(context, NOTIFICATION_CHANNEL_ID)
            .setContentTitle("Downloading: ${downloadItem.title}")
            .setContentText("${downloadItem.progress}% completed")
            .setSmallIcon(R.drawable.ic_download_24)
            .setProgress(100, downloadItem.progress, false)
            .setOngoing(true)
            .setAutoCancel(false)
            .build()

        try {
            notificationManager.notify(downloadItem.id.hashCode(), notification)
        } catch (e: SecurityException) {
            // Handle permission not granted
        }
    }

    private fun showDownloadCompleteNotification(downloadItem: DownloadItem) {
        val notification = NotificationCompat.Builder(context, NOTIFICATION_CHANNEL_ID)
            .setContentTitle("Download Complete")
            .setContentText(downloadItem.title)
            .setSmallIcon(R.drawable.ic_download_24)
            .setAutoCancel(true)
            .build()

        try {
            notificationManager.notify(downloadItem.id.hashCode(), notification)
        } catch (e: SecurityException) {
            // Handle permission not granted
        }
    }
    
    suspend fun startDownload(
        url: String,
        title: String,
        fileName: String? = null,
        quality: String? = null,
        thumbnailUrl: String? = null
    ): String {
        val downloadId = UUID.randomUUID().toString()
        val finalFileName = fileName ?: generateFileName(title, url)
        val downloadDir = getDownloadDirectory()
        val filePath = File(downloadDir, finalFileName).absolutePath

        // Resolve the actual download URL if it's a short link
        val resolvedUrl = resolveDownloadUrl(url)

        val downloadItem = DownloadItem(
            id = downloadId,
            title = title,
            url = resolvedUrl,
            fileName = finalFileName,
            filePath = filePath,
            quality = quality,
            thumbnailUrl = thumbnailUrl,
            status = DownloadStatus.PENDING
        )

        downloadDao.insertDownload(downloadItem)

        val job = downloadScope.launch {
            try {
                android.util.Log.d("CinepixDownloadManager", "Starting download: $title from $resolvedUrl")
                downloadFile(downloadItem)
            } catch (e: Exception) {
                android.util.Log.e("CinepixDownloadManager", "Download failed: $title", e)
                downloadDao.updateDownloadStatus(downloadId, DownloadStatus.FAILED)
                activeDownloads.remove(downloadId)
            }
        }

        activeDownloads[downloadId] = job
        return downloadId
    }
    
    suspend fun pauseDownload(downloadId: String) {
        activeDownloads[downloadId]?.cancel()
        activeDownloads.remove(downloadId)
        downloadDao.updateDownloadStatus(downloadId, DownloadStatus.PAUSED)
    }
    
    suspend fun resumeDownload(downloadId: String) {
        val downloadItem = downloadDao.getDownloadById(downloadId)
        if (downloadItem != null && downloadItem.status == DownloadStatus.PAUSED) {
            val job = downloadScope.launch {
                try {
                    downloadFile(downloadItem)
                } catch (e: Exception) {
                    downloadDao.updateDownloadStatus(downloadId, DownloadStatus.FAILED)
                    activeDownloads.remove(downloadId)
                }
            }
            activeDownloads[downloadId] = job
        }
    }
    
    suspend fun cancelDownload(downloadId: String) {
        activeDownloads[downloadId]?.cancel()
        activeDownloads.remove(downloadId)
        
        val downloadItem = downloadDao.getDownloadById(downloadId)
        if (downloadItem != null) {
            // Delete partial file
            downloadItem.filePath?.let { path ->
                File(path).delete()
            }
            downloadDao.updateDownloadStatus(downloadId, DownloadStatus.CANCELLED)
        }
    }
    
    suspend fun deleteDownload(downloadId: String) {
        cancelDownload(downloadId)
        val downloadItem = downloadDao.getDownloadById(downloadId)
        if (downloadItem != null) {
            // Delete file
            downloadItem.filePath?.let { path ->
                File(path).delete()
            }
            downloadDao.deleteDownloadById(downloadId)
        }
    }
    
    private suspend fun resolveDownloadUrl(url: String): String {
        return try {
            // Check if it's already a direct video file URL
            if (isDirectVideoUrl(url)) {
                return url
            }

            // For short links or redirect URLs, follow redirects to get the final URL
            val request = Request.Builder()
                .url(url)
                .head() // Use HEAD request to avoid downloading content
                .build()

            val response = okHttpClient.newCall(request).execute()

            // Follow redirects manually to get the final URL
            var finalUrl = url
            var currentResponse = response
            var redirectCount = 0
            val maxRedirects = 10

            while (currentResponse.isRedirect && redirectCount < maxRedirects) {
                val location = currentResponse.header("Location")
                if (location != null) {
                    finalUrl = if (location.startsWith("http")) {
                        location
                    } else {
                        // Relative URL, construct absolute URL
                        val baseUrl = currentResponse.request.url
                        baseUrl.resolve(location)?.toString() ?: location
                    }

                    currentResponse.close()

                    val newRequest = Request.Builder()
                        .url(finalUrl)
                        .head()
                        .build()

                    currentResponse = okHttpClient.newCall(newRequest).execute()
                    redirectCount++
                } else {
                    break
                }
            }

            currentResponse.close()

            // Check if the final URL is a direct video file
            if (isDirectVideoUrl(finalUrl)) {
                finalUrl
            } else {
                // If still not a direct video URL, try to extract from content
                extractVideoUrlFromPage(finalUrl)
            }
        } catch (e: Exception) {
            android.util.Log.e("CinepixDownloadManager", "Error resolving URL: $url", e)
            url // Return original URL if resolution fails
        }
    }

    private fun isDirectVideoUrl(url: String): Boolean {
        val videoExtensions = listOf(".mp4", ".mkv", ".avi", ".mov", ".wmv", ".flv", ".webm", ".m4v")
        return videoExtensions.any { url.lowercase().contains(it) } ||
               url.contains("googlevideo.com") ||
               url.contains("googleusercontent.com") ||
               url.contains("drive.google.com/uc?") ||
               url.contains("dropbox.com") && url.contains("dl=1")
    }

    private suspend fun extractVideoUrlFromPage(url: String): String {
        return try {
            val request = Request.Builder()
                .url(url)
                .build()

            val response = okHttpClient.newCall(request).execute()
            val html = response.body?.string() ?: ""
            response.close()

            // Look for common video URL patterns in the HTML
            val videoUrlPatterns = listOf(
                Regex("""src\s*=\s*["']([^"']*\.(?:mp4|mkv|avi|mov|wmv|flv|webm|m4v)[^"']*)["']""", RegexOption.IGNORE_CASE),
                Regex("""href\s*=\s*["']([^"']*\.(?:mp4|mkv|avi|mov|wmv|flv|webm|m4v)[^"']*)["']""", RegexOption.IGNORE_CASE),
                Regex("""url\s*:\s*["']([^"']*\.(?:mp4|mkv|avi|mov|wmv|flv|webm|m4v)[^"']*)["']""", RegexOption.IGNORE_CASE),
                Regex("""["']([^"']*googlevideo\.com[^"']*)["']"""),
                Regex("""["']([^"']*googleusercontent\.com[^"']*)["']"""),
                Regex("""["']([^"']*drive\.google\.com/uc\?[^"']*)["']""")
            )

            for (pattern in videoUrlPatterns) {
                val match = pattern.find(html)
                if (match != null) {
                    val videoUrl = match.groupValues[1]
                    if (videoUrl.isNotEmpty() && videoUrl.startsWith("http")) {
                        return videoUrl
                    }
                }
            }

            url // Return original URL if no video URL found
        } catch (e: Exception) {
            android.util.Log.e("CinepixDownloadManager", "Error extracting video URL from page: $url", e)
            url
        }
    }

    private suspend fun downloadFile(downloadItem: DownloadItem) {
        downloadDao.updateDownloadStatus(downloadItem.id, DownloadStatus.DOWNLOADING)

        android.util.Log.d("CinepixDownloadManager", "Downloading from URL: ${downloadItem.url}")

        val request = Request.Builder()
            .url(downloadItem.url)
            .addHeader("User-Agent", "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36")
            .build()

        val response = okHttpClient.newCall(request).execute()
        if (!response.isSuccessful) {
            android.util.Log.e("CinepixDownloadManager", "Download failed with code: ${response.code} for URL: ${downloadItem.url}")
            throw IOException("Download failed: ${response.code} - ${response.message}")
        }
        
        val body = response.body ?: throw IOException("Response body is null")
        val contentLength = body.contentLength()
        
        val file = File(downloadItem.filePath!!)
        file.parentFile?.mkdirs()
        
        val sink = file.sink().buffer()
        val source = body.source()
        
        var totalBytesRead = 0L
        val buffer = Buffer()
        
        try {
            var updateCounter = 0
            while (source.read(buffer, 65536) != -1L) { // Increased buffer size to 64KB
                sink.write(buffer, buffer.size)
                totalBytesRead += buffer.size

                val progress = if (contentLength > 0) {
                    ((totalBytesRead * 100) / contentLength).toInt()
                } else 0

                // Update progress less frequently for better performance
                updateCounter++
                if (updateCounter % 10 == 0 || progress == 100) {
                    downloadDao.updateDownloadProgress(
                        downloadItem.id,
                        progress,
                        totalBytesRead
                    )

                    // Show notification every 10% progress
                    if (progress % 10 == 0) {
                        val updatedItem = downloadItem.copy(progress = progress, downloadedSize = totalBytesRead)
                        showDownloadNotification(updatedItem)
                    }
                }
                
                // Check if download was cancelled
                if (!activeDownloads.containsKey(downloadItem.id)) {
                    sink.close()
                    source.close()
                    file.delete()
                    return
                }
            }
            
            sink.close()
            source.close()
            
            // Update as completed
            val completedItem = downloadItem.copy(
                status = DownloadStatus.COMPLETED,
                progress = 100,
                downloadedSize = totalBytesRead,
                fileSize = contentLength,
                completedAt = System.currentTimeMillis()
            )
            downloadDao.updateDownload(completedItem)

            // Show completion notification
            showDownloadCompleteNotification(completedItem)

            activeDownloads.remove(downloadItem.id)
            
        } catch (e: Exception) {
            sink.close()
            source.close()
            file.delete()
            throw e
        }
    }
    
    private fun getDownloadDirectory(): File {
        val downloadDir = File(
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
            "Cinepix"
        )
        if (!downloadDir.exists()) {
            downloadDir.mkdirs()
        }
        return downloadDir
    }
    
    private fun generateFileName(title: String, url: String): String {
        val cleanTitle = title.replace(Regex("[^a-zA-Z0-9\\s]"), "").trim()
        val extension = when {
            url.contains(".mp4") -> ".mp4"
            url.contains(".mkv") -> ".mkv"
            url.contains(".avi") -> ".avi"
            else -> ".mp4"
        }
        return "${cleanTitle}${extension}"
    }
    
    fun cleanup() {
        downloadScope.cancel()
        activeDownloads.clear()
    }
}
