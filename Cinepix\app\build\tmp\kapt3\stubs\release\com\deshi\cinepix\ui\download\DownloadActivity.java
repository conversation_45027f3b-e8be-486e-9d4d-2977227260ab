package com.deshi.cinepix.ui.download;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0005\u001a\u00020\u00062\b\u0010\u0007\u001a\u0004\u0018\u00010\bH\u0014J\b\u0010\t\u001a\u00020\u0006H\u0014J\u0018\u0010\n\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\fH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/deshi/cinepix/ui/download/DownloadActivity;", "Landroidx/activity/ComponentActivity;", "()V", "downloadManager", "Lcom/deshi/cinepix/download/CinepixDownloadManager;", "onCreate", "", "savedInstanceState", "Landroid/os/Bundle;", "onDestroy", "startDownload", "url", "", "filename", "app_release"})
public final class DownloadActivity extends androidx.activity.ComponentActivity {
    private com.deshi.cinepix.download.CinepixDownloadManager downloadManager;
    
    public DownloadActivity() {
        super();
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void startDownload(java.lang.String url, java.lang.String filename) {
    }
    
    @java.lang.Override
    protected void onDestroy() {
    }
}