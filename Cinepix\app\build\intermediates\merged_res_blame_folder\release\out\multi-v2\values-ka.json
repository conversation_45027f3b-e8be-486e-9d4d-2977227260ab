{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeReleaseResources-87:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "56,57,58,59,60,61,62,270", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4209,4305,4407,4506,4605,4711,4815,23982", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "4300,4402,4501,4600,4706,4810,4928,24078"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,127,204,272,340,421,504,599", "endColumns": "71,76,67,67,80,82,94,96", "endOffsets": "122,199,267,335,416,499,594,691"}, "to": {"startLines": "50,102,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3599,9219,19346,19414,19482,19563,19646,19741", "endColumns": "71,76,67,67,80,82,94,96", "endOffsets": "3666,9291,19409,19477,19558,19641,19736,19833"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,265,350,490,659,745", "endColumns": "71,87,84,139,168,85,80", "endOffsets": "172,260,345,485,654,740,821"}, "to": {"startLines": "69,158,258,263,272,290,291", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5504,13553,22945,23343,24192,25935,26021", "endColumns": "71,87,84,139,168,85,80", "endOffsets": "5571,13636,23025,23478,24356,26016,26097"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,386,489,579,659,755,845,932,1003,1072,1161,1252,1324,1403,1473", "endColumns": "95,85,98,102,89,79,95,89,86,70,68,88,90,71,78,69,120", "endOffsets": "196,282,381,484,574,654,750,840,927,998,1067,1156,1247,1319,1398,1468,1589"}, "to": {"startLines": "66,67,101,103,105,162,163,256,257,259,260,264,265,268,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5245,5341,9120,9296,9472,13888,13968,22768,22858,23030,23101,23483,23572,23827,24361,24440,24510", "endColumns": "95,85,98,102,89,79,95,89,86,70,68,88,90,71,78,69,120", "endOffsets": "5336,5422,9214,9394,9557,13963,14059,22853,22940,23096,23165,23567,23658,23894,24435,24505,24626"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,510,636,749,848,940,1074,1205,1311,1442,1594,1719,1841,1954,2045,2177,2268,2375,2485,2591,2692,2805,2928,3048,3165,3274,3384,3508,3630,3752,3872,3959,4042,4146,4283,4437", "endColumns": "106,100,99,96,125,112,98,91,133,130,105,130,151,124,121,112,90,131,90,106,109,105,100,112,122,119,116,108,109,123,121,121,119,86,82,103,136,153,92", "endOffsets": "207,308,408,505,631,744,843,935,1069,1200,1306,1437,1589,1714,1836,1949,2040,2172,2263,2370,2480,2586,2687,2800,2923,3043,3160,3269,3379,3503,3625,3747,3867,3954,4037,4141,4278,4432,4525"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14127,14234,14335,14435,14532,14658,14771,14870,14962,15096,15227,15333,15464,15616,15741,15863,15976,16067,16199,16290,16397,16507,16613,16714,16827,16950,17070,17187,17296,17406,17530,17652,17774,17894,17981,18064,18168,18305,23170", "endColumns": "106,100,99,96,125,112,98,91,133,130,105,130,151,124,121,112,90,131,90,106,109,105,100,112,122,119,116,108,109,123,121,121,119,86,82,103,136,153,92", "endOffsets": "14229,14330,14430,14527,14653,14766,14865,14957,15091,15222,15328,15459,15611,15736,15858,15971,16062,16194,16285,16392,16502,16608,16709,16822,16945,17065,17182,17291,17401,17525,17647,17769,17889,17976,18059,18163,18300,18454,23258"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "896,1004,1107,1218,1304,1409,1522,1605,1684,1775,1868,1963,2057,2157,2250,2345,2440,2531,2622,2703,2816,2922,3020,3133,3238,3342,3500,23745", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "999,1102,1213,1299,1404,1517,1600,1679,1770,1863,1958,2052,2152,2245,2340,2435,2526,2617,2698,2811,2917,3015,3128,3233,3337,3495,3594,23822"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,290,483,673,757,841,920,1018,1120,1205,1270,1369,1468,1533,1598,1662,1729,1857,1986,2113,2188,2267,2341,2426,2522,2618,2685,2751,2804,2865,2913,2974,3040,3119,3183,3251,3315,3376,3442,3508,3574,3626,3688,3764,3840", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,83,83,78,97,101,84,64,98,98,64,64,63,66,127,128,126,74,78,73,84,95,95,66,65,52,60,47,60,65,78,63,67,63,60,65,65,65,51,61,75,75,52", "endOffsets": "285,478,668,752,836,915,1013,1115,1200,1265,1364,1463,1528,1593,1657,1724,1852,1981,2108,2183,2262,2336,2421,2517,2613,2680,2746,2799,2860,2908,2969,3035,3114,3178,3246,3310,3371,3437,3503,3569,3621,3683,3759,3835,3888"}, "to": {"startLines": "2,11,15,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,340,533,9629,9713,9797,9876,9974,10076,10161,10226,10325,10424,10489,10554,10618,10685,10813,10942,11069,11144,11223,11297,11382,11478,11574,11641,12411,12464,12525,12573,12634,12700,12779,12843,12911,12975,13036,13102,13168,13234,13286,13348,13424,13500", "endLines": "10,14,18,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "endColumns": "17,12,12,83,83,78,97,101,84,64,98,98,64,64,63,66,127,128,126,74,78,73,84,95,95,66,65,52,60,47,60,65,78,63,67,63,60,65,65,65,51,61,75,75,52", "endOffsets": "335,528,718,9708,9792,9871,9969,10071,10156,10221,10320,10419,10484,10549,10613,10680,10808,10937,11064,11139,11218,11292,11377,11473,11569,11636,11702,12459,12520,12568,12629,12695,12774,12838,12906,12970,13031,13097,13163,13229,13281,13343,13419,13495,13548"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,192,270,343,424,499,587,674", "endColumns": "71,64,77,72,80,74,87,86,84", "endOffsets": "122,187,265,338,419,494,582,669,754"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11707,11779,11844,11922,11995,12076,12151,12239,12326", "endColumns": "71,64,77,72,80,74,87,86,84", "endOffsets": "11774,11839,11917,11990,12071,12146,12234,12321,12406"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,117", "endOffsets": "159,277"}, "to": {"startLines": "254,255", "startColumns": "4,4", "startOffsets": "22541,22650", "endColumns": "108,117", "endOffsets": "22645,22763"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,282,399,509,586,681,794,926,1040,1184,1266,1364,1454,1548,1666,1782,1885,2016,2149,2280,2448,2573,2686,2801,2919,3010,3103,3217,3352,3451,3549,3656,3789,3925,4032,4130,4203,4284,4366,4449,4558,4634,4715,4812,4911,5001,5099,5181,5283,5375,5478,5591,5667,5769", "endColumns": "113,112,116,109,76,94,112,131,113,143,81,97,89,93,117,115,102,130,132,130,167,124,112,114,117,90,92,113,134,98,97,106,132,135,106,97,72,80,81,82,108,75,80,96,98,89,97,81,101,91,102,112,75,101,92", "endOffsets": "164,277,394,504,581,676,789,921,1035,1179,1261,1359,1449,1543,1661,1777,1880,2011,2144,2275,2443,2568,2681,2796,2914,3005,3098,3212,3347,3446,3544,3651,3784,3920,4027,4125,4198,4279,4361,4444,4553,4629,4710,4807,4906,4996,5094,5176,5278,5370,5473,5586,5662,5764,5857"}, "to": {"startLines": "51,52,53,54,68,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,104,159,266,269,271,276,277,278,279,280,281,282,283,284,285,286,287,288,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3671,3785,3898,4015,5427,5576,5671,5784,5916,6030,6174,6256,6354,6444,6538,6656,6772,6875,7006,7139,7270,7438,7563,7676,7791,7909,8000,8093,8207,8342,8441,8539,8646,8779,8915,9022,9399,13641,23663,23899,24083,24631,24707,24788,24885,24984,25074,25172,25254,25356,25448,25551,25664,25740,25842", "endColumns": "113,112,116,109,76,94,112,131,113,143,81,97,89,93,117,115,102,130,132,130,167,124,112,114,117,90,92,113,134,98,97,106,132,135,106,97,72,80,81,82,108,75,80,96,98,89,97,81,101,91,102,112,75,101,92", "endOffsets": "3780,3893,4010,4120,5499,5666,5779,5911,6025,6169,6251,6349,6439,6533,6651,6767,6870,7001,7134,7265,7433,7558,7671,7786,7904,7995,8088,8202,8337,8436,8534,8641,8774,8910,9017,9115,9467,13717,23740,23977,24187,24702,24783,24880,24979,25069,25167,25249,25351,25443,25546,25659,25735,25837,25930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,228,312,411,536,624,691,788,857,920,1007,1073,1133,1202,1263,1317,1432,1491,1551,1605,1677,1807,1895,1979,2087,2165,2241,2335,2402,2468,2541,2619,2705,2778,2856,2934,3009,3099,3174,3268,3366,3440,3517,3617,3670,3738,3827,3916,3978,4043,4106,4213,4311,4411,4510", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,83,98,124,87,66,96,68,62,86,65,59,68,60,53,114,58,59,53,71,129,87,83,107,77,75,93,66,65,72,77,85,72,77,77,74,89,74,93,97,73,76,99,52,67,88,88,61,64,62,106,97,99,98,79", "endOffsets": "223,307,406,531,619,686,783,852,915,1002,1068,1128,1197,1258,1312,1427,1486,1546,1600,1672,1802,1890,1974,2082,2160,2236,2330,2397,2463,2536,2614,2700,2773,2851,2929,3004,3094,3169,3263,3361,3435,3512,3612,3665,3733,3822,3911,3973,4038,4101,4208,4306,4406,4505,4585"}, "to": {"startLines": "19,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "723,4125,4933,5032,5157,9562,13722,13819,14064,18459,18546,18612,18672,18741,18802,18856,18971,19030,19090,19144,19216,19838,19926,20010,20118,20196,20272,20366,20433,20499,20572,20650,20736,20809,20887,20965,21040,21130,21205,21299,21397,21471,21548,21648,21701,21769,21858,21947,22009,22074,22137,22244,22342,22442,23263", "endLines": "22,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "endColumns": "12,83,98,124,87,66,96,68,62,86,65,59,68,60,53,114,58,59,53,71,129,87,83,107,77,75,93,66,65,72,77,85,72,77,77,74,89,74,93,97,73,76,99,52,67,88,88,61,64,62,106,97,99,98,79", "endOffsets": "891,4204,5027,5152,5240,9624,13814,13883,14122,18541,18607,18667,18736,18797,18851,18966,19025,19085,19139,19211,19341,19921,20005,20113,20191,20267,20361,20428,20494,20567,20645,20731,20804,20882,20960,21035,21125,21200,21294,21392,21466,21543,21643,21696,21764,21853,21942,22004,22069,22132,22239,22337,22437,22536,23338"}}]}]}