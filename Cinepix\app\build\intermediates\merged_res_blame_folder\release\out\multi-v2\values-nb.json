{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeReleaseResources-87:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,128,201,277,347,426,509,607", "endColumns": "72,72,75,69,78,82,97,103", "endOffsets": "123,196,272,342,421,504,602,706"}, "to": {"startLines": "50,102,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3515,9020,18920,18996,19066,19145,19228,19326", "endColumns": "72,72,75,69,78,82,97,103", "endOffsets": "3583,9088,18991,19061,19140,19223,19321,19425"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,279,382,491,566,656,764,904,1022,1168,1248,1344,1429,1519,1622,1736,1837,1960,2078,2207,2370,2493,2606,2725,2843,2929,3023,3138,3271,3368,3474,3573,3702,3839,3940,4033,4109,4182,4262,4342,4449,4525,4605,4702,4797,4884,4980,5064,5165,5263,5363,5476,5552,5651", "endColumns": "112,110,102,108,74,89,107,139,117,145,79,95,84,89,102,113,100,122,117,128,162,122,112,118,117,85,93,114,132,96,105,98,128,136,100,92,75,72,79,79,106,75,79,96,94,86,95,83,100,97,99,112,75,98,94", "endOffsets": "163,274,377,486,561,651,759,899,1017,1163,1243,1339,1424,1514,1617,1731,1832,1955,2073,2202,2365,2488,2601,2720,2838,2924,3018,3133,3266,3363,3469,3568,3697,3834,3935,4028,4104,4177,4257,4337,4444,4520,4600,4697,4792,4879,4975,5059,5160,5258,5358,5471,5547,5646,5741"}, "to": {"startLines": "51,52,53,54,68,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,104,159,266,269,271,276,277,278,279,280,281,282,283,284,285,286,287,288,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3588,3701,3812,3915,5311,5456,5546,5654,5794,5912,6058,6138,6234,6319,6409,6512,6626,6727,6850,6968,7097,7260,7383,7496,7615,7733,7819,7913,8028,8161,8258,8364,8463,8592,8729,8830,9193,13313,23096,23326,23507,24047,24123,24203,24300,24395,24482,24578,24662,24763,24861,24961,25074,25150,25249", "endColumns": "112,110,102,108,74,89,107,139,117,145,79,95,84,89,102,113,100,122,117,128,162,122,112,118,117,85,93,114,132,96,105,98,128,136,100,92,75,72,79,79,106,75,79,96,94,86,95,83,100,97,99,112,75,98,94", "endOffsets": "3696,3807,3910,4019,5381,5541,5649,5789,5907,6053,6133,6229,6314,6404,6507,6621,6722,6845,6963,7092,7255,7378,7491,7610,7728,7814,7908,8023,8156,8253,8359,8458,8587,8724,8825,8918,9264,13381,23171,23401,23609,24118,24198,24295,24390,24477,24573,24657,24758,24856,24956,25069,25145,25244,25339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,279,376,476,564,640,728,817,899,963,1027,1107,1189,1259,1336,1403", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "193,274,371,471,559,635,723,812,894,958,1022,1102,1184,1254,1331,1398,1518"}, "to": {"startLines": "66,67,101,103,105,162,163,256,257,259,260,264,265,268,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5137,5230,8923,9093,9269,13546,13622,22248,22337,22497,22561,22934,23014,23256,23783,23860,23927", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "5225,5306,9015,9188,9352,13617,13705,22332,22414,22556,22620,23009,23091,23321,23855,23922,24042"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "56,57,58,59,60,61,62,270", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4111,4205,4307,4404,4503,4611,4717,23406", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "4200,4302,4399,4498,4606,4712,4832,23502"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,113", "endOffsets": "156,270"}, "to": {"startLines": "254,255", "startColumns": "4,4", "startOffsets": "22028,22134", "endColumns": "105,113", "endOffsets": "22129,22243"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,482,672,758,843,921,1007,1095,1170,1234,1327,1418,1491,1558,1624,1694,1803,1913,2020,2093,2176,2252,2325,2428,2530,2594,2659,2712,2770,2818,2879,2949,3017,3083,3153,3217,3276,3340,3405,3471,3523,3583,3657,3731", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,84,77,85,87,74,63,92,90,72,66,65,69,108,109,106,72,82,75,72,102,101,63,64,52,57,47,60,69,67,65,69,63,58,63,64,65,51,59,73,73,52", "endOffsets": "280,477,667,753,838,916,1002,1090,1165,1229,1322,1413,1486,1553,1619,1689,1798,1908,2015,2088,2171,2247,2320,2423,2525,2589,2654,2707,2765,2813,2874,2944,3012,3078,3148,3212,3271,3335,3400,3466,3518,3578,3652,3726,3779"}, "to": {"startLines": "2,11,15,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,532,9421,9507,9592,9670,9756,9844,9919,9983,10076,10167,10240,10307,10373,10443,10552,10662,10769,10842,10925,11001,11074,11177,11279,11343,12101,12154,12212,12260,12321,12391,12459,12525,12595,12659,12718,12782,12847,12913,12965,13025,13099,13173", "endLines": "10,14,18,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "endColumns": "17,12,12,85,84,77,85,87,74,63,92,90,72,66,65,69,108,109,106,72,82,75,72,102,101,63,64,52,57,47,60,69,67,65,69,63,58,63,64,65,51,59,73,73,52", "endOffsets": "330,527,717,9502,9587,9665,9751,9839,9914,9978,10071,10162,10235,10302,10368,10438,10547,10657,10764,10837,10920,10996,11069,11172,11274,11338,11403,12149,12207,12255,12316,12386,12454,12520,12590,12654,12713,12777,12842,12908,12960,13020,13094,13168,13221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,193,257,328,408,486,580,677", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "126,188,252,323,403,481,575,672,743"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11408,11484,11546,11610,11681,11761,11839,11933,12030", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "11479,11541,11605,11676,11756,11834,11928,12025,12096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "883,986,1081,1195,1281,1381,1494,1571,1646,1737,1830,1924,2018,2118,2211,2306,2404,2495,2586,2664,2767,2865,2961,3065,3164,3265,3418,23176", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "981,1076,1190,1276,1376,1489,1566,1641,1732,1825,1919,2013,2113,2206,2301,2399,2490,2581,2659,2762,2860,2956,3060,3159,3260,3413,3510,23251"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,340,482,651,730", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "170,257,335,477,646,725,801"}, "to": {"startLines": "69,158,258,263,272,290,291", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5386,13226,22419,22792,23614,25344,25423", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "5451,13308,22492,22929,23778,25418,25494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,216,303,402,521,603,667,759,827,887,974,1036,1100,1168,1233,1287,1396,1454,1516,1570,1645,1765,1847,1927,2031,2109,2189,2277,2344,2410,2478,2552,2642,2713,2791,2861,2931,3020,3098,3186,3276,3348,3420,3504,3555,3621,3702,3785,3847,3911,3974,4074,4172,4265,4363", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,86,98,118,81,63,91,67,59,86,61,63,67,64,53,108,57,61,53,74,119,81,79,103,77,79,87,66,65,67,73,89,70,77,69,69,88,77,87,89,71,71,83,50,65,80,82,61,63,62,99,97,92,97,77", "endOffsets": "211,298,397,516,598,662,754,822,882,969,1031,1095,1163,1228,1282,1391,1449,1511,1565,1640,1760,1842,1922,2026,2104,2184,2272,2339,2405,2473,2547,2637,2708,2786,2856,2926,3015,3093,3181,3271,3343,3415,3499,3550,3616,3697,3780,3842,3906,3969,4069,4167,4260,4358,4436"}, "to": {"startLines": "19,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "722,4024,4837,4936,5055,9357,13386,13478,13710,18042,18129,18191,18255,18323,18388,18442,18551,18609,18671,18725,18800,19430,19512,19592,19696,19774,19854,19942,20009,20075,20143,20217,20307,20378,20456,20526,20596,20685,20763,20851,20941,21013,21085,21169,21220,21286,21367,21450,21512,21576,21639,21739,21837,21930,22714", "endLines": "22,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "endColumns": "12,86,98,118,81,63,91,67,59,86,61,63,67,64,53,108,57,61,53,74,119,81,79,103,77,79,87,66,65,67,73,89,70,77,69,69,88,77,87,89,71,71,83,50,65,80,82,61,63,62,99,97,92,97,77", "endOffsets": "878,4106,4931,5050,5132,9416,13473,13541,13765,18124,18186,18250,18318,18383,18437,18546,18604,18666,18720,18795,18915,19507,19587,19691,19769,19849,19937,20004,20070,20138,20212,20302,20373,20451,20521,20591,20680,20758,20846,20936,21008,21080,21164,21215,21281,21362,21445,21507,21571,21634,21734,21832,21925,22023,22787"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,506,624,736,833,928,1068,1207,1313,1435,1585,1705,1824,1933,2032,2157,2250,2352,2460,2560,2659,2775,2883,3019,3154,3258,3368,3485,3601,3711,3820,3907,3988,4089,4223,4377", "endColumns": "106,100,97,94,117,111,96,94,139,138,105,121,149,119,118,108,98,124,92,101,107,99,98,115,107,135,134,103,109,116,115,109,108,86,80,100,133,153,88", "endOffsets": "207,308,406,501,619,731,828,923,1063,1202,1308,1430,1580,1700,1819,1928,2027,2152,2245,2347,2455,2555,2654,2770,2878,3014,3149,3253,3363,3480,3596,3706,3815,3902,3983,4084,4218,4372,4461"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13770,13877,13978,14076,14171,14289,14401,14498,14593,14733,14872,14978,15100,15250,15370,15489,15598,15697,15822,15915,16017,16125,16225,16324,16440,16548,16684,16819,16923,17033,17150,17266,17376,17485,17572,17653,17754,17888,22625", "endColumns": "106,100,97,94,117,111,96,94,139,138,105,121,149,119,118,108,98,124,92,101,107,99,98,115,107,135,134,103,109,116,115,109,108,86,80,100,133,153,88", "endOffsets": "13872,13973,14071,14166,14284,14396,14493,14588,14728,14867,14973,15095,15245,15365,15484,15593,15692,15817,15910,16012,16120,16220,16319,16435,16543,16679,16814,16918,17028,17145,17261,17371,17480,17567,17648,17749,17883,18037,22709"}}]}]}