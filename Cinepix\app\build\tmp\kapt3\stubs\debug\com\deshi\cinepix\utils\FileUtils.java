package com.deshi.cinepix.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\b\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\bJ\u000e\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fJ\u000e\u0010\r\u001a\u00020\f2\u0006\u0010\u000e\u001a\u00020\bJ\u000e\u0010\u000f\u001a\u00020\b2\u0006\u0010\u0010\u001a\u00020\fJ\u000e\u0010\u0011\u001a\u00020\f2\u0006\u0010\u0010\u001a\u00020\fJ\u000e\u0010\u0012\u001a\u00020\u00062\u0006\u0010\u0013\u001a\u00020\u0014J\u000e\u0010\u0015\u001a\u00020\u00062\u0006\u0010\u0013\u001a\u00020\u0014J\u000e\u0010\u0016\u001a\u00020\f2\u0006\u0010\u0017\u001a\u00020\fJ\u000e\u0010\u0018\u001a\u00020\b2\u0006\u0010\u000b\u001a\u00020\fJ\u0006\u0010\u0019\u001a\u00020\nJ\u0006\u0010\u001a\u001a\u00020\nJ\u000e\u0010\u001b\u001a\u00020\n2\u0006\u0010\u0017\u001a\u00020\f\u00a8\u0006\u001c"}, d2 = {"Lcom/deshi/cinepix/utils/FileUtils;", "", "()V", "cleanupOldFiles", "", "directory", "Ljava/io/File;", "maxAgeMillis", "", "deleteFile", "", "filePath", "", "formatFileSize", "bytes", "getAvailableSpace", "path", "getAvailableSpaceFormatted", "getCacheDirectory", "context", "Landroid/content/Context;", "getDownloadsDirectory", "getFileExtension", "fileName", "getFileSize", "isExternalStorageReadable", "isExternalStorageWritable", "isVideoFile", "app_debug"})
public final class FileUtils {
    @org.jetbrains.annotations.NotNull
    public static final com.deshi.cinepix.utils.FileUtils INSTANCE = null;
    
    private FileUtils() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.io.File getDownloadsDirectory(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.io.File getCacheDirectory(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
        return null;
    }
    
    public final long getAvailableSpace(@org.jetbrains.annotations.NotNull
    java.lang.String path) {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getAvailableSpaceFormatted(@org.jetbrains.annotations.NotNull
    java.lang.String path) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String formatFileSize(long bytes) {
        return null;
    }
    
    public final boolean deleteFile(@org.jetbrains.annotations.NotNull
    java.lang.String filePath) {
        return false;
    }
    
    public final long getFileSize(@org.jetbrains.annotations.NotNull
    java.lang.String filePath) {
        return 0L;
    }
    
    public final boolean isExternalStorageWritable() {
        return false;
    }
    
    public final boolean isExternalStorageReadable() {
        return false;
    }
    
    public final void cleanupOldFiles(@org.jetbrains.annotations.NotNull
    java.io.File directory, long maxAgeMillis) {
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getFileExtension(@org.jetbrains.annotations.NotNull
    java.lang.String fileName) {
        return null;
    }
    
    public final boolean isVideoFile(@org.jetbrains.annotations.NotNull
    java.lang.String fileName) {
        return false;
    }
}