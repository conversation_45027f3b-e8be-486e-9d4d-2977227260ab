{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeReleaseResources-87:/values-kn/values-kn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,295,413,532,614,708,820,973,1099,1247,1329,1431,1528,1634,1746,1875,1982,2117,2248,2377,2561,2681,2795,2913,3037,3135,3228,3346,3480,3582,3687,3789,3923,4064,4167,4271,4343,4425,4508,4593,4700,4776,4856,4952,5056,5152,5249,5332,5441,5539,5639,5756,5832,5938", "endColumns": "119,119,117,118,81,93,111,152,125,147,81,101,96,105,111,128,106,134,130,128,183,119,113,117,123,97,92,117,133,101,104,101,133,140,102,103,71,81,82,84,106,75,79,95,103,95,96,82,108,97,99,116,75,105,92", "endOffsets": "170,290,408,527,609,703,815,968,1094,1242,1324,1426,1523,1629,1741,1870,1977,2112,2243,2372,2556,2676,2790,2908,3032,3130,3223,3341,3475,3577,3682,3784,3918,4059,4162,4266,4338,4420,4503,4588,4695,4771,4851,4947,5051,5147,5244,5327,5436,5534,5634,5751,5827,5933,6026"}, "to": {"startLines": "51,52,53,54,68,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,104,159,266,269,271,276,277,278,279,280,281,282,283,284,285,286,287,288,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3791,3911,4031,4149,5581,5733,5827,5939,6092,6218,6366,6448,6550,6647,6753,6865,6994,7101,7236,7367,7496,7680,7800,7914,8032,8156,8254,8347,8465,8599,8701,8806,8908,9042,9183,9286,9672,13882,23985,24230,24416,24956,25032,25112,25208,25312,25408,25505,25588,25697,25795,25895,26012,26088,26194", "endColumns": "119,119,117,118,81,93,111,152,125,147,81,101,96,105,111,128,106,134,130,128,183,119,113,117,123,97,92,117,133,101,104,101,133,140,102,103,71,81,82,84,106,75,79,95,103,95,96,82,108,97,99,116,75,105,92", "endOffsets": "3906,4026,4144,4263,5658,5822,5934,6087,6213,6361,6443,6545,6642,6748,6860,6989,7096,7231,7362,7491,7675,7795,7909,8027,8151,8249,8342,8460,8594,8696,8801,8903,9037,9178,9281,9385,9739,13959,24063,24310,24518,25027,25107,25203,25307,25403,25500,25583,25692,25790,25890,26007,26083,26189,26282"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,382,482,571,655,748,839,924,995,1066,1148,1234,1313,1390,1459", "endColumns": "96,83,95,99,88,83,92,90,84,70,70,81,85,78,76,68,117", "endOffsets": "197,281,377,477,566,650,743,834,919,990,1061,1143,1229,1308,1385,1454,1572"}, "to": {"startLines": "66,67,101,103,105,162,163,256,257,259,260,264,265,268,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5400,5497,9390,9572,9744,14124,14208,23100,23191,23353,23424,23817,23899,24151,24692,24769,24838", "endColumns": "96,83,95,99,88,83,92,90,84,70,70,81,85,78,76,68,117", "endOffsets": "5492,5576,9481,9667,9828,14203,14296,23186,23271,23419,23490,23894,23980,24225,24764,24833,24951"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,261,338,487,656,736", "endColumns": "69,85,76,148,168,79,76", "endOffsets": "170,256,333,482,651,731,808"}, "to": {"startLines": "69,158,258,263,272,290,291", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5663,13796,23276,23668,24523,26287,26367", "endColumns": "69,85,76,148,168,79,76", "endOffsets": "5728,13877,23348,23812,24687,26362,26439"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "56,57,58,59,60,61,62,270", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4350,4448,4551,4652,4758,4859,4967,24315", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "4443,4546,4647,4753,4854,4962,5090,24411"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,126,212,286,357,442,530,622", "endColumns": "70,85,73,70,84,87,91,95", "endOffsets": "121,207,281,352,437,525,617,713"}, "to": {"startLines": "50,102,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3720,9486,19621,19695,19766,19851,19939,20031", "endColumns": "70,85,73,70,84,87,91,95", "endOffsets": "3786,9567,19690,19761,19846,19934,20026,20122"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,193,270,341,422,504,605,698", "endColumns": "73,63,76,70,80,81,100,92,85", "endOffsets": "124,188,265,336,417,499,600,693,779"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11963,12037,12101,12178,12249,12330,12412,12513,12606", "endColumns": "73,63,76,70,80,81,100,92,85", "endOffsets": "12032,12096,12173,12244,12325,12407,12508,12601,12687"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,173", "endColumns": "117,128", "endOffsets": "168,297"}, "to": {"startLines": "254,255", "startColumns": "4,4", "startOffsets": "22853,22971", "endColumns": "117,128", "endOffsets": "22966,23095"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,316,543,747,842,939,1021,1118,1216,1293,1357,1460,1561,1626,1689,1749,1820,1942,2067,2187,2255,2342,2418,2494,2587,2683,2750,2814,2867,2925,2975,3036,3096,3158,3222,3284,3343,3408,3474,3538,3605,3659,3718,3791,3864", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,94,96,81,96,97,76,63,102,100,64,62,59,70,121,124,119,67,86,75,75,92,95,66,63,52,57,49,60,59,61,63,61,58,64,65,63,66,53,58,72,72,53", "endOffsets": "311,538,742,837,934,1016,1113,1211,1288,1352,1455,1556,1621,1684,1744,1815,1937,2062,2182,2250,2337,2413,2489,2582,2678,2745,2809,2862,2920,2970,3031,3091,3153,3217,3279,3338,3403,3469,3533,3600,3654,3713,3786,3859,3913"}, "to": {"startLines": "2,11,15,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,366,593,9896,9991,10088,10170,10267,10365,10442,10506,10609,10710,10775,10838,10898,10969,11091,11216,11336,11404,11491,11567,11643,11736,11832,11899,12692,12745,12803,12853,12914,12974,13036,13100,13162,13221,13286,13352,13416,13483,13537,13596,13669,13742", "endLines": "10,14,18,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "endColumns": "17,12,12,94,96,81,96,97,76,63,102,100,64,62,59,70,121,124,119,67,86,75,75,92,95,66,63,52,57,49,60,59,61,63,61,58,64,65,63,66,53,58,72,72,53", "endOffsets": "361,588,792,9986,10083,10165,10262,10360,10437,10501,10604,10705,10770,10833,10893,10964,11086,11211,11331,11399,11486,11562,11638,11731,11827,11894,11958,12740,12798,12848,12909,12969,13031,13095,13157,13216,13281,13347,13411,13478,13532,13591,13664,13737,13791"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,532,639,765,843,919,1010,1103,1198,1292,1392,1485,1580,1674,1765,1856,1938,2054,2164,2263,2376,2481,2595,2759,2859", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,527,634,760,838,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1933,2049,2159,2258,2371,2476,2590,2754,2854,2937"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "966,1080,1192,1305,1393,1500,1626,1704,1780,1871,1964,2059,2153,2253,2346,2441,2535,2626,2717,2799,2915,3025,3124,3237,3342,3456,3620,24068", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "1075,1187,1300,1388,1495,1621,1699,1775,1866,1959,2054,2148,2248,2341,2436,2530,2621,2712,2794,2910,3020,3119,3232,3337,3451,3615,3715,24146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,413,512,636,750,847,942,1085,1224,1333,1458,1601,1737,1869,1980,2071,2207,2296,2409,2526,2637,2731,2842,2961,3089,3213,3320,3431,3546,3664,3776,3891,3978,4062,4162,4297,4454", "endColumns": "106,100,99,98,123,113,96,94,142,138,108,124,142,135,131,110,90,135,88,112,116,110,93,110,118,127,123,106,110,114,117,111,114,86,83,99,134,156,90", "endOffsets": "207,308,408,507,631,745,842,937,1080,1219,1328,1453,1596,1732,1864,1975,2066,2202,2291,2404,2521,2632,2726,2837,2956,3084,3208,3315,3426,3541,3659,3771,3886,3973,4057,4157,4292,4449,4540"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14363,14470,14571,14671,14770,14894,15008,15105,15200,15343,15482,15591,15716,15859,15995,16127,16238,16329,16465,16554,16667,16784,16895,16989,17100,17219,17347,17471,17578,17689,17804,17922,18034,18149,18236,18320,18420,18555,23495", "endColumns": "106,100,99,98,123,113,96,94,142,138,108,124,142,135,131,110,90,135,88,112,116,110,93,110,118,127,123,106,110,114,117,111,114,86,83,99,134,156,90", "endOffsets": "14465,14566,14666,14765,14889,15003,15100,15195,15338,15477,15586,15711,15854,15990,16122,16233,16324,16460,16549,16662,16779,16890,16984,17095,17214,17342,17466,17573,17684,17799,17917,18029,18144,18231,18315,18415,18550,18707,23581"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,306,413,526,611,674,768,834,896,999,1070,1129,1205,1270,1324,1437,1495,1556,1610,1689,1805,1888,1979,2091,2170,2249,2337,2404,2470,2550,2640,2724,2801,2878,2955,3024,3123,3200,3293,3388,3462,3543,3639,3690,3758,3844,3932,3995,4060,4123,4228,4331,4426,4531", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,106,112,84,62,93,65,61,102,70,58,75,64,53,112,57,60,53,78,115,82,90,111,78,78,87,66,65,79,89,83,76,76,76,68,98,76,92,94,73,80,95,50,67,85,87,62,64,62,104,102,94,104,81", "endOffsets": "219,301,408,521,606,669,763,829,891,994,1065,1124,1200,1265,1319,1432,1490,1551,1605,1684,1800,1883,1974,2086,2165,2244,2332,2399,2465,2545,2635,2719,2796,2873,2950,3019,3118,3195,3288,3383,3457,3538,3634,3685,3753,3839,3927,3990,4055,4118,4223,4326,4421,4526,4608"}, "to": {"startLines": "19,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "797,4268,5095,5202,5315,9833,13964,14058,14301,18712,18815,18886,18945,19021,19086,19140,19253,19311,19372,19426,19505,20127,20210,20301,20413,20492,20571,20659,20726,20792,20872,20962,21046,21123,21200,21277,21346,21445,21522,21615,21710,21784,21865,21961,22012,22080,22166,22254,22317,22382,22445,22550,22653,22748,23586", "endLines": "22,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "endColumns": "12,81,106,112,84,62,93,65,61,102,70,58,75,64,53,112,57,60,53,78,115,82,90,111,78,78,87,66,65,79,89,83,76,76,76,68,98,76,92,94,73,80,95,50,67,85,87,62,64,62,104,102,94,104,81", "endOffsets": "961,4345,5197,5310,5395,9891,14053,14119,14358,18810,18881,18940,19016,19081,19135,19248,19306,19367,19421,19500,19616,20205,20296,20408,20487,20566,20654,20721,20787,20867,20957,21041,21118,21195,21272,21341,21440,21517,21610,21705,21779,21860,21956,22007,22075,22161,22249,22312,22377,22440,22545,22648,22743,22848,23663"}}]}]}