{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeReleaseResources-87:/values-zh-rHK/values-zh-rHK.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,112,179,244,308,377,449,524", "endColumns": "56,66,64,63,68,71,74,78", "endOffsets": "107,174,239,303,372,444,519,598"}, "to": {"startLines": "50,102,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3360,8208,16928,16993,17057,17126,17198,17273", "endColumns": "56,66,64,63,68,71,74,78", "endOffsets": "3412,8270,16988,17052,17121,17193,17268,17347"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,405,495,608,709,803,892,1005,1117,1214,1325,1428,1536,1643,1740,1828,1936,2023,2122,2219,2318,2407,2513,2607,2709,2810,2907,3008,3105,3208,3302,3402,3489,3569,3660,3792,3935", "endColumns": "106,100,91,89,112,100,93,88,112,111,96,110,102,107,106,96,87,107,86,98,96,98,88,105,93,101,100,96,100,96,102,93,99,86,79,90,131,142,80", "endOffsets": "207,308,400,490,603,704,798,887,1000,1112,1209,1320,1423,1531,1638,1735,1823,1931,2018,2117,2214,2313,2402,2508,2602,2704,2805,2902,3003,3100,3203,3297,3397,3484,3564,3655,3787,3930,4011"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12324,12431,12532,12624,12714,12827,12928,13022,13111,13224,13336,13433,13544,13647,13755,13862,13959,14047,14155,14242,14341,14438,14537,14626,14732,14826,14928,15029,15126,15227,15324,15427,15521,15621,15708,15788,15879,16011,20232", "endColumns": "106,100,91,89,112,100,93,88,112,111,96,110,102,107,106,96,87,107,86,98,96,98,88,105,93,101,100,96,100,96,102,93,99,86,79,90,131,142,80", "endOffsets": "12426,12527,12619,12709,12822,12923,13017,13106,13219,13331,13428,13539,13642,13750,13857,13954,14042,14150,14237,14336,14433,14532,14621,14727,14821,14923,15024,15121,15222,15319,15422,15516,15616,15703,15783,15874,16006,16149,20308"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "56,57,58,59,60,61,62,270", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3881,3973,4072,4166,4260,4353,4446,20950", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3968,4067,4161,4255,4348,4441,4537,21046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,269,359,466,539,601,679,738,796,874,931,987,1046,1104,1158,1244,1300,1358,1412,1477,1570,1644,1722,1812,1875,1938,2015,2082,2148,2212,2281,2356,2417,2488,2555,2615,2695,2758,2841,2926,3000,3065,3141,3189,3253,3329,3407,3469,3533,3596,3676,3751,3827,3903", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,66,89,106,72,61,77,58,57,77,56,55,58,57,53,85,55,57,53,64,92,73,77,89,62,62,76,66,65,63,68,74,60,70,66,59,79,62,82,84,73,64,75,47,63,75,77,61,63,62,79,74,75,75,68", "endOffsets": "197,264,354,461,534,596,674,733,791,869,926,982,1041,1099,1153,1239,1295,1353,1407,1472,1565,1639,1717,1807,1870,1933,2010,2077,2143,2207,2276,2351,2412,2483,2550,2610,2690,2753,2836,2921,2995,3060,3136,3184,3248,3324,3402,3464,3528,3591,3671,3746,3822,3898,3967"}, "to": {"startLines": "19,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "652,3814,4542,4632,4739,8513,11978,12056,12266,16154,16232,16289,16345,16404,16462,16516,16602,16658,16716,16770,16835,17352,17426,17504,17594,17657,17720,17797,17864,17930,17994,18063,18138,18199,18270,18337,18397,18477,18540,18623,18708,18782,18847,18923,18971,19035,19111,19189,19251,19315,19378,19458,19533,19609,20313", "endLines": "22,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "endColumns": "12,66,89,106,72,61,77,58,57,77,56,55,58,57,53,85,55,57,53,64,92,73,77,89,62,62,76,66,65,63,68,74,60,70,66,59,79,62,82,84,73,64,75,47,63,75,77,61,63,62,79,74,75,75,68", "endOffsets": "794,3876,4627,4734,4807,8570,12051,12110,12319,16227,16284,16340,16399,16457,16511,16597,16653,16711,16765,16830,16923,17421,17499,17589,17652,17715,17792,17859,17925,17989,18058,18133,18194,18265,18332,18392,18472,18535,18618,18703,18777,18842,18918,18966,19030,19106,19184,19246,19310,19373,19453,19528,19604,19680,20377"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,181,255,343,434,512,586,663,741,815,878,941,1014,1089,1156,1231,1296", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "176,250,338,429,507,581,658,736,810,873,936,1009,1084,1151,1226,1291,1407"}, "to": {"startLines": "66,67,101,103,105,162,163,256,257,259,260,264,265,268,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4812,4888,8120,8275,8435,12115,12189,19884,19962,20106,20169,20501,20574,20806,21304,21379,21444", "endColumns": "75,73,87,90,77,73,76,77,73,62,62,72,74,66,74,64,115", "endOffsets": "4883,4957,8203,8361,8508,12184,12261,19957,20031,20164,20227,20569,20644,20868,21374,21439,21555"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,225,278,350,404,478,554", "endColumns": "55,55,57,52,71,53,73,75,58", "endOffsets": "106,162,220,273,345,399,473,549,608"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10231,10287,10343,10401,10454,10526,10580,10654,10730", "endColumns": "55,55,57,52,71,53,73,75,58", "endOffsets": "10282,10338,10396,10449,10521,10575,10649,10725,10784"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,154", "endColumns": "98,99", "endOffsets": "149,249"}, "to": {"startLines": "254,255", "startColumns": "4,4", "startOffsets": "19685,19784", "endColumns": "98,99", "endOffsets": "19779,19879"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,281,444,602,672,741,811,887,962,1017,1078,1152,1226,1288,1349,1408,1473,1562,1648,1737,1800,1867,1932,1987,2061,2134,2195,2258,2310,2368,2415,2476,2532,2594,2651,2711,2767,2822,2885,2947,3010,3059,3112,3179,3246", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,69,68,69,75,74,54,60,73,73,61,60,58,64,88,85,88,62,66,64,54,73,72,60,62,51,57,46,60,55,61,56,59,55,54,62,61,62,48,52,66,66,48", "endOffsets": "276,439,597,667,736,806,882,957,1012,1073,1147,1221,1283,1344,1403,1468,1557,1643,1732,1795,1862,1927,1982,2056,2129,2190,2253,2305,2363,2410,2471,2527,2589,2646,2706,2762,2817,2880,2942,3005,3054,3107,3174,3241,3290"}, "to": {"startLines": "2,11,15,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,331,494,8575,8645,8714,8784,8860,8935,8990,9051,9125,9199,9261,9322,9381,9446,9535,9621,9710,9773,9840,9905,9960,10034,10107,10168,10789,10841,10899,10946,11007,11063,11125,11182,11242,11298,11353,11416,11478,11541,11590,11643,11710,11777", "endLines": "10,14,18,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "endColumns": "17,12,12,69,68,69,75,74,54,60,73,73,61,60,58,64,88,85,88,62,66,64,54,73,72,60,62,51,57,46,60,55,61,56,59,55,54,62,61,62,48,52,66,66,48", "endOffsets": "326,489,647,8640,8709,8779,8855,8930,8985,9046,9120,9194,9256,9317,9376,9441,9530,9616,9705,9768,9835,9900,9955,10029,10102,10163,10226,10836,10894,10941,11002,11058,11120,11177,11237,11293,11348,11411,11473,11536,11585,11638,11705,11772,11821"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,155,254,354,452,524,609,711,812,913,1025,1103,1195,1275,1359,1460,1569,1666,1770,1872,1976,2088,2189,2287,2389,2492,2573,2664,2765,2870,2956,3054,3148,3252,3362,3458,3544,3613,3684,3762,3839,3924,4000,4078,4171,4261,4350,4439,4519,4611,4703,4793,4897,4973,5061", "endColumns": "99,98,99,97,71,84,101,100,100,111,77,91,79,83,100,108,96,103,101,103,111,100,97,101,102,80,90,100,104,85,97,93,103,109,95,85,68,70,77,76,84,75,77,92,89,88,88,79,91,91,89,103,75,87,85", "endOffsets": "150,249,349,447,519,604,706,807,908,1020,1098,1190,1270,1354,1455,1564,1661,1765,1867,1971,2083,2184,2282,2384,2487,2568,2659,2760,2865,2951,3049,3143,3247,3357,3453,3539,3608,3679,3757,3834,3919,3995,4073,4166,4256,4345,4434,4514,4606,4698,4788,4892,4968,5056,5142"}, "to": {"startLines": "51,52,53,54,68,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,104,159,266,269,271,276,277,278,279,280,281,282,283,284,285,286,287,288,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3417,3517,3616,3716,4962,5100,5185,5287,5388,5489,5601,5679,5771,5851,5935,6036,6145,6242,6346,6448,6552,6664,6765,6863,6965,7068,7149,7240,7341,7446,7532,7630,7724,7828,7938,8034,8366,11907,20649,20873,21051,21560,21636,21714,21807,21897,21986,22075,22155,22247,22339,22429,22533,22609,22697", "endColumns": "99,98,99,97,71,84,101,100,100,111,77,91,79,83,100,108,96,103,101,103,111,100,97,101,102,80,90,100,104,85,97,93,103,109,95,85,68,70,77,76,84,75,77,92,89,88,88,79,91,91,89,103,75,87,85", "endOffsets": "3512,3611,3711,3809,5029,5180,5282,5383,5484,5596,5674,5766,5846,5930,6031,6140,6237,6341,6443,6547,6659,6760,6858,6960,7063,7144,7235,7336,7441,7527,7625,7719,7823,7933,8029,8115,8430,11973,20722,20945,21131,21631,21709,21802,21892,21981,22070,22150,22242,22334,22424,22528,22604,22692,22778"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,322,441,609,688", "endColumns": "65,80,69,118,167,78,75", "endOffsets": "166,247,317,436,604,683,759"}, "to": {"startLines": "69,158,258,263,272,290,291", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5034,11826,20036,20382,21136,22783,22862", "endColumns": "65,80,69,118,167,78,75", "endOffsets": "5095,11902,20101,20496,21299,22857,22933"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1109,1205,1300,1394,1490,1582,1674,1766,1844,1940,2035,2130,2227,2323,2421,2572,2666", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1104,1200,1295,1389,1485,1577,1669,1761,1839,1935,2030,2125,2222,2318,2416,2567,2661,2740"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "799,894,987,1087,1169,1266,1374,1451,1526,1618,1712,1803,1899,1994,2088,2184,2276,2368,2460,2538,2634,2729,2824,2921,3017,3115,3266,20727", "endColumns": "94,92,99,81,96,107,76,74,91,93,90,95,94,93,95,91,91,91,77,95,94,94,96,95,97,150,93,78", "endOffsets": "889,982,1082,1164,1261,1369,1446,1521,1613,1707,1798,1894,1989,2083,2179,2271,2363,2455,2533,2629,2724,2819,2916,3012,3110,3261,3355,20801"}}]}]}