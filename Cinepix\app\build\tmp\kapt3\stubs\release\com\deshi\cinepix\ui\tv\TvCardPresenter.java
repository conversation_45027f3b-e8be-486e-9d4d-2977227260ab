package com.deshi.cinepix.ui.tv;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\u0018\u0000 \u00152\u00020\u0001:\u0001\u0015B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bH\u0016J\u0010\u0010\f\u001a\u00020\t2\u0006\u0010\r\u001a\u00020\u000eH\u0016J\u0010\u0010\u000f\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH\u0016J\u0018\u0010\u0010\u001a\u00020\u00072\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0014H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0016"}, d2 = {"Lcom/deshi/cinepix/ui/tv/TvCardPresenter;", "Landroidx/leanback/widget/Presenter;", "()V", "defaultBackgroundColor", "", "selectedBackgroundColor", "onBindViewHolder", "", "viewHolder", "Landroidx/leanback/widget/Presenter$ViewHolder;", "item", "", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "onUnbindViewHolder", "updateCardBackgroundColor", "view", "Landroidx/leanback/widget/ImageCardView;", "selected", "", "Companion", "app_release"})
public final class TvCardPresenter extends androidx.leanback.widget.Presenter {
    private int selectedBackgroundColor = -1;
    private int defaultBackgroundColor = -1;
    private static final int CARD_WIDTH = 313;
    private static final int CARD_HEIGHT = 176;
    @org.jetbrains.annotations.NotNull
    public static final com.deshi.cinepix.ui.tv.TvCardPresenter.Companion Companion = null;
    
    public TvCardPresenter() {
        super();
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public androidx.leanback.widget.Presenter.ViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull
    android.view.ViewGroup parent) {
        return null;
    }
    
    @java.lang.Override
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull
    androidx.leanback.widget.Presenter.ViewHolder viewHolder, @org.jetbrains.annotations.NotNull
    java.lang.Object item) {
    }
    
    @java.lang.Override
    public void onUnbindViewHolder(@org.jetbrains.annotations.NotNull
    androidx.leanback.widget.Presenter.ViewHolder viewHolder) {
    }
    
    private final void updateCardBackgroundColor(androidx.leanback.widget.ImageCardView view, boolean selected) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/deshi/cinepix/ui/tv/TvCardPresenter$Companion;", "", "()V", "CARD_HEIGHT", "", "CARD_WIDTH", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}