{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-91:/values-v18/values-v18.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-v18\\values-v18.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "48", "endOffsets": "99"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-v18\\values-v18.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "10", "endColumns": "12", "endOffsets": "642"}, "to": {"startLines": "3", "startColumns": "4", "startOffsets": "104", "endLines": "11", "endColumns": "12", "endOffsets": "691"}}]}]}