{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-99:/values-v18/values-v18.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\35536fe73c76a56f94e622d08fe45089\\transformed\\leanback-1.0.0\\res\\values-v18\\values-v18.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "10", "endColumns": "12", "endOffsets": "642"}, "to": {"startLines": "3", "startColumns": "4", "startOffsets": "104", "endLines": "11", "endColumns": "12", "endOffsets": "691"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\de0470acc971f743e832aff97e9527de\\transformed\\appcompat-1.6.1\\res\\values-v18\\values-v18.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "48", "endOffsets": "99"}}]}]}