package com.deshi.cinepix.ui.player

import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.media3.common.*
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.datasource.cache.LeastRecentlyUsedCacheEvictor
import androidx.media3.datasource.cache.SimpleCache
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.exoplayer.trackselection.DefaultTrackSelector
import androidx.media3.ui.PlayerView
import java.io.File
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.draw.clip
import androidx.compose.ui.unit.dp
import com.deshi.cinepix.ui.theme.CinepixTheme

class SimpleVideoPlayerActivity : ComponentActivity() {

    private var exoPlayer: ExoPlayer? = null
    private var trackSelector: DefaultTrackSelector? = null
    private var currentVideoUrl: String = ""
    private var currentPosition: Long = 0

    companion object {
        private const val EXTRA_VIDEO_URL = "extra_video_url"
        private const val EXTRA_VIDEO_TITLE = "extra_video_title"
        private var videoCache: SimpleCache? = null

        fun createIntent(context: Context, videoUrl: String, title: String): Intent {
            return Intent(context, SimpleVideoPlayerActivity::class.java).apply {
                putExtra(EXTRA_VIDEO_URL, videoUrl)
                putExtra(EXTRA_VIDEO_TITLE, title)
            }
        }

        fun getVideoCache(context: Context): SimpleCache {
            if (videoCache == null) {
                val cacheDir = File(context.cacheDir, "video_cache")
                val cacheEvictor = LeastRecentlyUsedCacheEvictor(200 * 1024 * 1024) // 200MB cache
                videoCache = SimpleCache(cacheDir, cacheEvictor)
            }
            return videoCache!!
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Set up fullscreen
        setupFullscreen()
        
        val videoUrl = intent.getStringExtra(EXTRA_VIDEO_URL) ?: return
        val title = intent.getStringExtra(EXTRA_VIDEO_TITLE) ?: "Video"

        currentVideoUrl = videoUrl

        // Restore playback position
        val prefs = getSharedPreferences("video_playback", Context.MODE_PRIVATE)
        currentPosition = prefs.getLong(getVideoKey(videoUrl), 0)
        
        setContent {
            CinepixTheme {
                SimpleVideoPlayerScreen(
                    videoUrl = videoUrl,
                    title = title,
                    onPlayerCreated = { player, selector ->
                        exoPlayer = player
                        trackSelector = selector
                        setupPlayer(player, videoUrl)
                    },
                    onBackPressed = { finish() },
                    onShowAudioSelector = { showAudioSelectionDialog() },
                    onShowSubtitleSelector = { showSubtitleSelectionDialog() }
                )
            }
        }
    }
    
    private fun setupFullscreen() {
        WindowCompat.setDecorFitsSystemWindows(window, false)
        val controller = WindowInsetsControllerCompat(window, window.decorView)
        controller.hide(WindowInsetsCompat.Type.systemBars())
        controller.systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
    }
    
    private fun setupPlayer(player: ExoPlayer, videoUrl: String) {
        val mediaItem = MediaItem.fromUri(videoUrl)
        player.setMediaItem(mediaItem)
        player.prepare()

        // Restore playback position
        if (currentPosition > 0) {
            player.seekTo(currentPosition)
        }

        player.playWhenReady = true

        player.addListener(object : Player.Listener {
            override fun onPlaybackStateChanged(playbackState: Int) {
                when (playbackState) {
                    Player.STATE_ENDED -> {
                        finish()
                    }
                }
            }

            override fun onTracksChanged(tracks: Tracks) {
                super.onTracksChanged(tracks)
                logAvailableTracks(tracks)
            }
        })
    }

    private fun logAvailableTracks(tracks: Tracks) {
        for (trackGroup in tracks.groups) {
            for (i in 0 until trackGroup.length) {
                val format = trackGroup.getTrackFormat(i)
                when {
                    MimeTypes.isAudio(format.sampleMimeType) -> {
                        android.util.Log.d("Player", "Audio track: ${format.language} - ${format.label}")
                    }
                    MimeTypes.isVideo(format.sampleMimeType) -> {
                        android.util.Log.d("Player", "Video track: ${format.width}x${format.height}")
                    }
                    MimeTypes.isText(format.sampleMimeType) -> {
                        android.util.Log.d("Player", "Subtitle track: ${format.language} - ${format.label}")
                    }
                }
            }
        }
    }
    
    override fun onPause() {
        super.onPause()
        savePlaybackPosition()
    }

    override fun onDestroy() {
        super.onDestroy()
        savePlaybackPosition()
        exoPlayer?.release()
    }

    private fun savePlaybackPosition() {
        exoPlayer?.let { player ->
            val position = player.currentPosition
            val currentTime = System.currentTimeMillis()
            val prefs = getSharedPreferences("video_playback", Context.MODE_PRIVATE)
            prefs.edit()
                .putLong(getVideoKey(currentVideoUrl), position)
                .putLong("${getVideoKey(currentVideoUrl)}_timestamp", currentTime)
                .apply()
        }
    }

    private fun getVideoKey(url: String): String {
        return "video_${url.hashCode()}"
    }

    private fun showAudioSelectionDialog() {
        val tracks = exoPlayer?.currentTracks ?: return

        val audioTracks = mutableListOf<Triple<String, Int, Int>>() // label, groupIndex, trackIndex
        var audioTrackCount = 1

        // Collect audio tracks
        for (i in 0 until tracks.groups.size) {
            val trackGroup = tracks.groups[i]
            if (trackGroup.type == C.TRACK_TYPE_AUDIO) {
                for (j in 0 until trackGroup.length) {
                    val format = trackGroup.getTrackFormat(j)
                    val label = getAudioTrackLabel(format, audioTrackCount)
                    audioTracks.add(Triple(label, i, j))
                    audioTrackCount++
                }
            }
        }

        if (audioTracks.isEmpty()) {
            return
        }

        val trackLabels = audioTracks.map { it.first }.toTypedArray()

        android.app.AlertDialog.Builder(this)
            .setTitle("Select Audio Track")
            .setItems(trackLabels) { _, which ->
                val (_, groupIndex, trackIndex) = audioTracks[which]
                selectAudioTrack(groupIndex, trackIndex)
            }
            .show()
    }

    private fun showSubtitleSelectionDialog() {
        val tracks = exoPlayer?.currentTracks ?: return

        val subtitleTracks = mutableListOf<Triple<String, Int, Int>>() // label, groupIndex, trackIndex
        var subtitleTrackCount = 1

        // Add "Disable Subtitles" option
        subtitleTracks.add(Triple("Disable Subtitles", -1, -1))

        // Collect subtitle tracks
        for (i in 0 until tracks.groups.size) {
            val trackGroup = tracks.groups[i]
            if (trackGroup.type == C.TRACK_TYPE_TEXT) {
                for (j in 0 until trackGroup.length) {
                    val format = trackGroup.getTrackFormat(j)
                    val label = getSubtitleTrackLabel(format, subtitleTrackCount)
                    subtitleTracks.add(Triple(label, i, j))
                    subtitleTrackCount++
                }
            }
        }

        if (subtitleTracks.isEmpty()) {
            return
        }

        val trackLabels = subtitleTracks.map { it.first }.toTypedArray()

        android.app.AlertDialog.Builder(this)
            .setTitle("Select Subtitle")
            .setItems(trackLabels) { _, which ->
                val (_, groupIndex, trackIndex) = subtitleTracks[which]
                if (groupIndex == -1) {
                    disableSubtitles()
                } else {
                    selectSubtitleTrack(groupIndex, trackIndex)
                }
            }
            .show()
    }



    private fun selectAudioTrack(groupIndex: Int, trackIndex: Int) {
        val player = exoPlayer ?: return
        val selector = trackSelector ?: return

        try {
            val tracks = player.currentTracks
            if (groupIndex >= 0 && groupIndex < tracks.groups.size) {
                val trackGroup = tracks.groups[groupIndex]
                if (trackGroup.type == C.TRACK_TYPE_AUDIO && trackIndex < trackGroup.length) {
                    val format = trackGroup.getTrackFormat(trackIndex)

                    val parametersBuilder = selector.buildUponParameters()

                    // Set preferred audio language
                    if (!format.language.isNullOrEmpty() && format.language != "und") {
                        parametersBuilder.setPreferredAudioLanguage(format.language)
                    }

                    // Force audio track selection by disabling and re-enabling
                    parametersBuilder.setTrackTypeDisabled(C.TRACK_TYPE_AUDIO, true)
                    selector.setParameters(parametersBuilder)

                    // Re-enable with new preference
                    parametersBuilder.setTrackTypeDisabled(C.TRACK_TYPE_AUDIO, false)
                    selector.setParameters(parametersBuilder)

                    // Log for debugging
                    android.util.Log.d("Player", "Selected audio track: ${format.language} - ${format.label}")
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("Player", "Error selecting audio track", e)
        }
    }

    private fun selectSubtitleTrack(groupIndex: Int, trackIndex: Int) {
        val player = exoPlayer ?: return
        val selector = trackSelector ?: return

        try {
            val tracks = player.currentTracks
            if (groupIndex >= 0 && groupIndex < tracks.groups.size) {
                val trackGroup = tracks.groups[groupIndex]
                if (trackGroup.type == C.TRACK_TYPE_TEXT && trackIndex < trackGroup.length) {
                    val format = trackGroup.getTrackFormat(trackIndex)

                    val parametersBuilder = selector.buildUponParameters()

                    // Set preferred subtitle language
                    if (!format.language.isNullOrEmpty() && format.language != "und") {
                        parametersBuilder.setPreferredTextLanguage(format.language)
                    }

                    // Force subtitle track selection by disabling and re-enabling
                    parametersBuilder.setTrackTypeDisabled(C.TRACK_TYPE_TEXT, true)
                    selector.setParameters(parametersBuilder)

                    // Re-enable with new preference
                    parametersBuilder.setTrackTypeDisabled(C.TRACK_TYPE_TEXT, false)
                    selector.setParameters(parametersBuilder)

                    // Log for debugging
                    android.util.Log.d("Player", "Selected subtitle track: ${format.language} - ${format.label}")
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("Player", "Error selecting subtitle track", e)
        }
    }

    private fun disableSubtitles() {
        trackSelector?.let { selector ->
            val parametersBuilder = selector.buildUponParameters()
            parametersBuilder.setTrackTypeDisabled(C.TRACK_TYPE_TEXT, true)
            selector.setParameters(parametersBuilder)
        }
    }

    private fun getAudioTrackLabel(format: Format, trackNumber: Int): String {
        return when {
            !format.language.isNullOrEmpty() && format.language != "und" -> {
                val languageName = getLanguageName(format.language!!)
                val channels = if (format.channelCount > 0) " (${format.channelCount}ch)" else ""
                val bitrate = if (format.bitrate > 0) " ${format.bitrate/1000}kbps" else ""
                "$languageName$channels$bitrate"
            }
            !format.label.isNullOrEmpty() && !format.label!!.contains("MoviesMod") -> {
                format.label!!
            }
            else -> "Audio Track $trackNumber"
        }
    }

    private fun getSubtitleTrackLabel(format: Format, trackNumber: Int): String {
        return when {
            !format.language.isNullOrEmpty() && format.language != "und" -> {
                getLanguageName(format.language!!)
            }
            !format.label.isNullOrEmpty() && !format.label!!.contains("MoviesMod") -> {
                format.label!!
            }
            else -> "Subtitle $trackNumber"
        }
    }

    private fun getLanguageName(languageCode: String): String {
        return when (languageCode.lowercase()) {
            "en", "eng" -> "English"
            "hi", "hin" -> "Hindi"
            "bn", "ben" -> "Bengali"
            "ta", "tam" -> "Tamil"
            "te", "tel" -> "Telugu"
            "ml", "mal" -> "Malayalam"
            "kn", "kan" -> "Kannada"
            "gu", "guj" -> "Gujarati"
            "mr", "mar" -> "Marathi"
            "pa", "pan" -> "Punjabi"
            "ur", "urd" -> "Urdu"
            "as", "asm" -> "Assamese"
            "or", "ori" -> "Odia"
            "es", "spa" -> "Spanish"
            "fr", "fra" -> "French"
            "de", "deu" -> "German"
            "it", "ita" -> "Italian"
            "pt", "por" -> "Portuguese"
            "ru", "rus" -> "Russian"
            "ja", "jpn" -> "Japanese"
            "ko", "kor" -> "Korean"
            "zh", "chi" -> "Chinese"
            "ar", "ara" -> "Arabic"
            else -> languageCode.uppercase()
        }
    }
}

@Composable
fun SimpleVideoPlayerScreen(
    videoUrl: String,
    title: String,
    onPlayerCreated: (ExoPlayer, DefaultTrackSelector) -> Unit,
    onBackPressed: () -> Unit,
    onShowAudioSelector: () -> Unit,
    onShowSubtitleSelector: () -> Unit
) {
    val context = LocalContext.current
    var controlsVisible by remember { mutableStateOf(true) }

    DisposableEffect(Unit) {
        onDispose { }
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
            .clickable {
                controlsVisible = !controlsVisible
            }
    ) {
        AndroidView(
            factory = { context ->
                PlayerView(context).apply {
                    useController = true
                    controllerAutoShow = true
                    controllerHideOnTouch = true

                    // Listen for controller visibility changes
                    setControllerVisibilityListener(
                        androidx.media3.ui.PlayerView.ControllerVisibilityListener { visibility ->
                            controlsVisible = visibility == android.view.View.VISIBLE
                        }
                    )
                    
                    val httpDataSourceFactory = DefaultHttpDataSource.Factory()
                        .setUserAgent("CinepixApp/1.0")
                        .setAllowCrossProtocolRedirects(true)

                    // Create cache data source for offline viewing
                    val cache = SimpleVideoPlayerActivity.getVideoCache(context)
                    val cacheDataSourceFactory = CacheDataSource.Factory()
                        .setCache(cache)
                        .setUpstreamDataSourceFactory(httpDataSourceFactory)
                        .setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR)

                    val mediaSourceFactory = DefaultMediaSourceFactory(cacheDataSourceFactory)

                    val trackSelector = DefaultTrackSelector(context).apply {
                        setParameters(
                            buildUponParameters()
                                .setMaxVideoSizeSd()
                                .setPreferredAudioLanguage("en")
                                .setPreferredTextLanguage("en")
                        )
                    }

                    val player = ExoPlayer.Builder(context)
                        .setMediaSourceFactory(mediaSourceFactory)
                        .setTrackSelector(trackSelector)
                        .build()

                    this.player = player
                    onPlayerCreated(player, trackSelector)
                }
            },
            modifier = Modifier.fillMaxSize()
        )

        // Custom controls overlay - only show when controls are visible
        androidx.compose.animation.AnimatedVisibility(
            visible = controlsVisible,
            enter = androidx.compose.animation.fadeIn(),
            exit = androidx.compose.animation.fadeOut(),
            modifier = Modifier.align(Alignment.TopEnd)
        ) {
            Row(
                modifier = Modifier.padding(16.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
            // Audio selector button
            Card(
                modifier = Modifier
                    .size(48.dp)
                    .clickable { onShowAudioSelector() },
                colors = CardDefaults.cardColors(
                    containerColor = Color.Black.copy(alpha = 0.7f)
                ),
                shape = RoundedCornerShape(8.dp)
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        Icons.Default.VolumeUp,
                        contentDescription = "Audio Settings",
                        tint = Color.White,
                        modifier = Modifier.size(24.dp)
                    )
                }
            }

            // Subtitle selector button
            Card(
                modifier = Modifier
                    .size(48.dp)
                    .clickable { onShowSubtitleSelector() },
                colors = CardDefaults.cardColors(
                    containerColor = Color.Black.copy(alpha = 0.7f)
                ),
                shape = RoundedCornerShape(8.dp)
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        Icons.Default.Subtitles,
                        contentDescription = "Subtitle Settings",
                        tint = Color.White,
                        modifier = Modifier.size(24.dp)
                    )
                }
            }

            // Back button
            Card(
                modifier = Modifier
                    .size(48.dp)
                    .clickable { onBackPressed() },
                colors = CardDefaults.cardColors(
                    containerColor = Color.Black.copy(alpha = 0.7f)
                ),
                shape = RoundedCornerShape(8.dp)
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        Icons.Default.Close,
                        contentDescription = "Close",
                        tint = Color.White,
                        modifier = Modifier.size(24.dp)
                    )
                }
            }
            }
        }
    }
}
