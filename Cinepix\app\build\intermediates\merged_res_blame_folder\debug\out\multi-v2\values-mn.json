{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-91:/values-mn/values-mn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,299,397,516,601,666,764,845,904,997,1060,1118,1189,1251,1305,1426,1483,1544,1598,1669,1802,1886,1969,2072,2154,2232,2322,2389,2455,2526,2604,2690,2765,2843,2923,3006,3094,3173,3263,3356,3430,3500,3591,3645,3712,3796,3881,3943,4007,4070,4174,4280,4377,4482", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,78,97,118,84,64,97,80,58,92,62,57,70,61,53,120,56,60,53,70,132,83,82,102,81,77,89,66,65,70,77,85,74,77,79,82,87,78,89,92,73,69,90,53,66,83,84,61,63,62,103,105,96,104,83", "endOffsets": "215,294,392,511,596,661,759,840,899,992,1055,1113,1184,1246,1300,1421,1478,1539,1593,1664,1797,1881,1964,2067,2149,2227,2317,2384,2450,2521,2599,2685,2760,2838,2918,3001,3089,3168,3258,3351,3425,3495,3586,3640,3707,3791,3876,3938,4002,4065,4169,4275,4372,4477,4561"}, "to": {"startLines": "19,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "722,4108,4922,5020,5139,9535,13674,13772,14033,18414,18507,18570,18628,18699,18761,18815,18936,18993,19054,19108,19179,19811,19895,19978,20081,20163,20241,20331,20398,20464,20535,20613,20699,20774,20852,20932,21015,21103,21182,21272,21365,21439,21509,21600,21654,21721,21805,21890,21952,22016,22079,22183,22289,22386,23201", "endLines": "22,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "endColumns": "12,78,97,118,84,64,97,80,58,92,62,57,70,61,53,120,56,60,53,70,132,83,82,102,81,77,89,66,65,70,77,85,74,77,79,82,87,78,89,92,73,69,90,53,66,83,84,61,63,62,103,105,96,104,83", "endOffsets": "882,4182,5015,5134,5219,9595,13767,13848,14087,18502,18565,18623,18694,18756,18810,18931,18988,19049,19103,19174,19307,19890,19973,20076,20158,20236,20326,20393,20459,20530,20608,20694,20769,20847,20927,21010,21098,21177,21267,21360,21434,21504,21595,21649,21716,21800,21885,21947,22011,22074,22178,22284,22381,22486,23280"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "56,57,58,59,60,61,62,270", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4187,4285,4387,4488,4586,4691,4803,23911", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "4280,4382,4483,4581,4686,4798,4917,24007"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,265,345,483,652,737", "endColumns": "69,89,79,137,168,84,81", "endOffsets": "170,260,340,478,647,732,814"}, "to": {"startLines": "69,158,258,263,272,290,291", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5479,13508,22886,23285,24109,25849,25934", "endColumns": "69,89,79,137,168,84,81", "endOffsets": "5544,13593,22961,23418,24273,25929,26011"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,2873"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "887,1001,1101,1210,1296,1402,1516,1599,1680,1771,1864,1959,2055,2152,2245,2339,2431,2522,2612,2692,2799,2902,2999,3106,3208,3321,3480,23670", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "996,1096,1205,1291,1397,1511,1594,1675,1766,1859,1954,2050,2147,2240,2334,2426,2517,2607,2687,2794,2897,2994,3101,3203,3316,3475,3574,23746"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,283,375,471,554,640,734,821,902,975,1046,1129,1212,1285,1362,1428", "endColumns": "91,85,91,95,82,85,93,86,80,72,70,82,82,72,76,65,116", "endOffsets": "192,278,370,466,549,635,729,816,897,970,1041,1124,1207,1280,1357,1423,1540"}, "to": {"startLines": "66,67,101,103,105,162,163,256,257,259,260,264,265,268,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5224,5316,9103,9277,9452,13853,13939,22718,22805,22966,23039,23423,23506,23751,24278,24355,24421", "endColumns": "91,85,91,95,82,85,93,86,80,72,70,82,82,72,76,65,116", "endOffsets": "5311,5397,9190,9368,9530,13934,14028,22800,22881,23034,23105,23501,23584,23819,24350,24416,24533"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,415,510,633,744,842,935,1072,1204,1315,1442,1592,1722,1847,1952,2049,2185,2279,2384,2496,2600,2693,2803,2919,3038,3152,3259,3369,3495,3618,3743,3864,3951,4033,4138,4271,4427", "endColumns": "106,100,101,94,122,110,97,92,136,131,110,126,149,129,124,104,96,135,93,104,111,103,92,109,115,118,113,106,109,125,122,124,120,86,81,104,132,155,90", "endOffsets": "207,308,410,505,628,739,837,930,1067,1199,1310,1437,1587,1717,1842,1947,2044,2180,2274,2379,2491,2595,2688,2798,2914,3033,3147,3254,3364,3490,3613,3738,3859,3946,4028,4133,4266,4422,4513"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14092,14199,14300,14402,14497,14620,14731,14829,14922,15059,15191,15302,15429,15579,15709,15834,15939,16036,16172,16266,16371,16483,16587,16680,16790,16906,17025,17139,17246,17356,17482,17605,17730,17851,17938,18020,18125,18258,23110", "endColumns": "106,100,101,94,122,110,97,92,136,131,110,126,149,129,124,104,96,135,93,104,111,103,92,109,115,118,113,106,109,125,122,124,120,86,81,104,132,155,90", "endOffsets": "14194,14295,14397,14492,14615,14726,14824,14917,15054,15186,15297,15424,15574,15704,15829,15934,16031,16167,16261,16366,16478,16582,16675,16785,16901,17020,17134,17241,17351,17477,17600,17725,17846,17933,18015,18120,18253,18409,23196"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,136,218,292,363,444,525,621", "endColumns": "80,81,73,70,80,80,95,95", "endOffsets": "131,213,287,358,439,520,616,712"}, "to": {"startLines": "50,102,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3579,9195,19312,19386,19457,19538,19619,19715", "endColumns": "80,81,73,70,80,80,95,95", "endOffsets": "3655,9272,19381,19452,19533,19614,19710,19806"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,244,308,382,456,547,635", "endColumns": "64,58,64,63,73,73,90,87,80", "endOffsets": "115,174,239,303,377,451,542,630,711"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11716,11781,11840,11905,11969,12043,12117,12208,12296", "endColumns": "64,58,64,63,73,73,90,87,80", "endOffsets": "11776,11835,11900,11964,12038,12112,12203,12291,12372"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,120", "endOffsets": "156,277"}, "to": {"startLines": "254,255", "startColumns": "4,4", "startOffsets": "22491,22597", "endColumns": "105,120", "endOffsets": "22592,22713"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,496,672,758,847,931,1023,1114,1190,1255,1344,1437,1508,1576,1637,1705,1860,2018,2172,2239,2321,2392,2472,2563,2657,2723,2788,2841,2899,2947,3008,3070,3146,3208,3272,3333,3394,3458,3523,3589,3641,3705,3783,3861", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,88,83,91,90,75,64,88,92,70,67,60,67,154,157,153,66,81,70,79,90,93,65,64,52,57,47,60,61,75,61,63,60,60,63,64,65,51,63,77,77,57", "endOffsets": "280,491,667,753,842,926,1018,1109,1185,1250,1339,1432,1503,1571,1632,1700,1855,2013,2167,2234,2316,2387,2467,2558,2652,2718,2783,2836,2894,2942,3003,3065,3141,3203,3267,3328,3389,3453,3518,3584,3636,3700,3778,3856,3914"}, "to": {"startLines": "2,11,15,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,546,9600,9686,9775,9859,9951,10042,10118,10183,10272,10365,10436,10504,10565,10633,10788,10946,11100,11167,11249,11320,11400,11491,11585,11651,12377,12430,12488,12536,12597,12659,12735,12797,12861,12922,12983,13047,13112,13178,13230,13294,13372,13450", "endLines": "10,14,18,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "endColumns": "17,12,12,85,88,83,91,90,75,64,88,92,70,67,60,67,154,157,153,66,81,70,79,90,93,65,64,52,57,47,60,61,75,61,63,60,60,63,64,65,51,63,77,77,57", "endOffsets": "330,541,717,9681,9770,9854,9946,10037,10113,10178,10267,10360,10431,10499,10560,10628,10783,10941,11095,11162,11244,11315,11395,11486,11580,11646,11711,12425,12483,12531,12592,12654,12730,12792,12856,12917,12978,13042,13107,13173,13225,13289,13367,13445,13503"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,282,390,503,580,673,784,922,1036,1171,1252,1350,1438,1532,1646,1764,1867,2005,2145,2273,2445,2567,2684,2801,2918,3007,3103,3222,3356,3451,3555,3657,3796,3937,4040,4134,4213,4289,4370,4457,4554,4630,4709,4804,4900,4991,5089,5172,5276,5371,5471,5598,5674,5774", "endColumns": "114,111,107,112,76,92,110,137,113,134,80,97,87,93,113,117,102,137,139,127,171,121,116,116,116,88,95,118,133,94,103,101,138,140,102,93,78,75,80,86,96,75,78,94,95,90,97,82,103,94,99,126,75,99,90", "endOffsets": "165,277,385,498,575,668,779,917,1031,1166,1247,1345,1433,1527,1641,1759,1862,2000,2140,2268,2440,2562,2679,2796,2913,3002,3098,3217,3351,3446,3550,3652,3791,3932,4035,4129,4208,4284,4365,4452,4549,4625,4704,4799,4895,4986,5084,5167,5271,5366,5466,5593,5669,5769,5860"}, "to": {"startLines": "51,52,53,54,68,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,104,159,266,269,271,276,277,278,279,280,281,282,283,284,285,286,287,288,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3660,3775,3887,3995,5402,5549,5642,5753,5891,6005,6140,6221,6319,6407,6501,6615,6733,6836,6974,7114,7242,7414,7536,7653,7770,7887,7976,8072,8191,8325,8420,8524,8626,8765,8906,9009,9373,13598,23589,23824,24012,24538,24614,24693,24788,24884,24975,25073,25156,25260,25355,25455,25582,25658,25758", "endColumns": "114,111,107,112,76,92,110,137,113,134,80,97,87,93,113,117,102,137,139,127,171,121,116,116,116,88,95,118,133,94,103,101,138,140,102,93,78,75,80,86,96,75,78,94,95,90,97,82,103,94,99,126,75,99,90", "endOffsets": "3770,3882,3990,4103,5474,5637,5748,5886,6000,6135,6216,6314,6402,6496,6610,6728,6831,6969,7109,7237,7409,7531,7648,7765,7882,7971,8067,8186,8320,8415,8519,8621,8760,8901,9004,9098,9447,13669,23665,23906,24104,24609,24688,24783,24879,24970,25068,25151,25255,25350,25450,25577,25653,25753,25844"}}]}]}