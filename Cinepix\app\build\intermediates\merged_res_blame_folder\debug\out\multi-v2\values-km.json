{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-91:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "873,975,1074,1184,1271,1374,1495,1573,1649,1740,1833,1925,2019,2119,2212,2307,2401,2492,2583,2666,2770,2874,2974,3083,3192,3301,3463,23301", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "970,1069,1179,1266,1369,1490,1568,1644,1735,1828,1920,2014,2114,2207,2302,2396,2487,2578,2661,2765,2869,2969,3078,3187,3296,3458,3556,23380"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,470,654,731,807,886,970,1058,1139,1205,1292,1381,1446,1508,1570,1636,1763,1886,2009,2081,2166,2237,2320,2402,2483,2545,2611,2664,2722,2772,2833,2892,2960,3030,3099,3166,3225,3291,3356,3423,3478,3535,3612,3689", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,76,75,78,83,87,80,65,86,88,64,61,61,65,126,122,122,71,84,70,82,81,80,61,65,52,57,49,60,58,67,69,68,66,58,65,64,66,54,56,76,76,55", "endOffsets": "280,465,649,726,802,881,965,1053,1134,1200,1287,1376,1441,1503,1565,1631,1758,1881,2004,2076,2161,2232,2315,2397,2478,2540,2606,2659,2717,2767,2828,2887,2955,3025,3094,3161,3220,3286,3351,3418,3473,3530,3607,3684,3740"}, "to": {"startLines": "2,11,15,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,520,9554,9631,9707,9786,9870,9958,10039,10105,10192,10281,10346,10408,10470,10536,10663,10786,10909,10981,11066,11137,11220,11302,11383,11445,12182,12235,12293,12343,12404,12463,12531,12601,12670,12737,12796,12862,12927,12994,13049,13106,13183,13260", "endLines": "10,14,18,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "endColumns": "17,12,12,76,75,78,83,87,80,65,86,88,64,61,61,65,126,122,122,71,84,70,82,81,80,61,65,52,57,49,60,58,67,69,68,66,58,65,64,66,54,56,76,76,55", "endOffsets": "330,515,699,9626,9702,9781,9865,9953,10034,10100,10187,10276,10341,10403,10465,10531,10658,10781,10904,10976,11061,11132,11215,11297,11378,11440,11506,12230,12288,12338,12399,12458,12526,12596,12665,12732,12791,12857,12922,12989,13044,13101,13178,13255,13311"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,185,251,320,393,464,562,657", "endColumns": "70,58,65,68,72,70,97,94,68", "endOffsets": "121,180,246,315,388,459,557,652,721"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11511,11582,11641,11707,11776,11849,11920,12018,12113", "endColumns": "70,58,65,68,72,70,97,94,68", "endOffsets": "11577,11636,11702,11771,11844,11915,12013,12108,12177"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,160", "endColumns": "104,104", "endOffsets": "155,260"}, "to": {"startLines": "254,255", "startColumns": "4,4", "startOffsets": "22152,22257", "endColumns": "104,104", "endOffsets": "22252,22357"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "56,57,58,59,60,61,62,270", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4165,4260,4363,4461,4561,4662,4774,23545", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "4255,4358,4456,4556,4657,4769,4881,23641"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-km\\values-km.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,304,404,516,596,661,755,825,887,974,1039,1098,1163,1224,1281,1400,1458,1519,1576,1647,1777,1863,1941,2049,2124,2195,2292,2359,2425,2505,2595,2681,2760,2837,2907,2982,3070,3140,3240,3339,3413,3489,3596,3650,3723,3814,3910,3972,4036,4099,4198,4296,4388,4488", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,79,99,111,79,64,93,69,61,86,64,58,64,60,56,118,57,60,56,70,129,85,77,107,74,70,96,66,65,79,89,85,78,76,69,74,87,69,99,98,73,75,106,53,72,90,95,61,63,62,98,97,91,99,82", "endOffsets": "219,299,399,511,591,656,750,820,882,969,1034,1093,1158,1219,1276,1395,1453,1514,1571,1642,1772,1858,1936,2044,2119,2190,2287,2354,2420,2500,2590,2676,2755,2832,2902,2977,3065,3135,3235,3334,3408,3484,3591,3645,3718,3809,3905,3967,4031,4094,4193,4291,4383,4483,4566"}, "to": {"startLines": "19,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "704,4085,4886,4986,5098,9489,13484,13578,13815,18087,18174,18239,18298,18363,18424,18481,18600,18658,18719,18776,18847,19441,19527,19605,19713,19788,19859,19956,20023,20089,20169,20259,20345,20424,20501,20571,20646,20734,20804,20904,21003,21077,21153,21260,21314,21387,21478,21574,21636,21700,21763,21862,21960,22052,22836", "endLines": "22,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "endColumns": "12,79,99,111,79,64,93,69,61,86,64,58,64,60,56,118,57,60,56,70,129,85,77,107,74,70,96,66,65,79,89,85,78,76,69,74,87,69,99,98,73,75,106,53,72,90,95,61,63,62,98,97,91,99,82", "endOffsets": "868,4160,4981,5093,5173,9549,13573,13643,13872,18169,18234,18293,18358,18419,18476,18595,18653,18714,18771,18842,18972,19522,19600,19708,19783,19854,19951,20018,20084,20164,20254,20340,20419,20496,20566,20641,20729,20799,20899,20998,21072,21148,21255,21309,21382,21473,21569,21631,21695,21758,21857,21955,22047,22147,22914"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,284,386,498,577,679,799,939,1063,1211,1298,1394,1490,1596,1716,1837,1938,2060,2182,2306,2464,2582,2692,2802,2922,3019,3115,3237,3372,3472,3573,3681,3801,3925,4038,4141,4213,4291,4375,4459,4556,4632,4712,4808,4906,4998,5103,5186,5284,5378,5484,5597,5673,5776", "endColumns": "113,114,101,111,78,101,119,139,123,147,86,95,95,105,119,120,100,121,121,123,157,117,109,109,119,96,95,121,134,99,100,107,119,123,112,102,71,77,83,83,96,75,79,95,97,91,104,82,97,93,105,112,75,102,95", "endOffsets": "164,279,381,493,572,674,794,934,1058,1206,1293,1389,1485,1591,1711,1832,1933,2055,2177,2301,2459,2577,2687,2797,2917,3014,3110,3232,3367,3467,3568,3676,3796,3920,4033,4136,4208,4286,4370,4454,4551,4627,4707,4803,4901,4993,5098,5181,5279,5373,5479,5592,5668,5771,5867"}, "to": {"startLines": "51,52,53,54,68,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,104,159,266,269,271,276,277,278,279,280,281,282,283,284,285,286,287,288,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3642,3756,3871,3973,5343,5491,5593,5713,5853,5977,6125,6212,6308,6404,6510,6630,6751,6852,6974,7096,7220,7378,7496,7606,7716,7836,7933,8029,8151,8286,8386,8487,8595,8715,8839,8952,9329,13406,23217,23461,23646,24180,24256,24336,24432,24530,24622,24727,24810,24908,25002,25108,25221,25297,25400", "endColumns": "113,114,101,111,78,101,119,139,123,147,86,95,95,105,119,120,100,121,121,123,157,117,109,109,119,96,95,121,134,99,100,107,119,123,112,102,71,77,83,83,96,75,79,95,97,91,104,82,97,93,105,112,75,102,95", "endOffsets": "3751,3866,3968,4080,5417,5588,5708,5848,5972,6120,6207,6303,6399,6505,6625,6746,6847,6969,7091,7215,7373,7491,7601,7711,7831,7928,8024,8146,8281,8381,8482,8590,8710,8834,8947,9050,9396,13479,23296,23540,23738,24251,24331,24427,24525,24617,24722,24805,24903,24997,25103,25216,25292,25395,25491"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,136,208,276,342,416,498,585", "endColumns": "80,71,67,65,73,81,86,86", "endOffsets": "131,203,271,337,411,493,580,667"}, "to": {"startLines": "50,102,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3561,9159,18977,19045,19111,19185,19267,19354", "endColumns": "80,71,67,65,73,81,86,86", "endOffsets": "3637,9226,19040,19106,19180,19262,19349,19436"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,264,342,475,644,724", "endColumns": "68,89,77,132,168,79,76", "endOffsets": "169,259,337,470,639,719,796"}, "to": {"startLines": "69,158,258,263,272,290,291", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5422,13316,22534,22919,23743,25496,25576", "endColumns": "68,89,77,132,168,79,76", "endOffsets": "5486,13401,22607,23047,23907,25571,25648"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,190,270,374,472,560,644,727,812,899,964,1029,1109,1194,1270,1354,1420", "endColumns": "84,79,103,97,87,83,82,84,86,64,64,79,84,75,83,65,117", "endOffsets": "185,265,369,467,555,639,722,807,894,959,1024,1104,1189,1265,1349,1415,1533"}, "to": {"startLines": "66,67,101,103,105,162,163,256,257,259,260,264,265,268,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5178,5263,9055,9231,9401,13648,13732,22362,22447,22612,22677,23052,23132,23385,23912,23996,24062", "endColumns": "84,79,103,97,87,83,82,84,86,64,64,79,84,75,83,65,117", "endOffsets": "5258,5338,9154,9324,9484,13727,13810,22442,22529,22672,22737,23127,23212,23456,23991,24057,24175"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,407,501,623,731,830,924,1043,1161,1264,1383,1529,1646,1762,1873,1964,2088,2177,2287,2396,2502,2600,2715,2836,2946,3056,3160,3267,3387,3512,3627,3745,3832,3917,4023,4159,4315", "endColumns": "106,100,93,93,121,107,98,93,118,117,102,118,145,116,115,110,90,123,88,109,108,105,97,114,120,109,109,103,106,119,124,114,117,86,84,105,135,155,93", "endOffsets": "207,308,402,496,618,726,825,919,1038,1156,1259,1378,1524,1641,1757,1868,1959,2083,2172,2282,2391,2497,2595,2710,2831,2941,3051,3155,3262,3382,3507,3622,3740,3827,3912,4018,4154,4310,4404"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13877,13984,14085,14179,14273,14395,14503,14602,14696,14815,14933,15036,15155,15301,15418,15534,15645,15736,15860,15949,16059,16168,16274,16372,16487,16608,16718,16828,16932,17039,17159,17284,17399,17517,17604,17689,17795,17931,22742", "endColumns": "106,100,93,93,121,107,98,93,118,117,102,118,145,116,115,110,90,123,88,109,108,105,97,114,120,109,109,103,106,119,124,114,117,86,84,105,135,155,93", "endOffsets": "13979,14080,14174,14268,14390,14498,14597,14691,14810,14928,15031,15150,15296,15413,15529,15640,15731,15855,15944,16054,16163,16269,16367,16482,16603,16713,16823,16927,17034,17154,17279,17394,17512,17599,17684,17790,17926,18082,22831"}}]}]}