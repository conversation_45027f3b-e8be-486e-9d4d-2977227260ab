package com.deshi.cinepix;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H\u0002J\b\u0010\u0005\u001a\u00020\u0004H\u0016\u00a8\u0006\u0006"}, d2 = {"Lcom/deshi/cinepix/CinepixApplication;", "Landroidx/multidex/MultiDexApplication;", "()V", "initializeApp", "", "onCreate", "app_release"})
public final class CinepixApplication extends androidx.multidex.MultiDexApplication {
    
    public CinepixApplication() {
        super();
    }
    
    @java.lang.Override
    public void onCreate() {
    }
    
    private final void initializeApp() {
    }
}