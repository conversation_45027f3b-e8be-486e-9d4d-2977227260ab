{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-91:/values-en-rXC/values-en-rXC.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,251,456,657,858,1065,1270,1482", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "246,451,652,853,1060,1265,1477,1681"}, "to": {"startLines": "33,34,35,36,37,38,39,134", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "6380,6576,6781,6982,7183,7390,7595,27056", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "6571,6776,6977,7178,7385,7590,7802,27255"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,276,465,642,894,1196,1378", "endColumns": "170,188,176,251,301,181,172", "endOffsets": "271,460,637,889,1191,1373,1546"}, "to": {"startLines": "43,79,123,127,136,154,155", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "8364,15884,25014,25715,27461,31079,31261", "endColumns": "170,188,176,251,301,181,172", "endOffsets": "8530,16068,25186,25962,27758,31256,31429"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,312,515,725,912,1113,1329,1509,1684,1878,2072,2267,2464,2663,2858,3056,3253,3447,3641,3826,4031,4234,4435,4641,4846,5053,5327,5528", "endColumns": "206,202,209,186,200,215,179,174,193,193,194,196,198,194,197,196,193,193,184,204,202,200,205,204,206,273,200,185", "endOffsets": "307,510,720,907,1108,1324,1504,1679,1873,2067,2262,2459,2658,2853,3051,3248,3442,3636,3821,4026,4229,4430,4636,4841,5048,5322,5523,5709"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,131", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,312,515,725,912,1113,1329,1509,1684,1878,2072,2267,2464,2663,2858,3056,3253,3447,3641,3826,4031,4234,4435,4641,4846,5053,5327,26519", "endColumns": "206,202,209,186,200,215,179,174,193,193,194,196,198,194,197,196,193,193,184,204,202,200,205,204,206,273,200,185", "endOffsets": "307,510,720,907,1108,1324,1504,1679,1873,2067,2262,2459,2658,2853,3051,3248,3442,3636,3821,4026,4229,4430,4636,4841,5048,5322,5523,26700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,296,481,678,880,1067,1252,1445,1633,1820,1985,2152,2333,2518,2684,2863,3030", "endColumns": "190,184,196,201,186,184,192,187,186,164,166,180,184,165,178,166,237", "endOffsets": "291,476,673,875,1062,1247,1440,1628,1815,1980,2147,2328,2513,2679,2858,3025,3263"}, "to": {"startLines": "40,41,75,76,78,81,82,121,122,124,125,128,129,132,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7807,7998,15124,15321,15697,16252,16437,24639,24827,25191,25356,25967,26148,26705,27763,27942,28109", "endColumns": "190,184,196,201,186,184,192,187,186,164,166,180,184,165,178,166,237", "endOffsets": "7993,8178,15316,15518,15879,16432,16625,24822,25009,25351,25518,26143,26328,26866,27937,28104,28342"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,313,516,713,909,1130,1344,1541,1739,1972,2202,2409,2629,2856,3078,3297,3503,3694,3924,4113,4316,4521,4724,4917,5129,5337,5550,5761,5962,6170,6382,6599,6807,7018,7207,7393,7594,7846,8114", "endColumns": "207,202,196,195,220,213,196,197,232,229,206,219,226,221,218,205,190,229,188,202,204,202,192,211,207,212,210,200,207,211,216,207,210,188,185,200,251,267,191", "endOffsets": "308,511,708,904,1125,1339,1536,1734,1967,2197,2404,2624,2851,3073,3292,3498,3689,3919,4108,4311,4516,4719,4912,5124,5332,5545,5756,5957,6165,6377,6594,6802,7013,7202,7388,7589,7841,8109,8301"}, "to": {"startLines": "83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16630,16838,17041,17238,17434,17655,17869,18066,18264,18497,18727,18934,19154,19381,19603,19822,20028,20219,20449,20638,20841,21046,21249,21442,21654,21862,22075,22286,22487,22695,22907,23124,23332,23543,23732,23918,24119,24371,25523", "endColumns": "207,202,196,195,220,213,196,197,232,229,206,219,226,221,218,205,190,229,188,202,204,202,192,211,207,212,210,200,207,211,216,207,210,188,185,200,251,267,191", "endOffsets": "16833,17036,17233,17429,17650,17864,18061,18259,18492,18722,18929,19149,19376,19598,19817,20023,20214,20444,20633,20836,21041,21244,21437,21649,21857,22070,22281,22482,22690,22902,23119,23327,23538,23727,23913,24114,24366,24634,25710"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,272,485,695,907,1088,1281,1493,1728,1943,2178,2361,2559,2748,2943,3161,3385,3588,3814,4035,4262,4518,4736,4951,5174,5398,5589,5786,6002,6223,6418,6619,6820,7041,7278,7483,7677,7851,8030,8216,8401,8602,8781,8964,9163,9363,9562,9760,9947,10152,10351,10550,10765,10942,11140", "endColumns": "216,212,209,211,180,192,211,234,214,234,182,197,188,194,217,223,202,225,220,226,255,217,214,222,223,190,196,215,220,194,200,200,220,236,204,193,173,178,185,184,200,178,182,198,199,198,197,186,204,198,198,214,176,197,193", "endOffsets": "267,480,690,902,1083,1276,1488,1723,1938,2173,2356,2554,2743,2938,3156,3380,3583,3809,4030,4257,4513,4731,4946,5169,5393,5584,5781,5997,6218,6413,6614,6815,7036,7273,7478,7672,7846,8025,8211,8396,8597,8776,8959,9158,9358,9557,9755,9942,10147,10346,10545,10760,10937,11135,11329"}, "to": {"startLines": "29,30,31,32,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,77,80,130,133,135,140,141,142,143,144,145,146,147,148,149,150,151,152,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5528,5745,5958,6168,8183,8535,8728,8940,9175,9390,9625,9808,10006,10195,10390,10608,10832,11035,11261,11482,11709,11965,12183,12398,12621,12845,13036,13233,13449,13670,13865,14066,14267,14488,14725,14930,15523,16073,26333,26871,27260,28347,28526,28709,28908,29108,29307,29505,29692,29897,30096,30295,30510,30687,30885", "endColumns": "216,212,209,211,180,192,211,234,214,234,182,197,188,194,217,223,202,225,220,226,255,217,214,222,223,190,196,215,220,194,200,200,220,236,204,193,173,178,185,184,200,178,182,198,199,198,197,186,204,198,198,214,176,197,193", "endOffsets": "5740,5953,6163,6375,8359,8723,8935,9170,9385,9620,9803,10001,10190,10385,10603,10827,11030,11256,11477,11704,11960,12178,12393,12616,12840,13031,13228,13444,13665,13860,14061,14262,14483,14720,14925,15119,15692,16247,26514,27051,27456,28521,28704,28903,29103,29302,29500,29687,29892,30091,30290,30505,30682,30880,31074"}}]}]}