{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-91:/values-vi/values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,298,397,511,591,654,748,822,881,967,1028,1086,1150,1211,1265,1382,1439,1499,1553,1628,1755,1839,1917,2017,2101,2179,2270,2337,2403,2471,2547,2628,2707,2782,2855,2931,3020,3097,3188,3282,3356,3426,3519,3568,3634,3719,3805,3867,3931,3994,4093,4198,4296,4401", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,77,98,113,79,62,93,73,58,85,60,57,63,60,53,116,56,59,53,74,126,83,77,99,83,77,90,66,65,67,75,80,78,74,72,75,88,76,90,93,73,69,92,48,65,84,85,61,63,62,98,104,97,104,81", "endOffsets": "215,293,392,506,586,649,743,817,876,962,1023,1081,1145,1206,1260,1377,1434,1494,1548,1623,1750,1834,1912,2012,2096,2174,2265,2332,2398,2466,2542,2623,2702,2777,2850,2926,3015,3092,3183,3277,3351,3421,3514,3563,3629,3714,3800,3862,3926,3989,4088,4193,4291,4396,4478"}, "to": {"startLines": "19,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "697,4068,4876,4975,5089,9382,13455,13549,13801,18019,18105,18166,18224,18288,18349,18403,18520,18577,18637,18691,18766,19361,19445,19523,19623,19707,19785,19876,19943,20009,20077,20153,20234,20313,20388,20461,20537,20626,20703,20794,20888,20962,21032,21125,21174,21240,21325,21411,21473,21537,21600,21699,21804,21902,22710", "endLines": "22,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "endColumns": "12,77,98,113,79,62,93,73,58,85,60,57,63,60,53,116,56,59,53,74,126,83,77,99,83,77,90,66,65,67,75,80,78,74,72,75,88,76,90,93,73,69,92,48,65,84,85,61,63,62,98,104,97,104,81", "endOffsets": "857,4141,4970,5084,5164,9440,13544,13618,13855,18100,18161,18219,18283,18344,18398,18515,18572,18632,18686,18761,18888,19440,19518,19618,19702,19780,19871,19938,20004,20072,20148,20229,20308,20383,20456,20532,20621,20698,20789,20883,20957,21027,21120,21169,21235,21320,21406,21468,21532,21595,21694,21799,21897,22002,22787"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,393,493,585,670,763,857,938,1008,1078,1168,1259,1331,1408,1474", "endColumns": "95,85,105,99,91,84,92,93,80,69,69,89,90,71,76,65,113", "endOffsets": "196,282,388,488,580,665,758,852,933,1003,1073,1163,1254,1326,1403,1469,1583"}, "to": {"startLines": "66,67,101,103,105,162,163,256,257,259,260,264,265,268,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5169,5265,8937,9115,9290,13623,13708,22228,22322,22478,22548,22935,23025,23286,23811,23888,23954", "endColumns": "95,85,105,99,91,84,92,93,80,69,69,89,90,71,76,65,113", "endOffsets": "5260,5346,9038,9210,9377,13703,13796,22317,22398,22543,22613,23020,23111,23353,23883,23949,24063"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,283,386,499,578,669,778,911,1027,1164,1244,1343,1428,1520,1635,1754,1858,1981,2100,2223,2378,2501,2620,2737,2853,2939,3035,3149,3278,3369,3471,3574,3692,3818,3922,4013,4088,4166,4251,4331,4434,4510,4589,4684,4778,4869,4965,5047,5144,5238,5336,5448,5524,5633", "endColumns": "114,112,102,112,78,90,108,132,115,136,79,98,84,91,114,118,103,122,118,122,154,122,118,116,115,85,95,113,128,90,101,102,117,125,103,90,74,77,84,79,102,75,78,94,93,90,95,81,96,93,97,111,75,108,99", "endOffsets": "165,278,381,494,573,664,773,906,1022,1159,1239,1338,1423,1515,1630,1749,1853,1976,2095,2218,2373,2496,2615,2732,2848,2934,3030,3144,3273,3364,3466,3569,3687,3813,3917,4008,4083,4161,4246,4326,4429,4505,4584,4679,4773,4864,4960,5042,5139,5233,5331,5443,5519,5628,5728"}, "to": {"startLines": "51,52,53,54,68,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,104,159,266,269,271,276,277,278,279,280,281,282,283,284,285,286,287,288,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3624,3739,3852,3955,5351,5502,5593,5702,5835,5951,6088,6168,6267,6352,6444,6559,6678,6782,6905,7024,7147,7302,7425,7544,7661,7777,7863,7959,8073,8202,8293,8395,8498,8616,8742,8846,9215,13377,23116,23358,23539,24068,24144,24223,24318,24412,24503,24599,24681,24778,24872,24970,25082,25158,25267", "endColumns": "114,112,102,112,78,90,108,132,115,136,79,98,84,91,114,118,103,122,118,122,154,122,118,116,115,85,95,113,128,90,101,102,117,125,103,90,74,77,84,79,102,75,78,94,93,90,95,81,96,93,97,111,75,108,99", "endOffsets": "3734,3847,3950,4063,5425,5588,5697,5830,5946,6083,6163,6262,6347,6439,6554,6673,6777,6900,7019,7142,7297,7420,7539,7656,7772,7858,7954,8068,8197,8288,8390,8493,8611,8737,8841,8932,9285,13450,23196,23433,23637,24139,24218,24313,24407,24498,24594,24676,24773,24867,24965,25077,25153,25262,25362"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "56,57,58,59,60,61,62,270", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4146,4243,4345,4444,4544,4647,4760,23438", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "4238,4340,4439,4539,4642,4755,4871,23534"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,264,339,482,651,731", "endColumns": "71,86,74,142,168,79,76", "endOffsets": "172,259,334,477,646,726,803"}, "to": {"startLines": "69,158,258,263,272,290,291", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5430,13290,22403,22792,23642,25367,25447", "endColumns": "71,86,74,142,168,79,76", "endOffsets": "5497,13372,22473,22930,23806,25442,25519"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,124,196,267,333,407,483,574", "endColumns": "68,71,70,65,73,75,90,89", "endOffsets": "119,191,262,328,402,478,569,659"}, "to": {"startLines": "50,102,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3555,9043,18893,18964,19030,19104,19180,19271", "endColumns": "68,71,70,65,73,75,90,89", "endOffsets": "3619,9110,18959,19025,19099,19175,19266,19356"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,191,259,328,419,489,579,667", "endColumns": "72,62,67,68,90,69,89,87,79", "endOffsets": "123,186,254,323,414,484,574,662,742"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11465,11538,11601,11669,11738,11829,11899,11989,12077", "endColumns": "72,62,67,68,90,69,89,87,79", "endOffsets": "11533,11596,11664,11733,11824,11894,11984,12072,12152"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "862,969,1071,1180,1264,1367,1486,1564,1640,1731,1824,1919,2013,2113,2206,2301,2395,2486,2577,2661,2765,2873,2974,3079,3194,3299,3456,23201", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "964,1066,1175,1259,1362,1481,1559,1635,1726,1819,1914,2008,2108,2201,2296,2390,2481,2572,2656,2760,2868,2969,3074,3189,3294,3451,3550,23281"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,112", "endOffsets": "158,271"}, "to": {"startLines": "254,255", "startColumns": "4,4", "startOffsets": "22007,22115", "endColumns": "107,112", "endOffsets": "22110,22223"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,290,473,647,723,798,870,973,1074,1153,1221,1320,1421,1489,1552,1615,1683,1813,1933,2060,2128,2206,2276,2361,2446,2530,2593,2667,2720,2781,2831,2892,2954,3020,3084,3149,3210,3269,3338,3405,3471,3529,3589,3663,3737", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,75,74,71,102,100,78,67,98,100,67,62,62,67,129,119,126,67,77,69,84,84,83,62,73,52,60,49,60,61,65,63,64,60,58,68,66,65,57,59,73,73,62", "endOffsets": "285,468,642,718,793,865,968,1069,1148,1216,1315,1416,1484,1547,1610,1678,1808,1928,2055,2123,2201,2271,2356,2441,2525,2588,2662,2715,2776,2826,2887,2949,3015,3079,3144,3205,3264,3333,3400,3466,3524,3584,3658,3732,3795"}, "to": {"startLines": "2,11,15,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,340,523,9445,9521,9596,9668,9771,9872,9951,10019,10118,10219,10287,10350,10413,10481,10611,10731,10858,10926,11004,11074,11159,11244,11328,11391,12157,12210,12271,12321,12382,12444,12510,12574,12639,12700,12759,12828,12895,12961,13019,13079,13153,13227", "endLines": "10,14,18,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "endColumns": "17,12,12,75,74,71,102,100,78,67,98,100,67,62,62,67,129,119,126,67,77,69,84,84,83,62,73,52,60,49,60,61,65,63,64,60,58,68,66,65,57,59,73,73,62", "endOffsets": "335,518,692,9516,9591,9663,9766,9867,9946,10014,10113,10214,10282,10345,10408,10476,10606,10726,10853,10921,10999,11069,11154,11239,11323,11386,11460,12205,12266,12316,12377,12439,12505,12569,12634,12695,12754,12823,12890,12956,13014,13074,13148,13222,13285"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,507,624,736,832,924,1040,1155,1254,1369,1507,1633,1758,1862,1956,2081,2170,2275,2380,2486,2580,2691,2813,2928,3042,3156,3269,3379,3494,3596,3703,3790,3876,3976,4113,4264", "endColumns": "106,100,97,95,116,111,95,91,115,114,98,114,137,125,124,103,93,124,88,104,104,105,93,110,121,114,113,113,112,109,114,101,106,86,85,99,136,150,91", "endOffsets": "207,308,406,502,619,731,827,919,1035,1150,1249,1364,1502,1628,1753,1857,1951,2076,2165,2270,2375,2481,2575,2686,2808,2923,3037,3151,3264,3374,3489,3591,3698,3785,3871,3971,4108,4259,4351"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13860,13967,14068,14166,14262,14379,14491,14587,14679,14795,14910,15009,15124,15262,15388,15513,15617,15711,15836,15925,16030,16135,16241,16335,16446,16568,16683,16797,16911,17024,17134,17249,17351,17458,17545,17631,17731,17868,22618", "endColumns": "106,100,97,95,116,111,95,91,115,114,98,114,137,125,124,103,93,124,88,104,104,105,93,110,121,114,113,113,112,109,114,101,106,86,85,99,136,150,91", "endOffsets": "13962,14063,14161,14257,14374,14486,14582,14674,14790,14905,15004,15119,15257,15383,15508,15612,15706,15831,15920,16025,16130,16236,16330,16441,16563,16678,16792,16906,17019,17129,17244,17346,17453,17540,17626,17726,17863,18014,22705"}}]}]}