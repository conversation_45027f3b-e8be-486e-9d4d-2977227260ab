{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeReleaseResources-87:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,506,632,748,849,942,1074,1199,1309,1444,1600,1730,1851,1958,2049,2184,2273,2384,2490,2601,2704,2833,2946,3058,3186,3294,3407,3532,3652,3775,3893,3980,4065,4173,4318,4485", "endColumns": "106,100,98,93,125,115,100,92,131,124,109,134,155,129,120,106,90,134,88,110,105,110,102,128,112,111,127,107,112,124,119,122,117,86,84,107,144,166,89", "endOffsets": "207,308,407,501,627,743,844,937,1069,1194,1304,1439,1595,1725,1846,1953,2044,2179,2268,2379,2485,2596,2699,2828,2941,3053,3181,3289,3402,3527,3647,3770,3888,3975,4060,4168,4313,4480,4570"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13996,14103,14204,14303,14397,14523,14639,14740,14833,14965,15090,15200,15335,15491,15621,15742,15849,15940,16075,16164,16275,16381,16492,16595,16724,16837,16949,17077,17185,17298,17423,17543,17666,17784,17871,17956,18064,18209,23062", "endColumns": "106,100,98,93,125,115,100,92,131,124,109,134,155,129,120,106,90,134,88,110,105,110,102,128,112,111,127,107,112,124,119,122,117,86,84,107,144,166,89", "endOffsets": "14098,14199,14298,14392,14518,14634,14735,14828,14960,15085,15195,15330,15486,15616,15737,15844,15935,16070,16159,16270,16376,16487,16590,16719,16832,16944,17072,17180,17293,17418,17538,17661,17779,17866,17951,18059,18204,18371,23147"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,2855"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "877,982,1077,1177,1259,1359,1476,1561,1639,1730,1823,1918,2012,2106,2199,2294,2389,2480,2572,2656,2766,2872,2972,3080,3186,3288,3449,23628", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "977,1072,1172,1254,1354,1471,1556,1634,1725,1818,1913,2007,2101,2194,2289,2384,2475,2567,2651,2761,2867,2967,3075,3181,3283,3444,3543,23707"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,145,223,291,357,437,521,612", "endColumns": "89,77,67,65,79,83,90,94", "endOffsets": "140,218,286,352,432,516,607,702"}, "to": {"startLines": "50,102,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3548,9100,19295,19363,19429,19509,19593,19684", "endColumns": "89,77,67,65,79,83,90,94", "endOffsets": "3633,9173,19358,19424,19504,19588,19679,19774"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,279,389,499,577,671,783,912,1017,1152,1232,1327,1417,1511,1621,1738,1843,1964,2083,2209,2373,2494,2611,2732,2850,2941,3035,3148,3270,3370,3476,3579,3697,3821,3930,4029,4109,4185,4269,4351,4448,4524,4604,4700,4800,4892,4987,5071,5175,5271,5369,5504,5580,5692", "endColumns": "112,110,109,109,77,93,111,128,104,134,79,94,89,93,109,116,104,120,118,125,163,120,116,120,117,90,93,112,121,99,105,102,117,123,108,98,79,75,83,81,96,75,79,95,99,91,94,83,103,95,97,134,75,111,98", "endOffsets": "163,274,384,494,572,666,778,907,1012,1147,1227,1322,1412,1506,1616,1733,1838,1959,2078,2204,2368,2489,2606,2727,2845,2936,3030,3143,3265,3365,3471,3574,3692,3816,3925,4024,4104,4180,4264,4346,4443,4519,4599,4695,4795,4887,4982,5066,5170,5266,5364,5499,5575,5687,5786"}, "to": {"startLines": "51,52,53,54,68,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,104,159,266,269,271,276,277,278,279,280,281,282,283,284,285,286,287,288,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3638,3751,3862,3972,5391,5544,5638,5750,5879,5984,6119,6199,6294,6384,6478,6588,6705,6810,6931,7050,7176,7340,7461,7578,7699,7817,7908,8002,8115,8237,8337,8443,8546,8664,8788,8897,9285,13507,23544,23786,23969,24503,24579,24659,24755,24855,24947,25042,25126,25230,25326,25424,25559,25635,25747", "endColumns": "112,110,109,109,77,93,111,128,104,134,79,94,89,93,109,116,104,120,118,125,163,120,116,120,117,90,93,112,121,99,105,102,117,123,108,98,79,75,83,81,96,75,79,95,99,91,94,83,103,95,97,134,75,111,98", "endOffsets": "3746,3857,3967,4077,5464,5633,5745,5874,5979,6114,6194,6289,6379,6473,6583,6700,6805,6926,7045,7171,7335,7456,7573,7694,7812,7903,7997,8110,8232,8332,8438,8541,8659,8783,8892,8991,9360,13578,23623,23863,24061,24574,24654,24750,24850,24942,25037,25121,25225,25321,25419,25554,25630,25742,25841"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,186,256,320,399,467,569,663", "endColumns": "64,65,69,63,78,67,101,93,79", "endOffsets": "115,181,251,315,394,462,564,658,738"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11604,11669,11735,11805,11869,11948,12016,12118,12212", "endColumns": "64,65,69,63,78,67,101,93,79", "endOffsets": "11664,11730,11800,11864,11943,12011,12113,12207,12287"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "56,57,58,59,60,61,62,270", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4165,4267,4369,4470,4570,4678,4782,23868", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "4262,4364,4465,4565,4673,4777,4896,23964"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,156", "endColumns": "100,121", "endOffsets": "151,273"}, "to": {"startLines": "254,255", "startColumns": "4,4", "startOffsets": "22448,22549", "endColumns": "100,121", "endOffsets": "22544,22666"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,468,644,730,825,908,1006,1105,1180,1248,1349,1450,1515,1578,1641,1713,1841,1973,2100,2177,2251,2324,2399,2489,2588,2657,2723,2776,2836,2884,2945,3006,3077,3137,3205,3268,3333,3399,3463,3529,3581,3642,3717,3792", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,94,82,97,98,74,67,100,100,64,62,62,71,127,131,126,76,73,72,74,89,98,68,65,52,59,47,60,60,70,59,67,62,64,65,63,65,51,60,74,74,52", "endOffsets": "280,463,639,725,820,903,1001,1100,1175,1243,1344,1445,1510,1573,1636,1708,1836,1968,2095,2172,2246,2319,2394,2484,2583,2652,2718,2771,2831,2879,2940,3001,3072,3132,3200,3263,3328,3394,3458,3524,3576,3637,3712,3787,3840"}, "to": {"startLines": "2,11,15,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,518,9525,9611,9706,9789,9887,9986,10061,10129,10230,10331,10396,10459,10522,10594,10722,10854,10981,11058,11132,11205,11280,11370,11469,11538,12292,12345,12405,12453,12514,12575,12646,12706,12774,12837,12902,12968,13032,13098,13150,13211,13286,13361", "endLines": "10,14,18,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "endColumns": "17,12,12,85,94,82,97,98,74,67,100,100,64,62,62,71,127,131,126,76,73,72,74,89,98,68,65,52,59,47,60,60,70,59,67,62,64,65,63,65,51,60,74,74,52", "endOffsets": "330,513,689,9606,9701,9784,9882,9981,10056,10124,10225,10326,10391,10454,10517,10589,10717,10849,10976,11053,11127,11200,11275,11365,11464,11533,11599,12340,12400,12448,12509,12570,12641,12701,12769,12832,12897,12963,13027,13093,13145,13206,13281,13356,13409"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,238,321,416,543,627,691,794,864,931,1040,1107,1166,1240,1303,1357,1472,1530,1592,1646,1721,1850,1940,2029,2140,2222,2304,2390,2457,2523,2596,2674,2760,2832,2909,2984,3055,3149,3228,3324,3418,3492,3568,3654,3707,3773,3858,3949,4011,4075,4138,4240,4331,4427,4519", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,94,126,83,63,102,69,66,108,66,58,73,62,53,114,57,61,53,74,128,89,88,110,81,81,85,66,65,72,77,85,71,76,74,70,93,78,95,93,73,75,85,52,65,84,90,61,63,62,101,90,95,91,82", "endOffsets": "233,316,411,538,622,686,789,859,926,1035,1102,1161,1235,1298,1352,1467,1525,1587,1641,1716,1845,1935,2024,2135,2217,2299,2385,2452,2518,2591,2669,2755,2827,2904,2979,3050,3144,3223,3319,3413,3487,3563,3649,3702,3768,3853,3944,4006,4070,4133,4235,4326,4422,4514,4597"}, "to": {"startLines": "19,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "694,4082,4901,4996,5123,9461,13583,13686,13929,18376,18485,18552,18611,18685,18748,18802,18917,18975,19037,19091,19166,19779,19869,19958,20069,20151,20233,20319,20386,20452,20525,20603,20689,20761,20838,20913,20984,21078,21157,21253,21347,21421,21497,21583,21636,21702,21787,21878,21940,22004,22067,22169,22260,22356,23152", "endLines": "22,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "endColumns": "12,82,94,126,83,63,102,69,66,108,66,58,73,62,53,114,57,61,53,74,128,89,88,110,81,81,85,66,65,72,77,85,71,76,74,70,93,78,95,93,73,75,85,52,65,84,90,61,63,62,101,90,95,91,82", "endOffsets": "872,4160,4991,5118,5202,9520,13681,13751,13991,18480,18547,18606,18680,18743,18797,18912,18970,19032,19086,19161,19290,19864,19953,20064,20146,20228,20314,20381,20447,20520,20598,20684,20756,20833,20908,20979,21073,21152,21248,21342,21416,21492,21578,21631,21697,21782,21873,21935,21999,22062,22164,22255,22351,22443,23230"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,180,273,353,498,667,754", "endColumns": "74,92,79,144,168,86,78", "endOffsets": "175,268,348,493,662,749,828"}, "to": {"startLines": "69,158,258,263,272,290,291", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5469,13414,22847,23235,24066,25846,25933", "endColumns": "74,92,79,144,168,86,78", "endOffsets": "5539,13502,22922,23375,24230,25928,26007"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,289,393,500,596,679,769,862,945,1013,1080,1161,1244,1318,1401,1469", "endColumns": "98,84,103,106,95,82,89,92,82,67,66,80,82,73,82,67,116", "endOffsets": "199,284,388,495,591,674,764,857,940,1008,1075,1156,1239,1313,1396,1464,1581"}, "to": {"startLines": "66,67,101,103,105,162,163,256,257,259,260,264,265,268,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5207,5306,8996,9178,9365,13756,13839,22671,22764,22927,22995,23380,23461,23712,24235,24318,24386", "endColumns": "98,84,103,106,95,82,89,92,82,67,66,80,82,73,82,67,116", "endOffsets": "5301,5386,9095,9280,9456,13834,13924,22759,22842,22990,23057,23456,23539,23781,24313,24381,24498"}}]}]}