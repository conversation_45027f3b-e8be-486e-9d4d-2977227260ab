package com.deshi.cinepix.ui.webview

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.webkit.*
import androidx.activity.ComponentActivity
import androidx.activity.OnBackPressedCallback
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.clickable
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.text.font.FontWeight
import kotlinx.coroutines.launch
import com.deshi.cinepix.ui.player.SimpleVideoPlayerActivity
import com.deshi.cinepix.ui.download.DownloadActivity
import com.deshi.cinepix.ui.theme.CinepixTheme
import com.deshi.cinepix.ui.auth.AuthActivity
import com.deshi.cinepix.ui.auth.ProfileActivity
import com.deshi.cinepix.data.*

class SimpleWebViewActivity : ComponentActivity() {

    private var webView: WebView? = null
    private var isLoading by mutableStateOf(true)
    private var canGoBack by mutableStateOf(false)
    private var showActionDialog by mutableStateOf(false)
    private var pendingUrl by mutableStateOf("")
    private var pendingTitle by mutableStateOf("")
    private lateinit var authRepository: AuthRepository
    
    companion object {
        private const val EXTRA_URL = "extra_url"
        private const val CINEPIX_BASE_URL = "https://cinepix.top"
        
        fun createIntent(context: Context, url: String = CINEPIX_BASE_URL): Intent {
            return Intent(context, SimpleWebViewActivity::class.java).apply {
                putExtra(EXTRA_URL, url)
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Initialize auth repository
        val database = DownloadDatabase.getDatabase(this)
        authRepository = AuthRepository(database.userDao(), this)

        val url = intent.getStringExtra(EXTRA_URL) ?: CINEPIX_BASE_URL
        
        // Handle back press
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                if (canGoBack && webView?.canGoBack() == true) {
                    webView?.goBack()
                } else {
                    finish()
                }
            }
        })
        
        setContent {
            CinepixTheme {
                val drawerState = rememberDrawerState(DrawerValue.Closed)

                SimpleWebViewScreen(
                    url = url,
                    isLoading = isLoading,
                    drawerState = drawerState,
                    showActionDialog = showActionDialog,
                    pendingUrl = pendingUrl,
                    pendingTitle = pendingTitle,
                    onWebViewCreated = { webView ->
                        this.webView = webView
                        setupWebView(webView)
                    },
                    onLoadingChanged = { loading ->
                        isLoading = loading
                    },
                    onCanGoBackChanged = { canGoBack ->
                        this.canGoBack = canGoBack
                    },
                    onNavigateToDownloads = {
                        startActivity(Intent(this@SimpleWebViewActivity, DownloadActivity::class.java))
                    },
                    onNavigateToSettings = {
                        // Navigate to settings
                    },
                    onNavigateToProfile = {
                        val intent = ProfileActivity.createIntent(this@SimpleWebViewActivity)
                        startActivity(intent)
                    },
                    onNavigateToHome = {
                        webView?.loadUrl(CINEPIX_BASE_URL)
                    },
                    onNavigateToSearch = {
                        webView?.loadUrl("$CINEPIX_BASE_URL/search")
                    },
                    onDismissDialog = {
                        showActionDialog = false
                        pendingUrl = ""
                        pendingTitle = ""
                    },
                    onPlaySelected = { url, title ->
                        showActionDialog = false
                        startVideoPlayer(url, title)
                        pendingUrl = ""
                        pendingTitle = ""
                    },
                    onDownloadSelected = { url, title ->
                        showActionDialog = false
                        startDownload(url, title)
                        pendingUrl = ""
                        pendingTitle = ""
                    }
                )
            }
        }
    }
    
    @SuppressLint("SetJavaScriptEnabled")
    private fun setupWebView(webView: WebView) {
        webView.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            allowFileAccess = true
            allowContentAccess = true
            setSupportZoom(true)
            builtInZoomControls = true
            displayZoomControls = false
            loadWithOverviewMode = true
            useWideViewPort = true
            userAgentString = "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36 CinepixApp/1.0"
            mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
            mediaPlaybackRequiresUserGesture = false
        }

        // Set session cookie if available
        val sessionCookie = authRepository.getSessionCookie()
        if (!sessionCookie.isNullOrEmpty()) {
            val cookieManager = CookieManager.getInstance()
            cookieManager.setAcceptCookie(true)
            cookieManager.setCookie("https://cinepix.top", sessionCookie)
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                cookieManager.flush()
            }
        }
        
        // Add JavaScript interface
        webView.addJavascriptInterface(SimpleJavaScriptInterface(this), "CinepixApp")
        
        webView.webViewClient = SimpleWebViewClient()
        webView.webChromeClient = SimpleWebChromeClient()
    }
    
    private inner class SimpleWebViewClient : WebViewClient() {
        override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
            super.onPageStarted(view, url, favicon)
            isLoading = true
        }
        
        override fun onPageFinished(view: WebView?, url: String?) {
            super.onPageFinished(view, url)
            isLoading = false
            canGoBack = view?.canGoBack() ?: false
            
            // Inject comprehensive JavaScript to handle only specific play buttons
            view?.evaluateJavascript("""
                (function() {
                    console.log('Cinepix video handler initializing...');

                    // Override the playInBuiltPlayer function
                    window.playInBuiltPlayer = function(url, title) {
                        console.log('Playing video:', url, title);
                        CinepixApp.playVideo(url, title || 'Video');
                        return false;
                    };

                    // Function to check if element is specifically a "Play Now" button
                    function isVideoPlayButton(element) {
                        const classList = element.className || '';
                        const onclick = element.getAttribute('onclick') || '';
                        const textContent = element.textContent.toLowerCase().trim();

                        // Only handle blue "Play Now" buttons (btn-primary)
                        return (
                            onclick.includes('playInBuiltPlayer') ||
                            (classList.includes('btn-primary') && (textContent.includes('play') || textContent.includes('play now'))) ||
                            (classList.includes('btn') && classList.includes('btn-primary') && textContent === 'play now')
                        );
                    }

                    // Function to check if element is a download button
                    function isDownloadButton(element) {
                        const classList = element.className || '';
                        const textContent = element.textContent.toLowerCase().trim();
                        const href = element.getAttribute('href') || '';
                        const onclick = element.getAttribute('onclick') || '';
                        const title = element.getAttribute('title') || '';

                        // Check for download indicators - specifically looking for green download buttons
                        return (
                            (classList.includes('btn-success') && textContent.includes('download')) ||
                            classList.includes('download-btn') ||
                            classList.includes('btn-download') ||
                            (textContent === 'download' && classList.includes('btn')) ||
                            (href.includes('download') && classList.includes('btn')) ||
                            (onclick.includes('download') && classList.includes('btn'))
                        );
                    }

                    // Function to setup play button handlers
                    function setupPlayButtons() {
                        // Find only "Play Now" buttons, not download buttons
                        const playButtonSelectors = [
                            'a[onclick*="playInBuiltPlayer"]',
                            'button[onclick*="playInBuiltPlayer"]',
                            '.btn-primary',
                            'button.btn-primary',
                            'a.btn-primary'
                        ];

                        // Find download buttons separately
                        const downloadButtonSelectors = [
                            '.btn-success',
                            'button.btn-success',
                            'a.btn-success',
                            'a[href*="download"]',
                            'button[onclick*="download"]',
                            '.download-btn',
                            '.btn-download',
                            'a[title*="download"]',
                            'button[title*="download"]'
                        ];

                        // Setup Play Now buttons
                        playButtonSelectors.forEach(selector => {
                            document.querySelectorAll(selector).forEach(function(btn) {
                                if (btn.dataset.cinepixPlayHandled) return;
                                btn.dataset.cinepixPlayHandled = 'true';

                                console.log('Found play button:', btn);

                                btn.addEventListener('click', function(e) {
                                    if (isVideoPlayButton(this)) {
                                        console.log('Play Now button clicked:', this);

                                        const onclick = this.getAttribute('onclick');
                                        if (onclick && onclick.includes('playInBuiltPlayer')) {
                                            e.preventDefault();
                                            e.stopPropagation();

                                            const matches = onclick.match(/playInBuiltPlayer\s*\(\s*['"]([^'"]+)['"]\s*,\s*['"]([^'"]*)['"]\s*\)/);
                                            if (matches) {
                                                console.log('Extracted video URL:', matches[1]);
                                                CinepixApp.showActionDialog(matches[1], matches[2] || 'Video');
                                                return false;
                                            }
                                        }

                                        // Check for data attributes
                                        const videoUrl = this.getAttribute('data-video-url') || this.getAttribute('data-src');
                                        if (videoUrl) {
                                            e.preventDefault();
                                            e.stopPropagation();
                                            CinepixApp.showActionDialog(videoUrl, this.textContent || 'Video');
                                            return false;
                                        }
                                    }
                                });
                            });
                        });

                        // Setup Download buttons
                        downloadButtonSelectors.forEach(selector => {
                            document.querySelectorAll(selector).forEach(function(btn) {
                                if (btn.dataset.cinepixDownloadHandled) return;
                                btn.dataset.cinepixDownloadHandled = 'true';

                                console.log('Found download button:', btn);

                                btn.addEventListener('click', function(e) {
                                    if (isDownloadButton(this)) {
                                        console.log('Download button clicked:', this);
                                        e.preventDefault();
                                        e.stopPropagation();

                                        const href = this.getAttribute('href');
                                        if (href) {
                                            // Show action dialog for download links
                                            CinepixApp.showActionDialog(href, this.textContent || 'File');
                                        }
                                        return false;
                                    }
                                });
                            });
                        });

                        console.log('Play buttons setup completed');
                    }

                    // General link handler for any missed download links
                    function setupGeneralLinkHandler() {
                        document.addEventListener('click', function(e) {
                            const target = e.target.closest('a, button');
                            if (!target) return;

                            const href = target.getAttribute('href') || '';
                            const textContent = target.textContent.toLowerCase().trim();
                            const classList = target.className || '';

                            // Check if this looks like a video/download link
                            if (href && (
                                href.includes('.mp4') ||
                                href.includes('.mkv') ||
                                href.includes('.avi') ||
                                href.includes('stream') ||
                                href.includes('video') ||
                                textContent.includes('download') ||
                                classList.includes('download')
                            )) {
                                // If it's clearly a download button, show action dialog
                                if (textContent.includes('download') || classList.includes('download') || classList.includes('btn-success')) {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    console.log('General download link detected:', href);
                                    CinepixApp.showActionDialog(href, textContent || 'File');
                                    return false;
                                }
                            }
                        }, true);
                    }

                    // Special handler for link.php pages
                    function setupLinkPageHandlers() {
                        if (window.location.href.includes('link.php')) {
                            console.log('On link.php page, setting up special handlers');

                            // Look for play buttons on link.php page
                            setTimeout(function() {
                                const playButtons = document.querySelectorAll('a, button');
                                playButtons.forEach(function(btn) {
                                    const text = btn.textContent.toLowerCase();
                                    const onclick = btn.getAttribute('onclick') || '';

                                    if (text.includes('play') || onclick.includes('playInBuiltPlayer')) {
                                        console.log('Found play button on link.php:', btn);
                                        btn.addEventListener('click', function(e) {
                                            if (onclick.includes('playInBuiltPlayer')) {
                                                e.preventDefault();
                                                const matches = onclick.match(/playInBuiltPlayer\s*\(\s*['"]([^'"]+)['"]\s*,\s*['"]([^'"]*)['"]\s*\)/);
                                                if (matches) {
                                                    CinepixApp.showActionDialog(matches[1], matches[2] || 'Video');
                                                }
                                            }
                                        });
                                    }
                                });
                            }, 1000);
                        }
                    }

                    // Initial setup
                    setupPlayButtons();
                    setupLinkPageHandlers();
                    setupGeneralLinkHandler();

                    // Monitor for dynamically added content
                    const observer = new MutationObserver(function(mutations) {
                        let shouldSetup = false;
                        mutations.forEach(function(mutation) {
                            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                                shouldSetup = true;
                            }
                        });
                        if (shouldSetup) {
                            setTimeout(function() {
                                setupPlayButtons();
                                setupLinkPageHandlers();
                            }, 500);
                        }
                    });

                    observer.observe(document.body, {
                        childList: true,
                        subtree: true
                    });

                    // Also setup when DOM is fully loaded
                    if (document.readyState === 'loading') {
                        document.addEventListener('DOMContentLoaded', setupPlayButtons);
                    }

                    console.log('Cinepix video handler initialized successfully');
                })();
            """.trimIndent(), null)
        }
        
        override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
            val url = request?.url?.toString() ?: return false

            // Only handle direct video file URLs, not website navigation
            if (isDirectVideoUrl(url)) {
                startVideoPlayer(url, "Video")
                return true
            }

            // Allow normal website navigation
            return false
        }
    }
    
    private inner class SimpleWebChromeClient : WebChromeClient() {
        override fun onProgressChanged(view: WebView?, newProgress: Int) {
            super.onProgressChanged(view, newProgress)
            // Update progress if needed
        }
    }
    
    private fun isDirectVideoUrl(url: String): Boolean {
        val videoExtensions = listOf(".mp4", ".mkv", ".avi", ".m3u8", ".ts", ".webm", ".mov", ".flv")
        val lowerUrl = url.lowercase()

        // Only handle direct video file URLs, not website pages
        return videoExtensions.any { lowerUrl.endsWith(it) || lowerUrl.contains("$it?") } ||
               (lowerUrl.contains("stream") && (lowerUrl.contains(".m3u8") || lowerUrl.contains(".ts")))
    }
    
    fun startVideoPlayer(url: String, title: String) {
        val intent = SimpleVideoPlayerActivity.createIntent(this, url, title)
        startActivity(intent)
    }

    fun showActionDialog(url: String, title: String) {
        pendingUrl = url
        pendingTitle = title
        showActionDialog = true
    }

    fun startDownload(url: String, filename: String) {
        // Show download options
        val intent = Intent(this, DownloadActivity::class.java).apply {
            putExtra("download_url", url)
            putExtra("filename", filename)
        }
        startActivity(intent)
    }
}

class SimpleJavaScriptInterface(private val activity: SimpleWebViewActivity) {

    @JavascriptInterface
    fun playVideo(url: String, title: String) {
        android.util.Log.d("CinepixApp", "JavaScript called playVideo: $url, $title")
        activity.runOnUiThread {
            if (url.isNotBlank()) {
                // Show toast to confirm video is being played
                android.widget.Toast.makeText(activity, "Playing: ${title.ifBlank { "Video" }}", android.widget.Toast.LENGTH_SHORT).show()
                activity.startVideoPlayer(url, title.ifBlank { "Video" })
            } else {
                android.widget.Toast.makeText(activity, "Invalid video URL", android.widget.Toast.LENGTH_SHORT).show()
            }
        }
    }

    @JavascriptInterface
    fun showToast(message: String) {
        activity.runOnUiThread {
            android.widget.Toast.makeText(activity, message, android.widget.Toast.LENGTH_SHORT).show()
        }
    }

    @JavascriptInterface
    fun logMessage(message: String) {
        android.util.Log.d("CinepixApp", "JS Log: $message")
    }

    @JavascriptInterface
    fun handleDownload(url: String, filename: String) {
        android.util.Log.d("CinepixApp", "Download requested: $url, $filename")
        activity.runOnUiThread {
            activity.showActionDialog(url, filename)
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SimpleWebViewScreen(
    url: String,
    isLoading: Boolean,
    drawerState: DrawerState,
    showActionDialog: Boolean,
    pendingUrl: String,
    pendingTitle: String,
    onWebViewCreated: (WebView) -> Unit,
    onLoadingChanged: (Boolean) -> Unit,
    onCanGoBackChanged: (Boolean) -> Unit,
    onNavigateToDownloads: () -> Unit,
    onNavigateToSettings: () -> Unit,
    onNavigateToHome: () -> Unit,
    onNavigateToSearch: () -> Unit,
    onNavigateToProfile: () -> Unit,
    onDismissDialog: () -> Unit,
    onPlaySelected: (String, String) -> Unit,
    onDownloadSelected: (String, String) -> Unit
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    
    ModalNavigationDrawer(
        drawerState = drawerState,
        drawerContent = {
            ModalDrawerSheet {
                Column(
                    modifier = Modifier
                        .fillMaxHeight()
                        .padding(16.dp)
                ) {
                    Text(
                        text = "Cinepix",
                        style = MaterialTheme.typography.headlineMedium,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )

                    Divider()

                    NavigationDrawerItem(
                        icon = { Icon(Icons.Default.Home, contentDescription = null) },
                        label = { Text("Home") },
                        selected = false,
                        onClick = {
                            scope.launch { drawerState.close() }
                        }
                    )

                    NavigationDrawerItem(
                        icon = { Icon(Icons.Default.Download, contentDescription = null) },
                        label = { Text("Downloads") },
                        selected = false,
                        onClick = {
                            scope.launch { drawerState.close() }
                            onNavigateToDownloads()
                        }
                    )

                    NavigationDrawerItem(
                        icon = { Icon(Icons.Default.Settings, contentDescription = null) },
                        label = { Text("Settings") },
                        selected = false,
                        onClick = {
                            scope.launch { drawerState.close() }
                            onNavigateToSettings()
                        }
                    )

                    NavigationDrawerItem(
                        icon = { Icon(Icons.Default.Info, contentDescription = null) },
                        label = { Text("About") },
                        selected = false,
                        onClick = {
                            scope.launch { drawerState.close() }
                        }
                    )
                }
            }
        }
    ) {
        Scaffold(
            bottomBar = {
                NavigationBar(
                    containerColor = MaterialTheme.colorScheme.surface,
                    contentColor = MaterialTheme.colorScheme.onSurface
                ) {
                    NavigationBarItem(
                        icon = { Icon(Icons.Default.Home, contentDescription = "Home") },
                        label = { Text("Home") },
                        selected = true,
                        onClick = onNavigateToHome
                    )
                    NavigationBarItem(
                        icon = { Icon(Icons.Default.Search, contentDescription = "Search") },
                        label = { Text("Search") },
                        selected = false,
                        onClick = onNavigateToSearch
                    )
                    NavigationBarItem(
                        icon = { Icon(Icons.Default.Download, contentDescription = "Downloads") },
                        label = { Text("Downloads") },
                        selected = false,
                        onClick = onNavigateToDownloads
                    )
                    NavigationBarItem(
                        icon = { Icon(Icons.Default.AccountCircle, contentDescription = "Profile") },
                        label = { Text("Profile") },
                        selected = false,
                        onClick = onNavigateToProfile
                    )
                }
            }
        ) { paddingValues ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
            ) {
                AndroidView(
                    factory = { context ->
                        WebView(context).apply {
                            onWebViewCreated(this)
                            loadUrl(url)
                        }
                    },
                    modifier = Modifier.fillMaxSize()
                )

                if (isLoading) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(48.dp),
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }
            }
        }
    }

    // Action Dialog
    if (showActionDialog) {
        ActionSelectionDialog(
            title = pendingTitle,
            url = pendingUrl,
            onDismiss = onDismissDialog,
            onPlaySelected = { onPlaySelected(pendingUrl, pendingTitle) },
            onDownloadSelected = { onDownloadSelected(pendingUrl, pendingTitle) }
        )
    }
}

@Composable
fun ActionSelectionDialog(
    title: String,
    url: String,
    onDismiss: () -> Unit,
    onPlaySelected: () -> Unit,
    onDownloadSelected: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Choose Action",
                style = MaterialTheme.typography.headlineSmall
            )
        },
        text = {
            Column {
                Text(
                    text = "What would you like to do with:",
                    style = MaterialTheme.typography.bodyMedium
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Bold
                )
            }
        },
        confirmButton = {
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = onPlaySelected,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    )
                ) {
                    Icon(
                        Icons.Default.PlayArrow,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Play")
                }

                Button(
                    onClick = onDownloadSelected,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.secondary
                    )
                ) {
                    Icon(
                        Icons.Default.Download,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Download")
                }
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}
