{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-99:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e3384bca41f271b3200ce0446ec0f087\\transformed\\media3-exoplayer-1.2.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,193,257,328,408,486,580,677", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "126,188,252,323,403,481,575,672,743"}, "to": {"startLines": "149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "13622,13698,13760,13824,13895,13975,14053,14147,14244", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "13693,13755,13819,13890,13970,14048,14142,14239,14310"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b15b518726989b0578dc4af44fa5f9f8\\transformed\\media3-ui-1.2.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,482,672,758,843,921,1007,1095,1170,1234,1327,1418,1491,1558,1624,1694,1803,1913,2020,2093,2176,2252,2325,2428,2530,2594,2659,2712,2770,2818,2879,2949,3017,3083,3153,3217,3276,3340,3405,3471,3523,3583,3657,3731", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,84,77,85,87,74,63,92,90,72,66,65,69,108,109,106,72,82,75,72,102,101,63,64,52,57,47,60,69,67,65,69,63,58,63,64,65,51,59,73,73,52", "endOffsets": "280,477,667,753,838,916,1002,1090,1165,1229,1322,1413,1486,1553,1619,1689,1798,1908,2015,2088,2171,2247,2320,2423,2525,2589,2654,2707,2765,2813,2874,2944,3012,3078,3148,3212,3271,3335,3400,3466,3518,3578,3652,3726,3779"}, "to": {"startLines": "2,11,15,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,532,11635,11721,11806,11884,11970,12058,12133,12197,12290,12381,12454,12521,12587,12657,12766,12876,12983,13056,13139,13215,13288,13391,13493,13557,14315,14368,14426,14474,14535,14605,14673,14739,14809,14873,14932,14996,15061,15127,15179,15239,15313,15387", "endLines": "10,14,18,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175", "endColumns": "17,12,12,85,84,77,85,87,74,63,92,90,72,66,65,69,108,109,106,72,82,75,72,102,101,63,64,52,57,47,60,69,67,65,69,63,58,63,64,65,51,59,73,73,52", "endOffsets": "330,527,717,11716,11801,11879,11965,12053,12128,12192,12285,12376,12449,12516,12582,12652,12761,12871,12978,13051,13134,13210,13283,13386,13488,13552,13617,14363,14421,14469,14530,14600,14668,14734,14804,14868,14927,14991,15056,15122,15174,15234,15308,15382,15435"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\90e8727c8917f86c7d308f0c9abe80a3\\transformed\\material-1.4.0-beta01\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,216,303,402,521,603,667,759,827,887,974,1036,1100,1168,1233,1287,1396,1454,1516,1570,1645,1765,1847,1927,2031,2109,2189,2277,2344,2410,2478,2552,2642,2713,2791,2861,2931,3020,3098,3186,3276,3348,3420,3504,3555,3621,3702,3785,3847,3911,3974,4074,4172,4265,4363", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,86,98,118,81,63,91,67,59,86,61,63,67,64,53,108,57,61,53,74,119,81,79,103,77,79,87,66,65,67,73,89,70,77,69,69,88,77,87,89,71,71,83,50,65,80,82,61,63,62,99,97,92,97,77", "endOffsets": "211,298,397,516,598,662,754,822,882,969,1031,1095,1163,1228,1282,1391,1449,1511,1565,1640,1760,1842,1922,2026,2104,2184,2272,2339,2405,2473,2547,2637,2708,2786,2856,2926,3015,3093,3181,3271,3343,3415,3499,3550,3616,3697,3780,3842,3906,3969,4069,4167,4260,4358,4436"}, "to": {"startLines": "19,55,63,64,65,124,178,179,182,221,222,223,224,225,226,227,228,229,230,231,232,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,280", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "722,4024,4837,4936,5055,11571,15600,15692,15924,20256,20343,20405,20469,20537,20602,20656,20765,20823,20885,20939,21014,21644,21726,21806,21910,21988,22068,22156,22223,22289,22357,22431,22521,22592,22670,22740,22810,22899,22977,23065,23155,23227,23299,23383,23434,23500,23581,23664,23726,23790,23853,23953,24051,24144,24928", "endLines": "22,55,63,64,65,124,178,179,182,221,222,223,224,225,226,227,228,229,230,231,232,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,280", "endColumns": "12,86,98,118,81,63,91,67,59,86,61,63,67,64,53,108,57,61,53,74,119,81,79,103,77,79,87,66,65,67,73,89,70,77,69,69,88,77,87,89,71,71,83,50,65,80,82,61,63,62,99,97,92,97,77", "endOffsets": "878,4106,4931,5050,5132,11630,15687,15755,15979,20338,20400,20464,20532,20597,20651,20760,20818,20880,20934,21009,21129,21721,21801,21905,21983,22063,22151,22218,22284,22352,22426,22516,22587,22665,22735,22805,22894,22972,23060,23150,23222,23294,23378,23429,23495,23576,23659,23721,23785,23848,23948,24046,24139,24237,25001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cc96508f556f069c09e91b3a9267645a\\transformed\\play-services-base-18.0.1\\res\\values-nb\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,450,572,677,829,955,1071,1170,1320,1423,1580,1704,1842,2014,2077,2135", "endColumns": "101,154,121,104,151,125,115,98,149,102,156,123,137,171,62,57,73", "endOffsets": "294,449,571,676,828,954,1070,1169,1319,1422,1579,1703,1841,2013,2076,2134,2208"}, "to": {"startLines": "69,70,71,72,73,74,75,76,78,79,80,81,82,83,84,85,86", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5386,5492,5651,5777,5886,6042,6172,6292,6525,6679,6786,6947,7075,7217,7393,7460,7522", "endColumns": "105,158,125,108,155,129,119,102,153,106,160,127,141,175,66,61,77", "endOffsets": "5487,5646,5772,5881,6037,6167,6287,6390,6674,6781,6942,7070,7212,7388,7455,7517,7595"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2907b06edc976dc86930b7aa57b04408\\transformed\\media3-session-1.2.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,128,201,277,347,426,509,607", "endColumns": "72,72,75,69,78,82,97,103", "endOffsets": "123,196,272,342,421,504,602,706"}, "to": {"startLines": "50,120,233,234,235,236,237,238", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3515,11234,21134,21210,21280,21359,21442,21540", "endColumns": "72,72,75,69,78,82,97,103", "endOffsets": "3583,11302,21205,21275,21354,21437,21535,21639"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f70384b9a1e1b9ce2411b7c78f9fa615\\transformed\\preference-1.2.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,262,340,482,651,730", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "170,257,335,477,646,725,801"}, "to": {"startLines": "87,176,276,281,290,308,309", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "7600,15440,24633,25006,25828,27558,27637", "endColumns": "69,86,77,141,168,78,75", "endOffsets": "7665,15522,24706,25143,25992,27632,27708"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\de0470acc971f743e832aff97e9527de\\transformed\\appcompat-1.6.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,285", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "883,986,1081,1195,1281,1381,1494,1571,1646,1737,1830,1924,2018,2118,2211,2306,2404,2495,2586,2664,2767,2865,2961,3065,3164,3265,3418,25390", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "981,1076,1190,1276,1376,1489,1566,1641,1732,1825,1919,2013,2113,2206,2301,2399,2490,2581,2659,2762,2860,2956,3060,3159,3260,3413,3510,25465"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\62c17cba0a75b790331013fbfad47644\\transformed\\play-services-basement-18.1.0\\res\\values-nb\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "77", "startColumns": "4", "startOffsets": "6395", "endColumns": "129", "endOffsets": "6520"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\677a264eecd9b3484ff2620a344ffab3\\transformed\\material3-1.1.2\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,279,382,491,566,656,764,904,1022,1168,1248,1344,1429,1519,1622,1736,1837,1960,2078,2207,2370,2493,2606,2725,2843,2929,3023,3138,3271,3368,3474,3573,3702,3839,3940,4033,4109,4182,4262,4342,4449,4525,4605,4702,4797,4884,4980,5064,5165,5263,5363,5476,5552,5651", "endColumns": "112,110,102,108,74,89,107,139,117,145,79,95,84,89,102,113,100,122,117,128,162,122,112,118,117,85,93,114,132,96,105,98,128,136,100,92,75,72,79,79,106,75,79,96,94,86,95,83,100,97,99,112,75,98,94", "endOffsets": "163,274,377,486,561,651,759,899,1017,1163,1243,1339,1424,1514,1617,1731,1832,1955,2073,2202,2365,2488,2601,2720,2838,2924,3018,3133,3266,3363,3469,3568,3697,3834,3935,4028,4104,4177,4257,4337,4444,4520,4600,4697,4792,4879,4975,5059,5160,5258,5358,5471,5547,5646,5741"}, "to": {"startLines": "51,52,53,54,68,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,122,177,284,287,289,294,295,296,297,298,299,300,301,302,303,304,305,306,307", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3588,3701,3812,3915,5311,7670,7760,7868,8008,8126,8272,8352,8448,8533,8623,8726,8840,8941,9064,9182,9311,9474,9597,9710,9829,9947,10033,10127,10242,10375,10472,10578,10677,10806,10943,11044,11407,15527,25310,25540,25721,26261,26337,26417,26514,26609,26696,26792,26876,26977,27075,27175,27288,27364,27463", "endColumns": "112,110,102,108,74,89,107,139,117,145,79,95,84,89,102,113,100,122,117,128,162,122,112,118,117,85,93,114,132,96,105,98,128,136,100,92,75,72,79,79,106,75,79,96,94,86,95,83,100,97,99,112,75,98,94", "endOffsets": "3696,3807,3910,4019,5381,7755,7863,8003,8121,8267,8347,8443,8528,8618,8721,8835,8936,9059,9177,9306,9469,9592,9705,9824,9942,10028,10122,10237,10370,10467,10573,10672,10801,10938,11039,11132,11478,15595,25385,25615,25823,26332,26412,26509,26604,26691,26787,26871,26972,27070,27170,27283,27359,27458,27553"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\804c4e5761ff122de1231662730d5f72\\transformed\\ui-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,279,376,476,564,640,728,817,899,963,1027,1107,1189,1259,1336,1403", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "193,274,371,471,559,635,723,812,894,958,1022,1102,1184,1254,1331,1398,1518"}, "to": {"startLines": "66,67,119,121,123,180,181,274,275,277,278,282,283,286,291,292,293", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5137,5230,11137,11307,11483,15760,15836,24462,24551,24711,24775,25148,25228,25470,25997,26074,26141", "endColumns": "92,80,96,99,87,75,87,88,81,63,63,79,81,69,76,66,119", "endOffsets": "5225,5306,11229,11402,11566,15831,15919,24546,24628,24770,24834,25223,25305,25535,26069,26136,26256"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cb0d38356f8a450289b6a883ab8edf14\\transformed\\navigation-ui-2.7.6\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,113", "endOffsets": "156,270"}, "to": {"startLines": "272,273", "startColumns": "4,4", "startOffsets": "24242,24348", "endColumns": "105,113", "endOffsets": "24343,24457"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\35536fe73c76a56f94e622d08fe45089\\transformed\\leanback-1.0.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,506,624,736,833,928,1068,1207,1313,1435,1585,1705,1824,1933,2032,2157,2250,2352,2460,2560,2659,2775,2883,3019,3154,3258,3368,3485,3601,3711,3820,3907,3988,4089,4223,4377", "endColumns": "106,100,97,94,117,111,96,94,139,138,105,121,149,119,118,108,98,124,92,101,107,99,98,115,107,135,134,103,109,116,115,109,108,86,80,100,133,153,88", "endOffsets": "207,308,406,501,619,731,828,923,1063,1202,1308,1430,1580,1700,1819,1928,2027,2152,2245,2347,2455,2555,2654,2770,2878,3014,3149,3253,3363,3480,3596,3706,3815,3902,3983,4084,4218,4372,4461"}, "to": {"startLines": "183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,279", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15984,16091,16192,16290,16385,16503,16615,16712,16807,16947,17086,17192,17314,17464,17584,17703,17812,17911,18036,18129,18231,18339,18439,18538,18654,18762,18898,19033,19137,19247,19364,19480,19590,19699,19786,19867,19968,20102,24839", "endColumns": "106,100,97,94,117,111,96,94,139,138,105,121,149,119,118,108,98,124,92,101,107,99,98,115,107,135,134,103,109,116,115,109,108,86,80,100,133,153,88", "endOffsets": "16086,16187,16285,16380,16498,16610,16707,16802,16942,17081,17187,17309,17459,17579,17698,17807,17906,18031,18124,18226,18334,18434,18533,18649,18757,18893,19028,19132,19242,19359,19475,19585,19694,19781,19862,19963,20097,20251,24923"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\31a00a017e90aa446708533c4ea3d047\\transformed\\core-1.12.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "56,57,58,59,60,61,62,288", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4111,4205,4307,4404,4503,4611,4717,25620", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "4200,4302,4399,4498,4606,4712,4832,25716"}}]}]}