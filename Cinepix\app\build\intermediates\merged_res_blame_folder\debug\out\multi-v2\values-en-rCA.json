{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-99:/values-en-rCA/values-en-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\de0470acc971f743e832aff97e9527de\\transformed\\appcompat-1.6.1\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,131", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,13325", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,13403"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f70384b9a1e1b9ce2411b7c78f9fa615\\transformed\\preference-1.2.1\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,173,260,334,468,637,717", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "168,255,329,463,632,712,788"}, "to": {"startLines": "43,79,123,127,136,154,155", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4175,8016,12655,12948,13763,15493,15573", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "4238,8098,12724,13077,13927,15568,15644"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\35536fe73c76a56f94e622d08fe45089\\transformed\\leanback-1.0.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,411,505,624,736,832,928,1059,1188,1293,1414,1542,1663,1782,1887,1978,2106,2195,2296,2399,2500,2593,2703,2809,2920,3029,3128,3235,3345,3461,3567,3679,3766,3850,3950,4085,4236", "endColumns": "106,100,97,93,118,111,95,95,130,128,104,120,127,120,118,104,90,127,88,100,102,100,92,109,105,110,108,98,106,109,115,105,111,86,83,99,134,150,89", "endOffsets": "207,308,406,500,619,731,827,923,1054,1183,1288,1409,1537,1658,1777,1882,1973,2101,2190,2291,2394,2495,2588,2698,2804,2915,3024,3123,3230,3340,3456,3562,3674,3761,3845,3945,4080,4231,4321"}, "to": {"startLines": "83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8351,8458,8559,8657,8751,8870,8982,9078,9174,9305,9434,9539,9660,9788,9909,10028,10133,10224,10352,10441,10542,10645,10746,10839,10949,11055,11166,11275,11374,11481,11591,11707,11813,11925,12012,12096,12196,12331,12858", "endColumns": "106,100,97,93,118,111,95,95,130,128,104,120,127,120,118,104,90,127,88,100,102,100,92,109,105,110,108,98,106,109,115,105,111,86,83,99,134,150,89", "endOffsets": "8453,8554,8652,8746,8865,8977,9073,9169,9300,9429,9534,9655,9783,9904,10023,10128,10219,10347,10436,10537,10640,10741,10834,10944,11050,11161,11270,11369,11476,11586,11702,11808,11920,12007,12091,12191,12326,12477,12943"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\31a00a017e90aa446708533c4ea3d047\\transformed\\core-1.12.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,657,773", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "146,248,347,446,550,652,768,869"}, "to": {"startLines": "33,34,35,36,37,38,39,134", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3205,3301,3403,3502,3601,3705,3807,13564", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "3296,3398,3497,3596,3700,3802,3918,13660"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\804c4e5761ff122de1231662730d5f72\\transformed\\ui-release\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,373,472,558,640,730,819,903,968,1032,1110,1192,1265,1342,1408", "endColumns": "91,81,93,98,85,81,89,88,83,64,63,77,81,72,76,65,119", "endOffsets": "192,274,368,467,553,635,725,814,898,963,1027,1105,1187,1260,1337,1403,1523"}, "to": {"startLines": "40,41,75,76,78,81,82,121,122,124,125,128,129,132,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3923,4015,7665,7759,7930,8179,8261,12482,12571,12729,12794,13082,13160,13408,13932,14009,14075", "endColumns": "91,81,93,98,85,81,89,88,83,64,63,77,81,72,76,65,119", "endOffsets": "4010,4092,7754,7853,8011,8256,8346,12566,12650,12789,12853,13155,13237,13476,14004,14070,14190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\677a264eecd9b3484ff2620a344ffab3\\transformed\\material3-1.1.2\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,388,498,576,667,776,908,1020,1152,1232,1327,1414,1507,1622,1743,1843,1966,2085,2209,2367,2484,2596,2716,2838,2926,3020,3133,3253,3346,3444,3542,3667,3802,3904,3998,4070,4146,4229,4312,4410,4486,4566,4663,4760,4856,4951,5035,5137,5234,5333,5445,5521,5617", "endColumns": "113,111,106,109,77,90,108,131,111,131,79,94,86,92,114,120,99,122,118,123,157,116,111,119,121,87,93,112,119,92,97,97,124,134,101,93,71,75,82,82,97,75,79,96,96,95,94,83,101,96,98,111,75,95,90", "endOffsets": "164,276,383,493,571,662,771,903,1015,1147,1227,1322,1409,1502,1617,1738,1838,1961,2080,2204,2362,2479,2591,2711,2833,2921,3015,3128,3248,3341,3439,3537,3662,3797,3899,3993,4065,4141,4224,4307,4405,4481,4561,4658,4755,4851,4946,5030,5132,5229,5328,5440,5516,5612,5703"}, "to": {"startLines": "29,30,31,32,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,77,80,130,133,135,140,141,142,143,144,145,146,147,148,149,150,151,152,153", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2762,2876,2988,3095,4097,4243,4334,4443,4575,4687,4819,4899,4994,5081,5174,5289,5410,5510,5633,5752,5876,6034,6151,6263,6383,6505,6593,6687,6800,6920,7013,7111,7209,7334,7469,7571,7858,8103,13242,13481,13665,14195,14271,14351,14448,14545,14641,14736,14820,14922,15019,15118,15230,15306,15402", "endColumns": "113,111,106,109,77,90,108,131,111,131,79,94,86,92,114,120,99,122,118,123,157,116,111,119,121,87,93,112,119,92,97,97,124,134,101,93,71,75,82,82,97,75,79,96,96,95,94,83,101,96,98,111,75,95,90", "endOffsets": "2871,2983,3090,3200,4170,4329,4438,4570,4682,4814,4894,4989,5076,5169,5284,5405,5505,5628,5747,5871,6029,6146,6258,6378,6500,6588,6682,6795,6915,7008,7106,7204,7329,7464,7566,7660,7925,8174,13320,13559,13758,14266,14346,14443,14540,14636,14731,14815,14917,15014,15113,15225,15301,15397,15488"}}]}]}