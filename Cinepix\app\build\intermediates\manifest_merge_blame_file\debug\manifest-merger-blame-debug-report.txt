1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.deshi.cinepix"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:8:5-76
14-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission
15-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:9:5-10:38
16        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
16-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:9:22-78
17        android:maxSdkVersion="28" />
17-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:10:9-35
18    <uses-permission
18-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:11:5-12:38
19        android:name="android.permission.READ_EXTERNAL_STORAGE"
19-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:11:22-77
20        android:maxSdkVersion="32" />
20-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:12:9-35
21    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
21-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:13:5-75
21-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:13:22-72
22    <uses-permission android:name="android.permission.WAKE_LOCK" />
22-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:14:5-68
22-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:14:22-65
23    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
23-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:15:5-77
23-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:15:22-74
24    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
24-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:16:5-92
24-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:16:22-89
25    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
25-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:17:5-77
25-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:17:22-74
26
27    <!-- TV Features -->
28    <uses-feature
28-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:20:5-22:36
29        android:name="android.software.leanback"
29-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:21:9-49
30        android:required="false" />
30-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:22:9-33
31    <uses-feature
31-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:23:5-25:36
32        android:name="android.hardware.touchscreen"
32-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:24:9-52
33        android:required="false" />
33-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:25:9-33
34
35    <!-- Hardware features -->
36    <uses-feature
36-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:28:5-30:36
37        android:name="android.hardware.wifi"
37-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:29:9-45
38        android:required="false" />
38-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:30:9-33
39    <uses-feature
39-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:31:5-33:36
40        android:name="android.hardware.ethernet"
40-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:32:9-49
41        android:required="false" />
41-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:33:9-33
42
43    <!-- Required by older versions of Google Play services to create IID tokens -->
44    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
44-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:26:5-82
44-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:26:22-79
45    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
45-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
45-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:22-76
46    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
46-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
46-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
47    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
47-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
47-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
48    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
48-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
48-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
49    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
49-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
49-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
50
51    <permission
51-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31a00a017e90aa446708533c4ea3d047\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
52        android:name="com.deshi.cinepix.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
52-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31a00a017e90aa446708533c4ea3d047\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
53        android:protectionLevel="signature" />
53-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31a00a017e90aa446708533c4ea3d047\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
54
55    <uses-permission android:name="com.deshi.cinepix.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
55-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31a00a017e90aa446708533c4ea3d047\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
55-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31a00a017e90aa446708533c4ea3d047\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
56
57    <application
57-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:35:5-144:19
58        android:name="com.deshi.cinepix.CinepixApplication"
58-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:36:9-43
59        android:allowBackup="true"
59-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:37:9-35
60        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
60-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31a00a017e90aa446708533c4ea3d047\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
61        android:banner="@drawable/tv_banner"
61-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:48:9-45
62        android:dataExtractionRules="@xml/data_extraction_rules"
62-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:38:9-65
63        android:debuggable="true"
64        android:extractNativeLibs="false"
65        android:fullBackupContent="@xml/backup_rules"
65-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:39:9-54
66        android:hardwareAccelerated="true"
66-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:46:9-43
67        android:icon="@mipmap/ic_launcher"
67-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:40:9-43
68        android:label="@string/app_name"
68-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:41:9-41
69        android:largeHeap="true"
69-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:47:9-33
70        android:roundIcon="@mipmap/ic_launcher_round"
70-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:42:9-54
71        android:supportsRtl="true"
71-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:43:9-35
72        android:theme="@style/Theme.Cinepix"
72-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:44:9-45
73        android:usesCleartextTraffic="true" >
73-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:45:9-44
74        <activity
74-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:50:9-76:20
75            android:name="com.deshi.cinepix.MainActivity"
75-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:51:13-41
76            android:configChanges="orientation|screenSize|keyboardHidden|smallestScreenSize|screenLayout"
76-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:57:13-106
77            android:exported="true"
77-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:52:13-36
78            android:label="@string/app_name"
78-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:53:13-45
79            android:launchMode="singleTop"
79-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:55:13-43
80            android:screenOrientation="unspecified"
80-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:56:13-52
81            android:supportsPictureInPicture="true"
81-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:58:13-52
82            android:theme="@style/Theme.Cinepix" >
82-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:54:13-49
83            <intent-filter>
83-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:59:13-62:29
84                <action android:name="android.intent.action.MAIN" />
84-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:60:17-69
84-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:60:25-66
85
86                <category android:name="android.intent.category.LAUNCHER" />
86-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:61:17-77
86-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:61:27-74
87            </intent-filter>
88            <!-- TV Intent Filter -->
89            <intent-filter>
89-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:64:13-67:29
90                <action android:name="android.intent.action.MAIN" />
90-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:60:17-69
90-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:60:25-66
91
92                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
92-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:66:17-86
92-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:66:27-83
93            </intent-filter>
94            <!-- Handle video URLs -->
95            <intent-filter android:autoVerify="true" >
95-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:69:13-75:29
95-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:69:28-53
96                <action android:name="android.intent.action.VIEW" />
96-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:70:17-69
96-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:70:25-66
97
98                <category android:name="android.intent.category.DEFAULT" />
98-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:71:17-76
98-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:71:27-73
99                <category android:name="android.intent.category.BROWSABLE" />
99-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:72:17-78
99-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:72:27-75
100
101                <data
101-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:73:17-74
102                    android:host="cinepix.top"
102-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:73:45-71
103                    android:scheme="http" />
103-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:73:23-44
104                <data
104-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:73:17-74
105                    android:host="cinepix.top"
105-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:73:45-71
106                    android:scheme="https" />
106-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:73:23-44
107            </intent-filter>
108        </activity>
109
110        <!-- Video Player Activity -->
111        <activity
111-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:79:9-85:46
112            android:name="com.deshi.cinepix.ui.player.SimpleVideoPlayerActivity"
112-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:80:13-64
113            android:configChanges="orientation|screenSize|keyboardHidden|smallestScreenSize|screenLayout"
113-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:84:13-106
114            android:exported="false"
114-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:81:13-37
115            android:launchMode="singleTop"
115-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:85:13-43
116            android:screenOrientation="landscape"
116-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:83:13-50
117            android:theme="@style/Theme.Cinepix.Fullscreen" />
117-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:82:13-60
118
119        <!-- WebView Activity -->
120        <activity
120-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:88:9-92:109
121            android:name="com.deshi.cinepix.ui.webview.SimpleWebViewActivity"
121-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:89:13-61
122            android:configChanges="orientation|screenSize|keyboardHidden|smallestScreenSize|screenLayout"
122-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:92:13-106
123            android:exported="false"
123-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:90:13-37
124            android:theme="@style/Theme.Cinepix" />
124-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:91:13-49
125
126        <!-- Download Activity -->
127        <activity
127-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:95:9-98:52
128            android:name="com.deshi.cinepix.ui.download.DownloadActivity"
128-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:96:13-57
129            android:exported="false"
129-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:97:13-37
130            android:theme="@style/Theme.Cinepix" />
130-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:98:13-49
131
132        <!-- Auth Activity -->
133        <activity
133-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:101:9-104:52
134            android:name="com.deshi.cinepix.ui.auth.AuthActivity"
134-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:102:13-49
135            android:exported="false"
135-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:103:13-37
136            android:theme="@style/Theme.Cinepix" />
136-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:104:13-49
137
138        <!-- Profile Activity -->
139        <activity
139-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:107:9-110:52
140            android:name="com.deshi.cinepix.ui.auth.ProfileActivity"
140-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:108:13-52
141            android:exported="false"
141-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:109:13-37
142            android:theme="@style/Theme.Cinepix" />
142-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:110:13-49
143
144        <!-- TV Main Activity -->
145        <activity
145-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:113:9-122:20
146            android:name="com.deshi.cinepix.ui.tv.TvMainActivity"
146-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:114:13-49
147            android:exported="true"
147-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:115:13-36
148            android:screenOrientation="landscape"
148-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:117:13-50
149            android:theme="@style/Theme.Cinepix.Leanback" >
149-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:116:13-58
150            <intent-filter>
150-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:64:13-67:29
151                <action android:name="android.intent.action.MAIN" />
151-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:60:17-69
151-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:60:25-66
152
153                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
153-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:66:17-86
153-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:66:27-83
154            </intent-filter>
155        </activity>
156
157        <!-- Firebase Messaging Service -->
158        <service
158-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:125:9-131:19
159            android:name="com.deshi.cinepix.services.CinepixFirebaseMessagingService"
159-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:126:13-69
160            android:exported="false" >
160-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:127:13-37
161            <intent-filter>
161-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:128:13-130:29
162                <action android:name="com.google.firebase.MESSAGING_EVENT" />
162-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:129:17-78
162-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:129:25-75
163            </intent-filter>
164        </service>
165
166        <!-- File Provider -->
167        <provider
168            android:name="androidx.core.content.FileProvider"
168-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:135:13-62
169            android:authorities="com.deshi.cinepix.fileprovider"
169-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:136:13-64
170            android:exported="false"
170-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:137:13-37
171            android:grantUriPermissions="true" >
171-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:138:13-47
172            <meta-data
172-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:139:13-141:54
173                android:name="android.support.FILE_PROVIDER_PATHS"
173-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:140:17-67
174                android:resource="@xml/file_paths" />
174-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:141:17-51
175        </provider>
176
177        <service
177-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c036506dfefc398763599fe4c7111b8\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:8:9-14:19
178            android:name="com.google.firebase.components.ComponentDiscoveryService"
178-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c036506dfefc398763599fe4c7111b8\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:9:13-84
179            android:directBootAware="true"
179-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
180            android:exported="false" >
180-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c036506dfefc398763599fe4c7111b8\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:10:13-37
181            <meta-data
181-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c036506dfefc398763599fe4c7111b8\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:11:13-13:85
182                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
182-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c036506dfefc398763599fe4c7111b8\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:12:17-129
183                android:value="com.google.firebase.components.ComponentRegistrar" />
183-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c036506dfefc398763599fe4c7111b8\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:13:17-82
184            <meta-data
184-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3228cc311144672738c2f0d4ffc46c9\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:26:13-28:85
185                android:name="com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar"
185-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3228cc311144672738c2f0d4ffc46c9\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:27:17-129
186                android:value="com.google.firebase.components.ComponentRegistrar" />
186-->[com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3228cc311144672738c2f0d4ffc46c9\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:28:17-82
187            <meta-data
187-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:57:13-59:85
188                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
188-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:58:17-122
189                android:value="com.google.firebase.components.ComponentRegistrar" />
189-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:59:17-82
190            <meta-data
190-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:60:13-62:85
191                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
191-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:61:17-119
192                android:value="com.google.firebase.components.ComponentRegistrar" />
192-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:62:17-82
193            <meta-data
193-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
194                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
194-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
195                android:value="com.google.firebase.components.ComponentRegistrar" />
195-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
196            <meta-data
196-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
197                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
197-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
198                android:value="com.google.firebase.components.ComponentRegistrar" />
198-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
199            <meta-data
199-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
200                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
200-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
201                android:value="com.google.firebase.components.ComponentRegistrar" />
201-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
202            <meta-data
202-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\6338df35648d7063ceb474a555a314a0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
203                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
203-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\6338df35648d7063ceb474a555a314a0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
204                android:value="com.google.firebase.components.ComponentRegistrar" />
204-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\6338df35648d7063ceb474a555a314a0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
205            <meta-data
205-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
206                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
206-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
207                android:value="com.google.firebase.components.ComponentRegistrar" />
207-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
208            <meta-data
208-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\bd650446db21c4b346138f5447b227c0\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
209                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
209-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\bd650446db21c4b346138f5447b227c0\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
210                android:value="com.google.firebase.components.ComponentRegistrar" />
210-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\bd650446db21c4b346138f5447b227c0\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
211        </service>
212
213        <receiver
213-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:29:9-40:20
214            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
214-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:30:13-78
215            android:exported="true"
215-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:31:13-36
216            android:permission="com.google.android.c2dm.permission.SEND" >
216-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:32:13-73
217            <intent-filter>
217-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:33:13-35:29
218                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
218-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:34:17-81
218-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:34:25-78
219            </intent-filter>
220
221            <meta-data
221-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:37:13-39:40
222                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
222-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:38:17-92
223                android:value="true" />
223-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:39:17-37
224        </receiver>
225        <!--
226             FirebaseMessagingService performs security checks at runtime,
227             but set to not exported to explicitly avoid allowing another app to call it.
228        -->
229        <service
229-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:46:9-53:19
230            android:name="com.google.firebase.messaging.FirebaseMessagingService"
230-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:47:13-82
231            android:directBootAware="true"
231-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:48:13-43
232            android:exported="false" >
232-->[com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:49:13-37
233            <intent-filter android:priority="-500" >
233-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:128:13-130:29
234                <action android:name="com.google.firebase.MESSAGING_EVENT" />
234-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:129:17-78
234-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:129:25-75
235            </intent-filter>
236        </service>
237
238        <property
238-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
239            android:name="android.adservices.AD_SERVICES_CONFIG"
239-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
240            android:resource="@xml/ga_ad_services_config" />
240-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
241
242        <provider
242-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
243            android:name="com.google.firebase.provider.FirebaseInitProvider"
243-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
244            android:authorities="com.deshi.cinepix.firebaseinitprovider"
244-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
245            android:directBootAware="true"
245-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
246            android:exported="false"
246-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
247            android:initOrder="100" />
247-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
248
249        <receiver
249-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
250            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
250-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
251            android:enabled="true"
251-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
252            android:exported="false" >
252-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
253        </receiver>
254
255        <service
255-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
256            android:name="com.google.android.gms.measurement.AppMeasurementService"
256-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
257            android:enabled="true"
257-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
258            android:exported="false" />
258-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
259        <service
259-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
260            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
260-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
261            android:enabled="true"
261-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
262            android:exported="false"
262-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
263            android:permission="android.permission.BIND_JOB_SERVICE" />
263-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
264
265        <activity
265-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\85c107345bd020db339f1b0fa8b559fe\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
266            android:name="androidx.compose.ui.tooling.PreviewActivity"
266-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\85c107345bd020db339f1b0fa8b559fe\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
267            android:exported="true" />
267-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\85c107345bd020db339f1b0fa8b559fe\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
268        <activity
268-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\ebe04cf194a845a65a4280d5d1c12288\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
269            android:name="androidx.activity.ComponentActivity"
269-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\ebe04cf194a845a65a4280d5d1c12288\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
270            android:exported="true" />
270-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\ebe04cf194a845a65a4280d5d1c12288\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
271
272        <provider
272-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
273            android:name="androidx.startup.InitializationProvider"
273-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
274            android:authorities="com.deshi.cinepix.androidx-startup"
274-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
275            android:exported="false" >
275-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
276            <meta-data
276-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
277                android:name="androidx.work.WorkManagerInitializer"
277-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
278                android:value="androidx.startup" />
278-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
279            <meta-data
279-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbb45b556602582e98243d90f2d7d909\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
280                android:name="androidx.emoji2.text.EmojiCompatInitializer"
280-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbb45b556602582e98243d90f2d7d909\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
281                android:value="androidx.startup" />
281-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbb45b556602582e98243d90f2d7d909\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
282            <meta-data
282-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7194d0d1f6901b6dd5f8dc0c5bab8c53\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
283                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
283-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7194d0d1f6901b6dd5f8dc0c5bab8c53\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
284                android:value="androidx.startup" />
284-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7194d0d1f6901b6dd5f8dc0c5bab8c53\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
285            <meta-data
285-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
286                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
286-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
287                android:value="androidx.startup" />
287-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
288        </provider>
289
290        <service
290-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
291            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
291-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
292            android:directBootAware="false"
292-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
293            android:enabled="@bool/enable_system_alarm_service_default"
293-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
294            android:exported="false" />
294-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
295        <service
295-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
296            android:name="androidx.work.impl.background.systemjob.SystemJobService"
296-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
297            android:directBootAware="false"
297-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
298            android:enabled="@bool/enable_system_job_service_default"
298-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
299            android:exported="true"
299-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
300            android:permission="android.permission.BIND_JOB_SERVICE" />
300-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
301        <service
301-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
302            android:name="androidx.work.impl.foreground.SystemForegroundService"
302-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
303            android:directBootAware="false"
303-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
304            android:enabled="@bool/enable_system_foreground_service_default"
304-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
305            android:exported="false" />
305-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
306
307        <receiver
307-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
308            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
308-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
309            android:directBootAware="false"
309-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
310            android:enabled="true"
310-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
311            android:exported="false" />
311-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
312        <receiver
312-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
313            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
313-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
314            android:directBootAware="false"
314-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
315            android:enabled="false"
315-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
316            android:exported="false" >
316-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
317            <intent-filter>
317-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
318                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
318-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
318-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
319                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
319-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
319-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
320            </intent-filter>
321        </receiver>
322        <receiver
322-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
323            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
323-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
324            android:directBootAware="false"
324-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
325            android:enabled="false"
325-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
326            android:exported="false" >
326-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
327            <intent-filter>
327-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
328                <action android:name="android.intent.action.BATTERY_OKAY" />
328-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
328-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
329                <action android:name="android.intent.action.BATTERY_LOW" />
329-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
329-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
330            </intent-filter>
331        </receiver>
332        <receiver
332-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
333            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
333-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
334            android:directBootAware="false"
334-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
335            android:enabled="false"
335-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
336            android:exported="false" >
336-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
337            <intent-filter>
337-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
338                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
338-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
338-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
339                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
339-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
339-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
340            </intent-filter>
341        </receiver>
342        <receiver
342-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
343            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
343-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
344            android:directBootAware="false"
344-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
345            android:enabled="false"
345-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
346            android:exported="false" >
346-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
347            <intent-filter>
347-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
348                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
348-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
348-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
349            </intent-filter>
350        </receiver>
351        <receiver
351-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
352            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
352-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
353            android:directBootAware="false"
353-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
354            android:enabled="false"
354-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
355            android:exported="false" >
355-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
356            <intent-filter>
356-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
357                <action android:name="android.intent.action.BOOT_COMPLETED" />
357-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
357-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
358                <action android:name="android.intent.action.TIME_SET" />
358-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
358-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
359                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
359-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
359-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
360            </intent-filter>
361        </receiver>
362        <receiver
362-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
363            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
363-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
364            android:directBootAware="false"
364-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
365            android:enabled="@bool/enable_system_alarm_service_default"
365-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
366            android:exported="false" >
366-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
367            <intent-filter>
367-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
368                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
368-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
368-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
369            </intent-filter>
370        </receiver>
371        <receiver
371-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
372            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
372-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
373            android:directBootAware="false"
373-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
374            android:enabled="true"
374-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
375            android:exported="true"
375-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
376            android:permission="android.permission.DUMP" >
376-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
377            <intent-filter>
377-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
378                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
378-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
378-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
379            </intent-filter>
380        </receiver>
381
382        <uses-library
382-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\43aca4c73a9704d5bd9903c6316396f8\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
383            android:name="android.ext.adservices"
383-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\43aca4c73a9704d5bd9903c6316396f8\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
384            android:required="false" />
384-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\43aca4c73a9704d5bd9903c6316396f8\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
385
386        <activity
386-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cc96508f556f069c09e91b3a9267645a\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
387            android:name="com.google.android.gms.common.api.GoogleApiActivity"
387-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cc96508f556f069c09e91b3a9267645a\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
388            android:exported="false"
388-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cc96508f556f069c09e91b3a9267645a\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
389            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
389-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cc96508f556f069c09e91b3a9267645a\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
390
391        <uses-library
391-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f35fc8775ad80a93919efd2167512d74\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
392            android:name="androidx.window.extensions"
392-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f35fc8775ad80a93919efd2167512d74\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
393            android:required="false" />
393-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f35fc8775ad80a93919efd2167512d74\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
394        <uses-library
394-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f35fc8775ad80a93919efd2167512d74\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
395            android:name="androidx.window.sidecar"
395-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f35fc8775ad80a93919efd2167512d74\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
396            android:required="false" />
396-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f35fc8775ad80a93919efd2167512d74\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
397
398        <meta-data
398-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\62c17cba0a75b790331013fbfad47644\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
399            android:name="com.google.android.gms.version"
399-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\62c17cba0a75b790331013fbfad47644\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
400            android:value="@integer/google_play_services_version" />
400-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\62c17cba0a75b790331013fbfad47644\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
401
402        <service
402-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\71e16755cc979dce3fe5d21e8a73baf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
403            android:name="androidx.room.MultiInstanceInvalidationService"
403-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\71e16755cc979dce3fe5d21e8a73baf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
404            android:directBootAware="true"
404-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\71e16755cc979dce3fe5d21e8a73baf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
405            android:exported="false" />
405-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\71e16755cc979dce3fe5d21e8a73baf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
406        <service
406-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d6677376643b2c66825a15928b8d3851\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
407            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
407-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d6677376643b2c66825a15928b8d3851\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
408            android:exported="false" >
408-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d6677376643b2c66825a15928b8d3851\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
409            <meta-data
409-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d6677376643b2c66825a15928b8d3851\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
410                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
410-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d6677376643b2c66825a15928b8d3851\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
411                android:value="cct" />
411-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d6677376643b2c66825a15928b8d3851\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
412        </service>
413        <service
413-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
414            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
414-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
415            android:exported="false"
415-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
416            android:permission="android.permission.BIND_JOB_SERVICE" >
416-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
417        </service>
418
419        <receiver
419-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
420            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
420-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
421            android:exported="false" />
421-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
422        <receiver
422-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
423            android:name="androidx.profileinstaller.ProfileInstallReceiver"
423-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
424            android:directBootAware="false"
424-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
425            android:enabled="true"
425-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
426            android:exported="true"
426-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
427            android:permission="android.permission.DUMP" >
427-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
428            <intent-filter>
428-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
429                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
429-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
429-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
430            </intent-filter>
431            <intent-filter>
431-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
432                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
432-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
432-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
433            </intent-filter>
434            <intent-filter>
434-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
435                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
435-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
435-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
436            </intent-filter>
437            <intent-filter>
437-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
438                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
438-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
438-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
439            </intent-filter>
440        </receiver>
441    </application>
442
443</manifest>
