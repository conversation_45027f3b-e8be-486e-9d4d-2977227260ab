1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.deshi.cinepix"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:6:5-67
12-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:7:5-79
13-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:8:5-76
14-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:8:22-73
15    <uses-permission
15-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:9:5-10:38
16        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
16-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:9:22-78
17        android:maxSdkVersion="28" />
17-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:10:9-35
18    <uses-permission
18-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:11:5-12:38
19        android:name="android.permission.READ_EXTERNAL_STORAGE"
19-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:11:22-77
20        android:maxSdkVersion="32" />
20-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:12:9-35
21    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
21-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:13:5-75
21-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:13:22-72
22    <uses-permission android:name="android.permission.WAKE_LOCK" />
22-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:14:5-68
22-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:14:22-65
23    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
23-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:15:5-77
23-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:15:22-74
24    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
24-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:16:5-92
24-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:16:22-89
25    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
25-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:17:5-77
25-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:17:22-74
26
27    <!-- TV Features -->
28    <uses-feature
28-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:20:5-22:36
29        android:name="android.software.leanback"
29-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:21:9-49
30        android:required="false" />
30-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:22:9-33
31    <uses-feature
31-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:23:5-25:36
32        android:name="android.hardware.touchscreen"
32-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:24:9-52
33        android:required="false" />
33-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:25:9-33
34
35    <!-- Hardware features -->
36    <uses-feature
36-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:28:5-30:36
37        android:name="android.hardware.wifi"
37-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:29:9-45
38        android:required="false" />
38-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:30:9-33
39    <uses-feature
39-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:31:5-33:36
40        android:name="android.hardware.ethernet"
40-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:32:9-49
41        android:required="false" />
41-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:33:9-33
42
43    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
43-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
43-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
44
45    <permission
45-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
46        android:name="com.deshi.cinepix.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
46-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
47        android:protectionLevel="signature" />
47-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
48
49    <uses-permission android:name="com.deshi.cinepix.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
49-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
49-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
50
51    <application
51-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:35:5-135:19
52        android:name="com.deshi.cinepix.CinepixApplication"
52-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:36:9-43
53        android:allowBackup="true"
53-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:37:9-35
54        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
54-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\f7431c6a202a424a0713952a4987d458\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
55        android:banner="@drawable/tv_banner"
55-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:48:9-45
56        android:dataExtractionRules="@xml/data_extraction_rules"
56-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:38:9-65
57        android:debuggable="true"
58        android:extractNativeLibs="false"
59        android:fullBackupContent="@xml/backup_rules"
59-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:39:9-54
60        android:hardwareAccelerated="true"
60-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:46:9-43
61        android:icon="@mipmap/ic_launcher"
61-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:40:9-43
62        android:label="@string/app_name"
62-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:41:9-41
63        android:largeHeap="true"
63-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:47:9-33
64        android:roundIcon="@mipmap/ic_launcher_round"
64-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:42:9-54
65        android:supportsRtl="true"
65-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:43:9-35
66        android:theme="@style/Theme.Cinepix"
66-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:44:9-45
67        android:usesCleartextTraffic="true" >
67-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:45:9-44
68        <activity
68-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:50:9-76:20
69            android:name="com.deshi.cinepix.MainActivity"
69-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:51:13-41
70            android:configChanges="orientation|screenSize|keyboardHidden|smallestScreenSize|screenLayout"
70-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:57:13-106
71            android:exported="true"
71-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:52:13-36
72            android:label="@string/app_name"
72-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:53:13-45
73            android:launchMode="singleTop"
73-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:55:13-43
74            android:screenOrientation="unspecified"
74-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:56:13-52
75            android:supportsPictureInPicture="true"
75-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:58:13-52
76            android:theme="@style/Theme.Cinepix" >
76-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:54:13-49
77            <intent-filter>
77-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:59:13-62:29
78                <action android:name="android.intent.action.MAIN" />
78-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:60:17-69
78-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:60:25-66
79
80                <category android:name="android.intent.category.LAUNCHER" />
80-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:61:17-77
80-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:61:27-74
81            </intent-filter>
82            <!-- TV Intent Filter -->
83            <intent-filter>
83-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:64:13-67:29
84                <action android:name="android.intent.action.MAIN" />
84-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:60:17-69
84-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:60:25-66
85
86                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
86-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:66:17-86
86-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:66:27-83
87            </intent-filter>
88            <!-- Handle video URLs -->
89            <intent-filter android:autoVerify="true" >
89-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:69:13-75:29
89-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:69:28-53
90                <action android:name="android.intent.action.VIEW" />
90-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:70:17-69
90-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:70:25-66
91
92                <category android:name="android.intent.category.DEFAULT" />
92-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:71:17-76
92-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:71:27-73
93                <category android:name="android.intent.category.BROWSABLE" />
93-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:72:17-78
93-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:72:27-75
94
95                <data
95-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:73:17-74
96                    android:host="cinepix.top"
96-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:73:45-71
97                    android:scheme="http" />
97-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:73:23-44
98                <data
98-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:73:17-74
99                    android:host="cinepix.top"
99-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:73:45-71
100                    android:scheme="https" />
100-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:73:23-44
101            </intent-filter>
102        </activity>
103
104        <!-- Video Player Activity -->
105        <activity
105-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:79:9-85:46
106            android:name="com.deshi.cinepix.ui.player.SimpleVideoPlayerActivity"
106-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:80:13-64
107            android:configChanges="orientation|screenSize|keyboardHidden|smallestScreenSize|screenLayout"
107-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:84:13-106
108            android:exported="false"
108-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:81:13-37
109            android:launchMode="singleTop"
109-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:85:13-43
110            android:screenOrientation="landscape"
110-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:83:13-50
111            android:theme="@style/Theme.Cinepix.Fullscreen" />
111-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:82:13-60
112
113        <!-- WebView Activity -->
114        <activity
114-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:88:9-92:109
115            android:name="com.deshi.cinepix.ui.webview.SimpleWebViewActivity"
115-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:89:13-61
116            android:configChanges="orientation|screenSize|keyboardHidden|smallestScreenSize|screenLayout"
116-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:92:13-106
117            android:exported="false"
117-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:90:13-37
118            android:theme="@style/Theme.Cinepix" />
118-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:91:13-49
119
120        <!-- Download Activity -->
121        <activity
121-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:95:9-98:52
122            android:name="com.deshi.cinepix.ui.download.DownloadActivity"
122-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:96:13-57
123            android:exported="false"
123-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:97:13-37
124            android:theme="@style/Theme.Cinepix" />
124-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:98:13-49
125
126        <!-- Auth Activity -->
127        <activity
127-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:101:9-104:52
128            android:name="com.deshi.cinepix.ui.auth.AuthActivity"
128-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:102:13-49
129            android:exported="false"
129-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:103:13-37
130            android:theme="@style/Theme.Cinepix" />
130-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:104:13-49
131
132        <!-- Profile Activity -->
133        <activity
133-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:107:9-110:52
134            android:name="com.deshi.cinepix.ui.auth.ProfileActivity"
134-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:108:13-52
135            android:exported="false"
135-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:109:13-37
136            android:theme="@style/Theme.Cinepix" />
136-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:110:13-49
137
138        <!-- TV Main Activity -->
139        <activity
139-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:113:9-122:20
140            android:name="com.deshi.cinepix.ui.tv.TvMainActivity"
140-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:114:13-49
141            android:exported="true"
141-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:115:13-36
142            android:screenOrientation="landscape"
142-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:117:13-50
143            android:theme="@style/Theme.Cinepix.Leanback" >
143-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:116:13-58
144            <intent-filter>
144-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:64:13-67:29
145                <action android:name="android.intent.action.MAIN" />
145-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:60:17-69
145-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:60:25-66
146
147                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
147-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:66:17-86
147-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:66:27-83
148            </intent-filter>
149        </activity>
150
151        <!-- File Provider -->
152        <provider
153            android:name="androidx.core.content.FileProvider"
153-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:126:13-62
154            android:authorities="com.deshi.cinepix.fileprovider"
154-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:127:13-64
155            android:exported="false"
155-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:128:13-37
156            android:grantUriPermissions="true" >
156-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:129:13-47
157            <meta-data
157-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:130:13-132:54
158                android:name="android.support.FILE_PROVIDER_PATHS"
158-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:131:17-67
159                android:resource="@xml/file_paths" />
159-->D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:132:17-51
160        </provider>
161
162        <activity
162-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e825e68470ea6220e0337fe9edc2905f\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
163            android:name="androidx.compose.ui.tooling.PreviewActivity"
163-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e825e68470ea6220e0337fe9edc2905f\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
164            android:exported="true" />
164-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e825e68470ea6220e0337fe9edc2905f\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
165        <activity
165-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\927b30f66ced77625c55c2e33ef43ff1\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
166            android:name="androidx.activity.ComponentActivity"
166-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\927b30f66ced77625c55c2e33ef43ff1\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
167            android:exported="true" />
167-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\927b30f66ced77625c55c2e33ef43ff1\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
168
169        <provider
169-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
170            android:name="androidx.startup.InitializationProvider"
170-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
171            android:authorities="com.deshi.cinepix.androidx-startup"
171-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
172            android:exported="false" >
172-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
173            <meta-data
173-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
174                android:name="androidx.work.WorkManagerInitializer"
174-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
175                android:value="androidx.startup" />
175-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
176            <meta-data
176-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\995d1cadead2c5b2445ce5fadf00951a\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
177                android:name="androidx.emoji2.text.EmojiCompatInitializer"
177-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\995d1cadead2c5b2445ce5fadf00951a\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
178                android:value="androidx.startup" />
178-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\995d1cadead2c5b2445ce5fadf00951a\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
179            <meta-data
179-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a50ec885653e6cd09785f808ddd3075\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
180                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
180-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a50ec885653e6cd09785f808ddd3075\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
181                android:value="androidx.startup" />
181-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3a50ec885653e6cd09785f808ddd3075\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
182            <meta-data
182-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
183                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
183-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
184                android:value="androidx.startup" />
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
185        </provider>
186
187        <service
187-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
188            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
188-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
189            android:directBootAware="false"
189-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
190            android:enabled="@bool/enable_system_alarm_service_default"
190-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
191            android:exported="false" />
191-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
192        <service
192-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
193            android:name="androidx.work.impl.background.systemjob.SystemJobService"
193-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
194            android:directBootAware="false"
194-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
195            android:enabled="@bool/enable_system_job_service_default"
195-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
196            android:exported="true"
196-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
197            android:permission="android.permission.BIND_JOB_SERVICE" />
197-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
198        <service
198-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
199            android:name="androidx.work.impl.foreground.SystemForegroundService"
199-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
200            android:directBootAware="false"
200-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
201            android:enabled="@bool/enable_system_foreground_service_default"
201-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
202            android:exported="false" />
202-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
203
204        <receiver
204-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
205            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
205-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
206            android:directBootAware="false"
206-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
207            android:enabled="true"
207-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
208            android:exported="false" />
208-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
209        <receiver
209-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
210            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
210-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
211            android:directBootAware="false"
211-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
212            android:enabled="false"
212-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
213            android:exported="false" >
213-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
214            <intent-filter>
214-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
215                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
215-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
215-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
216                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
216-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
216-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
217            </intent-filter>
218        </receiver>
219        <receiver
219-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
220            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
220-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
221            android:directBootAware="false"
221-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
222            android:enabled="false"
222-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
223            android:exported="false" >
223-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
224            <intent-filter>
224-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
225                <action android:name="android.intent.action.BATTERY_OKAY" />
225-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
225-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
226                <action android:name="android.intent.action.BATTERY_LOW" />
226-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
226-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
227            </intent-filter>
228        </receiver>
229        <receiver
229-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
230            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
230-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
231            android:directBootAware="false"
231-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
232            android:enabled="false"
232-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
233            android:exported="false" >
233-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
234            <intent-filter>
234-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
235                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
235-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
235-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
236                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
236-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
236-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
237            </intent-filter>
238        </receiver>
239        <receiver
239-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
240            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
240-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
241            android:directBootAware="false"
241-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
242            android:enabled="false"
242-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
243            android:exported="false" >
243-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
244            <intent-filter>
244-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
245                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
245-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
245-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
246            </intent-filter>
247        </receiver>
248        <receiver
248-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
249            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
249-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
250            android:directBootAware="false"
250-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
251            android:enabled="false"
251-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
252            android:exported="false" >
252-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
253            <intent-filter>
253-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
254                <action android:name="android.intent.action.BOOT_COMPLETED" />
254-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
254-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
255                <action android:name="android.intent.action.TIME_SET" />
255-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
255-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
256                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
256-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
256-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
257            </intent-filter>
258        </receiver>
259        <receiver
259-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
260            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
260-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
261            android:directBootAware="false"
261-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
262            android:enabled="@bool/enable_system_alarm_service_default"
262-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
263            android:exported="false" >
263-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
264            <intent-filter>
264-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
265                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
265-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
265-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
266            </intent-filter>
267        </receiver>
268        <receiver
268-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
269            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
269-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
270            android:directBootAware="false"
270-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
271            android:enabled="true"
271-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
272            android:exported="true"
272-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
273            android:permission="android.permission.DUMP" >
273-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
274            <intent-filter>
274-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
275                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
275-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
275-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\ed97ae41ff85fbcc4b7c8323471eba14\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
276            </intent-filter>
277        </receiver>
278
279        <uses-library
279-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
280            android:name="androidx.window.extensions"
280-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
281            android:required="false" />
281-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
282        <uses-library
282-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
283            android:name="androidx.window.sidecar"
283-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
284            android:required="false" />
284-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91475f77bc23bdcba8ba5ca544d8935e\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
285
286        <service
286-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\29ad22528f4df7a20b7ea327a6a067ab\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
287            android:name="androidx.room.MultiInstanceInvalidationService"
287-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\29ad22528f4df7a20b7ea327a6a067ab\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
288            android:directBootAware="true"
288-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\29ad22528f4df7a20b7ea327a6a067ab\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
289            android:exported="false" />
289-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\29ad22528f4df7a20b7ea327a6a067ab\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
290
291        <receiver
291-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
292            android:name="androidx.profileinstaller.ProfileInstallReceiver"
292-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
293            android:directBootAware="false"
293-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
294            android:enabled="true"
294-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
295            android:exported="true"
295-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
296            android:permission="android.permission.DUMP" >
296-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
297            <intent-filter>
297-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
298                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
298-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
298-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
299            </intent-filter>
300            <intent-filter>
300-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
301                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
301-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
301-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
302            </intent-filter>
303            <intent-filter>
303-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
304                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
304-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
304-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
305            </intent-filter>
306            <intent-filter>
306-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
307                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
307-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
307-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\69b1ff78d3fbf1109abc6687436ffa27\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
308            </intent-filter>
309        </receiver>
310    </application>
311
312</manifest>
