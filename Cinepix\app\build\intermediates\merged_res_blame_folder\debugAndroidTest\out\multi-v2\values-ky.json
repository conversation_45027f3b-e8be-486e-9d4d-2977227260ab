{"logs": [{"outputFile": "com.deshi.cinepix.test.app-mergeDebugAndroidTestResources-32:/values-ky/values-ky.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\804c4e5761ff122de1231662730d5f72\\transformed\\ui-release\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,392,492,577,659,757,846,931,997,1064,1149,1236,1309,1388,1456", "endColumns": "92,83,109,99,84,81,97,88,84,65,66,84,86,72,78,67,117", "endOffsets": "193,277,387,487,572,654,752,841,926,992,1059,1144,1231,1304,1383,1451,1569"}, "to": {"startLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "836,929,1013,1123,1223,1308,1390,1488,1577,1662,1728,1795,1880,1967,2141,2220,2288", "endColumns": "92,83,109,99,84,81,97,88,84,65,66,84,86,72,78,67,117", "endOffsets": "924,1008,1118,1218,1303,1385,1483,1572,1657,1723,1790,1875,1962,2035,2215,2283,2401"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\31a00a017e90aa446708533c4ea3d047\\transformed\\core-1.12.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,571,675,786", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "150,252,355,462,566,670,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,23", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,205,307,410,517,621,725,2040", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "200,302,405,512,616,720,831,2136"}}]}]}