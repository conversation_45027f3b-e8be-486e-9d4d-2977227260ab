<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- TV Features -->
    <uses-feature
        android:name="android.software.leanback"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />

    <!-- Hardware features -->
    <uses-feature
        android:name="android.hardware.wifi"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.ethernet"
        android:required="false" />

    <application
        android:name=".CinepixApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Cinepix"
        android:usesCleartextTraffic="true"
        android:hardwareAccelerated="true"
        android:largeHeap="true"
        android:banner="@drawable/tv_banner"
        tools:targetApi="31">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.Cinepix"
            android:launchMode="singleTop"
            android:screenOrientation="unspecified"
            android:configChanges="orientation|screenSize|keyboardHidden|smallestScreenSize|screenLayout"
            android:supportsPictureInPicture="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <!-- TV Intent Filter -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>
            <!-- Handle video URLs -->
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="http" android:host="cinepix.top" />
                <data android:scheme="https" android:host="cinepix.top" />
            </intent-filter>
        </activity>

        <!-- Video Player Activity -->
        <activity
            android:name=".ui.player.SimpleVideoPlayerActivity"
            android:exported="false"
            android:theme="@style/Theme.Cinepix.Fullscreen"
            android:screenOrientation="landscape"
            android:configChanges="orientation|screenSize|keyboardHidden|smallestScreenSize|screenLayout"
            android:launchMode="singleTop" />

        <!-- WebView Activity -->
        <activity
            android:name=".ui.webview.SimpleWebViewActivity"
            android:exported="false"
            android:theme="@style/Theme.Cinepix"
            android:configChanges="orientation|screenSize|keyboardHidden|smallestScreenSize|screenLayout" />

        <!-- Download Activity -->
        <activity
            android:name=".ui.download.DownloadActivity"
            android:exported="false"
            android:theme="@style/Theme.Cinepix" />

        <!-- Auth Activity -->
        <activity
            android:name=".ui.auth.AuthActivity"
            android:exported="false"
            android:theme="@style/Theme.Cinepix" />

        <!-- Profile Activity -->
        <activity
            android:name=".ui.auth.ProfileActivity"
            android:exported="false"
            android:theme="@style/Theme.Cinepix" />

        <!-- TV Main Activity -->
        <activity
            android:name=".ui.tv.TvMainActivity"
            android:exported="true"
            android:theme="@style/Theme.Cinepix.Leanback"
            android:screenOrientation="landscape">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- File Provider -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

    </application>

</manifest>