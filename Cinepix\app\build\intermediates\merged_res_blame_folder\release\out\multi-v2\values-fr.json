{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeReleaseResources-87:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,238,321,419,549,634,700,797,880,946,1048,1123,1179,1258,1318,1372,1494,1553,1615,1669,1751,1886,1978,2062,2176,2255,2336,2429,2496,2562,2642,2723,2826,2899,2977,3050,3122,3215,3290,3382,3474,3548,3632,3724,3781,3847,3930,4017,4079,4143,4206,4308,4406,4503,4604", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,97,129,84,65,96,82,65,101,74,55,78,59,53,121,58,61,53,81,134,91,83,113,78,80,92,66,65,79,80,102,72,77,72,71,92,74,91,91,73,83,91,56,65,82,86,61,63,62,101,97,96,100,88", "endOffsets": "233,316,414,544,629,695,792,875,941,1043,1118,1174,1253,1313,1367,1489,1548,1610,1664,1746,1881,1973,2057,2171,2250,2331,2424,2491,2557,2637,2718,2821,2894,2972,3045,3117,3210,3285,3377,3469,3543,3627,3719,3776,3842,3925,4012,4074,4138,4201,4303,4401,4498,4599,4688"}, "to": {"startLines": "19,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "742,4209,5019,5117,5247,9753,13924,14021,14275,18762,18864,18939,18995,19074,19134,19188,19310,19369,19431,19485,19567,20203,20295,20379,20493,20572,20653,20746,20813,20879,20959,21040,21143,21216,21294,21367,21439,21532,21607,21699,21791,21865,21949,22041,22098,22164,22247,22334,22396,22460,22523,22625,22723,22820,23644", "endLines": "22,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "endColumns": "12,82,97,129,84,65,96,82,65,101,74,55,78,59,53,121,58,61,53,81,134,91,83,113,78,80,92,66,65,79,80,102,72,77,72,71,92,74,91,91,73,83,91,56,65,82,86,61,63,62,101,97,96,100,88", "endOffsets": "920,4287,5112,5242,5327,9814,14016,14099,14336,18859,18934,18990,19069,19129,19183,19305,19364,19426,19480,19562,19697,20290,20374,20488,20567,20648,20741,20808,20874,20954,21035,21138,21211,21289,21362,21434,21527,21602,21694,21786,21860,21944,22036,22093,22159,22242,22329,22391,22455,22518,22620,22718,22815,22916,23728"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "56,57,58,59,60,61,62,270", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4292,4390,4492,4591,4693,4797,4901,24374", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "4385,4487,4586,4688,4792,4896,5014,24470"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,508,631,746,846,940,1072,1200,1306,1431,1608,1736,1860,1967,2058,2191,2283,2393,2507,2623,2724,2845,2971,3106,3237,3353,3475,3588,3714,3818,3935,4022,4110,4218,4362,4526", "endColumns": "106,100,98,95,122,114,99,93,131,127,105,124,176,127,123,106,90,132,91,109,113,115,100,120,125,134,130,115,121,112,125,103,116,86,87,107,143,163,97", "endOffsets": "207,308,407,503,626,741,841,935,1067,1195,1301,1426,1603,1731,1855,1962,2053,2186,2278,2388,2502,2618,2719,2840,2966,3101,3232,3348,3470,3583,3709,3813,3930,4017,4105,4213,4357,4521,4619"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14341,14448,14549,14648,14744,14867,14982,15082,15176,15308,15436,15542,15667,15844,15972,16096,16203,16294,16427,16519,16629,16743,16859,16960,17081,17207,17342,17473,17589,17711,17824,17950,18054,18171,18258,18346,18454,18598,23546", "endColumns": "106,100,98,95,122,114,99,93,131,127,105,124,176,127,123,106,90,132,91,109,113,115,100,120,125,134,130,115,121,112,125,103,116,86,87,107,143,163,97", "endOffsets": "14443,14544,14643,14739,14862,14977,15077,15171,15303,15431,15537,15662,15839,15967,16091,16198,16289,16422,16514,16624,16738,16854,16955,17076,17202,17337,17468,17584,17706,17819,17945,18049,18166,18253,18341,18449,18593,18757,23639"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,285,403,520,595,685,794,934,1049,1192,1272,1369,1466,1563,1680,1803,1905,2051,2193,2319,2507,2629,2743,2863,2993,3091,3192,3312,3433,3531,3634,3735,3875,4023,4128,4232,4315,4392,4479,4562,4665,4741,4822,4920,5028,5122,5218,5302,5414,5511,5609,5737,5813,5919", "endColumns": "115,113,117,116,74,89,108,139,114,142,79,96,96,96,116,122,101,145,141,125,187,121,113,119,129,97,100,119,120,97,102,100,139,147,104,103,82,76,86,82,102,75,80,97,107,93,95,83,111,96,97,127,75,105,93", "endOffsets": "166,280,398,515,590,680,789,929,1044,1187,1267,1364,1461,1558,1675,1798,1900,2046,2188,2314,2502,2624,2738,2858,2988,3086,3187,3307,3428,3526,3629,3730,3870,4018,4123,4227,4310,4387,4474,4557,4660,4736,4817,4915,5023,5117,5213,5297,5409,5506,5604,5732,5808,5914,6008"}, "to": {"startLines": "51,52,53,54,68,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,104,159,266,269,271,276,277,278,279,280,281,282,283,284,285,286,287,288,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3744,3860,3974,4092,5519,5664,5754,5863,6003,6118,6261,6341,6438,6535,6632,6749,6872,6974,7120,7262,7388,7576,7698,7812,7932,8062,8160,8261,8381,8502,8600,8703,8804,8944,9092,9197,9583,13847,24041,24291,24475,25016,25092,25173,25271,25379,25473,25569,25653,25765,25862,25960,26088,26164,26270", "endColumns": "115,113,117,116,74,89,108,139,114,142,79,96,96,96,116,122,101,145,141,125,187,121,113,119,129,97,100,119,120,97,102,100,139,147,104,103,82,76,86,82,102,75,80,97,107,93,95,83,111,96,97,127,75,105,93", "endOffsets": "3855,3969,4087,4204,5589,5749,5858,5998,6113,6256,6336,6433,6530,6627,6744,6867,6969,7115,7257,7383,7571,7693,7807,7927,8057,8155,8256,8376,8497,8595,8698,8799,8939,9087,9192,9296,9661,13919,24123,24369,24573,25087,25168,25266,25374,25468,25564,25648,25760,25857,25955,26083,26159,26265,26359"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,392,492,579,658,750,842,929,1000,1068,1149,1234,1310,1388,1457", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "199,287,387,487,574,653,745,837,924,995,1063,1144,1229,1305,1383,1452,1574"}, "to": {"startLines": "66,67,101,103,105,162,163,256,257,259,260,264,265,268,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5332,5431,9301,9483,9666,14104,14183,23150,23242,23407,23478,23875,23956,24215,24747,24825,24894", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "5426,5514,9396,9578,9748,14178,14270,23237,23324,23473,23541,23951,24036,24286,24820,24889,25011"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,291,483,692,781,872,951,1049,1146,1225,1291,1397,1504,1569,1635,1699,1771,1891,2014,2136,2211,2299,2372,2452,2543,2636,2702,2766,2819,2879,2927,2988,3059,3130,3197,3275,3340,3399,3465,3530,3596,3648,3708,3782,3856", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,90,78,97,96,78,65,105,106,64,65,63,71,119,122,121,74,87,72,79,90,92,65,63,52,59,47,60,70,70,66,77,64,58,65,64,65,51,59,73,73,53", "endOffsets": "286,478,687,776,867,946,1044,1141,1220,1286,1392,1499,1564,1630,1694,1766,1886,2009,2131,2206,2294,2367,2447,2538,2631,2697,2761,2814,2874,2922,2983,3054,3125,3192,3270,3335,3394,3460,3525,3591,3643,3703,3777,3851,3905"}, "to": {"startLines": "2,11,15,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,341,533,9819,9908,9999,10078,10176,10273,10352,10418,10524,10631,10696,10762,10826,10898,11018,11141,11263,11338,11426,11499,11579,11670,11763,11829,12606,12659,12719,12767,12828,12899,12970,13037,13115,13180,13239,13305,13370,13436,13488,13548,13622,13696", "endLines": "10,14,18,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "endColumns": "17,12,12,88,90,78,97,96,78,65,105,106,64,65,63,71,119,122,121,74,87,72,79,90,92,65,63,52,59,47,60,70,70,66,77,64,58,65,64,65,51,59,73,73,53", "endOffsets": "336,528,737,9903,9994,10073,10171,10268,10347,10413,10519,10626,10691,10757,10821,10893,11013,11136,11258,11333,11421,11494,11574,11665,11758,11824,11888,12654,12714,12762,12823,12894,12965,13032,13110,13175,13234,13300,13365,13431,13483,13543,13617,13691,13745"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,263,335,418,495,592,685", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "124,189,258,330,413,490,587,680,763"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11893,11967,12032,12101,12173,12256,12333,12430,12523", "endColumns": "73,64,68,71,82,76,96,92,82", "endOffsets": "11962,12027,12096,12168,12251,12328,12425,12518,12601"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,158", "endColumns": "102,125", "endOffsets": "153,279"}, "to": {"startLines": "254,255", "startColumns": "4,4", "startOffsets": "22921,23024", "endColumns": "102,125", "endOffsets": "23019,23145"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,913,1004,1097,1195,1290,1390,1483,1576,1671,1762,1853,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,326,436,518,624,754,832,908,999,1092,1190,1285,1385,1478,1571,1666,1757,1848,1934,2044,2155,2258,2369,2477,2584,2743,2842,2929"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "925,1036,1151,1261,1343,1449,1579,1657,1733,1824,1917,2015,2110,2210,2303,2396,2491,2582,2673,2759,2869,2980,3083,3194,3302,3409,3568,24128", "endColumns": "110,114,109,81,105,129,77,75,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "1031,1146,1256,1338,1444,1574,1652,1728,1819,1912,2010,2105,2205,2298,2391,2486,2577,2668,2754,2864,2975,3078,3189,3297,3404,3563,3662,24210"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,132,214,282,351,436,513,611", "endColumns": "76,81,67,68,84,76,97,103", "endOffsets": "127,209,277,346,431,508,606,710"}, "to": {"startLines": "50,102,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3667,9401,19702,19770,19839,19924,20001,20099", "endColumns": "76,81,67,68,84,76,97,103", "endOffsets": "3739,9478,19765,19834,19919,19996,20094,20198"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,350,492,661,747", "endColumns": "69,96,77,141,168,85,79", "endOffsets": "170,267,345,487,656,742,822"}, "to": {"startLines": "69,158,258,263,272,290,291", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5594,13750,23329,23733,24578,26364,26450", "endColumns": "69,96,77,141,168,85,79", "endOffsets": "5659,13842,23402,23870,24742,26445,26525"}}]}]}