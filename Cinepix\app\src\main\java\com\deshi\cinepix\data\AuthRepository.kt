package com.deshi.cinepix.data

import android.content.Context
import android.content.SharedPreferences
import kotlinx.coroutines.flow.Flow
import java.security.MessageDigest
import java.util.*
import com.deshi.cinepix.network.CinepixApiService

class AuthRepository(
    private val userDao: UserDao,
    private val context: Context
) {
    private val prefs: SharedPreferences = context.getSharedPreferences("auth_prefs", Context.MODE_PRIVATE)
    private val apiService = CinepixApiService()
    
    companion object {
        private const val KEY_IS_LOGGED_IN = "is_logged_in"
        private const val KEY_USER_ID = "user_id"
        private const val KEY_AUTH_TOKEN = "auth_token"
        private const val KEY_SESSION_COOKIE = "session_cookie"
    }
    
    fun getCurrentUser(): Flow<User?> = userDao.getCurrentUserFlow()
    
    suspend fun login(email: String, password: String): AuthResponse {
        return try {
            // Try to login via Cinepix API
            val apiResponse = apiService.login(email, password)

            if (apiResponse.success) {
                // Create or update user in local database
                val userId = UUID.randomUUID().toString()
                val user = User(
                    id = userId,
                    username = apiResponse.userInfo?.get("username") ?: email.substringBefore("@"),
                    email = email,
                    passwordHash = hashPassword(password), // Store hashed password locally
                    fullName = apiResponse.userInfo?.get("name"),
                    isLoggedIn = true
                )

                // Logout all other users first
                userDao.logoutAllUsers()

                // Insert/update user
                userDao.insertUser(user)

                // Save auth state including session cookie
                saveAuthState(userId, generateToken(), apiResponse.sessionCookie)

                AuthResponse(true, "Login successful", user)
            } else {
                AuthResponse(false, apiResponse.message)
            }
        } catch (e: Exception) {
            AuthResponse(false, "Login failed: ${e.message}")
        }
    }
    
    suspend fun register(request: RegisterRequest): AuthResponse {
        return try {
            // Validate email format
            if (!isValidEmail(request.email)) {
                return AuthResponse(false, "Invalid email format")
            }

            // Validate password strength
            if (!isValidPassword(request.password)) {
                return AuthResponse(false, "Password must be at least 6 characters long")
            }

            // Try to register via Cinepix API
            val apiResponse = apiService.register(
                username = request.username,
                email = request.email,
                password = request.password,
                fullName = request.fullName
            )

            if (apiResponse.success) {
                // Registration successful, but don't auto-login
                // User needs to login after registration
                AuthResponse(true, "Registration successful. Please login with your credentials.")
            } else {
                AuthResponse(false, apiResponse.message)
            }
        } catch (e: Exception) {
            AuthResponse(false, "Registration failed: ${e.message}")
        }
    }
    
    suspend fun logout(): AuthResponse {
        return try {
            userDao.logoutAllUsers()
            clearAuthState()
            AuthResponse(true, "Logout successful")
        } catch (e: Exception) {
            AuthResponse(false, "Logout failed: ${e.message}")
        }
    }
    
    suspend fun isLoggedIn(): Boolean {
        val isLoggedInPref = prefs.getBoolean(KEY_IS_LOGGED_IN, false)
        val currentUser = userDao.getCurrentUser()
        return isLoggedInPref && currentUser != null
    }
    
    suspend fun getCurrentUserId(): String? {
        return if (isLoggedIn()) {
            prefs.getString(KEY_USER_ID, null)
        } else null
    }
    
    private fun saveAuthState(userId: String, token: String, sessionCookie: String? = null) {
        prefs.edit()
            .putBoolean(KEY_IS_LOGGED_IN, true)
            .putString(KEY_USER_ID, userId)
            .putString(KEY_AUTH_TOKEN, token)
            .putString(KEY_SESSION_COOKIE, sessionCookie)
            .apply()
    }
    
    private fun clearAuthState() {
        prefs.edit()
            .putBoolean(KEY_IS_LOGGED_IN, false)
            .remove(KEY_USER_ID)
            .remove(KEY_AUTH_TOKEN)
            .remove(KEY_SESSION_COOKIE)
            .apply()
    }

    fun getSessionCookie(): String? {
        return prefs.getString(KEY_SESSION_COOKIE, null)
    }
    
    private fun hashPassword(password: String): String {
        val digest = MessageDigest.getInstance("SHA-256")
        val hash = digest.digest(password.toByteArray())
        return hash.fold("") { str, it -> str + "%02x".format(it) }
    }
    
    private fun generateToken(): String {
        return UUID.randomUUID().toString()
    }
    
    private fun isValidEmail(email: String): Boolean {
        return android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }
    
    private fun isValidPassword(password: String): Boolean {
        return password.length >= 6
    }
}
