{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeReleaseResources-87:/values-ldrtl-v17/values-ldrtl-v17.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-ldrtl-v17\\values-ldrtl-v17.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,178", "endColumns": "77,44,46", "endOffsets": "128,173,220"}, "to": {"startLines": "2,5,6", "startColumns": "4,4,4", "startOffsets": "55,410,455", "endColumns": "77,44,46", "endOffsets": "128,450,497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-ldrtl-v17\\values-ldrtl-v17.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,191", "endColumns": "135,140", "endOffsets": "186,327"}, "to": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "133,269", "endColumns": "135,140", "endOffsets": "264,405"}}]}]}