{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-91:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "919,1030,1137,1247,1334,1440,1570,1655,1735,1826,1919,2017,2112,2212,2305,2398,2493,2584,2675,2761,2871,2982,3085,3196,3304,3411,3570,24369", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "1025,1132,1242,1329,1435,1565,1650,1730,1821,1914,2012,2107,2207,2300,2393,2488,2579,2670,2756,2866,2977,3080,3191,3299,3406,3565,3664,24451"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,499,686,775,866,945,1043,1140,1219,1285,1382,1479,1544,1607,1671,1743,1864,1990,2115,2190,2278,2351,2431,2530,2631,2697,2761,2814,2872,2920,2981,3048,3125,3192,3264,3322,3381,3447,3512,3578,3630,3695,3774,3853", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,90,78,97,96,78,65,96,96,64,62,63,71,120,125,124,74,87,72,79,98,100,65,63,52,57,47,60,66,76,66,71,57,58,65,64,65,51,64,78,78,53", "endOffsets": "280,494,681,770,861,940,1038,1135,1214,1280,1377,1474,1539,1602,1666,1738,1859,1985,2110,2185,2273,2346,2426,2525,2626,2692,2756,2809,2867,2915,2976,3043,3120,3187,3259,3317,3376,3442,3507,3573,3625,3690,3769,3848,3902"}, "to": {"startLines": "2,11,15,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,549,9921,10010,10101,10180,10278,10375,10454,10520,10617,10714,10779,10842,10906,10978,11099,11225,11350,11425,11513,11586,11666,11765,11866,11932,12726,12779,12837,12885,12946,13013,13090,13157,13229,13287,13346,13412,13477,13543,13595,13660,13739,13818", "endLines": "10,14,18,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "endColumns": "17,12,12,88,90,78,97,96,78,65,96,96,64,62,63,71,120,125,124,74,87,72,79,98,100,65,63,52,57,47,60,66,76,66,71,57,58,65,64,65,51,64,78,78,53", "endOffsets": "330,544,731,10005,10096,10175,10273,10370,10449,10515,10612,10709,10774,10837,10901,10973,11094,11220,11345,11420,11508,11581,11661,11760,11861,11927,11991,12774,12832,12880,12941,13008,13085,13152,13224,13282,13341,13407,13472,13538,13590,13655,13734,13813,13867"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,198,339,457,601,676,766,875,1013,1128,1283,1363,1460,1557,1654,1771,1902,2004,2150,2292,2424,2622,2747,2861,2981,3111,3209,3310,3430,3554,3652,3750,3851,3991,4139,4244,4348,4422,4499,4585,4667,4770,4846,4927,5020,5127,5216,5316,5400,5512,5609,5713,5831,5907,6013", "endColumns": "142,140,117,143,74,89,108,137,114,154,79,96,96,96,116,130,101,145,141,131,197,124,113,119,129,97,100,119,123,97,97,100,139,147,104,103,73,76,85,81,102,75,80,92,106,88,99,83,111,96,103,117,75,105,92", "endOffsets": "193,334,452,596,671,761,870,1008,1123,1278,1358,1455,1552,1649,1766,1897,1999,2145,2287,2419,2617,2742,2856,2976,3106,3204,3305,3425,3549,3647,3745,3846,3986,4134,4239,4343,4417,4494,4580,4662,4765,4841,4922,5015,5122,5211,5311,5395,5507,5604,5708,5826,5902,6008,6101"}, "to": {"startLines": "51,52,53,54,68,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,104,159,266,269,271,276,277,278,279,280,281,282,283,284,285,286,287,288,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3746,3889,4030,4148,5591,5736,5826,5935,6073,6188,6343,6423,6520,6617,6714,6831,6962,7064,7210,7352,7484,7682,7807,7921,8041,8171,8269,8370,8490,8614,8712,8810,8911,9051,9199,9304,9694,13969,24283,24532,24715,25257,25333,25414,25507,25614,25703,25803,25887,25999,26096,26200,26318,26394,26500", "endColumns": "142,140,117,143,74,89,108,137,114,154,79,96,96,96,116,130,101,145,141,131,197,124,113,119,129,97,100,119,123,97,97,100,139,147,104,103,73,76,85,81,102,75,80,92,106,88,99,83,111,96,103,117,75,105,92", "endOffsets": "3884,4025,4143,4287,5661,5821,5930,6068,6183,6338,6418,6515,6612,6709,6826,6957,7059,7205,7347,7479,7677,7802,7916,8036,8166,8264,8365,8485,8609,8707,8805,8906,9046,9194,9299,9403,9763,14041,24364,24609,24813,25328,25409,25502,25609,25698,25798,25882,25994,26091,26195,26313,26389,26495,26588"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,390,496,583,663,757,849,936,1007,1075,1156,1241,1317,1396,1465", "endColumns": "98,87,97,105,86,79,93,91,86,70,67,80,84,75,78,68,121", "endOffsets": "199,287,385,491,578,658,752,844,931,1002,1070,1151,1236,1312,1391,1460,1582"}, "to": {"startLines": "66,67,101,103,105,162,163,256,257,259,260,264,265,268,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5404,5503,9408,9588,9768,14223,14303,23394,23486,23650,23721,24117,24198,24456,24987,25066,25135", "endColumns": "98,87,97,105,86,79,93,91,86,70,67,80,84,75,78,68,121", "endOffsets": "5498,5586,9501,9689,9850,14298,14392,23481,23568,23716,23784,24193,24278,24527,25061,25130,25252"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,272,344,427,503,600,693", "endColumns": "73,64,77,71,82,75,96,92,91", "endOffsets": "124,189,267,339,422,498,595,688,780"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11996,12070,12135,12213,12285,12368,12444,12541,12634", "endColumns": "73,64,77,71,82,75,96,92,91", "endOffsets": "12065,12130,12208,12280,12363,12439,12536,12629,12721"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,508,631,746,845,939,1071,1199,1305,1429,1607,1743,1875,1982,2073,2209,2298,2408,2517,2626,2727,2846,2972,3099,3222,3339,3465,3600,3741,3867,3999,4086,4174,4282,4426,4590", "endColumns": "106,100,98,95,122,114,98,93,131,127,105,123,177,135,131,106,90,135,88,109,108,108,100,118,125,126,122,116,125,134,140,125,131,86,87,107,143,163,95", "endOffsets": "207,308,407,503,626,741,840,934,1066,1194,1300,1424,1602,1738,1870,1977,2068,2204,2293,2403,2512,2621,2722,2841,2967,3094,3217,3334,3460,3595,3736,3862,3994,4081,4169,4277,4421,4585,4681"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14459,14566,14667,14766,14862,14985,15100,15199,15293,15425,15553,15659,15783,15961,16097,16229,16336,16427,16563,16652,16762,16871,16980,17081,17200,17326,17453,17576,17693,17819,17954,18095,18221,18353,18440,18528,18636,18780,23789", "endColumns": "106,100,98,95,122,114,98,93,131,127,105,123,177,135,131,106,90,135,88,109,108,108,100,118,125,126,122,116,125,134,140,125,131,86,87,107,143,163,95", "endOffsets": "14561,14662,14761,14857,14980,15095,15194,15288,15420,15548,15654,15778,15956,16092,16224,16331,16422,16558,16647,16757,16866,16975,17076,17195,17321,17448,17571,17688,17814,17949,18090,18216,18348,18435,18523,18631,18775,18939,23880"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,168", "endColumns": "112,125", "endOffsets": "163,289"}, "to": {"startLines": "254,255", "startColumns": "4,4", "startOffsets": "23155,23268", "endColumns": "112,125", "endOffsets": "23263,23389"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,132,214,282,348,441,535,639", "endColumns": "76,81,67,65,92,93,103,109", "endOffsets": "127,209,277,343,436,530,634,744"}, "to": {"startLines": "50,102,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3669,9506,19870,19938,20004,20097,20191,20295", "endColumns": "76,81,67,65,92,93,103,109", "endOffsets": "3741,9583,19933,19999,20092,20186,20290,20400"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,238,320,420,542,627,693,790,870,932,1024,1098,1159,1238,1302,1356,1472,1531,1593,1647,1729,1858,1950,2034,2148,2227,2308,2401,2468,2534,2613,2694,2785,2857,2935,3010,3082,3179,3256,3354,3452,3530,3611,3711,3768,3834,3917,4004,4066,4130,4193,4295,4402,4499,4608", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,81,99,121,84,65,96,79,61,91,73,60,78,63,53,115,58,61,53,81,128,91,83,113,78,80,92,66,65,78,80,90,71,77,74,71,96,76,97,97,77,80,99,56,65,82,86,61,63,62,101,106,96,108,88", "endOffsets": "233,315,415,537,622,688,785,865,927,1019,1093,1154,1233,1297,1351,1467,1526,1588,1642,1724,1853,1945,2029,2143,2222,2303,2396,2463,2529,2608,2689,2780,2852,2930,3005,3077,3174,3251,3349,3447,3525,3606,3706,3763,3829,3912,3999,4061,4125,4188,4290,4397,4494,4603,4692"}, "to": {"startLines": "19,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "736,4292,5097,5197,5319,9855,14046,14143,14397,18944,19036,19110,19171,19250,19314,19368,19484,19543,19605,19659,19741,20405,20497,20581,20695,20774,20855,20948,21015,21081,21160,21241,21332,21404,21482,21557,21629,21726,21803,21901,21999,22077,22158,22258,22315,22381,22464,22551,22613,22677,22740,22842,22949,23046,23885", "endLines": "22,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "endColumns": "12,81,99,121,84,65,96,79,61,91,73,60,78,63,53,115,58,61,53,81,128,91,83,113,78,80,92,66,65,78,80,90,71,77,74,71,96,76,97,97,77,80,99,56,65,82,86,61,63,62,101,106,96,108,88", "endOffsets": "914,4369,5192,5314,5399,9916,14138,14218,14454,19031,19105,19166,19245,19309,19363,19479,19538,19600,19654,19736,19865,20492,20576,20690,20769,20850,20943,21010,21076,21155,21236,21327,21399,21477,21552,21624,21721,21798,21896,21994,22072,22153,22253,22310,22376,22459,22546,22608,22672,22735,22837,22944,23041,23150,23969"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "56,57,58,59,60,61,62,270", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4374,4472,4574,4673,4775,4879,4983,24614", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "4467,4569,4668,4770,4874,4978,5092,24710"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,349,492,661,747", "endColumns": "69,96,76,142,168,85,79", "endOffsets": "170,267,344,487,656,742,822"}, "to": {"startLines": "69,158,258,263,272,290,291", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5666,13872,23573,23974,24818,26593,26679", "endColumns": "69,96,76,142,168,85,79", "endOffsets": "5731,13964,23645,24112,24982,26674,26754"}}]}]}