{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-91:/values-pl/values-pl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,408,503,628,741,838,933,1052,1169,1279,1405,1576,1697,1816,1923,2018,2144,2236,2343,2448,2552,2654,2773,2905,3026,3145,3249,3358,3466,3582,3684,3794,3881,3965,4070,4206,4363", "endColumns": "106,100,94,94,124,112,96,94,118,116,109,125,170,120,118,106,94,125,91,106,104,103,101,118,131,120,118,103,108,107,115,101,109,86,83,104,135,156,93", "endOffsets": "207,308,403,498,623,736,833,928,1047,1164,1274,1400,1571,1692,1811,1918,2013,2139,2231,2338,2443,2547,2649,2768,2900,3021,3140,3244,3353,3461,3577,3679,3789,3876,3960,4065,4201,4358,4452"}, "to": {"startLines": "171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14403,14510,14611,14706,14801,14926,15039,15136,15231,15350,15467,15577,15703,15874,15995,16114,16221,16316,16442,16534,16641,16746,16850,16952,17071,17203,17324,17443,17547,17656,17764,17880,17982,18092,18179,18263,18368,18504,23392", "endColumns": "106,100,94,94,124,112,96,94,118,116,109,125,170,120,118,106,94,125,91,106,104,103,101,118,131,120,118,103,108,107,115,101,109,86,83,104,135,156,93", "endOffsets": "14505,14606,14701,14796,14921,15034,15131,15226,15345,15462,15572,15698,15869,15990,16109,16216,16311,16437,16529,16636,16741,16845,16947,17066,17198,17319,17438,17542,17651,17759,17875,17977,18087,18174,18258,18363,18499,18656,23481"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,279,397,508,585,680,793,927,1040,1173,1253,1348,1436,1528,1641,1761,1861,1997,2129,2269,2434,2556,2673,2795,2913,3002,3098,3214,3334,3429,3528,3630,3765,3908,4015,4109,4181,4259,4348,4431,4541,4617,4700,4799,4900,4987,5084,5168,5270,5365,5462,5576,5652,5751", "endColumns": "110,112,117,110,76,94,112,133,112,132,79,94,87,91,112,119,99,135,131,139,164,121,116,121,117,88,95,115,119,94,98,101,134,142,106,93,71,77,88,82,109,75,82,98,100,86,96,83,101,94,96,113,75,98,92", "endOffsets": "161,274,392,503,580,675,788,922,1035,1168,1248,1343,1431,1523,1636,1756,1856,1992,2124,2264,2429,2551,2668,2790,2908,2997,3093,3209,3329,3424,3523,3625,3760,3903,4010,4104,4176,4254,4343,4426,4536,4612,4695,4794,4895,4982,5079,5163,5265,5360,5457,5571,5647,5746,5839"}, "to": {"startLines": "57,58,59,60,74,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,110,165,272,275,277,282,283,284,285,286,287,288,289,290,291,292,293,294,295", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4070,4181,4294,4412,5803,5950,6045,6158,6292,6405,6538,6618,6713,6801,6893,7006,7126,7226,7362,7494,7634,7799,7921,8038,8160,8278,8367,8463,8579,8699,8794,8893,8995,9130,9273,9380,9766,13949,23866,24110,24294,24843,24919,25002,25101,25202,25289,25386,25470,25572,25667,25764,25878,25954,26053", "endColumns": "110,112,117,110,76,94,112,133,112,132,79,94,87,91,112,119,99,135,131,139,164,121,116,121,117,88,95,115,119,94,98,101,134,142,106,93,71,77,88,82,109,75,82,98,100,86,96,83,101,94,96,113,75,98,92", "endOffsets": "4176,4289,4407,4518,5875,6040,6153,6287,6400,6533,6613,6708,6796,6888,7001,7121,7221,7357,7489,7629,7794,7916,8033,8155,8273,8362,8458,8574,8694,8789,8888,8990,9125,9268,9375,9469,9833,14022,23950,24188,24399,24914,24997,25096,25197,25284,25381,25465,25567,25662,25759,25873,25949,26048,26141"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,622,944,1022,1100,1183,1272,1361,1444,1511,1605,1699,1768,1834,1899,1971,2098,2221,2344,2420,2501,2574,2657,2754,2851,2919,2983,3036,3094,3142,3203,3276,3342,3406,3483,3550,3608,3675,3740,3805,3857,3924,4015,4106", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,77,77,82,88,88,82,66,93,93,68,65,64,71,126,122,122,75,80,72,82,96,96,67,63,52,57,47,60,72,65,63,76,66,57,66,64,64,51,66,90,90,54", "endOffsets": "282,617,939,1017,1095,1178,1267,1356,1439,1506,1600,1694,1763,1829,1894,1966,2093,2216,2339,2415,2496,2569,2652,2749,2846,2914,2978,3031,3089,3137,3198,3271,3337,3401,3478,3545,3603,3670,3735,3800,3852,3919,4010,4101,4156"}, "to": {"startLines": "2,11,17,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,337,672,9979,10057,10135,10218,10307,10396,10479,10546,10640,10734,10803,10869,10934,11006,11133,11256,11379,11455,11536,11609,11692,11789,11886,11954,12680,12733,12791,12839,12900,12973,13039,13103,13180,13247,13305,13372,13437,13502,13554,13621,13712,13803", "endLines": "10,16,22,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163", "endColumns": "17,12,12,77,77,82,88,88,82,66,93,93,68,65,64,71,126,122,122,75,80,72,82,96,96,67,63,52,57,47,60,72,65,63,76,66,57,66,64,64,51,66,90,90,54", "endOffsets": "332,667,989,10052,10130,10213,10302,10391,10474,10541,10635,10729,10798,10864,10929,11001,11128,11251,11374,11450,11531,11604,11687,11784,11881,11949,12013,12728,12786,12834,12895,12968,13034,13098,13175,13242,13300,13367,13432,13497,13549,13616,13707,13798,13853"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,285,394,499,576,653,746,836,919,990,1060,1143,1230,1302,1384,1452", "endColumns": "94,84,108,104,76,76,92,89,82,70,69,82,86,71,81,67,119", "endOffsets": "195,280,389,494,571,648,741,831,914,985,1055,1138,1225,1297,1379,1447,1567"}, "to": {"startLines": "72,73,107,109,111,168,169,262,263,265,266,270,271,274,279,280,281", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5623,5718,9474,9661,9838,14172,14249,22998,23088,23251,23322,23696,23779,24038,24573,24655,24723", "endColumns": "94,84,108,104,76,76,92,89,82,70,69,82,86,71,81,67,119", "endOffsets": "5713,5798,9578,9761,9910,14244,14337,23083,23166,23317,23387,23774,23861,24105,24650,24718,24838"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,189,251,320,398,468,561,652", "endColumns": "72,60,61,68,77,69,92,90,64", "endOffsets": "123,184,246,315,393,463,556,647,712"}, "to": {"startLines": "137,138,139,140,141,142,143,144,145", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "12018,12091,12152,12214,12283,12361,12431,12524,12615", "endColumns": "72,60,61,68,77,69,92,90,64", "endOffsets": "12086,12147,12209,12278,12356,12426,12519,12610,12675"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,116", "endOffsets": "158,275"}, "to": {"startLines": "260,261", "startColumns": "4,4", "startOffsets": "22773,22881", "endColumns": "107,116", "endOffsets": "22876,22993"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,273", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1280,1395,1497,1605,1691,1798,1917,1996,2072,2163,2256,2351,2445,2546,2639,2734,2829,2920,3011,3093,3202,3302,3401,3510,3622,3733,3896,23955", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "1390,1492,1600,1686,1793,1912,1991,2067,2158,2251,2346,2440,2541,2634,2729,2824,2915,3006,3088,3197,3297,3396,3505,3617,3728,3891,3987,24033"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,133,211,283,352,434,521,621", "endColumns": "77,77,71,68,81,86,99,105", "endOffsets": "128,206,278,347,429,516,616,722"}, "to": {"startLines": "56,108,221,222,223,224,225,226", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3992,9583,19568,19640,19709,19791,19878,19978", "endColumns": "77,77,71,68,81,86,99,105", "endOffsets": "4065,9656,19635,19704,19786,19873,19973,20079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,266,346,480,649,730", "endColumns": "69,90,79,133,168,80,76", "endOffsets": "170,261,341,475,644,725,802"}, "to": {"startLines": "75,164,264,269,278,296,297", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5880,13858,23171,23562,24404,26146,26227", "endColumns": "69,90,79,133,168,80,76", "endOffsets": "5945,13944,23246,23691,24568,26222,26299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,341,420,505,622,704,768,849,913,974,1085,1153,1207,1276,1338,1392,1503,1564,1626,1680,1752,1881,1970,2052,2171,2253,2336,2423,2490,2556,2627,2703,2792,2869,2947,3025,3101,3191,3264,3359,3456,3528,3602,3702,3754,3820,3908,3998,4060,4124,4187,4294,4383,4482,4570", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "12,78,84,116,81,63,80,63,60,110,67,53,68,61,53,110,60,61,53,71,128,88,81,118,81,82,86,66,65,70,75,88,76,77,77,75,89,72,94,96,71,73,99,51,65,87,89,61,63,62,106,88,98,87,75", "endOffsets": "336,415,500,617,699,763,844,908,969,1080,1148,1202,1271,1333,1387,1498,1559,1621,1675,1747,1876,1965,2047,2166,2248,2331,2418,2485,2551,2622,2698,2787,2864,2942,3020,3096,3186,3259,3354,3451,3523,3597,3697,3749,3815,3903,3993,4055,4119,4182,4289,4378,4477,4565,4641"}, "to": {"startLines": "23,61,69,70,71,112,166,167,170,209,210,211,212,213,214,215,216,217,218,219,220,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,268", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "994,4523,5339,5424,5541,9915,14027,14108,14342,18661,18772,18840,18894,18963,19025,19079,19190,19251,19313,19367,19439,20084,20173,20255,20374,20456,20539,20626,20693,20759,20830,20906,20995,21072,21150,21228,21304,21394,21467,21562,21659,21731,21805,21905,21957,22023,22111,22201,22263,22327,22390,22497,22586,22685,23486", "endLines": "28,61,69,70,71,112,166,167,170,209,210,211,212,213,214,215,216,217,218,219,220,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,268", "endColumns": "12,78,84,116,81,63,80,63,60,110,67,53,68,61,53,110,60,61,53,71,128,88,81,118,81,82,86,66,65,70,75,88,76,77,77,75,89,72,94,96,71,73,99,51,65,87,89,61,63,62,106,88,98,87,75", "endOffsets": "1275,4597,5419,5536,5618,9974,14103,14167,14398,18767,18835,18889,18958,19020,19074,19185,19246,19308,19362,19434,19563,20168,20250,20369,20451,20534,20621,20688,20754,20825,20901,20990,21067,21145,21223,21299,21389,21462,21557,21654,21726,21800,21900,21952,22018,22106,22196,22258,22322,22385,22492,22581,22680,22768,23557"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "62,63,64,65,66,67,68,276", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4602,4699,4801,4899,4998,5112,5217,24193", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "4694,4796,4894,4993,5107,5212,5334,24289"}}]}]}