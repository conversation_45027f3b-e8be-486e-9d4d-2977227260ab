package com.deshi.cinepix.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u0011\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0011\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\u0002\u0010\u0006J\u000e\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nJ\u000e\u0010\u000b\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nJ\u000e\u0010\f\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nJ\u000e\u0010\r\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n\u00a8\u0006\u000e"}, d2 = {"Lcom/deshi/cinepix/utils/PermissionUtils;", "", "()V", "getRequiredPermissions", "", "", "()[Ljava/lang/String;", "hasAllRequiredPermissions", "", "context", "Landroid/content/Context;", "hasNotificationPermission", "hasStoragePermission", "hasWriteStoragePermission", "app_debug"})
public final class PermissionUtils {
    @org.jetbrains.annotations.NotNull
    public static final com.deshi.cinepix.utils.PermissionUtils INSTANCE = null;
    
    private PermissionUtils() {
        super();
    }
    
    public final boolean hasStoragePermission(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
        return false;
    }
    
    public final boolean hasWriteStoragePermission(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String[] getRequiredPermissions() {
        return null;
    }
    
    public final boolean hasNotificationPermission(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
        return false;
    }
    
    public final boolean hasAllRequiredPermissions(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
        return false;
    }
}