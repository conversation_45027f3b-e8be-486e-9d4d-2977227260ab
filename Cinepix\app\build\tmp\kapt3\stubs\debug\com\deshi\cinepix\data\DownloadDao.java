package com.deshi.cinepix.data;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0004\bg\u0018\u00002\u00020\u0001J\u0019\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0006J\u0019\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\nJ\u0014\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\r0\fH\'J\u001b\u0010\u000e\u001a\u0004\u0018\u00010\u00052\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\nJ\u001c\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\r0\f2\u0006\u0010\u0010\u001a\u00020\u0011H\'J\u0019\u0010\u0012\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0006J\u0019\u0010\u0013\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0006J)\u0010\u0014\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0018H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0019J!\u0010\u001a\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\u0010\u001a\u00020\u0011H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u001b\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006\u001c"}, d2 = {"Lcom/deshi/cinepix/data/DownloadDao;", "", "deleteDownload", "", "download", "Lcom/deshi/cinepix/data/DownloadItem;", "(Lcom/deshi/cinepix/data/DownloadItem;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteDownloadById", "id", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllDownloads", "Lkotlinx/coroutines/flow/Flow;", "", "getDownloadById", "getDownloadsByStatus", "status", "Lcom/deshi/cinepix/data/DownloadStatus;", "insertDownload", "updateDownload", "updateDownloadProgress", "progress", "", "downloadedSize", "", "(Ljava/lang/String;IJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateDownloadStatus", "(Ljava/lang/String;Lcom/deshi/cinepix/data/DownloadStatus;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
@androidx.room.Dao
public abstract interface DownloadDao {
    
    @androidx.room.Query(value = "SELECT * FROM downloads ORDER BY createdAt DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.deshi.cinepix.data.DownloadItem>> getAllDownloads();
    
    @androidx.room.Query(value = "SELECT * FROM downloads WHERE status = :status")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.deshi.cinepix.data.DownloadItem>> getDownloadsByStatus(@org.jetbrains.annotations.NotNull
    com.deshi.cinepix.data.DownloadStatus status);
    
    @androidx.room.Query(value = "SELECT * FROM downloads WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getDownloadById(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.deshi.cinepix.data.DownloadItem> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertDownload(@org.jetbrains.annotations.NotNull
    com.deshi.cinepix.data.DownloadItem download, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateDownload(@org.jetbrains.annotations.NotNull
    com.deshi.cinepix.data.DownloadItem download, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteDownload(@org.jetbrains.annotations.NotNull
    com.deshi.cinepix.data.DownloadItem download, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM downloads WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteDownloadById(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE downloads SET status = :status WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateDownloadStatus(@org.jetbrains.annotations.NotNull
    java.lang.String id, @org.jetbrains.annotations.NotNull
    com.deshi.cinepix.data.DownloadStatus status, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE downloads SET progress = :progress, downloadedSize = :downloadedSize WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateDownloadProgress(@org.jetbrains.annotations.NotNull
    java.lang.String id, int progress, long downloadedSize, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}