{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-99:/values-ldrtl-v17/values-ldrtl-v17.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\35536fe73c76a56f94e622d08fe45089\\transformed\\leanback-1.0.0\\res\\values-ldrtl-v17\\values-ldrtl-v17.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,133,178", "endColumns": "77,44,46", "endOffsets": "128,173,220"}, "to": {"startLines": "2,5,6", "startColumns": "4,4,4", "startOffsets": "55,410,455", "endColumns": "77,44,46", "endOffsets": "128,450,497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\90e8727c8917f86c7d308f0c9abe80a3\\transformed\\material-1.4.0-beta01\\res\\values-ldrtl-v17\\values-ldrtl-v17.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,191", "endColumns": "135,140", "endOffsets": "186,327"}, "to": {"startLines": "3,4", "startColumns": "4,4", "startOffsets": "133,269", "endColumns": "135,140", "endOffsets": "264,405"}}]}]}