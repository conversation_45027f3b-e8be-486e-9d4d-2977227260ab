-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:134:9-142:20
	android:grantUriPermissions
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:138:13-47
	android:authorities
		INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:136:13-64
	android:exported
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:137:13-37
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:135:13-62
manifest
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:2:1-146:12
INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:2:1-146:12
INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:2:1-146:12
INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:2:1-146:12
MERGED from [androidx.databinding:databinding-adapters:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b494ff1ede7fae1bc574027bf89c902e\transformed\databinding-adapters-8.2.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\406541cb37539d656519f27b1d978906\transformed\databinding-ktx-8.2.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\04655170da3762e1f214196e37a80825\transformed\databinding-runtime-8.2.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e6745d42fd13ee820ba94e00ed35fc8c\transformed\viewbinding-8.2.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-gif:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9c20c1cc99af4d5a4406a179b0b2103\transformed\coil-gif-2.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee4eb5805838f6aa47a7ecdda7cd78c1\transformed\coil-compose-2.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\6c2148b84fb19e9d842be6572f5ffd23\transformed\coil-compose-base-2.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\7241f89e2f1705cb82dd0916c240c7ed\transformed\coil-2.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\b1e59e540c21782a6abb04bcdeb9ebbf\transformed\coil-base-2.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7310a47e8ff90cc7061e198503f0dd6f\transformed\preference-ktx-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\8aeb2264e6ae24064c6fa9a3b81edd81\transformed\navigation-common-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\e43cfaf3e31118ad59a0d5553a966cc1\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\5d814f873171f360d33fc6e5dc8143b6\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\26d9fb81b47243bffc3fbaa9c194fddb\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\ea045d02dff8e8c7940aa1f9feee1157\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\e1aa8245057795a112140769510d98f3\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\84f14a12d5c26224b773057a63636dd0\transformed\navigation-compose-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\066ccd4e00972837d4db87a12359f059\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\cb0d38356f8a450289b6a883ab8edf14\transformed\navigation-ui-2.7.6\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.4.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-3\90e8727c8917f86c7d308f0c9abe80a3\transformed\material-1.4.0-beta01\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\7e22a40457733280d7d2957f397e6633\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.leanback:leanback-preference:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a690a1efe68e67130ba45e4b7da0973f\transformed\leanback-preference-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7dac6a98b09dff857686d519405654b4\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-preference-v14:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\136a131f56a52b17163ce7425ba99258\transformed\legacy-preference-v14-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\f70384b9a1e1b9ce2411b7c78f9fa615\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\de0470acc971f743e832aff97e9527de\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.leanback:leanback:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\35536fe73c76a56f94e622d08fe45089\transformed\leanback-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c036506dfefc398763599fe4c7111b8\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3228cc311144672738c2f0d4ffc46c9\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891b8b28f2b74e48f431023feaa4c2c2\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\6338df35648d7063ceb474a555a314a0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\815329e95e0f95880c8fe7851855055d\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:17:1-25:12
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\677a264eecd9b3484ff2620a344ffab3\transformed\material3-1.1.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.accompanist:accompanist-permissions:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a76adc2a9cbb1381a800f10f2c7dbc5\transformed\accompanist-permissions-0.30.1\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\71af9f350020765da793048399cedb59\transformed\accompanist-systemuicontroller-0.30.1\AndroidManifest.xml:17:1-25:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e25f89bc72719ab69d04c21e72721a9f\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\8f98a0bd52445ac3abfa50e4177becd5\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\f96ba2a77405268b9f99449e1e0f648c\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7c07b5deb1135b22a2335fe8923c461a\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\289fa02dc3214a9c6d8b23830ce851a4\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\053e77c0ef9c802ee58124e90ba50cad\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\eb1fbe4588c6fbb3e41813ede99e608d\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\413d14526f82cdd393f059d632b8d5b8\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\635180540b03095636d33fd1ab9e43e7\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\8d5fd40ff4a6281c3438776929c85a7d\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\731c2beb60720b6fcd0e9a1df3fbc20a\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9d2b276c5ecf248cb9979253cdc963d4\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2e6670df0640e9be424fa016c96e8df9\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2624bb0b865480f6c302a7703195e31e\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\85c107345bd020db339f1b0fa8b559fe\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c631ff7e532de02754af7115f0970f7e\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\df88ea77231017449b3bb9c999ee14cb\transformed\accompanist-drawablepainter-0.30.1\AndroidManifest.xml:17:1-25:12
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\804c4e5761ff122de1231662730d5f72\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\ebe04cf194a845a65a4280d5d1c12288\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\a0ccb96e02ee51b63e4c03cca5816810\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\6f4e14acb777aa538cd63a4fb7951fcf\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\f7fb5a917ea895cde00c19876db1fb4d\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e5ce76bf2bb54b699cfa887b5924ed06\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fb5aa1269ed244634170ff10c889425b\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-livedata:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\169d354f252812988d0ef5c4af84713c\transformed\runtime-livedata-1.5.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9a26e1bfbb053e592abb444090c4b6d\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\cda10ed6a126e5d092fd6e07ef1211c6\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7e3aef029a1c7ecf48414c7ec76f853c\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e5c63cbbf1b7c6b0321420abbdc2faf\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\84685f11bb5a1ede833f263310f57cfe\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7503004123c9a3033132356440e70e63\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc80922b98afe931943c635af95148\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a38cbdbee9488fe98a3a782653888687\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\273767b1e2f873c68c2eb9d6191fd856\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1dc652a7c338b7d255da040c77abbf1f\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b96f6d2e69f727ab7ebec045b6e5112f\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\de261d2f163a108033204b57b7654d90\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\31ce7f1555c193c21e46e914ddbec794\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b652c596524b4e375a702fa4012c94a\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbb45b556602582e98243d90f2d7d909\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7194d0d1f6901b6dd5f8dc0c5bab8c53\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6f485953063b326ea3d914611813eb8\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b8c1d18138610e12240635c0b9d1888d\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-hls:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d720376f95679dda816980a05aa758eb\transformed\media3-exoplayer-hls-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer-dash:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\dccecafaf74602b8d6cdd1a10930a6aa\transformed\media3-exoplayer-dash-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3384bca41f271b3200ce0446ec0f087\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-session:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\2907b06edc976dc86930b7aa57b04408\transformed\media3-session-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-extractor:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\294f3a9098372ca2a1afdd2347cf4f02\transformed\media3-extractor-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-container:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eab7192abe03f6fd4d69c64c3178d0fc\transformed\media3-container-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-datasource:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\24551529d625bad7ceb50f8cfeeecc7a\transformed\media3-datasource-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-decoder:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\1ea24c786ee31f7c8a59c315a4950b14\transformed\media3-decoder-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-database:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b32f14ec72ba7c737f4d8f9ca4740f1f\transformed\media3-database-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d2af8331fa976290408325fcbf6e0d2e\transformed\media3-common-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media3:media3-ui:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b15b518726989b0578dc4af44fa5f9f8\transformed\media3-ui-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f149b11daa2e90a73a3f613cc33dde8e\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa5b5f8e7f3fa934fc5e8965eccf445\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\80d99540cd5318a367dde4de846f552a\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\2349001851af7276447f771c3f2e12c6\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\43aca4c73a9704d5bd9903c6316396f8\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6eb043ba4e47b862ee6f6546fd01f66\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\transforms-3\13937523763bb446d7c00f9d839e6138\transformed\webkit-1.12.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\a07c621de681aef3000bac422655fb4e\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c690842c324e7c735d2762f89f0381cf\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3101aff03c52e449f72fbfdaff6da6f7\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddaf7e01f7a3ceccbea94f4bbbf5a0b0\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\95ec10d41ecb28b642ea568c9dd4092c\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f78afdf418d44d49930cbe83f38f3cd1\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4bc4e7700e6a945a93ad4d5a28d3b2c2\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9adc6cc40a5cd8a97c14f0bcb8d1d064\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\756847d3ed90d6f6fe65eed5ec99764a\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b622796a4a6efa0e7a48175ce08cf677\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8a59cd618100c117e5d81defb686de0\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cc96508f556f069c09e91b3a9267645a\transformed\play-services-base-18.0.1\AndroidManifest.xml:16:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9bb932b0969f699c88eb3c8e53bc8d91\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f35fc8775ad80a93919efd2167512d74\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31a00a017e90aa446708533c4ea3d047\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\1347443e2360b61953997eecdce4e1f7\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b6499dd00700003ac998b62cba41b58\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\cda0c6a654148b2b31a12ed9cf7d5326\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbbc63d48874e635e30be8ce8b6e8a55\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\573b5324239a288ee4b73046ec9c5d56\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\986ae0ecce6a0249160d095ef80f03c0\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\a59e210c722ea7eaa1c01c88f31edbaf\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b6ad2b5e64df2b4d80eecc5617c24e\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\433e61d12d367d5fa3a08e438130d2cd\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\79f4c9d69caf3b2b2cd968ef0621ee72\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a5a7c50fdf9c77e921b8ba85db44b409\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\62c17cba0a75b790331013fbfad47644\transformed\play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\7b3544053e1a40af3a5f83dceab43ffe\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\d7cfc243d831b28704c669de3a9c3c70\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0afa9e3e3921ae2f6c065bd95acb50c9\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c259ea9ac193ee3c7c43acd233dbd746\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\71e16755cc979dce3fe5d21e8a73baf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\e98def400858eb18134ac6695b7b30f9\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c545d6870927f18f48c8373e0f6e7bdf\transformed\multidex-2.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\bd650446db21c4b346138f5447b227c0\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:15:1-33:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d6677376643b2c66825a15928b8d3851\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e1935de9b9c69e2b40c7a95ea811040\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f65258d27b67c849bda0b9d9b28d2079\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd88056b135a432753fb437aa194c64a\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\4630f0f338404e1362305b9933e1939d\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\15069948cd027b5e258bf2fdc3cc694d\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\1cf0a26fa2ee7dce6ab0da8c2fa20a70\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\0c43cb65e90dfe56829d0e7579a710be\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\db66cf980af2febcd00581257f5bfed1\transformed\firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a834768fa38c1265ce3dc4fb89701741\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5394f43c8bf24e9a04524e2cf8f6e7cf\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bff90d0bf015af21c4d061de2f297e95\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a61676fd9aa6b3156239ebfbf126148c\transformed\transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f32680e9de2d24c1d5636335b524496\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8f8eef65f8013747bf605da61481732\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cfa355cd0d8f3dfb451c52ba9118aac6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa2af0a68cb6d0f7174c311d9da2dd46\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\684939f40979ecd4951af60d9507bad9\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\19e9aa069984997804d580b83481d880\transformed\napier-debug\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7503004123c9a3033132356440e70e63\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7503004123c9a3033132356440e70e63\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\573b5324239a288ee4b73046ec9c5d56\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\573b5324239a288ee4b73046ec9c5d56\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\79f4c9d69caf3b2b2cd968ef0621ee72\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\79f4c9d69caf3b2b2cd968ef0621ee72\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d6677376643b2c66825a15928b8d3851\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d6677376643b2c66825a15928b8d3851\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7503004123c9a3033132356440e70e63\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7503004123c9a3033132356440e70e63\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3384bca41f271b3200ce0446ec0f087\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3384bca41f271b3200ce0446ec0f087\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d2af8331fa976290408325fcbf6e0d2e\transformed\media3-common-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d2af8331fa976290408325fcbf6e0d2e\transformed\media3-common-1.2.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\573b5324239a288ee4b73046ec9c5d56\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\573b5324239a288ee4b73046ec9c5d56\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\79f4c9d69caf3b2b2cd968ef0621ee72\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\79f4c9d69caf3b2b2cd968ef0621ee72\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d6677376643b2c66825a15928b8d3851\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d6677376643b2c66825a15928b8d3851\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:8:5-76
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:8:22-73
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:9:5-10:38
	android:maxSdkVersion
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:10:9-35
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:9:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:11:5-12:38
	android:maxSdkVersion
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:12:9-35
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:11:22-77
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:13:5-75
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:13:22-72
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:14:5-68
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7503004123c9a3033132356440e70e63\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7503004123c9a3033132356440e70e63\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\573b5324239a288ee4b73046ec9c5d56\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\573b5324239a288ee4b73046ec9c5d56\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\79f4c9d69caf3b2b2cd968ef0621ee72\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\79f4c9d69caf3b2b2cd968ef0621ee72\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:14:22-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:15:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:15:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:16:5-92
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:16:22-89
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:17:5-77
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:23:5-77
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:17:22-74
uses-feature#android.software.leanback
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:20:5-22:36
	android:required
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:22:9-33
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:21:9-49
uses-feature#android.hardware.touchscreen
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:23:5-25:36
	android:required
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:25:9-33
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:24:9-52
uses-feature#android.hardware.wifi
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:28:5-30:36
	android:required
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:30:9-33
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:29:9-45
uses-feature#android.hardware.ethernet
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:31:5-33:36
	android:required
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:33:9-33
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:32:9-49
application
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:35:5-144:19
INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:35:5-144:19
MERGED from [com.google.android.material:material:1.4.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-3\90e8727c8917f86c7d308f0c9abe80a3\transformed\material-1.4.0-beta01\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.4.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-3\90e8727c8917f86c7d308f0c9abe80a3\transformed\material-1.4.0-beta01\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\7e22a40457733280d7d2957f397e6633\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\7e22a40457733280d7d2957f397e6633\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c036506dfefc398763599fe4c7111b8\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c036506dfefc398763599fe4c7111b8\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3228cc311144672738c2f0d4ffc46c9\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:22:5-30:19
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3228cc311144672738c2f0d4ffc46c9\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:22:5-30:19
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891b8b28f2b74e48f431023feaa4c2c2\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891b8b28f2b74e48f431023feaa4c2c2\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\6338df35648d7063ceb474a555a314a0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\6338df35648d7063ceb474a555a314a0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\815329e95e0f95880c8fe7851855055d\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\815329e95e0f95880c8fe7851855055d\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:22:5-23:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\85c107345bd020db339f1b0fa8b559fe\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\85c107345bd020db339f1b0fa8b559fe\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\ebe04cf194a845a65a4280d5d1c12288\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\ebe04cf194a845a65a4280d5d1c12288\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7503004123c9a3033132356440e70e63\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7503004123c9a3033132356440e70e63\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc80922b98afe931943c635af95148\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc80922b98afe931943c635af95148\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbb45b556602582e98243d90f2d7d909\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbb45b556602582e98243d90f2d7d909\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7194d0d1f6901b6dd5f8dc0c5bab8c53\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7194d0d1f6901b6dd5f8dc0c5bab8c53\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\43aca4c73a9704d5bd9903c6316396f8\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\43aca4c73a9704d5bd9903c6316396f8\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cc96508f556f069c09e91b3a9267645a\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cc96508f556f069c09e91b3a9267645a\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f35fc8775ad80a93919efd2167512d74\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f35fc8775ad80a93919efd2167512d74\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31a00a017e90aa446708533c4ea3d047\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31a00a017e90aa446708533c4ea3d047\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbbc63d48874e635e30be8ce8b6e8a55\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbbc63d48874e635e30be8ce8b6e8a55\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\a59e210c722ea7eaa1c01c88f31edbaf\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\a59e210c722ea7eaa1c01c88f31edbaf\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b6ad2b5e64df2b4d80eecc5617c24e\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b6ad2b5e64df2b4d80eecc5617c24e\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\433e61d12d367d5fa3a08e438130d2cd\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\433e61d12d367d5fa3a08e438130d2cd\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a5a7c50fdf9c77e921b8ba85db44b409\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a5a7c50fdf9c77e921b8ba85db44b409\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\62c17cba0a75b790331013fbfad47644\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\62c17cba0a75b790331013fbfad47644\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\71e16755cc979dce3fe5d21e8a73baf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\71e16755cc979dce3fe5d21e8a73baf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\bd650446db21c4b346138f5447b227c0\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\bd650446db21c4b346138f5447b227c0\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:23:5-31:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d6677376643b2c66825a15928b8d3851\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d6677376643b2c66825a15928b8d3851\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\1cf0a26fa2ee7dce6ab0da8c2fa20a70\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\1cf0a26fa2ee7dce6ab0da8c2fa20a70\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5394f43c8bf24e9a04524e2cf8f6e7cf\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5394f43c8bf24e9a04524e2cf8f6e7cf\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml
	android:roundIcon
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:42:9-54
	android:largeHeap
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:47:9-33
	android:icon
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:40:9-43
	android:banner
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:48:9-45
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31a00a017e90aa446708533c4ea3d047\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:43:9-35
	android:label
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:41:9-41
	android:hardwareAccelerated
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:46:9-43
	android:fullBackupContent
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:39:9-54
	tools:targetApi
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:49:9-29
	android:allowBackup
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:37:9-35
	android:theme
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:44:9-45
	android:dataExtractionRules
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:38:9-65
	android:usesCleartextTraffic
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:45:9-44
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:36:9-43
activity#com.deshi.cinepix.MainActivity
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:50:9-76:20
	android:screenOrientation
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:56:13-52
	android:label
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:53:13-45
	android:launchMode
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:55:13-43
	android:exported
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:52:13-36
	android:supportsPictureInPicture
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:58:13-52
	android:configChanges
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:57:13-106
	android:theme
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:54:13-49
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:51:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:59:13-62:29
action#android.intent.action.MAIN
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:60:17-69
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:60:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:61:17-77
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:61:27-74
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:64:13-67:29
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:66:17-86
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:66:27-83
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:cinepix.top+data:host:cinepix.top+data:scheme:http+data:scheme:https
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:69:13-75:29
	android:autoVerify
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:69:28-53
action#android.intent.action.VIEW
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:70:17-69
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:70:25-66
category#android.intent.category.DEFAULT
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:71:17-76
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:71:27-73
category#android.intent.category.BROWSABLE
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:72:17-78
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:72:27-75
data
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:73:17-74
	android:host
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:73:45-71
	android:scheme
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:73:23-44
activity#com.deshi.cinepix.ui.player.SimpleVideoPlayerActivity
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:79:9-85:46
	android:screenOrientation
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:83:13-50
	android:launchMode
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:85:13-43
	android:exported
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:81:13-37
	android:configChanges
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:84:13-106
	android:theme
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:82:13-60
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:80:13-64
activity#com.deshi.cinepix.ui.webview.SimpleWebViewActivity
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:88:9-92:109
	android:exported
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:90:13-37
	android:configChanges
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:92:13-106
	android:theme
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:91:13-49
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:89:13-61
activity#com.deshi.cinepix.ui.download.DownloadActivity
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:95:9-98:52
	android:exported
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:97:13-37
	android:theme
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:98:13-49
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:96:13-57
activity#com.deshi.cinepix.ui.auth.AuthActivity
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:101:9-104:52
	android:exported
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:103:13-37
	android:theme
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:104:13-49
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:102:13-49
activity#com.deshi.cinepix.ui.auth.ProfileActivity
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:107:9-110:52
	android:exported
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:109:13-37
	android:theme
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:110:13-49
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:108:13-52
activity#com.deshi.cinepix.ui.tv.TvMainActivity
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:113:9-122:20
	android:screenOrientation
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:117:13-50
	android:exported
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:115:13-36
	android:theme
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:116:13-58
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:114:13-49
service#com.deshi.cinepix.services.CinepixFirebaseMessagingService
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:125:9-131:19
	android:exported
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:127:13-37
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:126:13-69
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:128:13-130:29
action#com.google.firebase.MESSAGING_EVENT
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:129:17-78
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:129:25-75
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:139:13-141:54
	android:resource
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:141:17-51
	android:name
		ADDED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml:140:17-67
uses-sdk
INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml
INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:databinding-adapters:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b494ff1ede7fae1bc574027bf89c902e\transformed\databinding-adapters-8.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b494ff1ede7fae1bc574027bf89c902e\transformed\databinding-adapters-8.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\406541cb37539d656519f27b1d978906\transformed\databinding-ktx-8.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\406541cb37539d656519f27b1d978906\transformed\databinding-ktx-8.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\04655170da3762e1f214196e37a80825\transformed\databinding-runtime-8.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\04655170da3762e1f214196e37a80825\transformed\databinding-runtime-8.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e6745d42fd13ee820ba94e00ed35fc8c\transformed\viewbinding-8.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e6745d42fd13ee820ba94e00ed35fc8c\transformed\viewbinding-8.2.1\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-gif:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9c20c1cc99af4d5a4406a179b0b2103\transformed\coil-gif-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-gif:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f9c20c1cc99af4d5a4406a179b0b2103\transformed\coil-gif-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee4eb5805838f6aa47a7ecdda7cd78c1\transformed\coil-compose-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ee4eb5805838f6aa47a7ecdda7cd78c1\transformed\coil-compose-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\6c2148b84fb19e9d842be6572f5ffd23\transformed\coil-compose-base-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\6c2148b84fb19e9d842be6572f5ffd23\transformed\coil-compose-base-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\7241f89e2f1705cb82dd0916c240c7ed\transformed\coil-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\7241f89e2f1705cb82dd0916c240c7ed\transformed\coil-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\b1e59e540c21782a6abb04bcdeb9ebbf\transformed\coil-base-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\b1e59e540c21782a6abb04bcdeb9ebbf\transformed\coil-base-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7310a47e8ff90cc7061e198503f0dd6f\transformed\preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7310a47e8ff90cc7061e198503f0dd6f\transformed\preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\8aeb2264e6ae24064c6fa9a3b81edd81\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\8aeb2264e6ae24064c6fa9a3b81edd81\transformed\navigation-common-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\e43cfaf3e31118ad59a0d5553a966cc1\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\e43cfaf3e31118ad59a0d5553a966cc1\transformed\navigation-runtime-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\5d814f873171f360d33fc6e5dc8143b6\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\5d814f873171f360d33fc6e5dc8143b6\transformed\navigation-common-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\26d9fb81b47243bffc3fbaa9c194fddb\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\26d9fb81b47243bffc3fbaa9c194fddb\transformed\navigation-fragment-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\ea045d02dff8e8c7940aa1f9feee1157\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\ea045d02dff8e8c7940aa1f9feee1157\transformed\navigation-runtime-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\e1aa8245057795a112140769510d98f3\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\e1aa8245057795a112140769510d98f3\transformed\navigation-fragment-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\84f14a12d5c26224b773057a63636dd0\transformed\navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\84f14a12d5c26224b773057a63636dd0\transformed\navigation-compose-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\066ccd4e00972837d4db87a12359f059\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\066ccd4e00972837d4db87a12359f059\transformed\navigation-ui-ktx-2.7.6\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\cb0d38356f8a450289b6a883ab8edf14\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.6] C:\Users\<USER>\.gradle\caches\transforms-3\cb0d38356f8a450289b6a883ab8edf14\transformed\navigation-ui-2.7.6\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.4.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-3\90e8727c8917f86c7d308f0c9abe80a3\transformed\material-1.4.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.4.0-beta01] C:\Users\<USER>\.gradle\caches\transforms-3\90e8727c8917f86c7d308f0c9abe80a3\transformed\material-1.4.0-beta01\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\7e22a40457733280d7d2957f397e6633\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\7e22a40457733280d7d2957f397e6633\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.leanback:leanback-preference:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a690a1efe68e67130ba45e4b7da0973f\transformed\leanback-preference-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.leanback:leanback-preference:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a690a1efe68e67130ba45e4b7da0973f\transformed\leanback-preference-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7dac6a98b09dff857686d519405654b4\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\7dac6a98b09dff857686d519405654b4\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-preference-v14:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\136a131f56a52b17163ce7425ba99258\transformed\legacy-preference-v14-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-preference-v14:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\136a131f56a52b17163ce7425ba99258\transformed\legacy-preference-v14-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\f70384b9a1e1b9ce2411b7c78f9fa615\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\f70384b9a1e1b9ce2411b7c78f9fa615\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\de0470acc971f743e832aff97e9527de\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\de0470acc971f743e832aff97e9527de\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.leanback:leanback:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\35536fe73c76a56f94e622d08fe45089\transformed\leanback-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.leanback:leanback:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\35536fe73c76a56f94e622d08fe45089\transformed\leanback-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c036506dfefc398763599fe4c7111b8\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c036506dfefc398763599fe4c7111b8\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3228cc311144672738c2f0d4ffc46c9\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3228cc311144672738c2f0d4ffc46c9\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891b8b28f2b74e48f431023feaa4c2c2\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\891b8b28f2b74e48f431023feaa4c2c2\transformed\firebase-analytics-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\6338df35648d7063ceb474a555a314a0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\6338df35648d7063ceb474a555a314a0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\815329e95e0f95880c8fe7851855055d\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\815329e95e0f95880c8fe7851855055d\transformed\play-services-measurement-sdk-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\677a264eecd9b3484ff2620a344ffab3\transformed\material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\677a264eecd9b3484ff2620a344ffab3\transformed\material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a76adc2a9cbb1381a800f10f2c7dbc5\transformed\accompanist-permissions-0.30.1\AndroidManifest.xml:21:5-23:42
MERGED from [com.google.accompanist:accompanist-permissions:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a76adc2a9cbb1381a800f10f2c7dbc5\transformed\accompanist-permissions-0.30.1\AndroidManifest.xml:21:5-23:42
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\71af9f350020765da793048399cedb59\transformed\accompanist-systemuicontroller-0.30.1\AndroidManifest.xml:21:5-23:42
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\71af9f350020765da793048399cedb59\transformed\accompanist-systemuicontroller-0.30.1\AndroidManifest.xml:21:5-23:42
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e25f89bc72719ab69d04c21e72721a9f\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e25f89bc72719ab69d04c21e72721a9f\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\8f98a0bd52445ac3abfa50e4177becd5\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\8f98a0bd52445ac3abfa50e4177becd5\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\f96ba2a77405268b9f99449e1e0f648c\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\f96ba2a77405268b9f99449e1e0f648c\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7c07b5deb1135b22a2335fe8923c461a\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7c07b5deb1135b22a2335fe8923c461a\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\289fa02dc3214a9c6d8b23830ce851a4\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\289fa02dc3214a9c6d8b23830ce851a4\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\053e77c0ef9c802ee58124e90ba50cad\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\053e77c0ef9c802ee58124e90ba50cad\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\eb1fbe4588c6fbb3e41813ede99e608d\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\eb1fbe4588c6fbb3e41813ede99e608d\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\413d14526f82cdd393f059d632b8d5b8\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\413d14526f82cdd393f059d632b8d5b8\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\635180540b03095636d33fd1ab9e43e7\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\635180540b03095636d33fd1ab9e43e7\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\8d5fd40ff4a6281c3438776929c85a7d\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\8d5fd40ff4a6281c3438776929c85a7d\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\731c2beb60720b6fcd0e9a1df3fbc20a\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\731c2beb60720b6fcd0e9a1df3fbc20a\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9d2b276c5ecf248cb9979253cdc963d4\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9d2b276c5ecf248cb9979253cdc963d4\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2e6670df0640e9be424fa016c96e8df9\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2e6670df0640e9be424fa016c96e8df9\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2624bb0b865480f6c302a7703195e31e\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2624bb0b865480f6c302a7703195e31e\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\85c107345bd020db339f1b0fa8b559fe\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\85c107345bd020db339f1b0fa8b559fe\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c631ff7e532de02754af7115f0970f7e\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c631ff7e532de02754af7115f0970f7e\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\df88ea77231017449b3bb9c999ee14cb\transformed\accompanist-drawablepainter-0.30.1\AndroidManifest.xml:21:5-23:42
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\df88ea77231017449b3bb9c999ee14cb\transformed\accompanist-drawablepainter-0.30.1\AndroidManifest.xml:21:5-23:42
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\804c4e5761ff122de1231662730d5f72\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\804c4e5761ff122de1231662730d5f72\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\ebe04cf194a845a65a4280d5d1c12288\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\ebe04cf194a845a65a4280d5d1c12288\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\a0ccb96e02ee51b63e4c03cca5816810\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\a0ccb96e02ee51b63e4c03cca5816810\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\6f4e14acb777aa538cd63a4fb7951fcf\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\6f4e14acb777aa538cd63a4fb7951fcf\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\f7fb5a917ea895cde00c19876db1fb4d\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\f7fb5a917ea895cde00c19876db1fb4d\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e5ce76bf2bb54b699cfa887b5924ed06\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\e5ce76bf2bb54b699cfa887b5924ed06\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fb5aa1269ed244634170ff10c889425b\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fb5aa1269ed244634170ff10c889425b\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\169d354f252812988d0ef5c4af84713c\transformed\runtime-livedata-1.5.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\169d354f252812988d0ef5c4af84713c\transformed\runtime-livedata-1.5.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9a26e1bfbb053e592abb444090c4b6d\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a9a26e1bfbb053e592abb444090c4b6d\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\cda10ed6a126e5d092fd6e07ef1211c6\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\cda10ed6a126e5d092fd6e07ef1211c6\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7e3aef029a1c7ecf48414c7ec76f853c\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\7e3aef029a1c7ecf48414c7ec76f853c\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e5c63cbbf1b7c6b0321420abbdc2faf\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6e5c63cbbf1b7c6b0321420abbdc2faf\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\84685f11bb5a1ede833f263310f57cfe\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\84685f11bb5a1ede833f263310f57cfe\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7503004123c9a3033132356440e70e63\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7503004123c9a3033132356440e70e63\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc80922b98afe931943c635af95148\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc80922b98afe931943c635af95148\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a38cbdbee9488fe98a3a782653888687\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a38cbdbee9488fe98a3a782653888687\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\273767b1e2f873c68c2eb9d6191fd856\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\273767b1e2f873c68c2eb9d6191fd856\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1dc652a7c338b7d255da040c77abbf1f\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1dc652a7c338b7d255da040c77abbf1f\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b96f6d2e69f727ab7ebec045b6e5112f\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b96f6d2e69f727ab7ebec045b6e5112f\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\de261d2f163a108033204b57b7654d90\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\de261d2f163a108033204b57b7654d90\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\31ce7f1555c193c21e46e914ddbec794\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\31ce7f1555c193c21e46e914ddbec794\transformed\lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b652c596524b4e375a702fa4012c94a\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b652c596524b4e375a702fa4012c94a\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbb45b556602582e98243d90f2d7d909\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbb45b556602582e98243d90f2d7d909\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7194d0d1f6901b6dd5f8dc0c5bab8c53\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7194d0d1f6901b6dd5f8dc0c5bab8c53\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6f485953063b326ea3d914611813eb8\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6f485953063b326ea3d914611813eb8\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b8c1d18138610e12240635c0b9d1888d\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b8c1d18138610e12240635c0b9d1888d\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d720376f95679dda816980a05aa758eb\transformed\media3-exoplayer-hls-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-hls:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d720376f95679dda816980a05aa758eb\transformed\media3-exoplayer-hls-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\dccecafaf74602b8d6cdd1a10930a6aa\transformed\media3-exoplayer-dash-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer-dash:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\dccecafaf74602b8d6cdd1a10930a6aa\transformed\media3-exoplayer-dash-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3384bca41f271b3200ce0446ec0f087\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-exoplayer:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e3384bca41f271b3200ce0446ec0f087\transformed\media3-exoplayer-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\2907b06edc976dc86930b7aa57b04408\transformed\media3-session-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-session:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\2907b06edc976dc86930b7aa57b04408\transformed\media3-session-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\294f3a9098372ca2a1afdd2347cf4f02\transformed\media3-extractor-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-extractor:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\294f3a9098372ca2a1afdd2347cf4f02\transformed\media3-extractor-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eab7192abe03f6fd4d69c64c3178d0fc\transformed\media3-container-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-container:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\eab7192abe03f6fd4d69c64c3178d0fc\transformed\media3-container-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\24551529d625bad7ceb50f8cfeeecc7a\transformed\media3-datasource-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-datasource:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\24551529d625bad7ceb50f8cfeeecc7a\transformed\media3-datasource-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\1ea24c786ee31f7c8a59c315a4950b14\transformed\media3-decoder-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-decoder:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\1ea24c786ee31f7c8a59c315a4950b14\transformed\media3-decoder-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b32f14ec72ba7c737f4d8f9ca4740f1f\transformed\media3-database-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-database:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b32f14ec72ba7c737f4d8f9ca4740f1f\transformed\media3-database-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d2af8331fa976290408325fcbf6e0d2e\transformed\media3-common-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-common:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\d2af8331fa976290408325fcbf6e0d2e\transformed\media3-common-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b15b518726989b0578dc4af44fa5f9f8\transformed\media3-ui-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media3:media3-ui:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\b15b518726989b0578dc4af44fa5f9f8\transformed\media3-ui-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f149b11daa2e90a73a3f613cc33dde8e\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f149b11daa2e90a73a3f613cc33dde8e\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa5b5f8e7f3fa934fc5e8965eccf445\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\2fa5b5f8e7f3fa934fc5e8965eccf445\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\80d99540cd5318a367dde4de846f552a\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\80d99540cd5318a367dde4de846f552a\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\2349001851af7276447f771c3f2e12c6\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\2349001851af7276447f771c3f2e12c6\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\43aca4c73a9704d5bd9903c6316396f8\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\43aca4c73a9704d5bd9903c6316396f8\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6eb043ba4e47b862ee6f6546fd01f66\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\a6eb043ba4e47b862ee6f6546fd01f66\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\transforms-3\13937523763bb446d7c00f9d839e6138\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\transforms-3\13937523763bb446d7c00f9d839e6138\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\a07c621de681aef3000bac422655fb4e\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\a07c621de681aef3000bac422655fb4e\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c690842c324e7c735d2762f89f0381cf\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c690842c324e7c735d2762f89f0381cf\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3101aff03c52e449f72fbfdaff6da6f7\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\3101aff03c52e449f72fbfdaff6da6f7\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddaf7e01f7a3ceccbea94f4bbbf5a0b0\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\ddaf7e01f7a3ceccbea94f4bbbf5a0b0\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\95ec10d41ecb28b642ea568c9dd4092c\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\95ec10d41ecb28b642ea568c9dd4092c\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f78afdf418d44d49930cbe83f38f3cd1\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\f78afdf418d44d49930cbe83f38f3cd1\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4bc4e7700e6a945a93ad4d5a28d3b2c2\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\4bc4e7700e6a945a93ad4d5a28d3b2c2\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9adc6cc40a5cd8a97c14f0bcb8d1d064\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9adc6cc40a5cd8a97c14f0bcb8d1d064\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\756847d3ed90d6f6fe65eed5ec99764a\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\756847d3ed90d6f6fe65eed5ec99764a\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b622796a4a6efa0e7a48175ce08cf677\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b622796a4a6efa0e7a48175ce08cf677\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8a59cd618100c117e5d81defb686de0\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8a59cd618100c117e5d81defb686de0\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cc96508f556f069c09e91b3a9267645a\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cc96508f556f069c09e91b3a9267645a\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9bb932b0969f699c88eb3c8e53bc8d91\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9bb932b0969f699c88eb3c8e53bc8d91\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f35fc8775ad80a93919efd2167512d74\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f35fc8775ad80a93919efd2167512d74\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31a00a017e90aa446708533c4ea3d047\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31a00a017e90aa446708533c4ea3d047\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\1347443e2360b61953997eecdce4e1f7\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\1347443e2360b61953997eecdce4e1f7\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b6499dd00700003ac998b62cba41b58\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7b6499dd00700003ac998b62cba41b58\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\cda0c6a654148b2b31a12ed9cf7d5326\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\cda0c6a654148b2b31a12ed9cf7d5326\transformed\lifecycle-livedata-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbbc63d48874e635e30be8ce8b6e8a55\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbbc63d48874e635e30be8ce8b6e8a55\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\573b5324239a288ee4b73046ec9c5d56\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\573b5324239a288ee4b73046ec9c5d56\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\986ae0ecce6a0249160d095ef80f03c0\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\986ae0ecce6a0249160d095ef80f03c0\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\a59e210c722ea7eaa1c01c88f31edbaf\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\a59e210c722ea7eaa1c01c88f31edbaf\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b6ad2b5e64df2b4d80eecc5617c24e\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c4b6ad2b5e64df2b4d80eecc5617c24e\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\433e61d12d367d5fa3a08e438130d2cd\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\433e61d12d367d5fa3a08e438130d2cd\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\79f4c9d69caf3b2b2cd968ef0621ee72\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\79f4c9d69caf3b2b2cd968ef0621ee72\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a5a7c50fdf9c77e921b8ba85db44b409\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\a5a7c50fdf9c77e921b8ba85db44b409\transformed\play-services-measurement-base-21.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\62c17cba0a75b790331013fbfad47644\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\62c17cba0a75b790331013fbfad47644\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\7b3544053e1a40af3a5f83dceab43ffe\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\7b3544053e1a40af3a5f83dceab43ffe\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\d7cfc243d831b28704c669de3a9c3c70\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\d7cfc243d831b28704c669de3a9c3c70\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0afa9e3e3921ae2f6c065bd95acb50c9\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0afa9e3e3921ae2f6c065bd95acb50c9\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c259ea9ac193ee3c7c43acd233dbd746\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c259ea9ac193ee3c7c43acd233dbd746\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\71e16755cc979dce3fe5d21e8a73baf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\71e16755cc979dce3fe5d21e8a73baf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\e98def400858eb18134ac6695b7b30f9\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\e98def400858eb18134ac6695b7b30f9\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c545d6870927f18f48c8373e0f6e7bdf\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\c545d6870927f18f48c8373e0f6e7bdf\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\bd650446db21c4b346138f5447b227c0\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\bd650446db21c4b346138f5447b227c0\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d6677376643b2c66825a15928b8d3851\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d6677376643b2c66825a15928b8d3851\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e1935de9b9c69e2b40c7a95ea811040\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e1935de9b9c69e2b40c7a95ea811040\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f65258d27b67c849bda0b9d9b28d2079\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f65258d27b67c849bda0b9d9b28d2079\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd88056b135a432753fb437aa194c64a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dd88056b135a432753fb437aa194c64a\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\4630f0f338404e1362305b9933e1939d\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\4630f0f338404e1362305b9933e1939d\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\15069948cd027b5e258bf2fdc3cc694d\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\15069948cd027b5e258bf2fdc3cc694d\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\1cf0a26fa2ee7dce6ab0da8c2fa20a70\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\1cf0a26fa2ee7dce6ab0da8c2fa20a70\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\0c43cb65e90dfe56829d0e7579a710be\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\0c43cb65e90dfe56829d0e7579a710be\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\db66cf980af2febcd00581257f5bfed1\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\db66cf980af2febcd00581257f5bfed1\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a834768fa38c1265ce3dc4fb89701741\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a834768fa38c1265ce3dc4fb89701741\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5394f43c8bf24e9a04524e2cf8f6e7cf\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5394f43c8bf24e9a04524e2cf8f6e7cf\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bff90d0bf015af21c4d061de2f297e95\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bff90d0bf015af21c4d061de2f297e95\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a61676fd9aa6b3156239ebfbf126148c\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\a61676fd9aa6b3156239ebfbf126148c\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f32680e9de2d24c1d5636335b524496\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\7f32680e9de2d24c1d5636335b524496\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8f8eef65f8013747bf605da61481732\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8f8eef65f8013747bf605da61481732\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cfa355cd0d8f3dfb451c52ba9118aac6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\cfa355cd0d8f3dfb451c52ba9118aac6\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa2af0a68cb6d0f7174c311d9da2dd46\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa2af0a68cb6d0f7174c311d9da2dd46\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\684939f40979ecd4951af60d9507bad9\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\684939f40979ecd4951af60d9507bad9\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\19e9aa069984997804d580b83481d880\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\19e9aa069984997804d580b83481d880\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml
	tools:ignore
		ADDED from [com.google.accompanist:accompanist-permissions:0.30.1] C:\Users\<USER>\.gradle\caches\transforms-3\6a76adc2a9cbb1381a800f10f2c7dbc5\transformed\accompanist-permissions-0.30.1\AndroidManifest.xml:23:9-39
	android:minSdkVersion
		INJECTED from D:\xampp\htdocs\deshiflix\Cinepix\app\src\main\AndroidManifest.xml
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c036506dfefc398763599fe4c7111b8\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3228cc311144672738c2f0d4ffc46c9\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:23:9-29:19
MERGED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3228cc311144672738c2f0d4ffc46c9\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:23:9-29:19
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\6338df35648d7063ceb474a555a314a0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\6338df35648d7063ceb474a555a314a0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\bd650446db21c4b346138f5447b227c0\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
MERGED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\bd650446db21c4b346138f5447b227c0\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:24:9-30:19
	android:exported
		ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c036506dfefc398763599fe4c7111b8\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:10:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c036506dfefc398763599fe4c7111b8\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:9:13-84
meta-data#com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar
ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c036506dfefc398763599fe4c7111b8\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c036506dfefc398763599fe4c7111b8\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\1c036506dfefc398763599fe4c7111b8\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:12:17-129
meta-data#com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar
ADDED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3228cc311144672738c2f0d4ffc46c9\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:26:13-28:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3228cc311144672738c2f0d4ffc46c9\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:28:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging-ktx:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3228cc311144672738c2f0d4ffc46c9\transformed\firebase-messaging-ktx-23.4.0\AndroidManifest.xml:27:17-129
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\573b5324239a288ee4b73046ec9c5d56\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\573b5324239a288ee4b73046ec9c5d56\transformed\play-services-cloud-messaging-17.1.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:47:13-82
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:23.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\3f2587699e47b6fbe09c7c234ddec506\transformed\firebase-messaging-23.4.0\AndroidManifest.xml:61:17-119
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7503004123c9a3033132356440e70e63\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7503004123c9a3033132356440e70e63\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\433e61d12d367d5fa3a08e438130d2cd\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\433e61d12d367d5fa3a08e438130d2cd\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\79f4c9d69caf3b2b2cd968ef0621ee72\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\79f4c9d69caf3b2b2cd968ef0621ee72\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\79f4c9d69caf3b2b2cd968ef0621ee72\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\79f4c9d69caf3b2b2cd968ef0621ee72\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\79f4c9d69caf3b2b2cd968ef0621ee72\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\79f4c9d69caf3b2b2cd968ef0621ee72\transformed\play-services-measurement-sdk-api-21.5.0\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
property
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\5c2af52567252518a470dc3456af7630\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d7d209aa41b1b3a7cda01456c0f96bd\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\6338df35648d7063ceb474a555a314a0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\6338df35648d7063ceb474a555a314a0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\6338df35648d7063ceb474a555a314a0\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\4517ff94f72915c9c9876154305ef8a6\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7503004123c9a3033132356440e70e63\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\7503004123c9a3033132356440e70e63\transformed\play-services-measurement-impl-21.5.0\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\29448540d655867f8ca43bac4dbc189c\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\85c107345bd020db339f1b0fa8b559fe\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\85c107345bd020db339f1b0fa8b559fe\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\85c107345bd020db339f1b0fa8b559fe\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\ebe04cf194a845a65a4280d5d1c12288\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\ebe04cf194a845a65a4280d5d1c12288\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\ebe04cf194a845a65a4280d5d1c12288\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbb45b556602582e98243d90f2d7d909\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbb45b556602582e98243d90f2d7d909\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7194d0d1f6901b6dd5f8dc0c5bab8c53\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7194d0d1f6901b6dd5f8dc0c5bab8c53\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5394f43c8bf24e9a04524e2cf8f6e7cf\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\5394f43c8bf24e9a04524e2cf8f6e7cf\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c363e17464ba32fddd8658eb96dbf47\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbb45b556602582e98243d90f2d7d909\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbb45b556602582e98243d90f2d7d909\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbb45b556602582e98243d90f2d7d909\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7194d0d1f6901b6dd5f8dc0c5bab8c53\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7194d0d1f6901b6dd5f8dc0c5bab8c53\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7194d0d1f6901b6dd5f8dc0c5bab8c53\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\43aca4c73a9704d5bd9903c6316396f8\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\43aca4c73a9704d5bd9903c6316396f8\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\43aca4c73a9704d5bd9903c6316396f8\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cc96508f556f069c09e91b3a9267645a\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cc96508f556f069c09e91b3a9267645a\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cc96508f556f069c09e91b3a9267645a\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\cc96508f556f069c09e91b3a9267645a\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f35fc8775ad80a93919efd2167512d74\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f35fc8775ad80a93919efd2167512d74\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f35fc8775ad80a93919efd2167512d74\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f35fc8775ad80a93919efd2167512d74\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f35fc8775ad80a93919efd2167512d74\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f35fc8775ad80a93919efd2167512d74\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31a00a017e90aa446708533c4ea3d047\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31a00a017e90aa446708533c4ea3d047\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31a00a017e90aa446708533c4ea3d047\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.deshi.cinepix.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31a00a017e90aa446708533c4ea3d047\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31a00a017e90aa446708533c4ea3d047\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31a00a017e90aa446708533c4ea3d047\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31a00a017e90aa446708533c4ea3d047\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31a00a017e90aa446708533c4ea3d047\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.deshi.cinepix.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31a00a017e90aa446708533c4ea3d047\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\31a00a017e90aa446708533c4ea3d047\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\62c17cba0a75b790331013fbfad47644\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\62c17cba0a75b790331013fbfad47644\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\62c17cba0a75b790331013fbfad47644\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\71e16755cc979dce3fe5d21e8a73baf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\71e16755cc979dce3fe5d21e8a73baf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\71e16755cc979dce3fe5d21e8a73baf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\71e16755cc979dce3fe5d21e8a73baf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\71e16755cc979dce3fe5d21e8a73baf3\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\bd650446db21c4b346138f5447b227c0\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\bd650446db21c4b346138f5447b227c0\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\bd650446db21c4b346138f5447b227c0\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d6677376643b2c66825a15928b8d3851\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d6677376643b2c66825a15928b8d3851\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d6677376643b2c66825a15928b8d3851\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d6677376643b2c66825a15928b8d3851\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d6677376643b2c66825a15928b8d3851\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\d6677376643b2c66825a15928b8d3851\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\ae735e1e99f343d9795d05e672ada64c\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f432e123aab466454284e43070e12a84\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
