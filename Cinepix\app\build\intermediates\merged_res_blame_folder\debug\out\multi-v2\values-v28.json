{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-99:/values-v28/values-v28.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\de0470acc971f743e832aff97e9527de\\transformed\\appcompat-1.6.1\\res\\values-v28\\values-v28.xml", "from": {"startLines": "2,3,4,8", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,397", "endLines": "2,3,7,11", "endColumns": "74,86,12,12", "endOffsets": "125,212,392,584"}, "to": {"startLines": "10,11,12,16", "startColumns": "4,4,4,4", "startOffsets": "755,830,917,1097", "endLines": "10,11,15,19", "endColumns": "74,86,12,12", "endOffsets": "825,912,1092,1284"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\90e8727c8917f86c7d308f0c9abe80a3\\transformed\\material-1.4.0-beta01\\res\\values-v28\\values-v28.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,143,231,319,407,494,581,668", "endColumns": "87,87,87,87,86,86,86,86", "endOffsets": "138,226,314,402,489,576,663,750"}}]}]}