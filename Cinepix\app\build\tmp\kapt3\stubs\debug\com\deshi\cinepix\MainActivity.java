package com.deshi.cinepix;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0005\u001a\u00020\u00062\b\u0010\u0007\u001a\u0004\u0018\u00010\bH\u0002J\b\u0010\t\u001a\u00020\nH\u0002J\b\u0010\u000b\u001a\u00020\u0006H\u0002J\b\u0010\f\u001a\u00020\u0006H\u0002J\u0012\u0010\r\u001a\u00020\u00062\b\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u0014J\u0010\u0010\u0010\u001a\u00020\u00062\u0006\u0010\u0011\u001a\u00020\u0012H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/deshi/cinepix/MainActivity;", "Landroidx/activity/ComponentActivity;", "()V", "authRepository", "Lcom/deshi/cinepix/data/AuthRepository;", "handleNotificationIntent", "", "intent", "Landroid/content/Intent;", "isRunningOnTv", "", "launchAuth", "launchWebView", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "openWebView", "url", "", "app_debug"})
public final class MainActivity extends androidx.activity.ComponentActivity {
    private com.deshi.cinepix.data.AuthRepository authRepository;
    
    public MainActivity() {
        super();
    }
    
    @java.lang.Override
    protected void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final boolean isRunningOnTv() {
        return false;
    }
    
    private final void launchWebView() {
    }
    
    private final void launchAuth() {
    }
    
    private final void handleNotificationIntent(android.content.Intent intent) {
    }
    
    private final void openWebView(java.lang.String url) {
    }
}