{"logs": [{"outputFile": "com.deshi.cinepix.app-mergeDebugResources-91:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\790baa963f0557a5338a3a6815b465fa\\transformed\\appcompat-1.6.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,2899"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,267", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "890,1010,1119,1227,1312,1414,1530,1615,1695,1786,1879,1974,2068,2167,2260,2359,2455,2546,2637,2719,2826,2925,3024,3132,3240,3347,3506,23773", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "1005,1114,1222,1307,1409,1525,1610,1690,1781,1874,1969,2063,2162,2255,2354,2450,2541,2632,2714,2821,2920,3019,3127,3235,3342,3501,3601,23851"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ac1f4354741721caf5bce0b1d23444ae\\transformed\\material3-1.1.2\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,291,410,526,604,698,810,949,1063,1211,1292,1390,1483,1581,1695,1814,1914,2046,2175,2310,2482,2609,2725,2844,2968,3062,3154,3271,3400,3497,3598,3709,3839,3976,4083,4183,4256,4333,4416,4501,4608,4686,4766,4863,4965,5061,5156,5240,5351,5448,5547,5666,5744,5847", "endColumns": "117,117,118,115,77,93,111,138,113,147,80,97,92,97,113,118,99,131,128,134,171,126,115,118,123,93,91,116,128,96,100,110,129,136,106,99,72,76,82,84,106,77,79,96,101,95,94,83,110,96,98,118,77,102,94", "endOffsets": "168,286,405,521,599,693,805,944,1058,1206,1287,1385,1478,1576,1690,1809,1909,2041,2170,2305,2477,2604,2720,2839,2963,3057,3149,3266,3395,3492,3593,3704,3834,3971,4078,4178,4251,4328,4411,4496,4603,4681,4761,4858,4960,5056,5151,5235,5346,5443,5542,5661,5739,5842,5937"}, "to": {"startLines": "51,52,53,54,68,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,104,159,266,269,271,276,277,278,279,280,281,282,283,284,285,286,287,288,289", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3684,3802,3920,4039,5465,5613,5707,5819,5958,6072,6220,6301,6399,6492,6590,6704,6823,6923,7055,7184,7319,7491,7618,7734,7853,7977,8071,8163,8280,8409,8506,8607,8718,8848,8985,9092,9468,13581,23690,23933,24119,24666,24744,24824,24921,25023,25119,25214,25298,25409,25506,25605,25724,25802,25905", "endColumns": "117,117,118,115,77,93,111,138,113,147,80,97,92,97,113,118,99,131,128,134,171,126,115,118,123,93,91,116,128,96,100,110,129,136,106,99,72,76,82,84,106,77,79,96,101,95,94,83,110,96,98,118,77,102,94", "endOffsets": "3797,3915,4034,4150,5538,5702,5814,5953,6067,6215,6296,6394,6487,6585,6699,6818,6918,7050,7179,7314,7486,7613,7729,7848,7972,8066,8158,8275,8404,8501,8602,8713,8843,8980,9087,9187,9536,13653,23768,24013,24221,24739,24819,24916,25018,25114,25209,25293,25404,25501,25600,25719,25797,25900,25995"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9c78be4bd27d45b180b53d555e044be1\\transformed\\leanback-1.0.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,412,509,636,751,852,945,1084,1220,1322,1440,1622,1747,1869,1974,2066,2204,2299,2402,2504,2606,2703,2817,2952,3082,3209,3314,3422,3544,3666,3783,3900,3987,4071,4173,4307,4460", "endColumns": "106,100,98,96,126,114,100,92,138,135,101,117,181,124,121,104,91,137,94,102,101,101,96,113,134,129,126,104,107,121,121,116,116,86,83,101,133,152,94", "endOffsets": "207,308,407,504,631,746,847,940,1079,1215,1317,1435,1617,1742,1864,1969,2061,2199,2294,2397,2499,2601,2698,2812,2947,3077,3204,3309,3417,3539,3661,3778,3895,3982,4066,4168,4302,4455,4550"}, "to": {"startLines": "165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14061,14168,14269,14368,14465,14592,14707,14808,14901,15040,15176,15278,15396,15578,15703,15825,15930,16022,16160,16255,16358,16460,16562,16659,16773,16908,17038,17165,17270,17378,17500,17622,17739,17856,17943,18027,18129,18263,23193", "endColumns": "106,100,98,96,126,114,100,92,138,135,101,117,181,124,121,104,91,137,94,102,101,101,96,113,134,129,126,104,107,121,121,116,116,86,83,101,133,152,94", "endOffsets": "14163,14264,14363,14460,14587,14702,14803,14896,15035,15171,15273,15391,15573,15698,15820,15925,16017,16155,16250,16353,16455,16557,16654,16768,16903,17033,17160,17265,17373,17495,17617,17734,17851,17938,18022,18124,18258,18411,23283"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f143c6e419d61b5e7da1da45bd6aa2a1\\transformed\\media3-session-1.2.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,133,208,277,349,426,503,601", "endColumns": "77,74,68,71,76,76,97,100", "endOffsets": "128,203,272,344,421,498,596,697"}, "to": {"startLines": "50,102,215,216,217,218,219,220", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3606,9290,19348,19417,19489,19566,19643,19741", "endColumns": "77,74,68,71,76,76,97,100", "endOffsets": "3679,9360,19412,19484,19561,19638,19736,19837"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f7431c6a202a424a0713952a4987d458\\transformed\\core-1.12.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "56,57,58,59,60,61,62,270", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4241,4340,4442,4542,4640,4747,4853,24018", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "4335,4437,4537,4635,4742,4848,4968,24114"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\cd0d18521f4c5c20c6fe1e241ab341cc\\transformed\\preference-1.2.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,350,492,661,749", "endColumns": "69,96,77,141,168,87,81", "endOffsets": "170,267,345,487,656,744,826"}, "to": {"startLines": "69,158,258,263,272,290,291", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5543,13484,22987,23371,24226,26000,26088", "endColumns": "69,96,77,141,168,87,81", "endOffsets": "5608,13576,23060,23508,24390,26083,26165"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d967763d34b2e16001ff17d7de719a33\\transformed\\media3-ui-1.2.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,475,662,748,835,908,1004,1100,1180,1248,1347,1446,1512,1581,1647,1718,1813,1908,2003,2074,2158,2234,2314,2412,2511,2577,2641,2694,2752,2800,2861,2926,2988,3054,3126,3190,3251,3317,3382,3448,3501,3566,3645,3724", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,72,95,95,79,67,98,98,65,68,65,70,94,94,94,70,83,75,79,97,98,65,63,52,57,47,60,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "280,470,657,743,830,903,999,1095,1175,1243,1342,1441,1507,1576,1642,1713,1808,1903,1998,2069,2153,2229,2309,2407,2506,2572,2636,2689,2747,2795,2856,2921,2983,3049,3121,3185,3246,3312,3377,3443,3496,3561,3640,3719,3777"}, "to": {"startLines": "2,11,15,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,335,525,9695,9781,9868,9941,10037,10133,10213,10281,10380,10479,10545,10614,10680,10751,10846,10941,11036,11107,11191,11267,11347,11445,11544,11610,12343,12396,12454,12502,12563,12628,12690,12756,12828,12892,12953,13019,13084,13150,13203,13268,13347,13426", "endLines": "10,14,18,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157", "endColumns": "17,12,12,85,86,72,95,95,79,67,98,98,65,68,65,70,94,94,94,70,83,75,79,97,98,65,63,52,57,47,60,64,61,65,71,63,60,65,64,65,52,64,78,78,57", "endOffsets": "330,520,707,9776,9863,9936,10032,10128,10208,10276,10375,10474,10540,10609,10675,10746,10841,10936,11031,11102,11186,11262,11342,11440,11539,11605,11669,12391,12449,12497,12558,12623,12685,12751,12823,12887,12948,13014,13079,13145,13198,13263,13342,13421,13479"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e9729538bde244a4064dd4e07fe2100b\\transformed\\ui-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,286,384,487,576,655,751,843,930,994,1058,1145,1235,1312,1390,1460", "endColumns": "98,81,97,102,88,78,95,91,86,63,63,86,89,76,77,69,122", "endOffsets": "199,281,379,482,571,650,746,838,925,989,1053,1140,1230,1307,1385,1455,1578"}, "to": {"startLines": "66,67,101,103,105,162,163,256,257,259,260,264,265,268,273,274,275", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5284,5383,9192,9365,9541,13823,13902,22808,22900,23065,23129,23513,23600,23856,24395,24473,24543", "endColumns": "98,81,97,102,88,78,95,91,86,63,63,86,89,76,77,69,122", "endOffsets": "5378,5460,9285,9463,9625,13897,13993,22895,22982,23124,23188,23595,23685,23928,24468,24538,24661"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3c10f13ce021e8e2981c4376f2d01b28\\transformed\\media3-exoplayer-1.2.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,193,258,327,404,478,567,655", "endColumns": "74,62,64,68,76,73,88,87,68", "endOffsets": "125,188,253,322,399,473,562,650,719"}, "to": {"startLines": "131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "11674,11749,11812,11877,11946,12023,12097,12186,12274", "endColumns": "74,62,64,68,76,73,88,87,68", "endOffsets": "11744,11807,11872,11941,12018,12092,12181,12269,12338"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b8d3f188d948b1d91488b96ae88ce5c0\\transformed\\navigation-ui-2.7.6\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,127", "endOffsets": "165,293"}, "to": {"startLines": "254,255", "startColumns": "4,4", "startOffsets": "22565,22680", "endColumns": "114,127", "endOffsets": "22675,22803"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d037915cf63427aa04d936e78cd9314\\transformed\\material-1.4.0-beta01\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,233,319,421,549,630,695,790,860,923,1016,1088,1151,1225,1289,1346,1464,1522,1584,1641,1721,1855,1944,2025,2136,2217,2297,2387,2454,2520,2596,2678,2766,2839,2916,2986,3063,3152,3226,3320,3422,3494,3575,3679,3732,3799,3892,3981,4043,4107,4170,4281,4378,4480,4578", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,85,101,127,80,64,94,69,62,92,71,62,73,63,56,117,57,61,56,79,133,88,80,110,80,79,89,66,65,75,81,87,72,76,69,76,88,73,93,101,71,80,103,52,66,92,88,61,63,62,110,96,101,97,82", "endOffsets": "228,314,416,544,625,690,785,855,918,1011,1083,1146,1220,1284,1341,1459,1517,1579,1636,1716,1850,1939,2020,2131,2212,2292,2382,2449,2515,2591,2673,2761,2834,2911,2981,3058,3147,3221,3315,3417,3489,3570,3674,3727,3794,3887,3976,4038,4102,4165,4276,4373,4475,4573,4656"}, "to": {"startLines": "19,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "712,4155,4973,5075,5203,9630,13658,13753,13998,18416,18509,18581,18644,18718,18782,18839,18957,19015,19077,19134,19214,19842,19931,20012,20123,20204,20284,20374,20441,20507,20583,20665,20753,20826,20903,20973,21050,21139,21213,21307,21409,21481,21562,21666,21719,21786,21879,21968,22030,22094,22157,22268,22365,22467,23288", "endLines": "22,55,63,64,65,106,160,161,164,203,204,205,206,207,208,209,210,211,212,213,214,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,262", "endColumns": "12,85,101,127,80,64,94,69,62,92,71,62,73,63,56,117,57,61,56,79,133,88,80,110,80,79,89,66,65,75,81,87,72,76,69,76,88,73,93,101,71,80,103,52,66,92,88,61,63,62,110,96,101,97,82", "endOffsets": "885,4236,5070,5198,5279,9690,13748,13818,14056,18504,18576,18639,18713,18777,18834,18952,19010,19072,19129,19209,19343,19926,20007,20118,20199,20279,20369,20436,20502,20578,20660,20748,20821,20898,20968,21045,21134,21208,21302,21404,21476,21557,21661,21714,21781,21874,21963,22025,22089,22152,22263,22360,22462,22560,23366"}}]}]}