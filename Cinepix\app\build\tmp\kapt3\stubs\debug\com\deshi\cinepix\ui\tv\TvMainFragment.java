package com.deshi.cinepix.ui.tv;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\u0018\u00002\u00020\u0001:\u0003\f\r\u000eB\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0005\u001a\u00020\u0006H\u0002J\u0012\u0010\u0007\u001a\u00020\u00062\b\u0010\b\u001a\u0004\u0018\u00010\tH\u0016J\b\u0010\n\u001a\u00020\u0006H\u0002J\b\u0010\u000b\u001a\u00020\u0006H\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000f"}, d2 = {"Lcom/deshi/cinepix/ui/tv/TvMainFragment;", "Landroidx/leanback/app/BrowseSupportFragment;", "()V", "rowsAdapter", "Landroidx/leanback/widget/ArrayObjectAdapter;", "loadRows", "", "onActivityCreated", "savedInstanceState", "Landroid/os/Bundle;", "setupEventListeners", "setupUI", "ItemViewClickedListener", "ItemViewSelectedListener", "SearchClickedListener", "app_debug"})
public final class TvMainFragment extends androidx.leanback.app.BrowseSupportFragment {
    private androidx.leanback.widget.ArrayObjectAdapter rowsAdapter;
    
    public TvMainFragment() {
        super();
    }
    
    @java.lang.Override
    public void onActivityCreated(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupUI() {
    }
    
    private final void setupEventListeners() {
    }
    
    private final void loadRows() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0082\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J0\u0010\u0003\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u00062\b\u0010\u0007\u001a\u0004\u0018\u00010\b2\b\u0010\t\u001a\u0004\u0018\u00010\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u0016\u00a8\u0006\r"}, d2 = {"Lcom/deshi/cinepix/ui/tv/TvMainFragment$ItemViewClickedListener;", "Landroidx/leanback/widget/OnItemViewClickedListener;", "(Lcom/deshi/cinepix/ui/tv/TvMainFragment;)V", "onItemClicked", "", "itemViewHolder", "Landroidx/leanback/widget/Presenter$ViewHolder;", "item", "", "rowViewHolder", "Landroidx/leanback/widget/RowPresenter$ViewHolder;", "row", "Landroidx/leanback/widget/Row;", "app_debug"})
    final class ItemViewClickedListener implements androidx.leanback.widget.OnItemViewClickedListener {
        
        public ItemViewClickedListener() {
            super();
        }
        
        @java.lang.Override
        public void onItemClicked(@org.jetbrains.annotations.Nullable
        androidx.leanback.widget.Presenter.ViewHolder itemViewHolder, @org.jetbrains.annotations.Nullable
        java.lang.Object item, @org.jetbrains.annotations.Nullable
        androidx.leanback.widget.RowPresenter.ViewHolder rowViewHolder, @org.jetbrains.annotations.Nullable
        androidx.leanback.widget.Row row) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0082\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J0\u0010\u0003\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u00062\b\u0010\u0007\u001a\u0004\u0018\u00010\b2\b\u0010\t\u001a\u0004\u0018\u00010\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u0016\u00a8\u0006\r"}, d2 = {"Lcom/deshi/cinepix/ui/tv/TvMainFragment$ItemViewSelectedListener;", "Landroidx/leanback/widget/OnItemViewSelectedListener;", "(Lcom/deshi/cinepix/ui/tv/TvMainFragment;)V", "onItemSelected", "", "itemViewHolder", "Landroidx/leanback/widget/Presenter$ViewHolder;", "item", "", "rowViewHolder", "Landroidx/leanback/widget/RowPresenter$ViewHolder;", "row", "Landroidx/leanback/widget/Row;", "app_debug"})
    final class ItemViewSelectedListener implements androidx.leanback.widget.OnItemViewSelectedListener {
        
        public ItemViewSelectedListener() {
            super();
        }
        
        @java.lang.Override
        public void onItemSelected(@org.jetbrains.annotations.Nullable
        androidx.leanback.widget.Presenter.ViewHolder itemViewHolder, @org.jetbrains.annotations.Nullable
        java.lang.Object item, @org.jetbrains.annotations.Nullable
        androidx.leanback.widget.RowPresenter.ViewHolder rowViewHolder, @org.jetbrains.annotations.Nullable
        androidx.leanback.widget.Row row) {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0082\u0004\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0012\u0010\u0003\u001a\u00020\u00042\b\u0010\u0005\u001a\u0004\u0018\u00010\u0006H\u0016\u00a8\u0006\u0007"}, d2 = {"Lcom/deshi/cinepix/ui/tv/TvMainFragment$SearchClickedListener;", "Landroid/view/View$OnClickListener;", "(Lcom/deshi/cinepix/ui/tv/TvMainFragment;)V", "onClick", "", "view", "Landroid/view/View;", "app_debug"})
    final class SearchClickedListener implements android.view.View.OnClickListener {
        
        public SearchClickedListener() {
            super();
        }
        
        @java.lang.Override
        public void onClick(@org.jetbrains.annotations.Nullable
        android.view.View view) {
        }
    }
}